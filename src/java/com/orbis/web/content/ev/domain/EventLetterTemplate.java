package com.orbis.web.content.ev.domain;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.hibernate.annotations.ColumnDefault;
import com.orbis.web.content.doc.DocHelper;
import org.json.JSONObject;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.docTemp.DocTemplate;
import com.orbis.docTemp.DocTemplateHelper;
import com.orbis.docTemp.DocumentModel;
import com.orbis.docTemp.DocumentModelItem;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.DBUtils;
import com.orbis.utils.I18nLabel;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;

@Access(value = AccessType.FIELD)
public class EventLetterTemplate extends DocTemplate
{
    private static final long serialVersionUID = 939774858074205975L;

    public static final int TYPE_NAME_BADGES = 0;

    private GlobalEventModule module;

    public GlobalEventModule getModule()
    {
        return module;
    }

    public void setModule(GlobalEventModule module)
    {
        this.module = module;
    }

    @Override
    public String getTableName()
    {
        return "event_letter_template";
    }

    @Override
    protected Map<Integer, String> generateTypeMap(Locale locale)
    {
        Map<Integer, String> typeMap = new LinkedHashMap<>();
        typeMap.put(TYPE_NAME_BADGES,
                new I18nLabel("i18n.EventLetterTemplate.NameBadges0517824912958350")
                        .getTranslation(locale));
        return typeMap;
    }

    @Override
    public List<DocumentModel> populateDocumentData(JSONObject additionalParams,
            Locale locale, List<Integer> itemIds, String[] fieldsUsed,
            UserDetailsImpl userLoggedIn) throws Exception
    {
        List<DocumentModel> documentModels = new LinkedList<DocumentModel>();
        List<GlobalEventRegistration> registrants = PortalUtils.getHt()
                .find("from GlobalEventRegistration reg where reg.id in " + DBUtils
                        .buildInClause(additionalParams.getJSONArray("regIds")));

        for (GlobalEventRegistration registrant : registrants)
        {
            DocHelper.overrideFirstName(registrant.getUser());
        }

        DocumentModel docModel = new DocumentModel();
        docModel.addItem(new DocumentModelItem("registrants", registrants));

        documentModels.add(docModel);
        return documentModels;
    }

    @Override
    public String getAdditionalHql(JSONObject additionalParams) throws Exception
    {
        return " t.module.id = " + additionalParams.getInt("moduleId");
    }

    @Override
    public void setAdditionalData(JSONObject additionalParams) throws Exception
    {
        GlobalEventModule module = PortalUtils.getHt()
                .load(GlobalEventModule.class, additionalParams.getInt("moduleId"));
        this.setModule(module);
    }

    @Override
    public JSONObject getSample(JSONObject additionalParams) throws Exception
    {
        return DocTemplateHelper.loadDocTemplateSample("eventRecord");
    }

    @Override
    @Access(AccessType.PROPERTY)
    public String getName()
    {
        return super.getName();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public String getL2Name()
    {
        return super.getL2Name();
    }

    @Override
    @Access(AccessType.PROPERTY)
    @ColumnDefault("0")
    public int getType()
    {
        return super.getType();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public String getDocument()
    {
        return super.getDocument();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public String getL2Document()
    {
        return super.getL2Document();
    }

    @Override
    @Access(AccessType.PROPERTY)
    @ColumnDefault("0")
    public boolean isDefaultForType()
    {
        return super.isDefaultForType();
    }
}
