package com.orbis.web.content.ecommerce;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.multiaction.ParameterMethodNameResolver;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.jqgrid.JQGridHelper;
import com.orbis.jqgrid.JQGridModel;
import com.orbis.jqgrid.JQGridSubController;
import com.orbis.portal.OrbisRepost;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.portal.PortalUtils;
import com.orbis.search.SearchHelper;
import com.orbis.search.SearchModel;
import com.orbis.search.SearchSubControllerFactory;
import com.orbis.search.entity.Entity;
import com.orbis.utils.CollectionUtils;
import com.orbis.utils.FlashMessageUtils;
import com.orbis.utils.NumberUtils;
import com.orbis.utils.RequestUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.OrbisInteractionController;
import com.orbis.web.content.acrm.AcrmHelper;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.crm.Organization;
import com.orbis.web.content.grid.GridHelper;
import com.orbis.web.content.grid.GridOptions;
import com.orbis.web.content.interaction.InteractionHelper;
import com.orbis.web.content.interaction.InteractionHelper.INTERACTION_TYPE;
import com.orbis.web.content.np.NHelper;
import com.orbis.web.site.SiteElement;
import com.orbis.web.site.SiteManager;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * To be deployed in a secure area of ePortal. This controller supports the
 * administrative use-cases of the ecommerce framework.
 * 
 * <AUTHOR>
 */

@Controller("ecommerceAdminController")
@RequestMapping("ecommerceAdminController")
public class EcommerceAdminController
        extends OrbisInteractionController<EcommerceAdminModule>
        implements EcommerceOrderDetailsUI
{

    public EcommerceAdminController(SiteManager siteManager)
    {
        super(siteManager);
        setMethodNameResolver(
                new ParameterMethodNameResolver("action", "displayHome"));
    }

    private EcommerceSearchInterface searchInterface = new EcommerceSearchInterface(
            this);

    private EcommerceGridInterface ecommerceGridInterface = new EcommerceGridInterface(
            this);

    @Override
    protected ModelAndView handleRequestInternal(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = null;
        if (PortalUtils.getUserLoggedIn(request).isPortalStaff())
        {
            mv = super.handleRequestInternal(request, response);
        }
        return mv;
    }

    @Override
    @RequestMapping("jqGrid")
    public ModelAndView jqGrid(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        return JQGridSubController.getInstance(ecommerceGridInterface)
                .processRequest(request, response);
    }

    @RequestMapping("search")
    public ModelAndView search(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        return SearchSubControllerFactory.getInstance(searchInterface)
                .processRequest(request, response);
    }

    @Override
    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "displayHome")
    public ModelAndView displayHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        EcommerceAdminModule module = getModule(request);
        ModelAndView mv = new ModelAndView("ecommerce/ecommerce_adminHome");
        mv.addObject("ecommerceManager",
                EcommerceHelper.isUserEcommerceManager(request));
        mv.addObject("module", module);
        mv.addObject("configErrors",
                EcommerceHelper.validatePaymentVendorIntegration(module));

        Map<String, Object> statsBox = new HashMap<String, Object>();
        EcommerceReportHelper.populateStatsBox(statsBox, module);
        mv.addObject("statsBox", statsBox);

        return mv;
    }

    @RequestMapping("ajaxLoadAdminHomeStats")
    public ModelAndView ajaxLoadAdminHomeStats(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView(
                "ecommerce/ecommerce_adminHome_statsAjax");
        EcommerceReportFilter reportFilter = EcommerceReportHelper
                .getReportFilter(request, getModule(request));
        mv.addObject("stats", EcommerceReportHelper.getHomeReports(reportFilter,
                PortalUtils.getLocale(request)));
        return mv;
    }

    @RequestMapping("displaySavedSearch")
    public ModelAndView displaySavedSearch(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("ecommerce/ecommerce_nav_savedSearch");

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName("ajaxLoadSavedSearches");
        GridHelper.addGridSupport(request, mv, "advancedSearch_savedSearches",
                options);
        mv.addObject("shareLevelOptions",
                SearchHelper.getShareLevelOptions(PortalUtils.getLocale(request)));

        mv.addObject("ecommerceManager",
                EcommerceHelper.isUserEcommerceManager(request));
        return mv;
    }

    @RequestMapping("displayReports")
    public ModelAndView displayReports(HttpServletRequest request,
            HttpServletResponse response)
    {
        Locale locale = PortalUtils.getLocale(request);

        ModelAndView mv = new ModelAndView("ecommerce/ecommerce_nav_reports");

        EcommerceReportFilter reportFilter = EcommerceReportHelper
                .getReportFilter(request, getModule(request));

        String tabView = request.getParameter("tabView");

        if (StringUtils.isEmpty(tabView)
                && request.getParameter("reportFilter") != null)
        {
            tabView = "summaryTab";
        }

        if (!StringUtils.isEmpty(tabView))
        {
            mv.addObject("tabView", tabView);

            if (tabView.equals("summaryTab"))
            {
                mv.addObject("stats",
                        EcommerceReportHelper.getReports(reportFilter, locale));
            }
            else if (tabView.equals("detailsTab"))
            {
                populateReportDetailsTab(mv, request, reportFilter);
            }
        }

        mv.addObject("reportFilter", reportFilter);
        List<String> typeList = EcommerceReportHelper
                .getSelectedTypeListAsString(reportFilter);
        if (typeList == null || typeList.isEmpty())
        {
            typeList = new ArrayList();
            typeList.add("No Types Selected");
            mv.addObject("typeList", typeList);
        }
        else
        {
            mv.addObject("typeList", EcommerceReportHelper
                    .getSelectedTypeListAsString(reportFilter));
        }

        mv.addObject("ecommerceManager",
                EcommerceHelper.isUserEcommerceManager(request));

        return mv;
    }

    private void populateReportDetailsTab(ModelAndView mv,
            HttpServletRequest request, EcommerceReportFilter reportFilter)
    {
        Locale locale = PortalUtils.getLocale(request);

        final SiteElement siteElement = PortalUtils.getCurrentElement(request);

        SearchModel searchModel = EcommerceHelper.getSearchModel_EcommerceOrder(
                siteElement, PortalUtils.getLocale(request),
                PortalUtils.getUserLoggedIn(request));
        SearchHelper.setSearchModelToSession(searchModel, request);

        String filterClause = EcommerceReportHelper.getOrderFilterClause("o",
                reportFilter);

        if (filterClause.length() > 0)
        {
            Entity master = searchModel.getMasterEntity();
            String staticWhereHql = master.getStaticWhereHql();
            master.setStaticWhereHql(
                    staticWhereHql == null ? filterClause.toString()
                            : (staticWhereHql + " and " + filterClause.toString()));
        }

        JQGridModel gridModel = SearchHelper.createJQGridModel(searchModel, locale);
        gridModel.getDetails().values()
                .forEach(jqSearch -> jqSearch.setRelationship(SearchHelper
                        .getDetailRelationship(jqSearch, gridModel, searchModel)));

        JQGridHelper.populateSearchResultsPage(mv, gridModel, request,
                SearchHelper.SEARCH_MODEL_SESSION_KEY);
    }

    @RequestMapping("displayStats")
    public ModelAndView displayStats(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("ecommerce/ecommerce_nav_stats");
        mv.addObject("ecommerceManager",
                EcommerceHelper.isUserEcommerceManager(request));
        return mv;
    }

    @RequestMapping("displayUserLookupSample")
    public ModelAndView displayUserLookupSample(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("ecommerce/ecommerce_userLook-up");
        return mv;
    }

    @RequestMapping("displayConfig")
    public ModelAndView displayConfig(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("ecommerce/ecommerce_adminConfig");
        return mv;
    }

    @RequestMapping("displayVendorConfig")
    public ModelAndView displayVendorConfig(HttpServletRequest request,
            HttpServletResponse response)
    {
        EcommerceAdminModule module = getModule(request);

        ModelAndView mv = new ModelAndView("ecommerce/ecommerce_adminVendorConfig");

        mv.addObject("module", module);
        mv.addObject("configs", EcommerceHelper.getPaymentVendorConfigs(module));
        mv.addObject("ecommerceCallbackUrl",
                EcommerceHelper.getEcommerceCallbackUrl(module));
        mv.addObject("configErrors",
                EcommerceHelper.validatePaymentVendorIntegration(module));
        mv.addObject("ecommerceManager",
                EcommerceHelper.isUserEcommerceManager(request));

        return mv;
    }

    @RequestMapping("saveConfig")
    public ModelAndView saveConfig(HttpServletRequest request,
            HttpServletResponse response)
    {
        EcommerceAdminModule module = getModule(request);

        module.setSendEmailNotification(
                request.getParameter("sendEmailNotification") != null);

        module.setNotificationEmailAddress(
                request.getParameter("notificationEmailAddress"));

        getHt().update(module);

        List<EcommerceAdminVendorConfig> configs = EcommerceHelper
                .getPaymentVendorConfigs(module);

        for (EcommerceAdminVendorConfig c : configs)
        {
            c.setConfigValue(request.getParameter(c.getConfigKey()));
            getHt().saveOrUpdate(c);
        }

        PortalConfigHelper.reloadPortalConfigMap();
        PortalUtils.reloadPortalVaribles(request);

        ModelAndView mv = displayVendorConfig(request, response);
        return mv;
    }

    @RequestMapping("setPaymentVendor")
    public ModelAndView setPaymentVendor(HttpServletRequest request,
            HttpServletResponse response)
    {
        EcommerceAdminModule module = getModule(request);
        int pvType = Integer.valueOf(request.getParameter("pvType"));

        module.setPaymentVendorType(pvType);
        getHt().update(module);

        EcommerceVendorInterface ecomVendor = EcommerceHelper
                .getEcommerceVendorInterface(pvType);

        Integer configCount = getHt().findInt(
                "select count(e) from EcommerceAdminVendorConfig e where "
                        + " e.ecomModuleId = ? and e.vendorId = ? ",
                module.getId(), module.getPaymentVendorType());

        if (configCount <= 0)
        {
            String[] vcParams = ecomVendor.getConfigParams();
            for (String param : vcParams)
            {
                EcommerceAdminVendorConfig vc = new EcommerceAdminVendorConfig();
                vc.setEcomModuleId(module.getId());
                vc.setVendorId(pvType);
                vc.setConfigKey(param);
                vc.setConfigValue("");
                getHt().save(vc);
            }
        }

        ModelAndView mv = displayVendorConfig(request, response);

        return mv;
    }

    @OrbisRepost
    @RequestMapping("displayOrderDetails")
    public ModelAndView displayOrderDetails(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("ecommerce/ecommerce_adminOrderDetails");

        EcommerceOrder order = null;

        if (StringUtils.equals(request.getParameter("entity"),
                "EcommerceOrderItem"))
        {
            order = PortalUtils.getHt()
                    .get(EcommerceOrderItem.class,
                            RequestUtils.getInteger(request, "rowId"))
                    .getEcommerceOrder();
        }
        else
        {
            order = EcommerceHelper.getOrder(request);
        }

        if (null != order.getParentOrder() || order.isReconciliationOrder())
        {
            order = EcommerceHelper.getParentOrder(order);
        }

        mv.addObject("order", order);

        if (order.getCustomer() != null)
        {
            // populate mv with 'acrmSE' if the user-logged-in can view the
            // customer's account....
            if (AcrmHelper.canViewAccount(PortalUtils.getUserLoggedIn(request),
                    order.getCustomer().getPrimaryGroup()))
            {
                NHelper.populateAcrmCareerSiteElement(mv, request);
            }

            // populate the mv with org stuff if the customer belongs to an
            // Organization...
            if (order.getCustomer().getOrganization() != null)
            {
                Organization org = NHelper.getOrganizationByName(
                        order.getCustomer().getOrganization());
                mv.addObject("organization", org);

                UserDetailsImpl user = UserDetailsHelper
                        .getUserByUsername(order.getCustomer().getUsername());

                mv.addObject("acrmUser", user);
                mv.addObject("division",
                        NHelper.getDivisionByOrgAndName(org, user.getDivision()));
            }
        }

        EcommerceEntity orderEntity = EcommerceHelper.getEcommerceEntity(order);

        if (orderEntity != null)
        {
            mv.addObject("orderEntity", orderEntity);
            mv.addObject("typeDetail", orderEntity.getI18NSourceDescription()
                    .get(PortalUtils.getLocale(request).getLanguage()));
            mv.addObject("orderType", EcommerceHelper
                    .getEcommerceEntityTypeI18N(orderEntity, request));
            mv.addObject("emailLogs",
                    EcommerceHelper.getOrderEmailLogs(orderEntity));
        }

        mv.addObject("ecommerceManager",
                EcommerceHelper.isUserEcommerceManager(request));

        return mv;
    }

    @RequestMapping("displayTestVendor")
    public ModelAndView displayTestVendor(HttpServletRequest request,
            HttpServletResponse response)
    {
        EcommerceAdminModule module = getModule(request);

        ModelAndView mv = new ModelAndView("ecommerce/ecommerce_adminTestVendor");
        mv.addObject("module", module);
        mv.addObject("configs", EcommerceHelper.getPaymentVendorConfigs(module));
        mv.addObject("ecommerceCallbackUrl",
                EcommerceHelper.getEcommerceCallbackUrl(module));
        return mv;
    }

    @RequestMapping("checkoutTestOrder")
    public ModelAndView checkoutTestOrder(HttpServletRequest request,
            HttpServletResponse response)
    {
        EcommerceAdminModule module = getModule(request);

        String orderNumberPrefix = "TEST" + module.getPaymentVendorLabelShort();
        orderNumberPrefix = orderNumberPrefix.replace(" ", "");
        if (orderNumberPrefix
                .length() > EcommerceHelper.ORDER_CODE_PREFIX_MAX_LENGTH)
        {
            orderNumberPrefix = orderNumberPrefix.substring(0,
                    EcommerceHelper.ORDER_CODE_PREFIX_MAX_LENGTH - 1);
        }

        EcommerceTestEntity testEntity = new EcommerceTestEntity();
        testEntity.setEcommerceAdminModule(module);
        getHt().save(testEntity);

        EcommerceOrder order = null;

        try
        {
            // CREATE ECOMMERCE ORDER...
            order = EcommerceHelper.createOrder(testEntity, orderNumberPrefix,
                    EcommerceOrder.PAYMENT_TYPE_CREDIT_CARD,
                    PortalUtils.getUserLoggedIn(request), null, module, request);
        }
        catch (EcommerceException e)
        {
            e.printStackTrace();
            return EcommerceHelper.getErrorPage(e.getErrors(), request);
        }

        String productName = orderNumberPrefix;
        String productCode = orderNumberPrefix;
        String productDescription = "Test Transaction for "
                + module.getPaymentVendorLabel();

        Double total = null;
        if (NumberUtils.isDouble(request.getParameter("amount")))
        {
            total = new Double(request.getParameter("amount"));
        }
        else
        {
            total = 0.0;
        }

        EcommerceHelper.addOrderItem(order, productName,
                productName + PortalUtils.getSecondaryLocale(), productCode,
                productDescription,
                productDescription + PortalUtils.getSecondaryLocale(), total, total,
                null, null, null, null, null, null, null, null);

        // ROUTING...
        EcommerceRouting routing = new EcommerceRouting(
                PortalUtils.getPageUrl(
                        NHelper.getSiteElementByModule(getModule(request))),
                "displayTestReceipt", EcommerceTestOrderProcessor.class);

        // CHECKOUT!
        return EcommerceHelper.checkoutOrder(request, module, testEntity, order,
                routing);
    }

    @OrbisRepost
    @RequestMapping("displayTestReceipt")
    public ModelAndView displayTestReceipt(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "ecommerce/ecommerce_adminTestVendorReceipt");

        EcommerceOrder order = EcommerceHelper.getOrder(request);
        mv.addObject("order", order);
        mv.addObject("orderItems", EcommerceHelper.getOrderItems(order));
        mv.addObject("module", getModule(request));

        return mv;
    }

    @RequestMapping("displayTaxes")
    public ModelAndView displayTaxes(HttpServletRequest request,
            HttpServletResponse response)
    {
        EcommerceAdminModule module = getModule(request);

        List<EcommerceTax> taxes = EcommerceHelper.getTaxes(false, module.getId());

        for (EcommerceTax tax : taxes)
        {
            tax.setValid(EcommerceHelper.isValid(tax));
        }

        ModelAndView mv = new ModelAndView("ecommerce/ecommerce_adminTaxes");
        mv.addObject("taxes", taxes);
        mv.addObject("deletedTaxes",
                EcommerceHelper.getTaxes(true, module.getId()));
        mv.addObject("ecommerceManager",
                EcommerceHelper.isUserEcommerceManager(request));
        return mv;
    }

    @RequestMapping("createTax")
    public ModelAndView createTax(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        EcommerceAdminModule module = getModule(request);

        if (EcommerceHelper.getTaxes(module.getId()).size() >= 4)
        {
            mv = displayTaxes(request, response);
            mv.addObject("taxCreated", false);
        }
        else
        {
            EcommerceTax newTax = EcommerceHelper.createTax(module);
            mv = displayTaxes(request, response);
            mv.addObject("newTax", newTax);
            mv.addObject("taxCreated", true);
        }

        return mv;
    }

    @RequestMapping("deleteTax")
    public ModelAndView deleteTax(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        EcommerceTax tax = EcommerceHelper.getTax(request);
        EcommerceAdminModule module = getModule(request);

        if (tax.isDeleted() && EcommerceHelper.getTaxes(module.getId())
                .size() >= EcommerceTax.MAX_SORT_ORDER)
        {
            mv = displayTaxes(request, response);
            mv.addObject("taxUndeleted", false);
        }
        else
        {
            if (tax.isDeleted())
            {
                tax.setOrdering(EcommerceHelper.findSortOrderForDefaultTax(module));
            }
            tax.setDeleted(!tax.isDeleted());
            getHt().update(tax);

            mv = displayTaxes(request, response);

            if (tax.isDeleted())
            {
                mv.addObject("taxDeleted", true);
            }
            else
            {
                mv.addObject("taxUndeleted", true);
            }
        }

        return mv;
    }

    @RequestMapping("saveTaxes")
    public ModelAndView saveTaxes(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv;
        EcommerceAdminModule adminModule = getModule(request);
        List<EcommerceTax> taxes = EcommerceHelper.getTaxes(false,
                adminModule.getId());
        if (EcommerceHelper.validateDefaultTaxesSortOrders(taxes, request))
        {
            for (EcommerceTax tax : taxes)
            {
                String taxId = tax.getId().toString();
                tax.setTaxCode(request.getParameter("taxCode" + taxId));
                tax.setDescription(request.getParameter("description" + taxId));
                String taxValue = request.getParameter("taxValue" + taxId);
                if (StringUtils.isNumber(taxValue))
                {
                    tax.setTaxValue(new Double(taxValue).doubleValue());
                }
                else
                {
                    tax.setTaxValue(0d);
                }
                tax.setOrdering(
                        Integer.parseInt(request.getParameter("taxOrder" + taxId)));
                tax.setModule(adminModule);
                getHt().update(tax);
            }
            mv = displayTaxes(request, response);
            mv.addObject("taxesUpdated", true);
        }
        else
        {
            mv = displayTaxes(request, response);
            FlashMessageUtils.error(request,
                    "i18n.EcommerceAdminController.Incorrecto5894149173485121");
        }
        return mv;
    }

    @Override
    @RequestMapping("ajaxSaveOrderStatus")
    public ModelAndView ajaxSaveOrderStatus(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.ajaxSaveOrderStatus(request);
    }

    @Override
    @RequestMapping("ajaxSaveOrderNote")
    public ModelAndView ajaxSaveOrderNote(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.ajaxSaveOrderNote(request);
    }

    @RequestMapping("ajaxLookupOrder")
    public ModelAndView ajaxLookupOrder(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        JSONArray ret = new JSONArray();

        String searchTerm = request.getParameter("term") != null
                ? request.getParameter("term")
                : request.getParameter("q");

        List<Object[]> srchResults = new ArrayList<Object[]>();

        if (searchTerm.length() > 0)
        {
            String hql = "select distinct o.orderNumber, o.id from EcommerceOrder o where o.module=? and o.reconcileTarget is null and o.orderNumber like '%"
                    + searchTerm + "%' order by o.orderNumber";
            srchResults = getHt().find(hql, getModule(request));
            srchResults = CollectionUtils.limitList(srchResults, 50);
        }

        JSONObject result = null;
        for (Object[] user : srchResults)
        {
            result = new JSONObject();
            result.put("id", user[1]);
            result.put("label", user[0]);
            ret.put(result);
        }

        return jsonArrayResponse(ret);
    }

    @RequestMapping("ajaxLookupCustomer")
    public ModelAndView ajaxLookupCustomer(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        return AcrmHelper.lookupUsers(request.getParameter("term"), null,
                " and e.module=" + getModule(request).getId(), EcommerceOrder.class,
                "e", "e.customer", 20,
                "distinct e.customer.id, e.customer.preferredFirstName, e.customer.lastName, e.customer.username",
                "e.customer.preferredFirstName, e.customer.lastName, e.customer.username", 0,
                "[1] [2] ([3])", false, null);
    }

    @RequestMapping("displayCustomerOrders")
    public ModelAndView displayCustomerOrders(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("ecommerce/ecommerce_userLook-up");
        Integer userId = Integer.valueOf(request.getParameter("userId"));

        List<UserDetailsImpl> users = getHt()
                .find("from UserDetailsImpl a where a.id=?", userId);

        UserDetailsImpl user = null;
        if (!users.isEmpty())
        {
            user = users.get(0);
            Organization org = NHelper
                    .getOrganizationByName(user.getOrganization());

            if (org != null)
            {
                mv.addObject("organization", org.getName());
                mv.addObject("orgId", org.getId().toString());
            }
        }
        mv.addObject("user", user);
        mv.addObject("acrmAdminSe", NHelper
                .getAcrmCareerSiteElement(PortalUtils.getUserLoggedIn(request)));
        mv.addObject("ecommerceModule", getModule(request));
        mv.addObject("ecommerceManager",
                EcommerceHelper.isUserEcommerceManager(request));

        return mv;
    }

    @Override
    @RequestMapping("checkoutAdjustmentOrder")
    public ModelAndView checkoutAdjustmentOrder(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        return EcommerceHelper.checkoutAdjustmentOrder(request, getModule(request));
    }

    @Override
    @RequestMapping("ajaxLoadOrderDetails")
    public ModelAndView ajaxLoadOrderDetails(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.ajaxLoadOrderDetails(request);
    }

    @Override
    @RequestMapping("ajaxLoadOrderHistory")
    public ModelAndView ajaxLoadOrderHistory(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.ajaxLoadOrderHistory(request);
    }

    @Override
    @RequestMapping("ajaxLoadCustomerOrders")
    public ModelAndView ajaxLoadCustomerOrders(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.ajaxLoadCustomerOrders(request);
    }

    @Override
    @RequestMapping("printOrderHistory")
    public ModelAndView printOrderHistory(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.printOrderHistory(request);
    }

    @Override
    @RequestMapping("ajaxLoadEmailOrderHistory")
    public ModelAndView ajaxLoadEmailOrderHistory(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.ajaxLoadEmailOrderHistory(request);
    }

    @Override
    @RequestMapping("printOrder")
    public ModelAndView printOrder(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.printOrder(request);
    }

    @Override
    @RequestMapping("ajaxLoadEmailOrder")
    public ModelAndView ajaxLoadEmailOrder(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.ajaxLoadEmailOrder(request);
    }

    @Override
    @RequestMapping("ajaxSendOrderEmail")
    public ModelAndView ajaxSendOrderEmail(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.ajaxSendOrderEmail(request);
    }

    @RequestMapping("displayReceiptConfig")
    public ModelAndView displayReceiptConfig(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "ecommerce/ecommerce_adminReceiptConfig");
        mv.addObject("ecommerceManager",
                EcommerceHelper.isUserEcommerceManager(request));
        mv.addObject("module", getModule(request));
        return mv;
    }

    @RequestMapping("saveReceiptConfig")
    public ModelAndView saveReceiptConfig(HttpServletRequest request,
            HttpServletResponse response)
    {
        EcommerceAdminModule module = getModule(request);
        module.setReceiptHeader(request.getParameter("receiptHeader"));
        module.setReceiptFooter(request.getParameter("receiptFooter"));
        module.setL2ReceiptHeader(request.getParameter("l2ReceiptHeader"));
        module.setL2ReceiptFooter(request.getParameter("l2ReceiptFooter"));
        module.setInvoiceHeader(request.getParameter("invoiceHeader"));
        module.setInvoiceFooter(request.getParameter("invoiceFooter"));
        module.setL2InvoiceHeader(request.getParameter("l2InvoiceHeader"));
        module.setL2InvoiceFooter(request.getParameter("l2InvoiceFooter"));
        getHt().update(module);

        ModelAndView mv = displayReceiptConfig(request, response);
        mv.addObject("saved", true);
        return mv;
    }

    @RequestMapping("displayOrderEmailLog")
    public ModelAndView displayOrderEmailLog(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("ecommerce/ecommerce_orderEmailLog");

        EcommerceOrderEmailLog log = (EcommerceOrderEmailLog) getHt().load(
                EcommerceOrderEmailLog.class,
                Integer.valueOf(request.getParameter("orderEmailLogId")));

        mv.addObject("orderEmailLog", log);
        mv.addObject("orderId", request.getParameter("orderId"));
        return mv;
    }

    @Override
    @RequestMapping("displayInteractions")
    public ModelAndView displayInteractions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("ecommerce/ecommerce_orderInteractions");
        EcommerceOrder order = EcommerceHelper.getOrder(request);
        mv.addObject("order", order);

        InteractionHelper.populateInteractionActionsBar(mv, request);
        InteractionHelper.populateInteractionSummaryOptions(request, mv);
        return mv;
    }

    @Override
    @RequestMapping("displayInteractionNoteEdit")
    public ModelAndView displayInteractionNoteEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "ecommerce/ecommerce_orderInteractionNoteEdit");
        EcommerceOrder order = EcommerceHelper.getOrder(request);
        mv.addObject("order", order);

        InteractionHelper.populateNoteEdit(mv, request,
                INTERACTION_TYPE.ECOMMERCEORDER);

        return mv;
    }

    @Override
    @RequestMapping("displayInteractionTaskEdit")
    public ModelAndView displayInteractionTaskEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "ecommerce/ecommerce_orderInteractionTaskEdit");
        EcommerceOrder order = EcommerceHelper.getOrder(request);
        mv.addObject("order", order);

        InteractionHelper.populateTaskEdit(mv, request,
                INTERACTION_TYPE.ECOMMERCEORDER);

        return mv;
    }

    @Override
    @RequestMapping("viewInteractionNote")
    public ModelAndView viewInteractionNote(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "ecommerce/ecommerce_orderViewInteractionNote");
        EcommerceOrder order = EcommerceHelper.getOrder(request);
        mv.addObject("order", order);

        InteractionHelper.initializeNoteViewPage(mv, request);

        return mv;
    }

    @Override
    @RequestMapping("viewInteractionTask")
    public ModelAndView viewInteractionTask(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "ecommerce/ecommerce_orderViewInteractionTask");
        EcommerceOrder order = EcommerceHelper.getOrder(request);
        mv.addObject("order", order);

        InteractionHelper.initializeTaskViewPage(mv, request);

        return mv;
    }

    @Override
    @RequestMapping("reconcileOrderCheckout")
    public ModelAndView reconcileOrderCheckout(HttpServletRequest request,
            HttpServletResponse response)
    {
        String routingUrl = PortalUtils.getCurrentElement(request).getFullPath()
                + ".htm";
        String routingAction = "displayOrderDetails";
        return EcommerceHelper.checkoutReconcileOrder(request, routingUrl,
                routingAction);
    }


}
