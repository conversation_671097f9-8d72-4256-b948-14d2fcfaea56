package com.orbis.web.content.integration.loginProviders;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.jasig.cas.client.validation.Assertion;
import org.jasig.cas.client.validation.Cas20ServiceTicketValidator;
import org.jasig.cas.client.validation.Cas30ServiceTicketValidator;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.integration.IntegrationLoginConfigCAS;
import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.io.xml.DomDriver;

import edu.yale.its.tp.cas.client.ServiceTicketValidator;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public class CASLoginProvider extends AbstractLoginProvider
{
    static protected final Log logger = LogFactory.getLog(PortalUtils.class);

    public CASLoginProvider(IntegrationLoginConfigCAS config)
    {
        super(config);
    }

    private class Cas20Auth
    {
        private String user = null;

        public boolean authenticate(String ticket, IntegrationLoginConfigCAS config)
        {
            logger.info("cas20");
            logger.info("ticket=" + ticket);
            logger.info("serviceUrl=" + config.getCasServiceUrl());
            logger.info("validate url=" + config.getCasValidateUrl());

            Cas20ServiceTicketValidator v = new Cas20ServiceTicketValidator(
                    config.getCasValidateUrl());

            Assertion a = null;
            try
            {
                a = v.validate(ticket, config.getCasServiceUrl());
                logger.info(a.getAttributes());

                if (config.getCasUsernameLocation().equals("principle"))
                {
                    this.user = a.getPrincipal().getName();
                }
                else
                {
                    Map attrs = a.getAttributes();
                    this.user = (String) attrs
                            .get(config.getCasUsernameAttribute());

                }

            }
            catch (Exception e)
            {
                logger.error(ExceptionUtils.getStackTrace(e));
            }

            return !StringUtils.isEmpty(user);
        }

        public String getUser()
        {
            return user;
        }

    }

    private class Cas30Auth
    {
        private String user;

        public boolean authenticate(String ticket, IntegrationLoginConfigCAS config)
        {
            logger.info("cas30");
            logger.info("ticket=" + ticket);
            logger.info("serviceUrl=" + config.getCasServiceUrl());
            logger.info("validate url=" + config.getCasValidateUrl());
            Cas30ServiceTicketValidator v = new Cas30ServiceTicketValidator(
                    "https://" + config.getCasValidateUrl());
            Assertion a = null;
            try
            {
                a = v.validate(ticket, "https://" + config.getCasServiceUrl());
                logger.info(a.getAttributes());
                if (config.getCasUsernameLocation().equals("principle"))
                {
                    this.user = a.getPrincipal().getName();
                }
                else
                {
                    Map attrs = a.getPrincipal().getAttributes();
                    this.user = (String) attrs
                            .get(config.getCasUsernameAttribute());
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }

            return !StringUtils.isEmpty(user);
        }

        public String getUser()
        {
            return user;
        }

    }

    private class ServiceTicketAuth
    {
        private String user;

        public boolean authenticate(String ticket, String service, String validator)
        {
            ServiceTicketValidator stv = new ServiceTicketValidator();
            stv.setCasValidateUrl(validator);
            stv.setService(service);
            stv.setServiceTicket(ticket);

            try
            {
                stv.validate();
                if (stv.isAuthenticationSuccesful())
                {
                    user = stv.getUser();
                }
            }
            catch (Exception e)
            {
            }

            return user != null;
        }

        public String getUser()
        {
            return user;
        }

    }

    @Override
    public LoginResult login(HttpServletRequest request,
            HttpServletResponse response)
    {
        LoginResult loginResult = new LoginResult();

        IntegrationLoginConfigCAS casConfig = (IntegrationLoginConfigCAS) config;

        String ticket = request.getParameter(casConfig.getCasTicketParam());

        if (!StringUtils.isEmpty(ticket))
        {

            logger.info(casConfig.getClass());
            UserDetailsImpl user = null;
            logXML(casConfig, ticket);
            if (casConfig
                    .getCasLoginType() == IntegrationLoginConfigCAS.CAS_LOGIN_TYPE_SERVICE_TICKET)
            {
                ServiceTicketAuth auth = new ServiceTicketAuth();
                boolean authSuccess = auth.authenticate(ticket,
                        "https://" + casConfig.getCasServiceUrl(),
                        "https://" + casConfig.getCasValidateUrl());
                if (authSuccess)
                {
                    user = getUserByUsername(auth.getUser());
                    loginResult.setUser(user);
                    loginResult.setUsername(user.getUsername());
                    loginResult
                            .setStatusCode(LoginResult.STATUS_USER_AUTHENTICATED);
                }
            }
            else if (casConfig
                    .getCasLoginType() == IntegrationLoginConfigCAS.CAS_LOGIN_TYPE_2_0)
            {
                Cas20Auth auth = new Cas20Auth();
                boolean authSuccess = auth.authenticate(ticket, casConfig);

                if (authSuccess)
                {
                    user = getUserByUsername(auth.getUser());
                    loginResult.setUser(user);
                    loginResult.setUsername(user.getUsername());
                    loginResult
                            .setStatusCode(LoginResult.STATUS_USER_AUTHENTICATED);

                }
            }
            else if (casConfig
                    .getCasLoginType() == IntegrationLoginConfigCAS.CAS_LOGIN_TYPE_3_0)
            {
                Cas30Auth auth = new Cas30Auth();
                logger.info(new Date());
                logger.info("ticket = " + ticket);
                boolean authSuccess = auth.authenticate(ticket, casConfig);
                logger.info("authSuccess = " + authSuccess);
                if (authSuccess)
                {
                    user = getUserByUsername(auth.getUser());
                    loginResult.setUser(user);
                    loginResult.setUsername(user.getUsername());
                    loginResult
                            .setStatusCode(LoginResult.STATUS_USER_AUTHENTICATED);

                }
            }
            else if (casConfig
                    .getCasLoginType() == IntegrationLoginConfigCAS.CAS_LOGIN_TYPE_RAW)
            {
                String username = validateCas(casConfig, ticket);
                if (!StringUtils.isEmpty(username))
                {
                    user = getUserByUsername(username);
                    loginResult.setUser(user);
                    loginResult.setUsername(user.getUsername());
                    loginResult
                            .setStatusCode(LoginResult.STATUS_USER_AUTHENTICATED);
                }
            }
        }
        else
        {
            loginResult.setStatusCode(LoginResult.STATUS_AUTHENTICATION_ERROR);
        }

        return loginResult;
    }

    private UserDetailsImpl getUserByUsername(String username)
    {
        UserDetailsImpl ret = null;
        List users = PortalUtils.getHt()
                .find("from UserDetailsImpl u where u.username=?", username);
        if (users.size() == 1)
        {
            ret = (UserDetailsImpl) users.get(0);
        }
        return ret;
    }

    public String validateCas(IntegrationLoginConfigCAS casConfig, String ticket)
    {
        String rtn = "";

        try
        {
            URL url = new URL(casConfig.getCasValidateUrl() + "&ticket=" + ticket);
            URLConnection connection = url.openConnection();
            connection.setConnectTimeout(60000);
            connection.connect();
            BufferedReader in = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()));

            String xml = "";
            String decodedString = null;

            while ((decodedString = in.readLine()) != null)
            {
                xml += decodedString;
            }
            in.close();
            logger.info(xml);

            new XStream(new DomDriver()).fromXML(xml);

            rtn = extractIdFromCasResponse(xml);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return rtn;
    }

    private String extractIdFromCasResponse(String xml)
    {
        String ret = null;
        int studentIdStart = xml.indexOf("<cas:user>");
        int studentIdEnd = xml.indexOf("</cas:user>");
        if (studentIdStart > -1 && studentIdEnd > -1)
        {
            ret = xml.substring(studentIdStart + 10, studentIdEnd);

        }
        return ret;
    }

    @Override
    public Map<String, String> getSyncParameters(HttpServletRequest request)
    {
        return null;
    }

    private void logXML(IntegrationLoginConfigCAS casConfig, String ticket)
    {

        String xml = "";
        try
        {
            URL url = new URL(casConfig.getCasValidateUrl() + "&ticket=" + ticket);
            URLConnection connection = url.openConnection();
            connection.setConnectTimeout(60000);
            connection.connect();
            BufferedReader in = new BufferedReader(
                    new InputStreamReader(connection.getInputStream()));

            String decodedString = null;

            while ((decodedString = in.readLine()) != null)
            {
                xml += decodedString;
            }
            in.close();

            new XStream(new DomDriver()).fromXML(xml);

        }
        catch (Exception e)
        {
            logger.error(ExceptionUtils.getStackTrace(e));
        }
        logger.info(xml);
    }

}
