package com.orbis.web.content.os;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.multiaction.ParameterMethodNameResolver;

import com.google.common.collect.Lists;
import com.orbis.acegi.providers.dao.hibernate.PersonGroup;
import com.orbis.acegi.providers.dao.hibernate.PersonGroupHelper;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.email.EmailModel;
import com.orbis.jqgrid.JQGridHelper;
import com.orbis.jqgrid.JQGridModel;
import com.orbis.jqgrid.JQGridSubController;
import com.orbis.portal.OrbisRepost;
import com.orbis.portal.PortalUtils;
import com.orbis.search.SearchHelper;
import com.orbis.search.SearchModel;
import com.orbis.search.SearchSubControllerFactory;
import com.orbis.search.entity.Entity;
import com.orbis.utils.DBUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.EmailAttachment;
import com.orbis.utils.HtmlUtils;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.LookupUtils;
import com.orbis.utils.QueryBuilder;
import com.orbis.utils.RequestUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.OrbisController;
import com.orbis.web.content.acrm.AcrmHelper;
import com.orbis.web.content.acrm.AcrmTag;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.crm.Organization;
import com.orbis.web.content.ecommerce.EcommerceAdminModule;
import com.orbis.web.content.ecommerce.EcommerceHelper;
import com.orbis.web.content.ecommerce.EcommerceOrder;
import com.orbis.web.content.ecommerce.EcommerceOrderDetailsUI;
import com.orbis.web.content.ecommerce.EcommerceRouting;
import com.orbis.web.content.ecommerce.EcommerceTax;
import com.orbis.web.content.exp.EXPStudentExperienceStepHelper;
import com.orbis.web.content.grid.GridHelper;
import com.orbis.web.content.grid.GridOptions;
import com.orbis.web.content.np.NHelper;
import com.orbis.web.report.JasperController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Controller("osController")
@RequestMapping("osController")
public class OSController extends OrbisController<OSModule>
        implements EcommerceOrderDetailsUI
{

    private OSControllerSearchInterface searchInterface = new OSControllerSearchInterface(
            this);

    private OSGridInterface osGridInterface = new OSGridInterface(this);

    public OSController()
    {
        setMethodNameResolver(
                new ParameterMethodNameResolver("action", "displayHome"));
    }

    @Override
    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "displayHome")
    public ModelAndView displayHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
        if (user != null)
        {
            if (OSHelper.isUserOSAdmin(user))
            {
                mv = displayAdminHome(request, response);
            }
            else
            {
                mv = displayPlaceOrder(request, response);
            }
        }
        return mv;
    }

    @RequestMapping("displayPlaceOrder")
    public ModelAndView displayPlaceOrder(HttpServletRequest request,
            HttpServletResponse response)
    {
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
        boolean osAdmin = OSHelper.isUserOSAdmin(user);
        ModelAndView mv = new ModelAndView("os/os_placeOrder");
        List<Object[]> willSee = new ArrayList<>();
        OSOrder order = null;
        order = OSOrderHelper.getOrder(request);
        OSModule module = getModule(request);
        if (order != null)
        {
            mv.addObject("previousOrderer", order.getCustomer());
            order.setOrderItems(OSHelper.getOrderItemsForOrder(order));
        }

        List<Object[]> categories = getHt().find(
                "select pc.id, pc.name, pc.description from OSProductCategory pc where pc.deleted=false and pc.module=? "
                        + (osAdmin ? "" : "and pc.adminOnly=false")
                        + " order by pc.name",
                module);
        if (!osAdmin)
        {
            for (Object[] category : categories)
            {
                if (OSHelper.isValidUserForCategory(user, (Integer) category[0]))
                {
                    willSee.add(category);
                }
            }
            categories = willSee;
        }
        Map<Integer, List<Object[]>> products = new HashMap<>();
        Map<Integer, Object[]> previousOrder = new HashMap<>();
        List<Object[]> productsList;
        for (Object[] category : categories)
        {
            productsList = getHt().find(
                    "select p.id, p.label, p.description, p.fee, p.feeType, p.capacity, p.perUnitMin, p.perUnitMax, p.unitLabel from OSProduct p where p.category.id=? and p.deleted=false "
                            + (osAdmin ? "" : "and p.adminOnly=false ")
                            + " order by p.label",
                    category[0]);

            if (!osAdmin)
            {
                willSee = new ArrayList<>();
                for (Object[] product : productsList)
                {
                    if (OSHelper.isValidUserForProduct(user, (Integer) product[0]))
                    {
                        willSee.add(product);
                    }
                }
                productsList = willSee;
            }
            products.put((Integer) category[0], productsList);
            if (order != null)
            {
                for (Object[] product : productsList)
                {
                    previousOrder.put((Integer) product[0],
                            isProductInOrder((Integer) product[0], order));
                }
            }
        }
        if (order != null)
        {
            List<OSOrderItem> orderItems = PortalUtils.getHt()
                    .find("from OSOrderItem i where i.order=? ", order);
            for (OSOrderItem orderItem : orderItems)
            {
                if (orderItem.getProduct().getCapacity() != -1)
                {
                    orderItem.getProduct()
                            .setCapacity(orderItem.getProduct().getCapacity()
                                    + orderItem.getUnits());
                }
            }
            OSHelper.deleteOSOrder(order);
        }

        mv.addObject("categories", categories);
        mv.addObject("products", products);
        mv.addObject("previousOrderItems", previousOrder);
        mv.addObject("osAdmin", osAdmin);
        mv.addObject("module", getModule(request));
        mv.addObject("user", user);
        return mv;
    }

    @RequestMapping("displayCustomersOrdersList")
    public ModelAndView displayCustomersOrdersList(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_customerOrdersList");
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
        OSModule module = getModule(request);
        mv.addObject("orders", getHt().find(
                "select o.id, o.ecommerceOrder.orderNumber, o.status, o.total, o.orderDate from OSOrder o where o.customer.id=? and o.module.id=? and o.customer.id=o.createdBy.id and o.status<>4",
                new Object[] { user.getId(), module.getId() }));
        mv.addObject("orders2", getHt().find(
                "select o.id, o.total, o.orderDate from OSOrder o where o.ecommerceOrder is null and o.customer.id=? and o.module.id=? and o.customer.id=o.createdBy.id and o.status<>4",
                new Object[] { user.getId(), module.getId() }));
        mv.addObject("module", module);

        return mv;
    }

    @RequestMapping("ajaxLoadUserForOrder")
    public ModelAndView ajaxLoadUserForOrder(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("os/os_ajaxLoadUserForOrder");
        UserDetailsImpl purchaser = (UserDetailsImpl) getHt().load(
                UserDetailsImpl.class, Integer.valueOf(request.getParameter("userId")));
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        boolean osAdmin = OSHelper.isUserOSAdmin(userLoggedIn);

        if (userLoggedIn.equals(purchaser) || osAdmin)
        {
            if (purchaser.isEmployer() || purchaser.isStaffFaculty())
            {
                mv.addObject("companies", getHt().find(
                        "select cu.company.id, cu.company.organization.name, cu.company.name from CompanyUsers cu where cu.user.id=? and cu.company.deleted=false",
                        purchaser.getId()));
            }
            mv.addObject("purchaser", purchaser);
        }
        else
        {
            mv.addObject("errorMessage", new I18nLabel(
                    "i18n.OSController.Authentica6691464766107460").getTranslation(
                    PortalUtils.getLocale(request)));
        }
        return mv;
    }

    @RequestMapping("displayReviewOrder")
    public ModelAndView displayReviewOrder(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_reviewOrder");

        UserDetailsImpl purchaser = (UserDetailsImpl) PortalUtils.getHt()
                .load(UserDetailsImpl.class,
                        Integer.valueOf(request.getParameter("customerId")));
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        boolean osAdmin = OSHelper.isUserOSAdmin(userLoggedIn);

        if (userLoggedIn.equals(purchaser) || osAdmin)
        {
            String[] products = request.getParameterValues("products");
            OSOrder order = OSOrderHelper.createOrderForReview(getModule(request),
                    request, products, null);

            mv.addObject("module", getModule(request));
            mv.addObject("order", order);
        }
        else
        {
            mv.addObject("errorMessage", new I18nLabel(
                    "i18n.OSController.Authentica6691464766107460").getTranslation(
                    PortalUtils.getLocale(request)));
        }
        return mv;
    }

    @RequestMapping("cancelOrder")
    public ModelAndView cancelOrder(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        OSOrder order = OSOrderHelper.getOrder(request);
        OSOrderHelper.cancelOrder(request, order);
        mv = displayOrder(request, response);

        return mv;
    }

    @RequestMapping("displayAdminHomeSearchesTab")
    public ModelAndView displayAdminHomeSearchesTab(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_adminHomeSearchesTab");

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName("ajaxLoadSavedSearches");
        GridHelper.addGridSupport(request, mv, "advancedSearch_savedSearches",
                options);
        mv.addObject("shareLevelOptions",
                SearchHelper.getShareLevelOptions(PortalUtils.getLocale(request)));

        mv.addObject("module", getModule(request));
        return mv;
    }

    @RequestMapping("checkoutOrder")
    public ModelAndView checkoutOrder(HttpServletRequest request,
            HttpServletResponse response)
    {
        OSOrder order = OSOrderHelper.getOrder(request);
        ModelAndView mv = null;

        if (order == null)
        {
            mv = displayHome(request, response);
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.OSController.Therewasan3444075008775566")
                            .getTranslation(PortalUtils.getLocale(request)));
        }

        if (order != null)
        {
            // ROUTING...
            EcommerceRouting routing = new EcommerceRouting(
                    getModule(request).getSiteElementPath(), "displayOrderReceipt",
                    OSOrderEcommerceProcessor.class);

            Map<String, String> params = new HashMap<>();
            params.put("action", "displayOrder");
            params.put("osOrderId", order.getId().toString());

            mv = OSOrderHelper.checkoutOrder(request, order, routing, params);
        }
        return mv;
    }

    @OrbisRepost
    @RequestMapping("displayOrderReceipt")
    public ModelAndView displayOrderReceipt(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        EcommerceOrder ecOrder = EcommerceHelper.getOrder(request);

        OSOrder order = (OSOrder) EcommerceHelper.getEcommerceEntity(ecOrder);

        if (order == null || ecOrder == null)
        {
            mv = displayHome(request, response);
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.OSController.Therewasan3444075008775566")
                            .getTranslation(PortalUtils.getLocale(request)));
        }
        else
        {
            mv = new ModelAndView("os/os_orderReceipt");
            OSHelper.populateOSOrderReceiptSuccess(request, mv, ecOrder, order);
        }

        return mv;
    }

    @RequestMapping("displayAdminHomeReportsTab")
    public ModelAndView displayAdminHomeReportsTab(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_adminHomeReportsTab");
        Locale locale = PortalUtils.getLocale(request);
        OSModule module = getModule(request);
        mv.addObject("module", module);

        OSReportFilter reportFilter = OSHelper.getReportFilter(request, module);

        String tabView = request.getParameter("tabView");

        if (StringUtils.isEmpty(tabView)
                && request.getParameter("reportFilter") != null)
        {
            tabView = "summaryTab";
        }

        if (!StringUtils.isEmpty(tabView))
        {
            mv.addObject("tabView", tabView);

            if (tabView.equals("summaryTab"))
            {
                mv.addObject("stats", OSHelper.getReports(reportFilter, locale));
            }
            else if (tabView.equals("detailsTab"))
            {
                populateReportDetailsTab(mv, request, reportFilter);
            }
        }

        mv.addObject("reportFilter", reportFilter);
        List<String> typeList = OSHelper.getSelectedTypeListAsString(reportFilter);
        if (typeList == null || typeList.isEmpty())
        {
            typeList = new ArrayList();
            typeList.add("No Products Selected");
            mv.addObject("typeList", typeList);
        }
        else
        {
            mv.addObject("typeList", typeList);
        }

        mv.addObject("osAdmin",
                OSHelper.isUserOSAdmin(PortalUtils.getUserLoggedIn(request)));

        return mv;
    }

    @RequestMapping("jqGrid")
    public ModelAndView jqGrid(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        return JQGridSubController.getInstance(osGridInterface)
                .processRequest(request, response);
    }

    @RequestMapping("displayAdminHomeConfigTab")
    public ModelAndView displayAdminHomeConfigTab(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_adminHomeConfigTab");
        mv.addObject("module", getModule(request));
        return mv;
    }

    @RequestMapping("displayConfigureModuleGeneral")
    public ModelAndView displayConfigureModuleGeneral(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_configureModuleGeneral");
        mv.addObject("module", getModule(request));
        mv.addObject("ecommerceModules",
                EcommerceHelper.getEcommerceAdminModules());
        return mv;
    }

    @RequestMapping("saveModuleConfig")
    public ModelAndView saveModuleConfig(HttpServletRequest request,
            HttpServletResponse response)
    {
        OSModule module = getModule(request);
        module.setModuleName(request.getParameter("name"));
        module.setOrderPrefix(request.getParameter("orderPrefix"));
        module.setEcommerceModule(
                (EcommerceAdminModule) getHt().load(EcommerceAdminModule.class,
                        Integer.valueOf(request.getParameter("ecommerceModule"))));
        module.setAcceptPaymentByCash(
                HtmlUtils.isChecked(request.getParameter("cash")));
        module.setAcceptPaymentByCreditCard(
                HtmlUtils.isChecked(request.getParameter("creditCard")));
        module.setAcceptPaymentByCheque(
                HtmlUtils.isChecked(request.getParameter("cheque")));
        module.setAcceptPaymentByInvoice(
                HtmlUtils.isChecked(request.getParameter("invoice")));
        module.setUseDefaultReceiptHeaderFooter(
                HtmlUtils.isChecked(request.getParameter("defaultReceiptInvoice")));
        module.setModuleReceiptHeader(request.getParameter("receiptHeader"));
        module.setModuleReceiptFooter(request.getParameter("receiptFooter"));
        module.setModuleInvoiceHeader(request.getParameter("invoiceHeader"));
        module.setModuleInvoiceFooter(request.getParameter("invoiceFooter"));

        getHt().saveOrUpdate(module);
        ModelAndView mv = displayConfigureModuleGeneral(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.OSController.ModuleConf6790181335066462")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    @RequestMapping("displayManageProductCatalogue")
    public ModelAndView displayManageProductCatalogue(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_manageProductCatalogue");
        List<Object[]> products = getHt().find(
                "select p.id, p.category.name, p.label, p.adminOnly, p.feeType, p.fee, p.unitLabel, p.defaultTaxes from OSProduct p where p.category.module = ? and p.deleted = 0 order by p.category.name, p.label",
                getModule(request));
        Map<Object[], String> productMap = new LinkedHashMap<>();
        if (!products.isEmpty())
        {
            for (Object[] product : products)
            {
                List<PersonGroup> groups = getHt().find(
                        "select pg.group from OSProductGroup pg where pg.product.id = ?",
                        product[0]);
                StringBuilder sb = new StringBuilder();
                if (!groups.isEmpty())
                {
                    for (PersonGroup group : groups)
                    {
                        if (sb.length() > 0)
                        {
                            sb.append(", ");
                        }
                        sb.append(group.getName());
                    }
                }
                productMap.put(product,
                        sb.length() > 0 ? sb.toString() : "Everyone");
            }
        }
        mv.addObject("moduleHasCategories", (Integer) getHt().find(
                "select count(*) from OSProductCategory pc where pc.module=? and pc.deleted=false",
                getModule(request)).get(0) > 0);
        mv.addObject("products", productMap);
        mv.addObject("module", getModule(request));
        return mv;
    }

    @RequestMapping("displayManageProductCatalogueDeleted")
    public ModelAndView displayManageProductCatalogueDeleted(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_manageProductCatalogueDeleted");
        List<Object[]> products = getHt().find(
                "select p.id, p.category.name, p.label from OSProduct p where p.category.module = ?  and p.deleted = true order by p.category.name, p.label",
                getModule(request));
        Map<Object[], String> productMap = new LinkedHashMap<>();
        if (!products.isEmpty())
        {
            for (Object[] product : products)
            {
                List<PersonGroup> groups = getHt().find(
                        "select pg.group from OSProductGroup pg where pg.product.id = ?",
                        product[0]);
                StringBuilder sb = new StringBuilder();
                if (!groups.isEmpty())
                {
                    for (PersonGroup group : groups)
                    {
                        if (sb.length() > 0)
                        {
                            sb.append(", ");
                        }
                        sb.append(group.getName());
                    }
                }
                productMap.put(product,
                        sb.length() > 0 ? sb.toString() : "Everyone");
            }
        }
        mv.addObject("moduleHasCategories", (Integer) getHt().find(
                "select count(*) from OSProductCategory pc where pc.module=? and pc.deleted=false",
                getModule(request)).get(0) > 0);
        mv.addObject("products", productMap);
        mv.addObject("module", getModule(request));
        return mv;
    }

    @RequestMapping("displayProductEdit")
    public ModelAndView displayProductEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        OSModule osm = getModule(request);
        EcommerceAdminModule eam = osm.getEcommerceModule();

        ModelAndView mv = new ModelAndView("os/os_productEdit");
        OSProduct product = OSHelper.getProduct(request);

        if (product != null)
        {
            mv.addObject("product", product);
        }
        mv.addObject("categories", getHt().find(
                "select pc.id, pc.name from OSProductCategory pc where pc.module = ?",
                osm));
        mv.addObject("taxes", EcommerceHelper.getTaxes(eam.getId()));
        mv.addObject("module", getModule(request));

        mv.addObject("primaryGroups", OSHelper.getProductUserGroupsMap(product));
        mv.addObject("userPermissions",
                OSHelper.getProductUserPermissionsMap(product));
        return mv;
    }

    @RequestMapping("saveProduct")
    public ModelAndView saveProduct(HttpServletRequest request,
            HttpServletResponse response)
    {
        OSProduct product = OSHelper.getProduct(request);
        if (product == null)
        {
            product = new OSProduct();
        }

        product.setLabel(request.getParameter("name"));
        product.setCategory(OSHelper.getCategory(request));
        product.setAdminOnly(
                HtmlUtils.isChecked(request.getParameter("adminOnly")));
        product.setExperientialProduct(RequestUtils.getBooleanParameter(request,
                "experientialProduct", false));
        product.setDescription(request.getParameter("description"));
        product.setFeeType(RequestUtils.getIntParameter(request, "feeType",
                OSProduct.FEE_TYPE_FREE));
        product.setCapacity(RequestUtils.getIntParameter(request, "capacity", -1));
        product.setDeleted(HtmlUtils.isChecked(request.getParameter("deleted")));
        if (product.getFeeType() == OSProduct.FEE_TYPE_ONETIME)
        {
            product.setFee(
                    RequestUtils.getDoubleParameter(request, "oneTimeFee", 0d));
        }
        else if (product.getFeeType() == OSProduct.FEE_TYPE_PERUNIT)
        {
            product.setFee(
                    RequestUtils.getDoubleParameter(request, "perUnitFee", 0d));
            product.setPerUnitMin(
                    RequestUtils.getIntParameter(request, "perUnitMin", -1));
            product.setPerUnitMax(
                    RequestUtils.getIntParameter(request, "perUnitMax", -1));
            product.setUnitLabel(request.getParameter("unitLabel"));
        }
        product.setDefaultTaxes(
                HtmlUtils.isChecked(request.getParameter("defaultTaxes")));

        getHt().saveOrUpdate(product);
        PortalUtils.getJt().update("delete from os_product_tax where product = ?",
                new Object[] { product.getId() });
        if (!product.isDefaultTaxes())
        {
            OSProductTax productTax = null;
            String[] taxIds = request.getParameterValues("taxId");

            if (taxIds != null)
            {
                for (String taxId : taxIds)
                {
                    productTax = new OSProductTax();
                    productTax.setProduct(product);
                    productTax.setTax((EcommerceTax) getHt()
                            .load(EcommerceTax.class, Integer.valueOf(taxId)));
                    getHt().saveOrUpdate(productTax);
                }
            }
        }

        OSHelper.clearProductGroups(product);

        // Reset primary-group filters...
        String primaryGroupIds[] = request.getParameterValues("primaryGroupId");
        if (primaryGroupIds != null)
        {
            for (int i = 0; i < primaryGroupIds.length; i++)
            {
                PersonGroup pg = (PersonGroup) getHt().load(PersonGroup.class,
                        Integer.valueOf(primaryGroupIds[i]));

                OSProductGroup opg = new OSProductGroup();
                opg.setProduct(product);
                opg.setGroup(pg);
                getHt().save(opg);
            }
        }

        // Reset secondary-group filters...
        String secondaryGroupIds[] = request.getParameterValues("secondaryGroupId");
        if (secondaryGroupIds != null)
        {
            for (int i = 0; i < secondaryGroupIds.length; i++)
            {
                PersonGroup pg = (PersonGroup) getHt().load(PersonGroup.class,
                        Integer.valueOf(secondaryGroupIds[i]));

                OSProductGroup opg = new OSProductGroup();
                opg.setProduct(product);
                opg.setGroup(pg);
                getHt().save(opg);
            }
        }

        request.setAttribute("productId", product.getId());
        ModelAndView mv = displayProductEdit(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.OSController.ProductSav2656825740164933")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    @RequestMapping("deleteProduct")
    public ModelAndView deleteProduct(HttpServletRequest request,
            HttpServletResponse response)
    {
        OSProduct product = OSHelper.getProduct(request);
        product.setDeleted(true);
        getHt().saveOrUpdate(product);
        ModelAndView mv = displayManageProductCatalogue(request, response);
        return mv;
    }

    @RequestMapping("undeleteProduct")
    public ModelAndView undeleteProduct(HttpServletRequest request,
            HttpServletResponse response)
    {
        OSProduct product = OSHelper.getProduct(request);
        product.setDeleted(false);
        getHt().saveOrUpdate(product);
        ModelAndView mv = displayManageProductCatalogueDeleted(request, response);
        return mv;
    }

    @RequestMapping("displayManageProductCategories")
    public ModelAndView displayManageProductCategories(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_manageProductCategories");
        List<Object[]> categories = getHt().find(
                "select pc.id, pc.name, pc.adminOnly from OSProductCategory pc where pc.module = ? and pc.deleted = 0 order by pc.name",
                getModule(request));
        Map<Object[], Map<String, Object>> categoryMap = new LinkedHashMap<>();
        if (!categories.isEmpty())
        {

            Map<String, Object> innerMap;
            for (Object[] category : categories)
            {
                List<PersonGroup> groups = getHt().find(
                        "select pcg.group from OSProductCategoryGroup pcg where pcg.category.id = ?",
                        category[0]);
                StringBuilder sb = new StringBuilder();
                if (!groups.isEmpty())
                {
                    for (PersonGroup group : groups)
                    {
                        if (sb.length() > 0)
                        {
                            sb.append(", ");
                        }
                        sb.append(group.getName());
                    }
                }
                innerMap = new HashMap<>();
                innerMap.put(sb.length() > 0 ? sb.toString() : "Everyone", getHt()
                        .find("select count(*) from OSProduct p where p.category.id = ?",
                                category[0])
                        .get(0));

                categoryMap.put(category, innerMap);
            }
        }
        mv.addObject("categories", categoryMap);
        mv.addObject("module", getModule(request));
        return mv;
    }

    @RequestMapping("displayManageProductCategoriesDeleted")
    public ModelAndView displayManageProductCategoriesDeleted(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_manageProductCategoriesDeleted");
        List<Object[]> categories = getHt().find(
                "select pc.id, pc.name from OSProductCategory pc where pc.module = ? and pc.deleted = true order by pc.name",
                getModule(request));
        Map<Object[], Map<String, Object>> categoryMap = new LinkedHashMap<>();
        if (!categories.isEmpty())
        {

            Map<String, Object> innerMap;
            for (Object[] category : categories)
            {
                List<PersonGroup> groups = getHt().find(
                        "select pcg.group from OSProductCategoryGroup pcg where pcg.category.id = ?",
                        category[0]);
                StringBuilder sb = new StringBuilder();
                if (!groups.isEmpty())
                {
                    for (PersonGroup group : groups)
                    {
                        if (sb.length() > 0)
                        {
                            sb.append(", ");
                        }
                        sb.append(group.getName());
                    }
                }
                innerMap = new HashMap<>();
                innerMap.put(sb.length() > 0 ? sb.toString() : "Everyone", getHt()
                        .find("select count(*) from OSProduct p where p.category.id = ?",
                                category[0])
                        .get(0));

                categoryMap.put(category, innerMap);
            }
        }
        mv.addObject("categories", categoryMap);
        mv.addObject("module", getModule(request));
        return mv;
    }

    @RequestMapping("displayProductCategoryEdit")
    public ModelAndView displayProductCategoryEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_productCategoryEdit");
        OSProductCategory category = OSHelper.getCategory(request);
        mv.addObject("category", category);
        mv.addObject("module", getModule(request));
        mv.addObject("primaryGroups",
                OSHelper.getProductCategoryUserGroupsMap(category));
        mv.addObject("userPermissions",
                OSHelper.getProductCategoryUserPermissionsMap(category));

        return mv;
    }

    @RequestMapping("saveProductCategory")
    public ModelAndView saveProductCategory(HttpServletRequest request,
            HttpServletResponse response)
    {
        OSProductCategory category = OSHelper.getCategory(request);
        if (category == null)
        {
            category = new OSProductCategory();
        }

        category.setName(request.getParameter("name"));
        category.setDescription(request.getParameter("description"));
        category.setAdminOnly(
                HtmlUtils.isChecked(request.getParameter("adminOnly")));
        category.setModule(getModule(request));
        category.setDeleted(HtmlUtils.isChecked(request.getParameter("deleted")));
        getHt().saveOrUpdate(category);

        OSHelper.clearProductCategoryGroups(category);

        // Reset primary-group filters...
        String primaryGroupIds[] = request.getParameterValues("primaryGroupId");
        if (primaryGroupIds != null)
        {
            for (int i = 0; i < primaryGroupIds.length; i++)
            {
                PersonGroup pg = (PersonGroup) getHt().load(PersonGroup.class,
                        Integer.valueOf(primaryGroupIds[i]));

                OSProductCategoryGroup opcg = new OSProductCategoryGroup();
                opcg.setCategory(category);
                opcg.setGroup(pg);
                getHt().save(opcg);
            }
        }

        // Reset secondary-group filters...
        String secondaryGroupIds[] = request.getParameterValues("secondaryGroupId");
        if (secondaryGroupIds != null)
        {
            for (int i = 0; i < secondaryGroupIds.length; i++)
            {
                PersonGroup pg = (PersonGroup) getHt().load(PersonGroup.class,
                        Integer.valueOf(secondaryGroupIds[i]));

                OSProductCategoryGroup opcg = new OSProductCategoryGroup();
                opcg.setCategory(category);
                opcg.setGroup(pg);
                getHt().save(opcg);
            }
        }

        request.setAttribute("categoryId", category.getId());
        ModelAndView mv = displayProductCategoryEdit(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.OSController.ProductCat1990329553062700")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    @RequestMapping("deleteProductCategory")
    public ModelAndView deleteProductCategory(HttpServletRequest request,
            HttpServletResponse response)
    {
        OSProductCategory category = OSHelper.getCategory(request);
        category.setDeleted(true);
        getHt().saveOrUpdate(category);
        ModelAndView mv = displayManageProductCategories(request, response);
        return mv;
    }

    @RequestMapping("undeleteProductCategory")
    public ModelAndView undeleteProductCategory(HttpServletRequest request,
            HttpServletResponse response)
    {
        OSProductCategory category = OSHelper.getCategory(request);
        category.setDeleted(false);
        getHt().saveOrUpdate(category);
        ModelAndView mv = displayManageProductCategoriesDeleted(request, response);
        return mv;
    }

    @RequestMapping("displayManageEmailsAndContent")
    public ModelAndView displayManageEmailsAndContent(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_manageEmailsAndContent");
        mv.addObject("module", getModule(request));
        return mv;
    }

    @RequestMapping("saveModuleEmailsAndContent")
    public ModelAndView saveModuleEmailsAndContent(HttpServletRequest request,
            HttpServletResponse response)
    {
        OSModule module = getModule(request);

        module.setReceiptConfirmFrom(request.getParameter("receiptConfirmFrom"));
        module.setReceiptConfirmSubject(
                request.getParameter("receiptConfirmSubject"));
        module.setReceiptConfirmBody(request.getParameter("receiptConfirmBody"));
        module.setAdminCreatedFrom(request.getParameter("adminCreatedFrom"));
        module.setAdminCreatedSubject(request.getParameter("adminCreatedSubject"));
        module.setAdminCreatedBody(request.getParameter("adminCreatedBody"));
        module.setOrderCancellationFrom(
                request.getParameter("orderCancellationFrom"));
        module.setOrderCancellationSubject(
                request.getParameter("orderCancellationSubject"));
        module.setOrderCancellationBody(
                request.getParameter("orderCancellationBody"));
        module.setMainPageContent(request.getParameter("mainPageContent"));
        module.setNoProductsAvailable(request.getParameter("noProductsAvailable"));
        module.setReceiptAdditionalMessage(
                request.getParameter("receiptAdditionalMessage"));
        module.setInvoiceAdditionalMessage(
                request.getParameter("invoiceAdditionalMessage"));
        getHt().saveOrUpdate(module);

        ModelAndView mv = displayManageEmailsAndContent(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.OSController.ModuleCont5355659206527125")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    @RequestMapping("displayTaxOverrideTags")
    public ModelAndView displayTaxOverrideTags(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_taxOverrideTags");
        OSModule module = getModule(request);

        List<OSPaymentOverride> paymentOverrides = getHt().find(
                "from OSPaymentOverride po where po.module=? order by po.tag",
                module);
        mv.addObject("paymentOverrides", paymentOverrides);
        mv.addObject("module", module);
        return mv;
    }

    @RequestMapping("ajaxLoadPaymentOverrideEditor")
    public ModelAndView ajaxLoadPaymentOverrideEditor(HttpServletRequest request,
            HttpServletResponse response)
    {
        Locale locale = PortalUtils.getLocale(request);

        OSPaymentOverride paymentOverride = null;

        String paymentOverrideId = request.getParameter("paymentOverrideId");

        if (StringUtils.isInteger(paymentOverrideId))
        {
            paymentOverride = (OSPaymentOverride) getHt()
                    .load(OSPaymentOverride.class, Integer.valueOf(paymentOverrideId));
        }

        if (paymentOverride == null)
        {
            paymentOverride = new OSPaymentOverride();
        }
        EcommerceAdminModule eam = getModule(request).getEcommerceModule();

        ModelAndView mv = getShortCircuitView(
                "os/os_taxOverrideTags_ajaxOverrideEditor");
        mv.addObject("taxes", EcommerceHelper.getTaxes(eam.getId()));
        mv.addObject("paymentOverride", paymentOverride);
        mv.addObject("module", getModule(request));

        String nameFieldBilingual = LocaleUtils.isL1(locale) ? "l1Name" : "l2Name";

        List<Object[]> categories = getHt().find("select c.id, c."
                + nameFieldBilingual + " from AcrmRegModuleTagCategory c");
        Map<Integer, List<Object[]>> tagsForDropdown = new HashMap<>();
        for (Object[] category : categories)
        {
            tagsForDropdown.put((Integer) category[0],
                    getHt().find(
                            "select t.id, t." + nameFieldBilingual
                                    + " from AcrmTag t where t.category.id=?",
                            category[0]));
        }

        mv.addObject("categories", categories);
        mv.addObject("tagsForDropdown", tagsForDropdown);

        return mv;
    }

    @RequestMapping("displayOSOrderEmailer")
    public ModelAndView displayOSOrderEmailer(HttpServletRequest request,
            HttpServletResponse response)
    {

        OSOrder order = OSOrderHelper.getOrder(request);

        Map<String, String> additionalParams = new HashMap<>();
        Map<String, String> emailModelParams = new HashMap<>();
        emailModelParams.put("onCompleted", request.getParameter("comingFrom"));
        emailModelParams.put("onCancelled", request.getParameter("comingFrom"));
        emailModelParams.put("action", "sendEmailCommon");
        additionalParams.put("acrmUserId", order.getCustomer().getId().toString());
        additionalParams.put("osOrderId", order.getId().toString());
        EmailModel emailModel = getEmailModel(request, emailModelParams,
                additionalParams);
        if (order.getEcommerceOrder() == null || order.getEcommerceOrder()
                .getStatus() == EcommerceOrder.STATUS_PAYMENT_PAID)
        {
            emailModel.setEmailAttachment(OSHelper.getReceiptAttachment(order,
                    PortalUtils.getLocale(request)));
        }
        else
        {
            emailModel.setEmailAttachment(OSHelper.getInvoiceAttachment(order,
                    PortalUtils.getLocale(request)));
        }

        return displayEmailer(request, response, emailModel);
    }

    @RequestMapping("printDetailedOrder")
    public ModelAndView printDetailedOrder(HttpServletRequest request,
            HttpServletResponse response)
    {
        OSOrder order = OSOrderHelper.getOrder(request);
        ByteArrayOutputStream roi = null;
        String filename = "";
        if (order.getEcommerceOrder() == null || order.getEcommerceOrder()
                .getStatus() == EcommerceOrder.STATUS_PAYMENT_PAID)
        {
            roi = OSHelper.getReceiptOrInvoicePDF(order, true,
                    PortalUtils.getLocale(request));
            filename = "Order#" + order.getOrderNumber() + "Receipt.pdf";
        }
        else
        {
            roi = OSHelper.getReceiptOrInvoicePDF(order, false,
                    PortalUtils.getLocale(request));
            filename = "Order#" + order.getOrderNumber() + "Invoice.pdf";
        }

        ModelAndView mv = JasperController.getReportDownloadView(filename, roi);
        return mv;
    }

    @Override
    @RequestMapping("onEmailCompleted")
    public ModelAndView onEmailCompleted(HttpServletRequest request,
            HttpServletResponse response, EmailModel emailModel)
    {
        ModelAndView mv = null;

        try
        {
            request.setAttribute("osOrderId",
                    emailModel.getAdditionalParams().get("osOrderId"));
            mv = invokeNamedMethod(emailModel.getOnCompleted(), request, response);
        }
        catch (Exception e)
        {
            mv = displayError(
                    new I18nLabel("i18n.OrbisController.Anexceptio3401178166381804",
                            e.getMessage()).getTranslation(
                            PortalUtils.getLocale(request)));
            e.printStackTrace();
        }

        return mv;
    }

    @RequestMapping("savePaymentOverride")
    public ModelAndView savePaymentOverride(HttpServletRequest request,
            HttpServletResponse response)
    {
        boolean errors = false;

        String paymentOverrideId = request.getParameter("paymentOverrideId");
        // String tag = request.getParameter("tag");
        String description = request.getParameter("description");
        String tax1 = request.getParameter("tax1");
        String tax2 = request.getParameter("tax2");
        String tax3 = request.getParameter("tax3");

        OSModule module = getModule(request);

        OSPaymentOverride paymentOverride = null;

        if (StringUtils.isInteger(paymentOverrideId))
        {
            paymentOverride = (OSPaymentOverride) getHt()
                    .load(OSPaymentOverride.class, Integer.valueOf(paymentOverrideId));
        }

        if (paymentOverride == null)
        {
            paymentOverride = new OSPaymentOverride();
            paymentOverride.setModule(module);
            paymentOverride.setCreateDate(new Date());
            paymentOverride.setCreatedBy(PortalUtils.getUserLoggedIn(request));
        }

        if (!StringUtils.isEmpty(request.getParameter("tag")))
        {
            AcrmTag tag = (AcrmTag) getHt().load(AcrmTag.class,
                    AcrmHelper.getInteger(request, "tag"));
            paymentOverride.setTag(tag);

            paymentOverride.setDescription(description);

            if (StringUtils.isInteger(tax1))
            {
                paymentOverride.setTax1(EcommerceHelper.getTax(Integer.valueOf(tax1)));
            }
            else
            {
                paymentOverride.setTax1(null);
            }

            if (StringUtils.isInteger(tax2))
            {
                paymentOverride.setTax2(EcommerceHelper.getTax(Integer.valueOf(tax2)));
            }
            else
            {
                paymentOverride.setTax2(null);
            }

            if (StringUtils.isInteger(tax3))
            {
                paymentOverride.setTax3(EcommerceHelper.getTax(Integer.valueOf(tax3)));
            }
            else
            {
                paymentOverride.setTax3(null);
            }
            getHt().saveOrUpdate(paymentOverride);
        }
        else
        {
            errors = true;
        }

        ModelAndView mv = displayTaxOverrideTags(request, response);
        if (errors)
        {
            mv.addObject("failMessage",
                    new I18nLabel("i18n.OSController.TaxOverrid6361458961089837")
                            .getTranslation(PortalUtils.getLocale(request)));
        }
        else
        {
            mv.addObject("successMessage",
                    new I18nLabel("i18n.OSController.TaxOverrid4244775273429557")
                            .getTranslation(PortalUtils.getLocale(request)));
        }

        return mv;
    }

    @RequestMapping("deletePaymentOverride")
    public ModelAndView deletePaymentOverride(HttpServletRequest request,
            HttpServletResponse response)
    {
        String paymentOverrideId = request.getParameter("paymentOverrideId");

        if (StringUtils.isInteger(paymentOverrideId))
        {
            OSPaymentOverride paymentOverride = (OSPaymentOverride) getHt()
                    .load(OSPaymentOverride.class, Integer.valueOf(paymentOverrideId));
            getHt().delete(paymentOverride);
        }
        ModelAndView mv = displayTaxOverrideTags(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.OSController.TaxOverrid2285864000081146")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    @RequestMapping("displayAdmins")
    public ModelAndView displayAdmins(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_manageAdmins");

        List memberAdmins = OSHelper.getModuleAdmins(getModule(request));

        String excludedIdsClause = "";
        for (Iterator i = memberAdmins.iterator(); i.hasNext(); )
        {
            Object[] data = (Object[]) i.next();
            excludedIdsClause += data[0];
            if (i.hasNext())
            {
                excludedIdsClause += ", ";
            }
        }
        if (excludedIdsClause.length() > 0)
        {
            excludedIdsClause = " and user.id not in (" + excludedIdsClause + ") ";
        }

        List nonMemberProviders = getHt().find(
                "select user.id, user.preferredFirstName, user.lastName, user.username from UserDetailsImpl user join user.groups as g where g.name=? "
                        + excludedIdsClause
                        + "  and user.deleted=false order by user.lastName, user.preferredFirstName ",
                PersonGroupHelper.OS_ADMIN);

        mv.addObject("memberProviders", memberAdmins);
        mv.addObject("nonMemberProviders", nonMemberProviders);
        mv.addObject("module", getModule(request));

        return mv;
    }

    @RequestMapping("saveMemberAdmins")
    public ModelAndView saveMemberAdmins(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        JSONArray memberAdmins = new JSONArray(request.getParameter("membersJson"));

        OSModule module = getModule(request);
        PortalUtils.getJt().update("delete from os_module_admin where module = ?",
                new Object[] { module.getId() });

        for (int i = 0; i < memberAdmins.length(); i++)
        {
            OSModuleAdmin ma = new OSModuleAdmin();
            ma.setModule(module);
            Integer userId = Integer.valueOf(memberAdmins.getInt(i));
            ma.setAdmin(
                    (UserDetailsImpl) getHt().load(UserDetailsImpl.class, userId));
            getHt().save(ma);
        }

        return displayAdmins(request, response);
    }

    @RequestMapping("displayOrdersList")
    public ModelAndView displayOrdersList(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_ordersList");
        List<Object[]> orders = null;
        String listType = request.getParameter("listType");
        String selectFrom = "select o.id, o.customer.preferredFirstName, o.customer.lastName, o.status, o.total, o.orderDate from OSOrder o";
        OSModule module = getModule(request);

        if ("newOrders".equals(listType))
        {
            orders = getHt()
                    .find(selectFrom + " where " + getWhereNewOrders(module));
        }
        else if ("paidOrders".equals(listType))
        {
            orders = getHt().find(selectFrom + " where "
                    + getWhereRecentlyPaid(module) + " order by o.orderDate");
        }
        else if ("30daysOverdue".equals(listType))
        {
            orders = getHt().find(selectFrom + " where "
                    + getWhereThirtyDaysOverdue(module) + " order by o.orderDate");
        }
        else if ("60daysOverdue".equals(listType))
        {
            orders = getHt().find(selectFrom + " where "
                    + getWhereSixtyDaysOverdue(module) + " order by o.orderDate");
        }
        else if ("incompleteOrders".equals(listType))
        {
            orders = getHt().find(
                    selectFrom + " where " + getWhereIncompleteOrders(module));
        }
        else if ("cancelledOrders".equals(listType))
        {
            orders = getHt().find(selectFrom + " where "
                    + getWhereCancelledOrders(module) + " order by o.orderDate");
        }

        mv.addObject("listType", listType);
        mv.addObject("orders", orders);
        mv.addObject("module", module);

        return mv;
    }

    @OrbisRepost
    @RequestMapping("displayOrder")
    public ModelAndView displayOrder(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        OSOrder order = OSOrderHelper.getOrder(request);

        if (order == null)
        {
            EcommerceOrder ecOrder = EcommerceHelper.getOrder(request);
            order = (OSOrder) EcommerceHelper.getEcommerceEntity(ecOrder);
        }

        if (order != null)
        {
            mv = new ModelAndView("os/os_orderDetails");
            order.setOrderItems(OSHelper.getOrderItemsForOrder(order));
            mv.addObject("comingFrom", request.getParameter("comingFrom"));
            mv.addObject("listType", request.getParameter("listType"));
            mv.addObject("customerId", request.getParameter("customerId"));
            mv.addObject("orgId", request.getParameter("orgId"));
            mv.addObject("studentStep", EXPStudentExperienceStepHelper
                    .getStudentExperienceStep(request));
            mv.addObject("order", order);
            mv.addObject("module", getModule(request));
            mv.addObject("ecModule", order.getModule().getEcommerceModule());
            mv.addObject("osAdmin",
                    OSHelper.isUserOSAdmin(PortalUtils.getUserLoggedIn(request)));
        }

        return mv;
    }

    @RequestMapping("displayCustomer")
    public ModelAndView displayCustomer(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_customerDetails");
        UserDetailsImpl customer = (UserDetailsImpl) getHt().load(
                UserDetailsImpl.class,
                Integer.valueOf(request.getParameter("customerId")));
        OSModule module = getModule(request);
        mv.addObject("orders", getHt().find(
                "select o.id, o.ecommerceOrder.orderNumber, o.status, o.total, o.orderDate from OSOrder o where o.customer.id=? and o.module.id=? and o.status<>4",
                new Object[] { customer.getId(), module.getId() }));
        mv.addObject("orders2", getHt().find(
                "select o.id, o.status, o.total, o.orderDate from OSOrder o where o.ecommerceOrder is null and o.customer.id=? and o.module.id=? and o.status<>4",
                new Object[] { customer.getId(), module.getId() }));
        mv.addObject("customer", customer);
        mv.addObject("module", getModule(request));
        mv.addObject("acrmFullPath",
                NHelper.getAcrmCareerSiteElement(
                        PortalUtils.getUserLoggedIn(request)).getFullPath()
                        + ".htm");
        mv.addObject("acrmId", UserDetailsHelper
                .getUserByUserId(Integer.valueOf(request.getParameter("customerId"))));
        return mv;
    }

    @RequestMapping("displayCustomerOrg")
    public ModelAndView displayCustomerOrg(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_organizationDetails");
        Organization org = (Organization) getHt().load(Organization.class,
                Integer.valueOf(request.getParameter("orgId")));
        OSModule module = getModule(request);
        mv.addObject("orders", getHt().find(
                "select distinct o.id, o.ecommerceOrder.orderNumber, o.status, o.total, o.orderDate, o.customer.preferredFirstName, o.customer.lastName, cu.company.name from OSOrder o, CompanyUsers cu where o.customer.id=cu.user.id and cu.company.organization.id=? and o.module.id=? and o.status<>4",
                new Object[] { org.getId(), module.getId() }));
        mv.addObject("orders2", getHt().find(
                "select distinct o.id, o.status, o.total, o.orderDate, o.customer.preferredFirstName, o.customer.lastName, o.customer.id from OSOrder o, CompanyUsers cu where o.ecommerceOrder is null and o.customer.id=cu.user.id and cu.company.organization.id=? and o.module.id=? and o.status<>4",
                new Object[] { org.getId(), module.getId() }));
        mv.addObject("org", org);
        mv.addObject("module", getModule(request));
        mv.addObject("acrmFullPath",
                NHelper.getAcrmCareerSiteElement(
                        PortalUtils.getUserLoggedIn(request)).getFullPath()
                        + ".htm");
        return mv;
    }

    @Override
    @RequestMapping("sendEmail")
    public ModelAndView sendEmail(HttpServletRequest request,
            HttpServletResponse response)
    {
        String subject = request.getParameter("subject");
        String body = request.getParameter("body");
        String fromAddress = request.getParameter("from");
        String toAddress = request.getParameter("to");
        ArrayList<EmailAttachment> attachments = new ArrayList<>();
        OSOrder order = OSOrderHelper.getOrder(request);

        OSHelper.sendEmail(request, subject, body, fromAddress, toAddress, null,
                attachments, order);
        ModelAndView mv = displayOrder(request, response);
        return mv;
    }

    @RequestMapping("search")
    public ModelAndView search(HttpServletRequest request,
            HttpServletResponse response)
    {
        return SearchSubControllerFactory.getInstance(searchInterface)
                .processRequest(request, response);
    }

    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupUsers")
    public ModelAndView lookupUsers(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        return AcrmHelper.lookupUsers(request.getParameter("term"), null,
                UserDetailsImpl.class, "user", "user", 40, false, null);
    }

    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupOrderByNumber")
    public ModelAndView lookupOrderByNumber(HttpServletRequest request,
            HttpServletResponse response)
    {
        String searchTerm = request.getParameter("term").trim();

        List params = new ArrayList();

        String searchTermWhereClause = LookupUtils.getSearchTermWhereClause(
                searchTerm, Lists.newArrayList("o.orderNumber"), params);

        QueryBuilder q = new QueryBuilder();
        q.append(
                "select o.id, o.orderNumber, o.customer.preferredFirstName, o.customer.lastName from OSOrder o left join o.customer where o.module=?",
                getModule(request));
        q.append(" and " + searchTermWhereClause, params.toArray());

        List<Object[]> orders = getHt().find(q);

        return NHelper.lookupsAJAXResponse(orders, 0, "[1] [2] [3]");
    }

    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupCustomer")
    public ModelAndView lookupCustomer(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        return AcrmHelper.lookupUsers(request.getParameter("term"), null, "",
                OSOrder.class, "o", "o.customer", 20,
                "distinct o.customer.id, o.customer.preferredFirstName, o.customer.lastName, o.customer.username",
                "o.customer.preferredFirstName, o.customer.lastName, o.customer.username", 0,
                "[1] [2] ([3])", false, null);
    }

    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupCustomerOrg")
    public ModelAndView lookupCustomerOrg(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        String searchTerm = request.getParameter("term");

        List params = new ArrayList();
        List<Object[]> orgs = getHt().find(
                "select distinct cu.company.organization.id, cu.company.organization.name from OSOrder o, CompanyUsers cu join cu.company.organization where o.status<>4 and o.customer.id=cu.user.id and "
                        + LookupUtils.getOrganizationSearchTermWhereClause(
                        searchTerm, "cu.company.organization", params)
                        + " order by cu.company.organization.name",
                params.toArray());

        return NHelper.lookupsAJAXResponse(orgs, 0, "[1]");
    }

    @Override
    @RequestMapping("checkoutAdjustmentOrder")
    public ModelAndView checkoutAdjustmentOrder(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        return EcommerceHelper.checkoutAdjustmentOrder(request,
                getModule(request).getEcommerceModule());
    }

    @Override
    @RequestMapping("ajaxLoadOrderHistory")
    public ModelAndView ajaxLoadOrderHistory(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.ajaxLoadOrderHistory(request);
    }

    @Override
    @RequestMapping("ajaxLoadCustomerOrders")
    public ModelAndView ajaxLoadCustomerOrders(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.ajaxLoadCustomerOrders(request);
    }

    @Override
    @RequestMapping("ajaxLoadOrderDetails")
    public ModelAndView ajaxLoadOrderDetails(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.ajaxLoadOrderDetails(request);
    }

    @Override
    @RequestMapping("ajaxSaveOrderNote")
    public ModelAndView ajaxSaveOrderNote(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.ajaxSaveOrderNote(request);
    }

    @Override
    @RequestMapping("ajaxSaveOrderStatus")
    public ModelAndView ajaxSaveOrderStatus(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.ajaxSaveOrderStatus(request);
    }

    @Override
    @RequestMapping("printOrderHistory")
    public ModelAndView printOrderHistory(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = EcommerceHelper.printOrderHistory(request);
        // ecommerce does the work but a different OS jsp is needed to render
        // the module overridden headers and footers
        mv.setViewName("os/os_printEcommerce");
        mv.addObject("module", getModule(request));
        return mv;
    }

    @Override
    @RequestMapping("ajaxLoadEmailOrderHistory")
    public ModelAndView ajaxLoadEmailOrderHistory(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = EcommerceHelper.ajaxLoadEmailOrderHistory(request);
        mv.setViewName("os/os_ajax_order_emailer");
        mv.addObject("module", getModule(request));
        return mv;
    }

    @Override
    @RequestMapping("printOrder")
    public ModelAndView printOrder(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = EcommerceHelper.printOrder(request);
        // ecommerce does the work but a different OS jsp is needed to render
        // the module overridden headers and footers
        mv.setViewName("os/os_printEcommerce");
        mv.addObject("module", getModule(request));
        return mv;
    }

    @Override
    @RequestMapping("ajaxLoadEmailOrder")
    public ModelAndView ajaxLoadEmailOrder(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = EcommerceHelper.ajaxLoadEmailOrder(request);
        mv.setViewName("os/os_ajax_order_emailer");
        mv.addObject("module", getModule(request));
        return mv;
    }

    @Override
    @RequestMapping("ajaxSendOrderEmail")
    public ModelAndView ajaxSendOrderEmail(HttpServletRequest request,
            HttpServletResponse response)
    {
        return EcommerceHelper.ajaxSendOrderEmail(request);
    }

    private ModelAndView displayAdminHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("os/os_adminHomeOverviewTab");

        OSModule module = getModule(request);
        mv.addObject("module", getModule(request));

        // NEW ORDERS COUNT
        String newOrdersCount = "select count(*) from OSOrder o where "
                + getWhereNewOrders(module);
        mv.addObject("newOrdersCount", getHt().find(newOrdersCount).get(0));

        // RECENTLY PAID ORDERS COUNT
        String recentlyPaidCount = "select count(o) from OSOrder o where "
                + getWhereRecentlyPaid(module);
        mv.addObject("recentlyPaidCount", getHt().find(recentlyPaidCount).get(0));

        // THIRTY DAYS OVERDUE COUNT
        String thirtyDaysOverdue = "select count(o.id) from OSOrder o where "
                + getWhereThirtyDaysOverdue(module);
        mv.addObject("thirtyDaysOverdue", getHt().find(thirtyDaysOverdue).get(0));

        // SIXTY DAYS OVERDUE COUNT
        String sixtyDaysOverdue = "select count(o.id) from OSOrder o where "
                + getWhereSixtyDaysOverdue(module);
        mv.addObject("sixtyDaysOverdue", getHt().find(sixtyDaysOverdue).get(0));

        // INCOMPLETE/FAILED ORDERS COUNT
        String incompleteOrders = "select count(o.id) from OSOrder o where "
                + getWhereIncompleteOrders(module);
        mv.addObject("incompleteOrders", getHt().find(incompleteOrders).get(0));

        // CANCELLED ORDERS COUNT
        String cancelledOrders = "select count(o.id) from OSOrder o where "
                + getWhereCancelledOrders(module);
        mv.addObject("cancelledOrders", getHt().find(cancelledOrders).get(0));

        // ORDER HISTORY
        mv.addObject("orderHistory", OSHelper.getOrderHistory(module));

        return mv;
    }

    private String getWhereCancelledOrders(OSModule module)
    {
        StringBuilder hql = new StringBuilder();

        hql.append(" o.status = 3 and o.module.id=").append(module.getId());

        return hql.toString();
    }

    private String getWhereIncompleteOrders(OSModule module)
    {
        StringBuilder hql = new StringBuilder();

        hql.append(" (o.status = 0 or o.status = 1) and o.module.id=")
                .append(module.getId());

        return hql.toString();
    }

    private String getWhereSixtyDaysOverdue(OSModule module)
    {
        Date now = new Date();

        StringBuilder hql = new StringBuilder();
        hql.append(" o.status=2 and o.orderDate <= '")
                .append(DateUtils.formatDate(DateUtils.subtractDays(now, 60),
                        DBUtils.DB_DATE_TIME_FORMAT, null))
                .append("' and o.module.id=").append(module.getId());

        return hql.toString();
    }

    private String getWhereThirtyDaysOverdue(OSModule module)
    {
        Date now = new Date();

        StringBuilder hql = new StringBuilder();
        hql.append("o.status=2 and o.orderDate between '")
                .append(DateUtils.formatDate(DateUtils.subtractDays(now, 60),
                        DBUtils.DB_DATE_TIME_FORMAT, null))
                .append("' and '")
                .append(DateUtils.formatDate(DateUtils.subtractDays(now, 30),
                        DBUtils.DB_DATE_TIME_FORMAT, null))
                .append("' and o.module.id=").append(module.getId());

        return hql.toString();
    }

    private String getWhereRecentlyPaid(OSModule module)
    {
        String tenDaysAgo = DateUtils.formatDate(
                DateUtils.subtractDays(new Date(), 10), DBUtils.DB_DATE_TIME_FORMAT,
                null);

        StringBuilder hql = new StringBuilder();

        hql.append(
                        " o.ecommerceOrder.reconcileTarget is null and o.completedDate > '")
                .append(tenDaysAgo);

        hql.append("' and o.status=2 and o.module.id=").append(module.getId());

        return hql.toString();
    }

    private String getWhereNewOrders(OSModule module)
    {
        StringBuilder hql = new StringBuilder();
        hql.append(" o.status=2 and o.orderDate > '")
                .append(DateUtils.formatDate(DateUtils.subtractDays(new Date(), 10),
                        DBUtils.DB_DATE_TIME_FORMAT, null))
                .append("' and o.module.id=").append(module.getId());

        return hql.toString();
    }

    private Object[] isProductInOrder(Integer productId, OSOrder order)
    {
        ArrayList count = new ArrayList();
        for (OSOrderItem orderItem : order.getOrderItems())
        {
            if (orderItem.getProduct().getId().equals(productId))
            {
                count.add(orderItem.getUnits());
                if (orderItem.isOverridden()
                        && orderItem.getProduct().getFeeType() != 2)
                {
                    count.add(orderItem.getSubTotal());
                }
                else if (orderItem.isOverridden()
                        && orderItem.getProduct().getFeeType() == 2)
                {
                    count.add(orderItem.getSubTotal()
                            / Integer.valueOf(orderItem.getUnits()).doubleValue());
                }
            }
        }
        return count.toArray();
    }

    private void populateReportDetailsTab(ModelAndView mv,
            HttpServletRequest request, OSReportFilter reportFilter)
    {
        Locale locale = PortalUtils.getLocale(request);
        SearchModel searchModel = OSHelper.getSearchModel_storeOrders(request,
                getModule(request));
        SearchHelper.cleanSearchModelQuestions(searchModel);

        String filterClause = OSHelper.getOrderFilterClause("oso", reportFilter);

        if (filterClause.length() > 0)
        {
            Entity master = searchModel.getMasterEntity();
            String staticWhereHql = master.getStaticWhereHql();
            master.setStaticWhereHql(
                    staticWhereHql == null ? filterClause.toString()
                            : (staticWhereHql + " and " + filterClause.toString()));
        }

        JQGridModel gridModel = SearchHelper.createJQGridModel(searchModel, locale);
        gridModel.getDetails().values()
                .forEach(jqSearch -> jqSearch.setRelationship(SearchHelper
                        .getDetailRelationship(jqSearch, gridModel, searchModel)));

        JQGridHelper.populateSearchResultsPage(mv, gridModel, request,
                SearchHelper.SEARCH_MODEL_SESSION_KEY);
    }

    @RequestMapping("deleteOrder")
    public ModelAndView deleteOrder(HttpServletRequest request,
            HttpServletResponse response)
    {
        OSOrder order = OSOrderHelper.getOrder(request);

        order.setStatus(OSOrder.STATUS_DELETED);
        order.setDeletedDate(new Date());
        getHt().update(order);

        ModelAndView mv = displayOrder(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.OSController.Ordersucce7524880305579882")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    @Override
    @RequestMapping("reconcileOrderCheckout")
    public ModelAndView reconcileOrderCheckout(HttpServletRequest request,
            HttpServletResponse response)
    {
        String routingUrl = PortalUtils.getCurrentElement(request).getFullPath()
                + ".htm";
        String routingAction = "displayOrder";
        return EcommerceHelper.checkoutReconcileOrder(request, routingUrl,
                routingAction);
    }

}
