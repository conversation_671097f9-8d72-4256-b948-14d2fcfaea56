package com.orbis.web.content.exp;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.hibernate.annotations.ColumnDefault;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.df.DFAbstractModelEntity;
import com.orbis.df.DFAnswerEntity;
import com.orbis.df.DFQualifierEntity;
import com.orbis.qf.QFQualificationTest;
import com.orbis.qf.QFQualifierConfigLevel;
import com.orbis.qf.QFQualifierEntity;
import com.orbis.qf.QFStudentEntity;
import com.orbis.utils.LocaleUtils;
import com.orbis.web.OrbisModule;
import com.orbis.web.content.acrm.competency.Competenciable;
import com.orbis.web.content.acrm.reflection.config.assign.AcrmReflectionConfigEXPTermCourseTypeStep;
import com.orbis.web.content.acrm.reflection.config.configurable.AcrmReflectionConfigurable;
import com.orbis.web.content.exp.studentExperienceStep.creator.EXPStudentExperienceStepBasicCreator;
import com.orbis.web.content.exp.studentExperienceStep.creator.EXPStudentExperienceStepCreator;
import com.orbis.web.content.exp.studentExperienceStep.creator.ReflectionExperienceStepCreator;
import com.orbis.web.content.exp.studentExperienceStep.workflow.EXPStudentExperienceHourTrackingStepWorkflow;
import com.orbis.web.content.exp.studentExperienceStep.workflow.EXPStudentExperienceJournalStepWorkflow;
import com.orbis.web.content.exp.studentExperienceStep.workflow.EXPStudentExperienceQualifiersStepWorkflow;
import com.orbis.web.content.exp.studentExperienceStep.workflow.EXPStudentExperienceReflectionStepWorkflow;
import com.orbis.web.content.exp.studentExperienceStep.workflow.EXPStudentExperienceStepWorkflow;
import com.orbis.web.content.exp.studentExperienceStep.workflow.EXPStudentExperienceSubrecordStepWorkflow;
import com.orbis.web.content.exp.termCourseTypeStep.creator.AdminWaitStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.AttendAppointmentStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.AttendEventStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.CompetencyStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.CreateRecordStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.CustomStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.EXPTermCourseTypeStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.EcommercePurchaseStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.FacultyEvaluationStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.FormGroupStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.IndustryPartnerEvaluationStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.JournalStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.QualifiersStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.QuestionnaireStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.ReflectionStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.RequiredSystemActivityStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.StudentEvaluationStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.TermsAndConditionsStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.TrackHoursStepCreator;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;

@Access(value = AccessType.FIELD)
public class EXPTermCourseTypeStep extends DFAbstractModelEntity
        implements DFQualifierEntity,
        AcrmReflectionConfigurable<EXPTermCourseTypeStep, AcrmReflectionConfigEXPTermCourseTypeStep>,
        Competenciable, QFQualifierEntity
{
    private static final long serialVersionUID = 5549711920707193765L;

    /**
     * used to connect steps to a tct if new experiential is not enabled
     */
    private EXPTermCourseType tct;

    /**
     * Used in new Experiential if templates are set as the workflow mode in
     * {@link EXPTermCourseType}
     */
    private EXPTypeWorkflowTemplate template;

    @ColumnDefault("0")
    private int type;

    @ColumnDefault("0")
    private int stepOrder;

    private String instructions, l2Instructions;

    private String label, l2Label;

    /**
     * When this is true, the step does not need to be completed in order to
     * consider the course complete. Once the student gets to this step in the
     * workflow, it will automatically start the step below it to allow the student
     * to continue with the other required steps in the workflow
     */
    @ColumnDefault("0")
    private boolean optional;

    // Terms And Conditions
    private String tacStepContent, l2TacStepContent;

    // Form A Group
    @ColumnDefault("0")
    private boolean studentCanFormGroup = false;

    // Track Hours
    @ColumnDefault("0")
    private double requiredHoursToComplete = 0d;

    // Track Number of Hour Tracking or Journal Entries required
    @ColumnDefault("0")
    private int requiredEntriesToComplete = 0;

    // If this is true, the questionnaire must be approved to be marked as
    // complete
    @ColumnDefault("0")
    private boolean questionnaireApprovalRequired;

    // if questionnaireApprovalRequired is true then this option can be enabled
    // to allow the student to resubmit the questionnaire to be approved
    @ColumnDefault("0")
    private boolean allowQuestionnaireResubmit;

    @ColumnDefault("4")
    private int competencyUsecase = Competenciable.COMP_DISABLED;

    @ColumnDefault("0")
    private int minimumCompetencies, maximumCompetencies;

    /**
     * Is the step the final reflection?
     */
    @ColumnDefault("0")
    private boolean finalReflection;

    @ColumnDefault("0")
    private boolean entityCompetencyTracking;

    @ColumnDefault("0")
    private boolean finalCompetencyStep;

    @ColumnDefault("0")
    private boolean enableCompetencySupervisor;

    @ColumnDefault("0")
    private boolean enableAutomaticEmailCompetencySupervisor;

    @ColumnDefault("0")
    private boolean allowReflectionOnCompetency;

    @ColumnDefault("0")
    private int competencyStepForReflection;

    private String automaticCompetencyEmailBody, automaticCompetencyEmailSubject,
            l2AutomaticCompetencyEmailBody, l2AutomaticCompetencyEmailSubject,
            automaticCompetencyFromEmail;

    public EXPTermCourseType getTct()
    {
        return tct;
    }

    public void setTct(EXPTermCourseType tct)
    {
        this.tct = tct;
    }

    public int getType()
    {
        return type;
    }

    public Type getTypeEnum()
    {
        return Type.of(type);
    }

    public void setType(int type)
    {
        this.type = type;
    }

    public int getStepOrder()
    {
        return stepOrder;
    }

    public void setStepOrder(int stepOrder)
    {
        this.stepOrder = stepOrder;
    }

    public String getLabel()
    {
        return label;
    }

    public void setLabel(String label)
    {
        this.label = label;
    }

    public String getL2Label()
    {
        return l2Label;
    }

    public void setL2Label(String l2Label)
    {
        this.l2Label = l2Label;
    }

    public String getInstructions()
    {
        return instructions;
    }

    public void setInstructions(String instructions)
    {
        this.instructions = instructions;
    }

    public String getL2Instructions()
    {
        return l2Instructions;
    }

    public void setL2Instructions(String l2Instructions)
    {
        this.l2Instructions = l2Instructions;
    }

    public String getTacStepContent()
    {
        return tacStepContent;
    }

    public void setTacStepContent(String tacStepContent)
    {
        this.tacStepContent = tacStepContent;
    }

    public String getL2TacStepContent()
    {
        return l2TacStepContent;
    }

    public void setL2TacStepContent(String l2TacStepContent)
    {
        this.l2TacStepContent = l2TacStepContent;
    }

    // Questionnaire With Qualifiers
    @Override
    public Class<? extends DFAnswerEntity> getAnswerEntityClass()
    {
        return EXPStudentExperienceStep.class;
    }

    @Override
    public Class getQualifierClass()
    {
        return EXPTermCourseTypeStepQualifier.class;
    }

    @Override
    public String getQualifierEntityFieldName()
    {
        return "expTermCourseTypeStep";
    }

    public boolean isStudentCanFormGroup()
    {
        return studentCanFormGroup;
    }

    public void setStudentCanFormGroup(boolean studentCanFormGroup)
    {
        this.studentCanFormGroup = studentCanFormGroup;
    }

    public double getRequiredHoursToComplete()
    {
        return requiredHoursToComplete;
    }

    public void setRequiredHoursToComplete(double requiredHoursToComplete)
    {
        this.requiredHoursToComplete = requiredHoursToComplete;
    }

    public int getRequiredEntriesToComplete()
    {
        return requiredEntriesToComplete;
    }

    public void setRequiredEntriesToComplete(int requiredEntriesToComplete)
    {
        this.requiredEntriesToComplete = requiredEntriesToComplete;
    }

    public String getStepInstructions()
    {
        String ret = instructions;
        if (StringUtils.isEmpty(ret))
        {
            EXPExperienceType expType = tct.getType();
            EXPModule module = expType.getModule();
            ret = module.getStepInstructions(type);
        }
        return ret;
    }

    public EXPTypeWorkflowTemplate getTemplate()
    {
        return template;
    }

    public void setTemplate(EXPTypeWorkflowTemplate template)
    {
        this.template = template;
    }

    public String getL2StepInstructions()
    {
        String ret = l2Instructions;
        if (StringUtils.isEmpty(ret))
        {
            EXPExperienceType expType = tct.getType();
            EXPModule module = expType.getModule();
            ret = module.getL2StepInstructions(type);
        }
        return ret;
    }

    public String getStepLabel(Locale locale)
    {
        return LocaleUtils.isL1(locale) ? getStepLabel() : getL2StepLabel();
    }

    public String getStepLabel(String orbisLocale)
    {
        return LocaleUtils.isL1(orbisLocale) ? getStepLabel() : getL2StepLabel();
    }

    public String getStepLabel()
    {
        return label;
    }

    public String getL2StepLabel()
    {
        return l2Label;
    }

    public boolean isQuestionnaireApprovalRequired()
    {
        return questionnaireApprovalRequired;
    }

    public void setQuestionnaireApprovalRequired(
            boolean questionnaireApprovalRequired)
    {
        this.questionnaireApprovalRequired = questionnaireApprovalRequired;
    }

    public boolean isAllowQuestionnaireResubmit()
    {
        return allowQuestionnaireResubmit;
    }

    public void setAllowQuestionnaireResubmit(boolean allowQuestionnaireResubmit)
    {
        this.allowQuestionnaireResubmit = allowQuestionnaireResubmit;
    }

    @Override
    public EXPTermCourseTypeStepSupport getReflectionConfigurableSupport()
    {
        return new EXPTermCourseTypeStepSupport();
    }

    public boolean isFinalReflection()
    {
        return finalReflection;
    }

    public void setFinalReflection(boolean finalReflection)
    {
        this.finalReflection = finalReflection;
    }

    public boolean isEntityCompetencyTracking()
    {
        return entityCompetencyTracking;
    }

    public void setEntityCompetencyTracking(boolean entityCompetencyTracking)
    {
        this.entityCompetencyTracking = entityCompetencyTracking;
    }

    public boolean isFinalCompetencyStep()
    {
        return finalCompetencyStep;
    }

    public void setFinalCompetencyStep(boolean finalCompetencyStep)
    {
        this.finalCompetencyStep = finalCompetencyStep;
    }

    public boolean isOptional()
    {
        return optional;
    }

    public void setOptional(boolean optional)
    {
        this.optional = optional;
    }

    public boolean isAllowReflectionOnCompetency()
    {
        return allowReflectionOnCompetency;
    }

    public void setAllowReflectionOnCompetency(boolean allowReflectionOnCompetency)
    {
        this.allowReflectionOnCompetency = allowReflectionOnCompetency;
    }

    public int getCompetencyStepForReflection()
    {
        return competencyStepForReflection;
    }

    public void setCompetencyStepForReflection(int competencyStepForReflection)
    {
        this.competencyStepForReflection = competencyStepForReflection;
    }

    public boolean isEnableCompetencySupervisor()
    {
        return enableCompetencySupervisor;
    }

    public void setEnableCompetencySupervisor(boolean enableCompetencySupervisor)
    {
        this.enableCompetencySupervisor = enableCompetencySupervisor;
    }

    public boolean isEnableAutomaticEmailCompetencySupervisor()
    {
        return enableAutomaticEmailCompetencySupervisor;
    }

    public void setEnableAutomaticEmailCompetencySupervisor(
            boolean enableAutomaticEmailCompetencySupervisor)
    {
        this.enableAutomaticEmailCompetencySupervisor = enableAutomaticEmailCompetencySupervisor;
    }

    public String getAutomaticCompetencyEmailBody()
    {
        return automaticCompetencyEmailBody;
    }

    public void setAutomaticCompetencyEmailBody(String automaticCompetencyEmailBody)
    {
        this.automaticCompetencyEmailBody = automaticCompetencyEmailBody;
    }

    public String getAutomaticCompetencyEmailSubject()
    {
        return automaticCompetencyEmailSubject;
    }

    public void setAutomaticCompetencyEmailSubject(
            String automaticCompetencyEmailSubject)
    {
        this.automaticCompetencyEmailSubject = automaticCompetencyEmailSubject;
    }

    public String getL2AutomaticCompetencyEmailBody()
    {
        return l2AutomaticCompetencyEmailBody;
    }

    public void setL2AutomaticCompetencyEmailBody(
            String l2AutomaticCompetencyEmailBody)
    {
        this.l2AutomaticCompetencyEmailBody = l2AutomaticCompetencyEmailBody;
    }

    public String getL2AutomaticCompetencyEmailSubject()
    {
        return l2AutomaticCompetencyEmailSubject;
    }

    public void setL2AutomaticCompetencyEmailSubject(
            String l2AutomaticCompetencyEmailSubject)
    {
        this.l2AutomaticCompetencyEmailSubject = l2AutomaticCompetencyEmailSubject;
    }

    public String getAutomaticCompetencyFromEmail()
    {
        return automaticCompetencyFromEmail;
    }

    public void setAutomaticCompetencyFromEmail(String automaticCompetencyFromEmail)
    {
        this.automaticCompetencyFromEmail = automaticCompetencyFromEmail;
    }

    /**
     * The type of step
     *
     * <AUTHOR>
     *
     */
    public enum Type
    {
        TERMS_AND_CONDITIONS
        {
            @Override
            public int getValue()
            {
                return 0;
            }

            @Override
            public TermsAndConditionsStepCreator getCreator()
            {
                return new TermsAndConditionsStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getTacStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2TacStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getTacStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2TacStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return false;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isTacStepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }

            @Override
            public boolean isGateStep()
            {
                return true;
            }
        },
        QUESTIONNAIRE_WITH_QUALIFIERS
        {
            @Override
            public int getValue()
            {
                return 1;
            }

            @Override
            public QuestionnaireStepCreator getCreator()
            {
                return new QuestionnaireStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getQuesQualStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2QuesQualStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getQuesQualStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2QuesQualStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return false;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isQuesQualStepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateQuestionnaireStepPropagator();
            }
        },
        ATTEND_AN_EVENT
        {
            @Override
            public int getValue()
            {
                return 2;
            }

            @Override
            public AttendEventStepCreator getCreator()
            {
                return new AttendEventStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getAttendEventStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2AttendEventStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getAttendEventStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2AttendEventStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return false;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isAttendEventStepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }
        },
        FORM_A_GROUP
        {
            @Override
            public int getValue()
            {
                return 3;
            }

            @Override
            public FormGroupStepCreator getCreator()
            {
                return new FormGroupStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getFormGroupStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2FormGroupStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getFormGroupStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2FormGroupStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return true;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isFormGroupStepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }
        },
        CREATE_A_RECORD
        {
            @Override
            public int getValue()
            {
                return 4;
            }

            @Override
            public CreateRecordStepCreator getCreator()
            {
                return new CreateRecordStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getFindRecordStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2FindRecordStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getFindRecordStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2FindRecordStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return true;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isFindRecordStepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }
        },
        TRACK_HOURS
        {
            @Override
            public int getValue()
            {
                return 5;
            }

            @Override
            public TrackHoursStepCreator getCreator()
            {
                return new TrackHoursStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getHoursStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2HoursStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getHoursStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2HoursStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return true;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isHoursStepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPStudentExperienceStepWorkflow getExperienceStepWorkflow()
            {
                return new EXPStudentExperienceHourTrackingStepWorkflow();
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }

            @Override
            public boolean isRelatedToSubrecordForm()
            {
                return true;
            }
        },
        STUDENT_EVALUATION
        {
            @Override
            public int getValue()
            {
                return 6;
            }

            @Override
            public StudentEvaluationStepCreator getCreator()
            {
                return new StudentEvaluationStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getStudEvalStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2StudEvalStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getStudEvalStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2StudEvalStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return true;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isStudEvalStepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPStudentExperienceStepWorkflow getExperienceStepWorkflow()
            {
                return new EXPStudentExperienceSubrecordStepWorkflow(
                        EXPRecordAbstract.Type.STUDENT_EVALUATION);
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }

            @Override
            public boolean isRelatedToSubrecordForm()
            {
                return true;
            }
        },
        FACULTY_EVALUATION
        {
            @Override
            public int getValue()
            {
                return 7;
            }

            @Override
            public FacultyEvaluationStepCreator getCreator()
            {
                return new FacultyEvaluationStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getFacEvalStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2FacEvalStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getFacEvalStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2FacEvalStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return true;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isFacEvalStepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPStudentExperienceStepWorkflow getExperienceStepWorkflow()
            {
                return new EXPStudentExperienceSubrecordStepWorkflow(
                        EXPRecordAbstract.Type.FACULTY_EVALUATION);
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }

            @Override
            public boolean isRelatedToSubrecordForm()
            {
                return true;
            }
        },
        INDUSTRY_PARTNER_EVALUATION
        {
            @Override
            public int getValue()
            {
                return 8;
            }

            @Override
            public IndustryPartnerEvaluationStepCreator getCreator()
            {
                return new IndustryPartnerEvaluationStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getEmpEvalStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2EmpEvalStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getEmpEvalStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2EmpEvalStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return true;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isEmpEvalStepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPStudentExperienceStepWorkflow getExperienceStepWorkflow()
            {
                return new EXPStudentExperienceSubrecordStepWorkflow(
                        EXPRecordAbstract.Type.EVALUATION);
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }

            @Override
            public boolean isRelatedToSubrecordForm()
            {
                return true;
            }
        },
        STUDENT_JOURNAL
        {
            @Override
            public int getValue()
            {
                return 9;
            }

            @Override
            public JournalStepCreator getCreator()
            {
                return new JournalStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getJournalStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2JournalStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getJournalStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2JournalStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return true;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isJournalStepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPStudentExperienceStepWorkflow getExperienceStepWorkflow()
            {
                return new EXPStudentExperienceJournalStepWorkflow();
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }

            @Override
            public boolean isRelatedToSubrecordForm()
            {
                return true;
            }
        },
        CUSTOM1
        {
            @Override
            public int getValue()
            {
                return 10;
            }

            @Override
            public CustomStepCreator getCreator()
            {
                return new CustomStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getCustom1StepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2Custom1StepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getCustom1StepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2Custom1StepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return true;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isCustom1StepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPStudentExperienceStepWorkflow getExperienceStepWorkflow()
            {
                return new EXPStudentExperienceSubrecordStepWorkflow(
                        EXPRecordAbstract.Type.CUSTOM1);
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }

            @Override
            public boolean isRelatedToSubrecordForm()
            {
                return true;
            }
        },
        CUSTOM2
        {
            @Override
            public int getValue()
            {
                return 11;
            }

            @Override
            public CustomStepCreator getCreator()
            {
                return new CustomStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getCustom2StepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2Custom2StepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getCustom2StepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2Custom2StepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return true;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isCustom2StepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPStudentExperienceStepWorkflow getExperienceStepWorkflow()
            {
                return new EXPStudentExperienceSubrecordStepWorkflow(
                        EXPRecordAbstract.Type.CUSTOM2);
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }

            @Override
            public boolean isRelatedToSubrecordForm()
            {
                return true;
            }
        },
        CUSTOM3
        {
            @Override
            public int getValue()
            {
                return 12;
            }

            @Override
            public CustomStepCreator getCreator()
            {
                return new CustomStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getCustom3StepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2Custom3StepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getCustom3StepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2Custom3StepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return true;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isCustom3StepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPStudentExperienceStepWorkflow getExperienceStepWorkflow()
            {
                return new EXPStudentExperienceSubrecordStepWorkflow(
                        EXPRecordAbstract.Type.CUSTOM3);
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }

            @Override
            public boolean isRelatedToSubrecordForm()
            {
                return true;
            }
        },
        CUSTOM4
        {
            @Override
            public int getValue()
            {
                return 13;
            }

            @Override
            public CustomStepCreator getCreator()
            {
                return new CustomStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getCustom4StepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2Custom4StepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getCustom4StepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2Custom4StepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return true;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isCustom4StepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPStudentExperienceStepWorkflow getExperienceStepWorkflow()
            {
                return new EXPStudentExperienceSubrecordStepWorkflow(
                        EXPRecordAbstract.Type.CUSTOM4);
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }

            @Override
            public boolean isRelatedToSubrecordForm()
            {
                return true;
            }
        },
        CUSTOM5
        {
            @Override
            public int getValue()
            {
                return 14;
            }

            @Override
            public CustomStepCreator getCreator()
            {
                return new CustomStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getCustom5StepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2Custom5StepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getCustom5StepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2Custom5StepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return true;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isCustom5StepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPStudentExperienceStepWorkflow getExperienceStepWorkflow()
            {
                return new EXPStudentExperienceSubrecordStepWorkflow(
                        EXPRecordAbstract.Type.CUSTOM5);
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }

            @Override
            public boolean isRelatedToSubrecordForm()
            {
                return true;
            }
        },
        CUSTOM6
        {
            @Override
            public int getValue()
            {
                return 15;
            }

            @Override
            public CustomStepCreator getCreator()
            {
                return new CustomStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getCustom6StepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2Custom6StepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getCustom6StepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2Custom6StepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return true;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isCustom6StepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPStudentExperienceStepWorkflow getExperienceStepWorkflow()
            {
                return new EXPStudentExperienceSubrecordStepWorkflow(
                        EXPRecordAbstract.Type.CUSTOM6);
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }

            @Override
            public boolean isRelatedToSubrecordForm()
            {
                return true;
            }
        },
        ECOMMERCE_PURCHASE
        {
            @Override
            public int getValue()
            {
                return 16;
            }

            @Override
            public EcommercePurchaseStepCreator getCreator()
            {
                return new EcommercePurchaseStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getEcommerceStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2EcommerceStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getEcommerceStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2EcommerceStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return false;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isEcommerceStepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateEcommercePurchaseStepPropagator();
            }
        },
        ATTEND_APPOINTMENT
        {
            @Override
            public int getValue()
            {
                return 17;
            }

            @Override
            public AttendAppointmentStepCreator getCreator()
            {
                return new AttendAppointmentStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getAppointmentStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2AppointmentStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getAppointmentStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2AppointmentStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return false;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isAppointmentStepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }
        },
        ADMIN_WAIT
        {
            @Override
            public int getValue()
            {
                return 18;
            }

            @Override
            public AdminWaitStepCreator getCreator()
            {
                return new AdminWaitStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getAdminWaitStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2AdminWaitStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getAdminWaitStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2AdminWaitStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return false;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isAdminWaitStepEnabled();
            }

            @Override
            public EXPStudentExperienceStepBasicCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public EXPTypeWorkflowTemplateStepBasicPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }

            @Override
            public boolean isGateStep()
            {
                return true;
            }
        },
        STUDENT_REFLECTION
        {
            @Override
            public int getValue()
            {
                return 19;
            }

            @Override
            public ReflectionStepCreator getCreator()
            {
                return new ReflectionStepCreator();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getReflectionStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2ReflectionStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getReflectionStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2ReflectionStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return false;
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isReflectionStepEnabled()
                        && module.getReflectionConfigurableSupport()//
                                .getOrSaveNewReflectionConfig(module)//
                                .isAllAnyReflectionEnabled();
            }

            @Override
            public ReflectionExperienceStepCreator getExperienceStepCreator()
            {
                return new ReflectionExperienceStepCreator();
            }

            @Override
            public EXPStudentExperienceReflectionStepWorkflow getExperienceStepWorkflow()
            {
                return new EXPStudentExperienceReflectionStepWorkflow();
            }

            @Override
            public EXPTypeWorkflowTemplateReflectionStepPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateReflectionStepPropagator();
            }
        },
        REQUIRE_SYSTEM_ACTIVITY
        {
            @Override
            public int getValue()
            {
                return 20;
            }

            @Override
            public EXPTermCourseTypeStepCreator getCreator()
            {
                return new RequiredSystemActivityStepCreator();
            }

            @Override
            public EXPStudentExperienceStepCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isRequireSystemActivityEnabled();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getRequireSystemActivityStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2ReflectionStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getRequireSystemActivityStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2RequireSystemActivityInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return false;
            }

            @Override
            public EXPTypeWorkflowTemplateStepPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateSystemActivityStepPropagator();
            }
        },
        QUALIFIERS
        {
            @Override
            public int getValue()
            {
                return 21;
            }

            @Override
            public EXPTermCourseTypeStepCreator getCreator()
            {
                return new QualifiersStepCreator();
            }

            @Override
            public EXPStudentExperienceStepCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isQualifiersStepEnabled();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getQualifiersStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2QualifiersStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getQualifiersStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2QualifiersStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return false;
            }

            @Override
            public EXPTypeWorkflowTemplateStepPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateQualifiersStepPropagator();
            }

            @Override
            public EXPStudentExperienceStepWorkflow getExperienceStepWorkflow()
            {
                return new EXPStudentExperienceQualifiersStepWorkflow();
            }

            @Override
            public boolean isGateStep()
            {
                return true;
            }
        },
        COMPETENCY
        {
            @Override
            public int getValue()
            {
                return 22;
            }

            @Override
            public EXPTermCourseTypeStepCreator getCreator()
            {
                return new CompetencyStepCreator();
            }

            @Override
            public EXPStudentExperienceStepCreator getExperienceStepCreator()
            {
                return new EXPStudentExperienceStepBasicCreator();
            }

            @Override
            public boolean isEnabled(EXPModule module)
            {
                return module.isCompetencyStepEnabled();
            }

            @Override
            public String getLabel(EXPModule module)
            {
                return module.getCompetencyStepLabel();
            }

            @Override
            public String getL2Label(EXPModule module)
            {
                return module.getL2CompetencyStepLabel();
            }

            @Override
            public String getInstructions(EXPModule module)
            {
                return module.getCompetencyStepInstructions();
            }

            @Override
            public String getL2Instructions(EXPModule module)
            {
                return module.getL2CompetencyStepInstructions();
            }

            @Override
            public boolean isSingleton()
            {
                return false;
            }

            @Override
            public EXPTypeWorkflowTemplateStepPropagator getWorkflowTemplateStepPropagator()
            {
                return new EXPTypeWorkflowTemplateStepBasicPropagator();
            }

            @Override
            public EXPStudentExperienceStepWorkflow getExperienceStepWorkflow()
            {
                return (oldStatus, newStatus, step) -> {
                };
            }
        };

        public abstract int getValue();

        public EXPStudentExperienceStepWorkflow getExperienceStepWorkflow()
        {
            return EXPStudentExperienceStepWorkflow.NONE;
        }

        public abstract EXPTermCourseTypeStepCreator getCreator();

        public abstract EXPStudentExperienceStepCreator getExperienceStepCreator();

        public abstract boolean isEnabled(EXPModule module);

        public abstract String getLabel(EXPModule module);

        public abstract String getL2Label(EXPModule module);

        public abstract String getInstructions(EXPModule module);

        public abstract String getL2Instructions(EXPModule module);

        public abstract boolean isSingleton();

        public boolean isTermsAndConditions()
        {
            return this == TERMS_AND_CONDITIONS;
        }

        public boolean isQuestionnaireWithQualifiers()
        {
            return this == QUESTIONNAIRE_WITH_QUALIFIERS;
        }

        public boolean isAttendEvent()
        {
            return this == ATTEND_AN_EVENT;
        }

        public boolean isFormGroup()
        {
            return this == FORM_A_GROUP;
        }

        public boolean isCreateRecord()
        {
            return this == CREATE_A_RECORD;
        }

        public boolean isTrackHours()
        {
            return this == TRACK_HOURS;
        }

        public boolean isStudentEvaluation()
        {
            return this == STUDENT_EVALUATION;
        }

        public boolean isFacultyEvaluation()
        {
            return this == FACULTY_EVALUATION;
        }

        public boolean isIndustryPartnerEvaluation()
        {
            return this == INDUSTRY_PARTNER_EVALUATION;
        }

        public boolean isStudentJournal()
        {
            return this == STUDENT_JOURNAL;
        }

        public boolean isCustom1()
        {
            return this == CUSTOM1;
        }

        public boolean isCustom2()
        {
            return this == CUSTOM2;
        }

        public boolean isCustom3()
        {
            return this == CUSTOM3;
        }

        public boolean isCustom4()
        {
            return this == CUSTOM4;
        }

        public boolean isCustom5()
        {
            return this == CUSTOM5;
        }

        public boolean isCustom6()
        {
            return this == CUSTOM6;
        }

        public boolean isEcommercePurchase()
        {
            return this == ECOMMERCE_PURCHASE;
        }

        public boolean isAttendAppointment()
        {
            return this == ATTEND_APPOINTMENT;
        }

        public boolean isAdminWait()
        {
            return this == ADMIN_WAIT;
        }

        public boolean isReflection()
        {
            return this == STUDENT_REFLECTION;
        }

        public boolean isCompetency()
        {
            return this == COMPETENCY;
        }

        public String getLabel(EXPModule module, Locale locale)
        {
            return com.orbis.utils.StringUtils.getStringForLocale(getLabel(module),
                    getL2Label(module), locale);
        }

        public String getInstructions(EXPModule module, Locale locale)
        {
            return com.orbis.utils.StringUtils.getStringForLocale(
                    getInstructions(module), getL2Instructions(module), locale);
        }

        public abstract EXPTypeWorkflowTemplateStepPropagator getWorkflowTemplateStepPropagator();

        public static Type of(int type)
        {
            for (Type t : Type.values())
            {
                if (t.getValue() == type)
                {
                    return t;
                }
            }
            throw new IllegalArgumentException("Unknown step type");
        }

        public static List<Type> getEnabledTypes(EXPModule module)
        {
            return Arrays.stream(Type.values()).filter(t -> t.isEnabled(module))
                    .collect(Collectors.toList());
        }

        public static List<Type> getCustoms()
        {
            return Arrays.asList(CUSTOM1, CUSTOM2, CUSTOM3, CUSTOM4, CUSTOM5,
                    CUSTOM6);
        }

        public boolean isGateStep()
        {
            return false;
        }

        public boolean isRelatedToSubrecordForm()
        {
            return false;
        }
    }

    @Override
    public int getCompetencyUsecase()
    {
        return competencyUsecase;
    }

    @Override
    public void setCompetencyUsecase(int competencyUsecase)
    {
        this.competencyUsecase = competencyUsecase;
    }

    @Override
    public int getMinimumCompetencies()
    {
        return minimumCompetencies;
    }

    @Override
    public void setMinimumCompetencies(int minimumCompetencies)
    {
        this.minimumCompetencies = minimumCompetencies;
    }

    @Override
    public int getMaximumCompetencies()
    {
        return maximumCompetencies;
    }

    @Override
    public void setMaximumCompetencies(int maximumCompetencies)
    {
        this.maximumCompetencies = maximumCompetencies;
    }

    @Override
    public String getAnticipatedColumnName()
    {
        return "tcts";
    }

    @Override
    public String getQualificationTestField()
    {
        return "termCourseTypeStep";
    }

    @Override
    public void setQualifierEntityInTest(QFQualificationTest qualificationTest)
    {
        qualificationTest.setTermCourseTypeStep(this);
    }

    @Override
    public Class<? extends QFStudentEntity> getRelatedStudentEntity()
    {
        return EXPStudentExperienceStep.class;
    }

    @Override
    public String getBackToButtonLabelI18N()
    {
        return "i18n.exp_stepConfig_appointment.BackToMana2642912009367091";
    }

    @Override
    public List<QFQualifierConfigLevel> getConfigLevels()
    {
        EXPModule module = tct != null ? tct.getType().getModule()
                : template.getType().getModule();
        return List.of(new QFQualifierConfigLevel(EXPModuleQualifierAssigned.class,
                module));
    }

    @Override
    public boolean canUserApproveStudentQualification(UserDetailsImpl user,
            OrbisModule orbisModule)
    {
        EXPModuleUser moduleUser = EXPModuleUser.getInstance(user,
                (EXPModule) orbisModule);
        return QFQualifierEntity.super.canUserApproveStudentQualification(user,
                orbisModule)
                || moduleUser.isExpAdminOrExpCoordinatorOrCourseCoordinator();
    }
}
