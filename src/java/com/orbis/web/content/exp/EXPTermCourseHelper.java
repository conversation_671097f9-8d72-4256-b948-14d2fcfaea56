package com.orbis.web.content.exp;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.web.servlet.ModelAndView;

import com.orbis.acegi.providers.dao.hibernate.PersonGroup;
import com.orbis.acegi.providers.dao.hibernate.PersonGroupHelper;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.df.DFHelper;
import com.orbis.portal.OrbisHqlResultSet;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.CollectionUtils;
import com.orbis.utils.DBUtils;
import com.orbis.utils.FlashMessageUtils;
import com.orbis.utils.JSONUtils;
import com.orbis.utils.JSONUtils.JSONBuilder;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.QueryBuilder;
import com.orbis.utils.RequestUtils;
import com.orbis.utils.StringUtils;
import com.orbis.utils.query.Where;
import com.orbis.utils.query.WhereFragment;
import com.orbis.web.content.acrm.AcrmAcademicTag;
import com.orbis.web.content.acrm.Tag;
import com.orbis.web.content.acrm.TagAssignHelper;
import com.orbis.web.content.acrm.TagCategory;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.ec.infoRequest.InfoRequestFields;
import com.orbis.web.content.exp.EXPStudentExperienceStep.Status;
import com.orbis.web.content.exp.course.EXPCourseLabel;
import com.orbis.web.content.exp.term.EXPTermLabel;
import com.orbis.web.content.exp.termCourse.EXPTermCourseLabel;
import com.orbis.web.content.grid.EXPTermCourseStudentsHql;
import com.orbis.web.content.grid.EXPTermCourseTypeStudentsHql;
import com.orbis.web.content.grid.EXPUnplacedStudentsHQL;
import com.orbis.web.content.np.NHelper;
import com.orbis.web.content.portal.DeletionNodeHelper;

import jakarta.servlet.http.HttpServletRequest;

public class EXPTermCourseHelper
{
    private static final EXPTermCourse defaultInstance = new EXPTermCourse();

    public static EXPTermCourse getDefaultInstance()
    {
        return defaultInstance;
    }

    public static EXPTermCourse getTermCourse(HttpServletRequest request)
    {
        EXPTermCourse tc = null;

        if (request.getAttribute("tcId") != null)
        {
            tc = (EXPTermCourse) PortalUtils.getHt().load(EXPTermCourse.class,
                    (Integer) request.getAttribute("tcId"));
        }
        else if (!StringUtils.isEmpty(request.getParameter("tcId")))
        {
            tc = (EXPTermCourse) PortalUtils.getHt().load(EXPTermCourse.class,
                    Integer.parseInt(request.getParameter("tcId")));
        }
        else if (request.getAttribute("termCourseId") != null)
        {
            tc = (EXPTermCourse) PortalUtils.getHt().load(EXPTermCourse.class,
                    (Integer) request.getAttribute("termCourseId"));
        }
        else if (!StringUtils.isEmpty(request.getParameter("termCourseId")))
        {
            tc = (EXPTermCourse) PortalUtils.getHt().load(EXPTermCourse.class,
                    Integer.parseInt(request.getParameter("termCourseId")));
        }

        return tc;
    }

    public static List<Object[]> getTermCourses(EXPTerm term, Locale locale)
    {
        // oldExperiential
        String nameProperty = LocaleUtils.isL1(locale) ? "name" : "l2Name";
        String codeProperty = LocaleUtils.isL1(locale) ? "code" : "l2Code";
        return PortalUtils.getHt().find("select tc.id, tc.course.id, tc.course."
                + nameProperty + ", tc.course." + codeProperty
                + ", tc.term.id, tc.term." + nameProperty
                + " from EXPTermCourse tc where tc.term.id=? order by tc.course."
                + nameProperty, new Object[] { term.getId() });
    }

    public static List<Object[]> getTermCourses(EXPCourse course, Locale locale)
    {
        // oldExperiential
        String nameProperty = LocaleUtils.isL1(locale) ? "name" : "l2Name";
        return PortalUtils.getHt().find("select tc.id, tc.term.id, tc.term."
                + nameProperty
                + " from EXPTermCourse tc where tc.course.id=? order by tc.term."
                + nameProperty, new Object[] { course.getId() });
    }

    public static EXPTermCourse getTermCourse(EXPTerm term, EXPCourse course)
    {
        EXPTermCourse ret = null;
        List l = PortalUtils.getHt().find(
                "from EXPTermCourse tc where tc.term=? and tc.course=? ",
                new Object[] { term, course });
        if (l.size() > 0)
        {
            ret = (EXPTermCourse) l.get(0);
        }
        return ret;
    }

    public static List getTermCourses(String selectClause, String whereClause,
            EXPModule module, Locale locale)
    {
        String nameProperty = LocaleUtils.isL1(locale) ? "name" : "l2Name";
        List expTypes = PortalUtils.getHt().find(String.format(
                "%s from EXPTermCourse etc where etc.course.module.id=? order by etc.term.%s, etc.course.%s",
                selectClause, whereClause, nameProperty, nameProperty),
                module.getId());
        return expTypes;
    }

    public static String getTermCourseStatusLabel(int status)
    {
        String statusLabel;

        if (status == EXPTermCourse.STATUS_OPEN)
        {
            statusLabel = "Open";
        }
        else if (status == EXPTermCourse.STATUS_CLOSED)
        {
            statusLabel = "Closed";
        }
        else if (status == EXPTermCourse.STATUS_ARCHIVED)
        {
            statusLabel = "Archived";
        }
        else
        {
            statusLabel = "Unknown";
        }

        return statusLabel;
    }

    /**
     * Returns a persisted to the database termCourse for the term and course. Will
     * find from the database or create and save one if one does not already exist.
     * Does not set anything other than term, course, and created by for the new
     * termCourse.
     *
     * @param term
     *            The term for the termCourse
     * @param course
     *            The course for the termCourse
     * @param user
     *            Who to credit creating the term course
     * @return
     */
    public static EXPTermCourse getOrAssociateTermCourse(EXPTerm term,
            EXPCourse course, UserDetailsImpl user)
    {
        EXPTermCourse ret = null;
        ret = (EXPTermCourse) PortalUtils.getHt()
                .find("from EXPTermCourse tc where tc.term=? and tc.course=?",
                        new Object[] { term, course })
                .stream().findFirst().orElseGet(() -> {
                    return associateTC(term, course, user);
                });
        return ret;
    }

    public static void associateTC(HttpServletRequest request)
    {
        // oldExperiential
        EXPTerm term = EXPTermHelper.getTerm(request);
        EXPCourse course = EXPCourseHelper.getCourse(request);
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
        associateTC(term, course, user);
    }

    public static EXPTermCourse associateTC(EXPTerm term, EXPCourse course,
            UserDetailsImpl user)
    {
        EXPTermCourse ret = null;
        try
        {
            DeletionNodeHelper.deleteContentItems(EXPTermCourse.class,
                    "course=" + course.getId() + " and term=" + term.getId(), null);
            ret = new EXPTermCourse();
            ret.setTerm(term);
            ret.setCourse(course);
            ret.setDateCreated(new Date());
            ret.setCreatedBy(user);
            PortalUtils.getHt().save(ret);

            EXPTermCourseAcademicTag termCourseAssignInstance = EXPTermCourseAcademicTag.class
                    .newInstance();
            TagCategory categoryInstance = termCourseAssignInstance
                    .getTagCategoryClass().newInstance();
            AcrmAcademicTag tagInstance = (AcrmAcademicTag) termCourseAssignInstance
                    .getTagClass().newInstance();
            List<Integer> assignedTagIds = TagAssignHelper.getAssignedTagIds(
                    new JSONBuilder()
                            .put("userLoggedInId",
                                    user != null ? user.getId() : null)
                            .put("courseId", course.getId()).build(),
                    categoryInstance, tagInstance,
                    EXPCourseAcademicTag.class.newInstance());
            TagAssignHelper
                    .massApplyTags(
                            new JSONBuilder()
                                    .put("userLoggedInId",
                                            user != null ? user.getId() : null)
                                    .build(),
                            categoryInstance, tagInstance, termCourseAssignInstance,
                            ret, DBUtils.buildInClause(assignedTagIds),
                            Arrays.asList(ret.getId()));
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return ret;
    }

    public static EXPTermCourse cloneTermCourse(EXPTermCourse original,
            EXPTerm term, EXPCourse course)
    {
        EXPTermCourse tcClone = new EXPTermCourse();
        tcClone.setTerm(term);
        tcClone.setCourse(course);
        tcClone.setCreatedBy(original.getCreatedBy());
        tcClone.setAccessMode(original.getAccessMode());
        tcClone.setStatus(original.getStatus());
        tcClone.setDateCreated(new Date());
        tcClone.setProfessor(original.getProfessor());
        tcClone.setHeader(original.getHeader());
        tcClone.setL2Header(original.getL2Header());
        tcClone.setFooter(original.getFooter());
        tcClone.setL2Footer(original.getL2Footer());
        tcClone.setStartDate(original.getStartDate());
        tcClone.setEndDate(original.getEndDate());

        InfoRequestFields fields = new InfoRequestFields(
                original.getInfoRequestFields());
        PortalUtils.getHt().saveOrUpdate(fields);

        tcClone.setInfoRequestFields(fields);

        PortalUtils.getHt().save(tcClone);

        List<EXPTermCourseType> tcts = PortalUtils.getHt().find(
                "FROM EXPTermCourseType tct WHERE tct.termCourse.id=?",
                original.getId());
        for (EXPTermCourseType tct : tcts)
        {
            EXPTermCourseType tctClone = new EXPTermCourseType();
            tctClone.setTermCourse(tcClone);
            tctClone.setType(tct.getType());
            tctClone.setName(tct.getName());
            tctClone.setL2Name(tct.getL2Name());
            tctClone.setWorkflowTemplate(tct.getWorkflowTemplate());
            tctClone.setThirdParty(tct.isThirdParty());

            if (tct.isThirdParty())
            {
                tctClone.setThirdPartyDescription(tct.getThirdPartyDescription());
                tctClone.setThirdPartyLogo(tct.getThirdPartyLogo());
                tctClone.setThirdPartyName(tct.getL2ThirdPartyName());
                tctClone.setL2ThirdPartyDescription(
                        tct.getL2ThirdPartyDescription());
                tctClone.setL2ThirdPartyName(tct.getL2ThirdPartyName());
            }

            PortalUtils.getHt().save(tctClone);

            List<EXPTermCourseTypeStep> tctSteps = PortalUtils.getHt().find(
                    "FROM EXPTermCourseTypeStep tcts WHERE tcts.tct.id=?",
                    tct.getId());
            for (EXPTermCourseTypeStep step : tctSteps)
            {
                EXPTermCourseTypeStep stepClone = new EXPTermCourseTypeStep();
                stepClone.setTct(tctClone);
                stepClone.setInstructions(step.getInstructions());
                stepClone.setL2Instructions(step.getL2Instructions());
                stepClone.setRequiredHoursToComplete(
                        step.getRequiredHoursToComplete());
                stepClone.setRequiredEntriesToComplete(
                        step.getRequiredEntriesToComplete());
                stepClone.setStepOrder(step.getStepOrder());
                stepClone.setStudentCanFormGroup(step.isStudentCanFormGroup());
                stepClone.setTacStepContent(step.getTacStepContent());
                stepClone.setL2TacStepContent(step.getL2TacStepContent());
                stepClone.setType(step.getType());
                stepClone.setLabel(step.getLabel());
                stepClone.setL2Label(step.getL2Label());


                if (step.getType() == EXPTermCourseTypeStep.Type.QUESTIONNAIRE_WITH_QUALIFIERS
                        .getValue())
                {
                    DFHelper.createNewModel(stepClone, step.getDFModel());
                }

                PortalUtils.getHt().saveOrUpdate(stepClone);
            }
        }

        return tcClone;
    }

    public static List getTermCourseTypes(EXPTermCourse termCourse,
            String selectClause)
    {
        return PortalUtils.getHt().find(
                selectClause + " from EXPTermCourseType t where t.termCourse=?",
                termCourse);
    }

    public static QueryBuilder getSearchableTermCoursesQueryBuilder(
            List<Integer> expTypeIds, UserDetailsImpl student, EXPModule module,
            Locale locale)
    {
        QueryBuilder q = new QueryBuilder();
        q.append(" select termCourse.term as termId, course.id as courseId,");
        q.append(" course.").append(LocaleUtils.isL1(locale) ? "name" : "l2Name")
                .append(", course.")
                .append(LocaleUtils.isL1(locale) ? "code" : "l2Code")
                .append(", course.")
                .append(LocaleUtils.isL1(locale) ? "section" : "l2Section")
                .append(", termCourse.id as termCourseId");
        q.append(" from exp_term_course_student student");
        q.append(" inner join exp_term_course termCourse");
        q.append(" on student.termCourse=termCourse.id");
        q.append(" inner join exp_course course on termCourse.course=course.id");
        q.append(" inner join exp_student_profile profile");
        q.append(" on student.studentProfile=profile.id");
        q.append(" inner join exp_term term on term.id = termCourse.term ");
        q.append(
                " inner join exp_term_course_student_status tcss on tcss.id = student.status ");
        q.append(" where profile.owner=?", student.getId());
        q.append(" and tcss.type = ? ",
                EXPTermCourseStudentStatus.Type.ACTIVE.getValue());
        q.append(" and term.active = 1 ");
        q.append(" and course.module=?", module.getId());
        q.append(
                " and exists(select recordStep.id from exp_student_experience_step recordStep");
        q.append(
                " inner join exp_term_course_type_step tcts on recordStep.step=tcts.id");
        q.append(" inner join exp_term_course_type tct on tcts.tct=tct.id");
        q.append(" where recordStep.student=student.id and tcts.type=4");
        if (!expTypeIds.isEmpty())
        {
            q.append(" and tct.type in " + DBUtils.buildInClause(expTypeIds));
        }
        q.append(
                " and not exists (select r.id from exp_record r where r.studentStep=recordStep.id)");
        q.append(
                " and not exists(select tacStep.id from exp_student_experience_step tacStep");
        q.append(
                " inner join exp_term_course_type_step tcts2 on tacStep.step=tcts2.id");
        q.append(" where tacStep.student=recordStep.student and tcts2.tct=tct.id");
        q.append(" and tcts2.type=0 and tacStep.status!="
                + EXPStudentExperienceStep.Status.FINISHED.getValue()
                + " and tcts2.stepOrder < tcts.stepOrder))");
        return q;
    }

    public static int getAssociatedTypesCount(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        return PortalUtils.getHt().findInt(new QueryBuilder(
                "select count(*) from EXPTermCourseType tct where tct.termCourse=?",
                termCourse)
                .appendIf(
                        moduleUser.getUser() != null
                                && moduleUser.isExpCoordinator(),
                        " and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=tct.type and etc.coordinator=?)",
                        moduleUser.getUser()));
    }

    public static int countInactiveStudents(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(" select count(*) ");
            hql.append(" from EXPTermCourseStudent s ");
            hql.append(" where s.termCourse=? ", termCourse);
            hql.append(" and s.status.type=? ",
                    EXPTermCourseStudentStatus.Type.INACTIVE.getValue());
            hql.appendIf(moduleUser.isExpCoordinator(),
                    " and exists(select tct.id from EXPTermCourseType tct where tct.termCourse=s.termCourse and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=tct.type and etc.coordinator=?))",
                    moduleUser.getUser());
            if (moduleUser.isExpCourseCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPCourseCoordinatorHqlClause("s.termCourse",
                        moduleUser.getUser()));
            }
        }
        return PortalUtils.getHt().findInt(hql);
    }

    public static int countApprovedStudents(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(" select count(*) ");
            hql.append("from EXPTermCourseStudent s ");
            hql.append(" join s.student.owner u ");
            hql.append(" where s.termCourse=? ", termCourse);
            hql.append(" and s.status.type=?",
                    EXPTermCourseStudentStatus.Type.ACTIVE.getValue());
            hql.append(" and ");
            hql.append(UserDetailsHelper.getActiveUserWhereFragment("u"));

            if (moduleUser.isExpCourseCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPCourseCoordinatorHqlClause("s.termCourse",
                        moduleUser.getUser()));
            }
            hql.appendIf(moduleUser.isExpCoordinator(),
                    " and exists(select tct.id from EXPTermCourseType tct where tct.termCourse=s.termCourse and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=tct.type and etc.coordinator=?))",
                    moduleUser.getUser());
        }
        return PortalUtils.getHt().findInt(hql);
    }

    public static int countPendingStudents(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append("select count(*) ");
            hql.append("from EXPTermCourseStudent s ");
            hql.append(" join s.student.owner u ");
            hql.append(" join s.termCourse tc ");
            hql.append("where s.termCourse=? ", termCourse);
            hql.append("and s.status.type=?",
                    EXPTermCourseStudentStatus.Type.PENDING.getValue());
            hql.append(" and ");
            hql.append(UserDetailsHelper.getActiveUserWhereFragment("u"));
            if (moduleUser.isExpCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPTermCourseCoordinatorHqlClause("tc",
                        moduleUser.getUser()));
            }

            if (moduleUser.isExpCourseCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPCourseCoordinatorHqlClause("s.termCourse",
                        moduleUser.getUser()));
            }
        }
        return PortalUtils.getHt().findInt(hql);
    }

    public static int countTotalStudents(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(" select count(*) ");
            hql.append(" from EXPTermCourseStudent s ");
            hql.append("   join s.student.owner u ");
            hql.append(" where s.termCourse=?", termCourse);
            hql.append("   and ");
            hql.append(UserDetailsHelper.getActiveUserWhereFragment("u"));
            if (moduleUser.isExpCourseCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPCourseCoordinatorHqlClause("s.termCourse",
                        moduleUser.getUser()));
            }
        }
        return PortalUtils.getHt().findInt(hql.appendIf(
                moduleUser.isExpCoordinator(),
                " and exists(select tct.id from EXPTermCourseType tct where tct.termCourse=s.termCourse and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=tct.type and etc.coordinator=?))",
                moduleUser.getUser()));
    }

    public static int countCompletedStudents(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(" select count(*) ");
            hql.append(" from EXPTermCourseStudent s ");
            hql.append("   join s.student.owner u ");
            hql.append(" where s.termCourse = ? ", termCourse);
            hql.append("   and s.status.type = ? ",
                    EXPTermCourseStudentStatus.Type.COMPLETED.getValue());
            hql.append("   and ");
            hql.append(UserDetailsHelper.getActiveUserWhereFragment("u"));
            if (moduleUser.isExpCourseCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPCourseCoordinatorHqlClause("s.termCourse",
                        moduleUser.getUser()));
            }
        }
        return PortalUtils.getHt().findInt(hql.appendIf(
                moduleUser.isExpCoordinator(),
                " and exists(select tct.id from EXPTermCourseType tct where tct.termCourse=s.termCourse and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=tct.type and etc.coordinator=?))",
                moduleUser.getUser()));
    }

    public static int countTermCourseTypes(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(" select count(*) from EXPTermCourseType t ");
            hql.append(" where t.termCourse=?", termCourse);
            hql.appendIf(moduleUser.isExpCoordinator(),
                    " and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=t.type and etc.coordinator=?)",
                    moduleUser.getUser());
            if (moduleUser.isExpCourseCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPCourseCoordinatorHqlClause("t.termCourse",
                        moduleUser.getUser()));
            }
        }
        return PortalUtils.getHt().findInt(hql);
    }

    public static String getTermCourseTypeNameString(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(" select t.type.name from EXPTermCourseType t ");
            hql.append(" where t.termCourse=?", termCourse);
            hql.appendIf(moduleUser.isExpCoordinator(),
                    " and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=t.type and etc.coordinator=?)",
                    moduleUser.getUser());
            if (moduleUser.isExpCourseCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPCourseCoordinatorHqlClause("t.termCourse",
                        moduleUser.getUser()));
            }
        }
        List<String> names = PortalUtils.getHt().find(hql);
        StringBuilder expTypeNames = new StringBuilder();
        boolean first = true;
        for (String n : names)
        {
            if (!first)
            {
                expTypeNames.append(",");
            }
            expTypeNames.append(n.toString());
            first = false;
        }

        return expTypeNames.toString();
    }

    public static int countIndustryPartners(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(
                    " select count(distinct rip.partner.id) from EXPRecordIndustryPartner rip ");
            hql.append(" where rip.record.studentStep.student.termCourse=?",
                    termCourse);
            hql.appendIf(moduleUser.isExpCoordinator(),
                    " and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=rip.record.studentStep.step.tct.type and etc.coordinator=?)",
                    moduleUser.getUser());
            if (moduleUser.isExpCourseCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPCourseCoordinatorHqlClause(
                        "rip.record.studentStep.student.termCourse",
                        moduleUser.getUser()));
            }
        }
        return PortalUtils.getHt().findInt(hql);
    }

    public static List<JSONObject> getTop3IndustryPartners(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        List<Object[]> partners = PortalUtils.getHt()
                .find("select rip.partner, count(distinct rip.record.studentStep.student.id) as partnerCount " +
                        "from EXPRecordIndustryPartner rip " +
                        "where rip.record.studentStep.student.termCourse=? " +
                        "group by rip.partner " +
                        "order by partnerCount desc", termCourse);

        List<JSONObject> partnersJson = new ArrayList<>();
        for (Object[] row : partners)
        {
            JSONObject entry = new JSONObject();
            try
            {
                entry.put("id", ((EXPIndustryPartner) row[0]).getId());
                entry.put("name", ((EXPIndustryPartner) row[0]).getDisplayName());
                entry.put("isOrg", ((EXPIndustryPartner) row[0]).isOrgIp());
                entry.put("count", row[1]);
                partnersJson.add(entry);
            }
            catch (JSONException e)
            {
                e.printStackTrace();
            }
        }

        return partnersJson;
    }

    public static int countRecords(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(" select count(*) from EXPRecord r ");
            hql.append("where r.studentStep.student.termCourse=?", termCourse);
            hql.appendIf(moduleUser.isExpCoordinator(),
                    " and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=r.studentStep.step.tct.type and etc.coordinator=?)",
                    moduleUser.getUser());
            if (moduleUser.isExpCourseCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPCourseCoordinatorHqlClause(
                        "r.studentStep.studentExperience.student.termCourse",
                        moduleUser.getUser()));
            }
        }
        return PortalUtils.getHt().findInt(hql);
    }

    public static int countExperienceTypes(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(" select count(*) from EXPTermCourseType tct ");
            hql.append(" where tct.termCourse=?", termCourse);
            hql.appendIf(moduleUser.isExpCoordinator(),
                    " and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=tct.type and etc.coordinator=?)",
                    moduleUser.getUser());
            if (moduleUser.isExpCourseCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPCourseCoordinatorHqlClause("tct.termCourse",
                        moduleUser.getUser()));
            }

        }
        return PortalUtils.getHt().findInt(hql);
    }

    public static double countTotalTrackedHours(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(" SELECT SUM (r.totalTrackedHours) ");
            hql.append(" FROM EXPRecord r ");
            hql.append("   JOIN r.studentStep.studentExperience.student tcs ");
            hql.append("   JOIN tcs.status tcss ");
            hql.append("   JOIN r.owner u ");
            hql.append(
                    " WHERE r.studentStep.studentExperience.student.termCourse = ?",
                    termCourse);
            hql.append("   AND ");
            hql.append(EXPTermCourseStudentHelper
                    .getApprovedEnrollmentsWhereFragment("tcss", "u"));
            if (moduleUser.isExpCoordinator())
            {
                hql.append(
                        " and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=r.studentStep.step.tct.type and etc.coordinator=?)",
                        moduleUser.getUser());
            }

            if (moduleUser.isExpCourseCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPCourseCoordinatorHqlClause(
                        "r.studentStep.studentExperience.student.termCourse",
                        moduleUser.getUser()));
            }
        }
        return PortalUtils.getHt().findDouble(hql);
    }

    public static Map<String, Integer> getStudentStepProgressStats(
            EXPTermCourse termCourse, EXPModuleUser moduleUser, int stepType,
            String stepName)
    {
        Map<String, Integer> stats = new HashMap<>();
        int completed = countTermCourseStudentExperienceStepProgress(termCourse,
                moduleUser, stepType, EXPStudentExperienceStep.Status.FINISHED,
                true);
        stats.put(stepName + "Complete", completed);
        int total = countTermCourseStudentExperienceStepProgress(termCourse,
                moduleUser, stepType, EXPStudentExperienceStep.Status.DECLINED,
                false);
        stats.put(stepName + "Total", total);
        int percentage = EXPControllerDisplayHome.percentage(
                stats.get(stepName + "Complete"), stats.get(stepName + "Total"));
        stats.put(stepName + "Percent", percentage);
        stats.put(stepName + "Type", stepType);
        return stats;
    }

    public static int countTermCourseStudentExperienceStepProgress(
            EXPTermCourse termCourse, EXPModuleUser moduleUser, int stepType,
            EXPStudentExperienceStep.Status stepStatus, boolean finished)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(" SELECT COUNT (*) ");
            hql.append(" FROM EXPStudentExperienceStep step ");
            hql.append("   JOIN step.step tcts ");
            hql.append("   JOIN tcts.tct tct ");
            hql.append("   JOIN step.studentExperience.student.status status ");
            hql.append("   JOIN step.owner u ");
            hql.append(" WHERE tct.termCourse=? ", termCourse);
            hql.append("   AND tcts.type=? ", stepType);
            hql.append("   AND ").append(EXPTermCourseStudentHelper
                    .getApprovedEnrollmentsWhereFragment("status", "u"));

            if (finished)
            {
                hql.append(" AND (");
                hql.append("   step.status = ?", stepStatus.getValue());
                if (stepStatus == EXPStudentExperienceStep.Status.FINISHED)
                {
                    hql.append(" OR status.type = ? ",
                            EXPTermCourseStudentStatus.Type.COMPLETED.getValue());
                }
                hql.append(" ) ");
            }
            else
            {
                hql.append(" AND step.status <> ?", stepStatus.getValue());
            }

            hql.append(" AND ").append(
                    EXPHelper.getExperienceTypeCoordinatorHqlWhereFragmentForTct(
                            "tct", moduleUser));

            if (moduleUser.isExpFacultyMember())
            {
                hql.append(" and ").append(
                        EXPHelper.getTermCourseFacultyMemberHqlWhereFragment("tct",
                                moduleUser));

            }
        }
        return PortalUtils.getHt().findInt(hql);
    }

    /**
     * @see exp/exp_termCourseActions.jsp
     */
    public static Map<String, Object> populateTermCourseActions(
            EXPTermCourse termCourse, EXPModuleUser moduleUser)
    {
        Map<String, Object> ret = new HashMap<>();

        ret.put("termCourse", termCourse);

        ret.put("pendingStudents", PortalUtils.getHt().findInt(new QueryBuilder(
                "select count(e.id) from EXPTermCourseStudent e where e.termCourse=? and e.status.type=?",
                termCourse, EXPTermCourseStudentStatus.Type.PENDING.getValue())
                .appendIf(moduleUser.isExpCoordinator(),
                        " and exists(select tct.id from EXPTermCourseType tct where tct.termCourse=e.termCourse and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=tct.type and etc.coordinator=?))",
                        moduleUser.getUser())));

        ret.put("totalStudents", PortalUtils.getHt().findInt(new QueryBuilder(
                "select count(e.id) from EXPTermCourseStudent e where e.termCourse=?",
                termCourse).appendIf(moduleUser.isExpCoordinator(),
                        " and exists(select tct.id from EXPTermCourseType tct where tct.termCourse=e.termCourse and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=tct.type and etc.coordinator=?))",
                        moduleUser.getUser())));

        ret.put("questionnaireCount", PortalUtils.getHt().findInt(new QueryBuilder(
                "select count(tcts.id) from EXPTermCourseTypeStep tcts where tcts.tct.termCourse=? and tcts.type=?",
                termCourse,
                EXPTermCourseTypeStep.Type.QUESTIONNAIRE_WITH_QUALIFIERS.getValue())
                .appendIf(moduleUser.isExpCoordinator(),
                        " and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=tcts.tct.type and etc.coordinator=?)",
                        moduleUser.getUser())));

        QueryBuilder q = new QueryBuilder();
        q.append(" select count(tct.id)");
        q.append(" from EXPTermCourseType tct");
        q.append(" join tct.type t");
        q.append(" where tct.termCourse=?", termCourse);
        q.append(" and t.enablePostings=true");

        ret.put("postingsEnabledForTermCourse",
                termCourse.getCourse().getModule().isEnablePostings()
                        && PortalUtils.getHt().findInt(q) > 0);

        return ret;
    }

    /**
     * @see exp/exp_termCourseHeader.jsp
     */
    public static Map<String, Object> populateTermCourseHeader(
            EXPTermCourse termCourse, HttpServletRequest request)
    {
        Map<String, Object> ret = new HashMap<>();
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);

        int courseCoordinatorCount = getCourseCoordinatorCount(termCourse);
        ret.put("courseCoordinatorCount", courseCoordinatorCount);

        if (courseCoordinatorCount == 1)
        {
            UserDetailsImpl courseCoordinator = PortalUtils.getHt()
                    .getFirst("select tcc.user " //
                            + " from EXPTermCourseCoordinator tcc" //
                            + " where tcc.termCourse.id=?", termCourse.getId());
            ret.put("courseCoordinator", courseCoordinator);
            ret.put("courseCoordinatorImageUrl", UserDetailsHelper
                    .getProfileImageOrDefaultIfEnabled(courseCoordinator, request));
            ret.put("acrmSE", NHelper.getAcrmCareerSiteElement(
                    user));
        }

        ret.put("studentsCount", getStudentsCount(termCourse, user));
        List<EXPTermCourseFacultyAdvisor> facultyMembers = getFacultyMembers(
                termCourse);

        ret.put("facultyCount", facultyMembers.size());
        if (facultyMembers.size() == 1)
        {
            ret.put("facultyMember", facultyMembers.get(0));
            ret.put("facultyMemberImageUrl",
                    UserDetailsHelper.getProfileImageOrDefaultIfEnabled(
                            facultyMembers.get(0).getFacultyAdvisor(), request));
        }
        return ret;
    }

    public static List<EXPTermCourseStudent> getStudents(EXPTermCourse termCourse)
    {
        return PortalUtils.getHt().find(
                " FROM EXPTermCourseStudent tcs WHERE tcs.termCourse = ? ",
                termCourse);
    }

    public static Map<Integer, Integer> getDeclinedStudentsByTermCourseType(
            EXPTermCourse termCourse, EXPModuleUser moduleUser)
    {
        QueryBuilder q = new QueryBuilder();
        q.append("SELECT tct.id, COUNT(DISTINCT tcs.id)");
        q.append(" FROM EXPStudentExperienceStep s");
        q.append(" JOIN s.step tcts");
        q.append(" JOIN tcts.tct tct");
        q.append(" JOIN s.student tcs");
        q.append(" JOIN tcs.status tcss");
        q.append(" JOIN s.owner u");
        q.append(" WHERE tct.termCourse = ?", termCourse);
        q.append(" AND ").append(
                EXPHelper.getExperienceTypeCoordinatorHqlWhereFragmentForTct("tct",
                        moduleUser));
        q.append(" AND ").append(getExperienceStepStatusWhereFragment("s", "tcts",
                "tcss", "u", Status.DECLINED));
        q.append(" GROUP BY tct.id");

        return CollectionUtils.mapifyOneToOne(q.getStr(), q.getParams());
    }

    public static Map<Integer, Integer> getApprovedStudentsByTermCourseType(
            EXPTermCourse termCourse, EXPModuleUser moduleUser)
    {
        QueryBuilder q = new QueryBuilder();
        q.append("SELECT tct.id, COUNT(DISTINCT tcs.id)");
        q.append(" FROM EXPStudentExperienceStep s");
        q.append(" JOIN s.step tcts");
        q.append(" JOIN tcts.tct tct");
        q.append(" JOIN s.student tcs");
        q.append(" JOIN tcs.status tcss");
        q.append(" JOIN s.owner u");
        q.append(" WHERE tct.termCourse = ?", termCourse);
        q.append(" AND ").append(
                EXPHelper.getExperienceTypeCoordinatorHqlWhereFragmentForTct("tct",
                        moduleUser));
        q.append(" AND ").append(getExperienceStepStatusWhereFragment("s", "tcts",
                "tcss", "u", Status.APPROVED));
        q.append(" GROUP BY tct.id");

        return CollectionUtils.mapifyOneToOne(q.getStr(), q.getParams());
    }

    public static Map<Integer, Integer> getFinishedStudentsByTermCourseType(
            EXPTermCourse termCourse, EXPModuleUser moduleUser)
    {
        QueryBuilder q = new QueryBuilder();
        q.append("SELECT tct.id, COUNT(DISTINCT tcs.id)");
        q.append(" FROM EXPStudentExperienceStep s");
        q.append(" JOIN s.step tcts");
        q.append(" JOIN tcts.tct tct");
        q.append(" JOIN s.student tcs");
        q.append(" JOIN tcs.status tcss");
        q.append(" JOIN s.owner u");
        q.append(" WHERE tct.termCourse = ?", termCourse);
        q.append(" AND ").append(
                EXPHelper.getExperienceTypeCoordinatorHqlWhereFragmentForTct("tct",
                        moduleUser));
        q.append(" AND ").append(EXPTermCourseTypeStudentsHql
                .getFinishedWhereFragment("s", "tcts", "tcss", "u"));
        q.append(" GROUP BY tct.id");

        return CollectionUtils.mapifyOneToOne(q.getStr(), q.getParams());
    }

    public static Map<Integer, Integer> getStartedStudentsByTermCourseType(
            EXPTermCourse termCourse, EXPModuleUser moduleUser)
    {
        QueryBuilder q = new QueryBuilder();
        q.append("SELECT tct.id, COUNT(DISTINCT tcs.id)");
        q.append(" FROM EXPStudentExperienceStep s");
        q.append(" JOIN s.step tcts");
        q.append(" JOIN tcts.tct tct");
        q.append(" JOIN s.student tcs");
        q.append(" JOIN tcs.status tcss");
        q.append(" JOIN s.owner u");
        q.append(" WHERE tct.termCourse = ?", termCourse);
        q.append(" AND ").append(
                EXPHelper.getExperienceTypeCoordinatorHqlWhereFragmentForTct("tct",
                        moduleUser));
        q.append(" AND ").append(EXPTermCourseTypeStudentsHql
                .getStartedWhereFragment("s", "tcts", "tcss", "u"));
        q.append(" GROUP BY tct.id");

        return CollectionUtils.mapifyOneToOne(q.getStr(), q.getParams());
    }

    public static Map<Integer, Integer> getPendingStudentsByTermCourseType(
            EXPTermCourse termCourse, EXPModuleUser moduleUser)
    {
        QueryBuilder q = new QueryBuilder();
        q.append("SELECT tct.id, COUNT(DISTINCT tcs.id)");
        q.append(" FROM EXPStudentExperienceStep s");
        q.append(" JOIN s.step tcts");
        q.append(" JOIN tcts.tct tct");
        q.append(" JOIN s.student tcs");
        q.append(" JOIN tcs.status tcss");
        q.append(" JOIN s.owner u");
        q.append(" WHERE tct.termCourse = ?", termCourse);
        q.append(" AND ").append(
                EXPHelper.getExperienceTypeCoordinatorHqlWhereFragmentForTct("tct",
                        moduleUser));
        q.append(" AND ").append(EXPTermCourseTypeStudentsHql
                .getPendingWhereFragment("s", "tcts", "tcss", "u"));
        q.append(" GROUP BY tct.id");

        return CollectionUtils.mapifyOneToOne(q.getStr(), q.getParams());
    }

    public static WhereFragment getExperienceStepStatusWhereFragment(
            String studentStepAlias, String termCourseTypeStepAlias,
            String termCourseStudentStatusAlias, String userAlias, Status status)
    {
        Where ret = new Where();

        ret.add(EXPTermCourseTypeStudentsHql.getCommonWhereFragment(
                termCourseTypeStepAlias, termCourseStudentStatusAlias, userAlias));

        ret.add("exists (SELECT ses.id FROM EXPStudentExperienceStep ses"
                + " WHERE ses.student = " + studentStepAlias
                + ".student AND ses.step.tct = " + termCourseTypeStepAlias + ".tct"
                + " AND ses.status = ?)", status.getValue());

        return ret.join();
    }

    public static WhereFragment getActiveTermCourseWhereFragment(
            String termCourseAlias, String termAlias)
    {
        Where where = new Where();

        where.add(termCourseAlias + ".status=?", EXPTermCourse.STATUS_OPEN);

        where.add(termAlias + ".active=true");

        return where.join();
    }

    public static WhereFragment getActiveTermCourseSqlWhereFragment(
            String termCourseAlias, String termAlias)
    {
        Where where = new Where();

        where.add(termCourseAlias + ".status=?", EXPTermCourse.STATUS_OPEN);

        where.add(termAlias + ".active=1");

        return where.join();
    }

    public static EXPTermCourseLabel getTermCourseLabel(String sectionAlias,
            String courseNameAlias, String courseCodeAlias,
            String academicCourseAlias, String termNameAlias, OrbisHqlResultSet tc)
    {
        EXPCourseLabel courseLabel = EXPCourseHelper.getCourseLabel(courseNameAlias,
                courseCodeAlias, sectionAlias, academicCourseAlias, tc);

        EXPTermLabel termLabel = EXPTermLabel.builder()
                .name(tc.select(termNameAlias)).build();

        return EXPTermCourseLabel.builder()//
                .termLabel(termLabel)//
                .courseLabel(courseLabel)//
                .build();
    }

    public static void setTermCourseNames(List<OrbisHqlResultSet> termCourses,
            Locale locale)
    {
        String sectionAlias, courseNameAlias, courseCodeAlias, termNameAlias,
                academicCourseAlias = "course.academicCourse";

        if (LocaleUtils.isL1(locale))
        {
            sectionAlias = "course.section";
            courseNameAlias = "course.name";
            courseCodeAlias = "course.code";
            termNameAlias = "term.name";
        }
        else
        {
            sectionAlias = "course.l2Section";
            courseNameAlias = "course.l2Name";
            courseCodeAlias = "course.l2Code";
            termNameAlias = "term.l2Name";
        }

        for (OrbisHqlResultSet tc : termCourses)
        {
            tc.selectPut("termCourseName", EXPTermCourseHelper
                    .getTermCourseLabel(sectionAlias, courseNameAlias,
                            courseCodeAlias, academicCourseAlias, termNameAlias, tc)
                    .getLabel());
        }
    }

    /**
     * Use this method when editing an existing term course
     *
     * @see exp/exp_termCourseAccessModeEdit.jsp
     */
    public static Map<String, Object> populateTermCourseAccessModeEdit(
            EXPTermCourse termCourse, Locale locale)
    {
        return populateTermCourseAccessModeEdit(termCourse,
                termCourse.getCourse().isProgram(), locale);
    }

    /**
     * Use this method when creating a new term course
     *
     * @see exp/exp_termCourseAccessModeEdit.jsp
     */
    public static Map<String, Object> populateTermCourseAccessModeEdit(
            boolean isProgram, Locale locale)
    {
        return populateTermCourseAccessModeEdit(null, isProgram, locale);
    }

    /**
     * @see exp/exp_termCourseAccessModeEdit.jsp
     */
    private static Map<String, Object> populateTermCourseAccessModeEdit(
            EXPTermCourse termCourse, boolean isProgram, Locale locale)
    {
        List<Integer> selectedPermissions = termCourse != null
                ? PortalUtils.getHt().find(" select etcp.permission.id" //
                        + " from EXPTermCoursePermission etcp" //
                        + " where etcp.termCourse=?", termCourse)
                : new ArrayList<>();

        Map<PersonGroup, Boolean> permissionMap = new LinkedHashMap<>();

        for (PersonGroup pg : PersonGroupHelper.getSecondaryGroups())
        {
            permissionMap.put(pg, selectedPermissions.contains(pg.getId()));
        }

        List<String> tagTypes = new ArrayList<>();
        tagTypes.add("com.orbis.web.content.acrm.AcrmAcademicTag");
        tagTypes.add("com.orbis.web.content.acrm.AcrmCampusTag");
        tagTypes.add("com.orbis.web.content.acrm.AcrmComplianceTag");
        tagTypes.add("com.orbis.web.content.acrm.AcrmDemographicTag");
        tagTypes.add("com.orbis.web.content.acrm.AcrmIndustryTag");
        tagTypes.add("com.orbis.web.content.acrm.AcrmTag");

        String tagNameProperty = LocaleUtils.isL1(locale) ? "l1Name" : "l2Name";
        List<Map<String, Object>> tagOptions = PortalUtils.getJt().queryForList( //
                String.format(" select t.id, tc.%s tcName, t.%s name",
                        tagNameProperty, tagNameProperty) //
                        + " from tag_tag t join tag_category tc on tc.id=t.category" //
                        + " where t.tagType in "
                        + DBUtils.buildInClauseWithQuotes(tagTypes));

        Map<String, Object> ret = new HashMap<>();

        ret.put("isProgram", isProgram);

        ret.put("tagOptions", tagOptions);

        ret.put("permissionMap", permissionMap);

        ret.putAll(populateTermCourseWhitelistEdit(termCourse));

        return ret;
    }

    /**
     * @see exp/exp_termCourseAccessModeEdit.jsp
     */
    public static void processTermCourseAccessModeEdit(EXPTermCourse termCourse,
            HttpServletRequest request)
    {
        int accessMode = RequestUtils.getIntParameter(request, "accessMode",
                EXPTermCourse.ACCESS_MODE_OPEN);

        termCourse.setAccessMode(accessMode);
        PortalUtils.getHt().saveOrUpdate(termCourse);

        if (accessMode == EXPTermCourse.ACCESS_MODE_PERMISSION_BASED)
        {
            EXPHelper.saveTermCoursePermissions(termCourse,
                    request.getParameterValues("termCoursePermission"));

            processTermCourseWhitelistEdit(termCourse, request);
        }
        else if (accessMode == EXPTermCourse.ACCESS_MODE_WHITE_LIST)
        {
            processTermCourseWhitelistEdit(termCourse, request);
        }
        else if (accessMode == EXPTermCourse.ACCESS_MODE_TAG_BASED)
        {
            Integer tagId = RequestUtils.getInteger(request, "accessTagId");

            Tag tag = tagId != null
                    ? (Tag) PortalUtils.getHt().load(Tag.class, tagId)
                    : null;

            termCourse.setAccessTag(tag);
            PortalUtils.getHt().saveOrUpdate(termCourse);
        }
    }

    /**
     * @see exp/exp_termCourseWhitelistEdit.jsp
     */
    private static Map<String, Object> populateTermCourseWhitelistEdit(
            EXPTermCourse termCourse)
    {
        JSONObject whitelistedUsers = new JSONObject();

        if (termCourse != null)
        {
            QueryBuilder q = new QueryBuilder();
            q.append(" select u.username,");
            q.append("   concat(");
            q.append("     u.preferredFirstName, ' ', u.lastName, ' (', u.username, ')'");
            q.append("   ) as name");
            q.append(" from exp_term_course_student_white_list wl");
            q.append(" join exp_student_profile p on p.id=wl.student");
            q.append(" join user_details u on u.USER_DETAILS_ID=p.owner");
            q.append(" where wl.termCourse=?", termCourse.getId());
            q.append(" order by name");

            whitelistedUsers = PortalUtils.getJt().query(q, rse -> {
                JSONBuilder builder = JSONUtils.builder();

                while (rse.next())
                {
                    JSONObject value = JSONUtils.newJSONObject("name",
                            rse.getString("name"), "fromLookup", true);

                    builder.put(rse.getString("username"), value);
                }

                return builder.build();
            });
        }

        Map<String, Object> ret = new HashMap<>();

        ret.put("whitelistedUsers", whitelistedUsers);

        return ret;
    }

    /**
     * @see exp/exp_termCourseWhitelistEdit.jsp
     */
    private static void processTermCourseWhitelistEdit(EXPTermCourse termCourse,
            HttpServletRequest request)
    {
        String[] usernames = JSONUtils
                .toStringArray(request.getParameter("usernames"));

        if (usernames == null)
        {
            usernames = new String[0];
        }

        new EXPTermCourseWhitelistUpdateThread(usernames, termCourse,
                PortalUtils.getUserLoggedIn(request)).start();

        FlashMessageUtils.success(request,
                "i18n.EXPTermCourseHelper.MassUpdate7645781522008620");
    }

    /**
     * @see exp/exp_setMaxRegistrations.jsp
     */
    public static Map<String, Integer> populateTermCourseSetMaxRegistrations(
            EXPTermCourse termCourse)
    {
        Integer maxRegistrations = termCourse == null ? 0
                : termCourse.getMaxRegistrations();

        Map<String, Integer> ret = Map.of("maxRegistrations", maxRegistrations);

        return ret;
    }

    /**
     * @see exp/exp_setMaxRegistrations.jsp
     */
    public static void processTermCourseSetMaxRegistrations(
            EXPTermCourse termCourse, HttpServletRequest request)
    {
        int maxRegistrations = RequestUtils.getIntParameter(request,
                "maxRegistrations", 0);

        termCourse.setMaxRegistrations(maxRegistrations);
    }

    public static void addFacultyMemberToTermCourse(UserDetailsImpl facultyMember,
            EXPTermCourse termCourse)
    {
        PortalUtils.getJt().execute(
                "merge into exp_term_course_faculty as TARGET using (values ("
                        + facultyMember.getId() + ", " + termCourse.getId()
                        + ")) as SOURCE (facultyAdvisor, termCourse) on TARGET.facultyAdvisor = SOURCE.facultyAdvisor and TARGET.termCourse = SOURCE.termCourse when not matched then insert (facultyAdvisor, termCourse) values (SOURCE.facultyAdvisor, SOURCE.termCourse);");
    }

    public static void logStatusChange(EXPTermCourse termCourse)
    {
        // int status = termCourse.getStatus();
        // switch (status)
        // {
        // case EXPTermCourse.STATUS_ARCHIVED:
        // PortalUtils.getHt().save(
        // new EXPTermCourseArchivedLogFactory().create(termCourse));
        // break;
        // case EXPTermCourse.STATUS_OPEN:
        // PortalUtils.getHt()
        // .save(new EXPTermCourseOpenLogFactory().create(termCourse));
        // break;
        // case EXPTermCourse.STATUS_CLOSED:
        // PortalUtils.getHt().save(
        // new EXPTermCourseClosedLogFactory().create(termCourse));
        // break;
        // default:
        // break;
        // }
    }

    public static List<EXPRecord> getVisibleRecords(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        QueryBuilder q = new QueryBuilder();
        {
            q.append(" SELECT r ");
            q.append(" FROM EXPRecord r ");
            q.append(
                    "   JOIN r.studentStep.studentExperience.student.termCourse tc ");
            q.append("   JOIN r.studentStep.step.tct.type expt ");
            q.append(" WHERE ");
            q.append("   tc.id = ? ", termCourse.getId());
            if (moduleUser.isExpCoordinator())
            {
                q.append("   AND ");
                q.append(new EXPTypeCoordinatorHqlClause("expt",
                        moduleUser.getUser().getId().toString()));
            }
        }
        return PortalUtils.getHt().find(q);
    }

    public static int getRecordStatusStatForInactiveStudents(
            EXPTermCourse termCourse, Integer recordStatus,
            EXPModuleUser moduleUser)
    {

        QueryBuilder qb = new QueryBuilder();
        qb.append("select count (r.studentStep.student) from");
        qb.append(
                " EXPRecord r join r.studentStep ss join ss.student tcs join ss.step.tct tct");

        Where where = new Where();
        where.add("tcs.termCourse=?", termCourse);
        where.add("r.status=?", recordStatus);
        where.add("tcs.status.type=?",
                EXPTermCourseStudentStatus.Type.INACTIVE.getValue());
        if (moduleUser.isExpCoordinator())
        {
            where.add(EXPHelper.getExperienceTypeCoordinatorHqlWhereFragmentForTct(
                    "tct", moduleUser));
        }
        else if (moduleUser.isExpFacultyMember())
        {
            where.add(EXPHelper.getTermCourseFacultyMemberHqlWhereFragment("tct",
                    moduleUser));
        }

        qb.append(where.getWhereClause());

        int count = PortalUtils.getHt().findInt(qb);

        return count;
    }

    public static int bulkAddCoordinatorsToTermCourse(List<String> usernames,
            EXPTermCourse termCourse)
    {
        return PortalUtils.getJt().update(
                "insert into exp_term_course_coordinator (termCourse, uzer) "
                        + " select ?, u.USER_DETAILS_ID " //
                        + " from user_details u " //
                        + " join user_details_groups ug on u.USER_DETAILS_ID = ug.userDetailsId " //
                        + " join user_group g on ug.userGroupId = g.id " //
                        + " where g.name = ?" //
                        + " and u.username in "
                        + DBUtils.buildInClauseWithQuotes(usernames) //
                        + " and not exists (" //
                        + "     select 1 from exp_term_course_coordinator etcc " //
                        + "     where etcc.termCourse = ?" //
                        + "     and etcc.uzer = u.USER_DETAILS_ID" //
                        + ")",
                new Object[] { termCourse.getId(),
                        PersonGroupHelper.EXPERIENTIAL_EDUCATION_COURSE_COORDINATOR,
                        termCourse.getId() });
    }

    public static List<EXPTypeRecordModel> getTypeRecordModels(
            EXPTermCourse termCourse)
    {
        return PortalUtils.getHt().find(" select model" //
                + " from EXPTypeRecordModel model" //
                + " where exists (" //
                + "   select tct.id" //
                + "   from EXPTermCourseType tct" //
                + "   where tct.type=model.type" //
                + "   and tct.termCourse.id=?" //
                + " )", termCourse.getId());
    }

    public static int countUnplacedStudentRecord(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(
                    "select count (studentStep.studentExperience.student.student.owner) from");
            hql.append(" EXPStudentExperienceStep studentStep");
            hql.append(" join studentStep.step");
            hql.append(" join studentStep.step.tct tct");
            hql.append(" join studentStep.owner");
            hql.append(" join studentStep.step.tct.type expt");
            hql.append(" where ");
            hql.append(EXPUnplacedStudentsHQL.getCommonWhereFragment("studentStep",
                    "studentStep.step",
                    "studentStep.studentExperience.student.status",
                    "studentStep.owner"));
            hql.append(" and ");
            hql.append(EXPTermCourseHelper.getTermCourseModuleUserWhereHql(
                    moduleUser, "tct.termCourse", "expt"));
            hql.append(" and ");
            hql.append(" tct.termCourse=?", termCourse);
        }
        return PortalUtils.getHt().findInt(hql);
    }

    public static int countPlacementRecords(EXPTermCourse termCourse,
            EXPModuleUser moduleUser, Integer recordStatus)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(" select count(r.id) from EXPRecord r ");
            hql.append(" where r.status = ? ", recordStatus);
            hql.append(" and r.studentStep.student.termCourse=? ", termCourse);
            hql.appendIf(moduleUser.isExpCoordinator(),
                    " and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=r.studentStep.step.tct.type and etc.coordinator=?)",
                    moduleUser.getUser());
            if (moduleUser.isExpCourseCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPCourseCoordinatorHqlClause(
                        " r.studentStep.studentExperience.student.termCourse ",
                        moduleUser.getUser()));
            }
        }
        int count = PortalUtils.getHt().findInt(hql);
        return count;
    }
    
    public static void populateTotalPlacementsChartCounts(ModelAndView mv,
            EXPModuleUser moduleUser, EXPTermCourse termCourse)
    {
        mv.addObject("pendingPlacementRecordsCount",
                EXPTermCourseHelper.countPlacementRecords(termCourse, moduleUser,
                        EXPRecord.STATUS_PENDING));
        mv.addObject("approvedPlacementRecordsCount",
                EXPTermCourseHelper.countPlacementRecords(termCourse, moduleUser,
                        EXPRecord.STATUS_APPROVED));
        mv.addObject("declinedPlacementRecordsCount",
                EXPTermCourseHelper.countPlacementRecords(termCourse, moduleUser,
                        EXPRecord.STATUS_DECLINED));
        mv.addObject("adminOnlyPlacementRecordsCount",
                EXPTermCourseHelper.countPlacementRecords(termCourse, moduleUser,
                        EXPRecord.STATUS_ADMIN_ONLY));
        mv.addObject("draftPlacementRecordsCount",
                EXPTermCourseHelper.countPlacementRecords(termCourse, moduleUser,
                        EXPRecord.STATUS_DRAFT));
        mv.addObject("infoRequiredPlacementRecordsCount",
                EXPTermCourseHelper.countPlacementRecords(termCourse, moduleUser,
                        EXPRecord.STATUS_INFO_REQUIRED));
    }

    public static int countPlacementRecordsWithIndustryPartners(
            EXPTermCourse termCourse, EXPModuleUser moduleUser)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(
                    " select count(r.id) from EXPRecordIndustryPartner p join p.record r ");
            hql.append(" where r.studentStep.step.tct.termCourse=? ",
                    termCourse);
            hql.appendIf(moduleUser.isExpCoordinator(),
                    " and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=r.studentStep.step.tct.type and etc.coordinator=?)",
                    moduleUser.getUser());
            if (moduleUser.isExpCourseCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPCourseCoordinatorHqlClause(
                        " r.studentStep.studentExperience.student.termCourse ",
                        moduleUser.getUser()));
            }
        }
        return PortalUtils.getHt().findInt(hql);
    }

    public static int countPlacementRecordsWithoutIndustryPartners(
            EXPTermCourse termCourse, EXPModuleUser moduleUser)
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append("select count(r.id) from EXPRecord r ");
            hql.append(
                    " where not exists(select rip from EXPRecordIndustryPartner rip where rip.record = r) ");
            hql.append(" and r.studentStep.student.termCourse=? ", termCourse);
            hql.appendIf(moduleUser.isExpCoordinator(),
                    " and exists(select etc.id from EXPExperienceTypeCoordinator etc where etc.expType=r.studentStep.step.tct.type and etc.coordinator=?)",
                    moduleUser.getUser());
            if (moduleUser.isExpCourseCoordinator())
            {
                hql.append(" and ");
                hql.append(new EXPCourseCoordinatorHqlClause(
                        " r.studentStep.studentExperience.student.termCourse ",
                        moduleUser.getUser()));
            }

        }
        return PortalUtils.getHt().findInt(hql);
    }

    public static QueryBuilder buildQueryForProgssChart(EXPTermCourse termCourse,
            EXPModuleUser moduleUser)
    {
        QueryBuilder sql = new QueryBuilder();
        {
            sql.append(" SELECT COUNT(DISTINCT STUDENT) ");
            sql.append(" FROM exp_student_experience exp ");
            if (moduleUser.isExpCoordinator())
            {
                sql.append("     JOIN exp_term_course_type tct ");
                sql.append("      ON tct.id = exp.tct ");
                sql.append("     JOIN exp_experience_type_coordinator etc ");
                sql.append("      ON etc.expType = tct.type ");
                sql.append("       AND etc.coordinator = ? ",
                        moduleUser.getUser().getId());
            }
            sql.append("   JOIN exp_term_course_student tcs ");
            sql.append("     ON tcs.id = exp.student ");
            sql.append("       AND tcs.termCourse = ? ", termCourse.getId());
            if (moduleUser.isExpCourseCoordinator())
            {
                sql.append(" JOIN exp_term_course_coordinator tcc ");
                sql.append(" ON tcs.termCourse = tcc.termCourse ");
                sql.append(" AND tcc.uzer = ? ", moduleUser.getUser().getId());
            }
            sql.append("     JOIN exp_term_course_student_status status");
            sql.append("        ON tcs.status=status.id");
            sql.append("   JOIN exp_student_profile profile ");
            sql.append("     ON profile.id = tcs.studentProfile ");
            sql.append("   JOIN user_details u ");
            sql.append("     ON u.user_details_id = profile.owner ");
            sql.append("     WHERE ");
            sql.append(UserDetailsHelper.getActiveUserSqlWhereFragment("u"));
        }
        return sql;
    }

    public static int countActiveStudentsWithCompletedExperiences(
            EXPTermCourse termCourse, EXPModuleUser moduleUser)
    {
        QueryBuilder sql = buildQueryForProgssChart(termCourse, moduleUser);
        sql.append("   AND status.type = ? ",
                EXPTermCourseStudentStatus.Type.COMPLETED.getValue());
        return PortalUtils.getJt().queryForInt(sql);
    }

    public static int countActiveStudentsWithInProgressExperiences(
            EXPTermCourse termCourse, EXPModuleUser moduleUser)
    {
        QueryBuilder sql = buildQueryForProgssChart(termCourse, moduleUser);
        sql.append(" AND exp.status = ? ",
                EXPStudentExperience.Status.IN_PROGRESS.getValue());
        sql.append("   AND status.type = ? ",
                EXPTermCourseStudentStatus.Type.ACTIVE.getValue());
        return PortalUtils.getJt().queryForInt(sql);
    }

    public static int countInactiveStudentsWithInProgressExperiences(
            EXPTermCourse termCourse, EXPModuleUser moduleUser)
    {
        QueryBuilder sql = buildQueryForProgssChart(termCourse, moduleUser);
        sql.append(" AND exp.status = ? ",
                EXPStudentExperience.Status.IN_PROGRESS.getValue());
        sql.append("   AND status.type = ? ",
                EXPTermCourseStudentStatus.Type.INACTIVE.getValue());
        return PortalUtils.getJt().queryForInt(sql);
    }

    public static WhereFragment getTermCourseModuleUserWhereHql(
            EXPModuleUser moduleUser, String termCourseAlias, String expTypeAlias)
    {
        Where where = new Where();
        if (moduleUser.isExpCoordinator())
        {
            where.add(new EXPTypeCoordinatorHqlClause(expTypeAlias,
                    moduleUser.getUser().getId().toString()));
        }
        if (moduleUser.isExpCourseCoordinator())
        {
            where.add(new EXPCourseCoordinatorHqlClause(termCourseAlias,
                    moduleUser.getUser()));
        }
        return where.join("OR");
    }

    public static WhereFragment getTermCourseModuleUserWhereSql(
            EXPModuleUser moduleUser, String termCourseAlias, String expTypeId)
    {
        Where where = new Where();
        if (moduleUser.isExpCoordinator())
        {
            where.add(new EXPTypeCoordinatorSqlClause(expTypeId,
                    moduleUser.getUser().getId().toString()));
        }
        if (moduleUser.isExpCourseCoordinator())
        {
            where.add(new EXPCourseCoordinatorSqlClause(termCourseAlias,
                    moduleUser.getUser()));
        }
        return where.join("OR");
    }

    public static int getCourseCoordinatorCount(EXPTermCourse termCourse)
    {
        return PortalUtils.getJt()
                .queryForInt("select count(etcc.id) " //
                                + " from exp_term_course_coordinator etcc " //
                                + " where etcc.termCourse=? ",
                        new Object[] { termCourse.getId() });
    }

    public static List<OrbisHqlResultSet> getCourseCoordinators(
            EXPTermCourse termCourse)
    {
        return PortalUtils.getHt()
                .f("select etcc.id, etcc.user.firstAndLastName, " +
                        "etcc.user.emailAddress, etcc.user.phoneNumber"
                        + " from EXPTermCourseCoordinator etcc "
                        + " where etcc.termCourse=? ", termCourse);
    }

    public static List<EXPTermCourseFacultyAdvisor> getFacultyMembers(
            EXPTermCourse termCourse)
    {
        return PortalUtils.getHt().find(
                "from EXPTermCourseFacultyAdvisor f where f.termCourse=?",
                termCourse);
    }

    public static JSONArray convertFacultyMembersToJsonArray(
            List<EXPTermCourseFacultyAdvisor> facultyMembers)
    {
        JSONArray facultiesArray = new JSONArray();

        for (EXPTermCourseFacultyAdvisor faculty : facultyMembers)
        {
            JSONObject jsonFaculty = new JSONObject();
            try
            {
                jsonFaculty.put("id", faculty.getId());
                jsonFaculty.put("name",
                        faculty.getFacultyAdvisor().getFirstAndLastName());
                jsonFaculty.put("phone",
                        faculty.getFacultyAdvisor().getPhoneNumber());
                jsonFaculty.put("email", faculty.getFacultyAdvisor().getEmail());

                jsonFaculty.put("courses", EXPTermCourseFacultyAdvisorHelper
                        .getCoursesByFaculty(faculty));
            }
            catch (JSONException e)
            {
                throw new RuntimeException(e);
            }
            facultiesArray.put(jsonFaculty);
        }
        return facultiesArray;
    }

    public static int getStudentsCount(EXPTermCourse termCourse,
            UserDetailsImpl user)
    {
        int studentsCount;
        EXPModuleUser moduleUser = EXPModuleUser.getInstance(user,
                termCourse.getCourse().getModule());
        QueryBuilder q = new QueryBuilder();
        q.append("select count(tcs.id)");
        q.append(" from EXPTermCourseStudent tcs");
        q.append(" join tcs.status tcss");
        q.append(" join tcs.student.owner u");
        q.append(" where ")
                .append(EXPTermCourseStudentsHql
                        .getApprovedEnrollmentsWhereFragment("tcs", "tcss", "u",
                                termCourse, moduleUser));

        studentsCount = PortalUtils.getHt().findInt(q);
        return studentsCount;
    }

    public static JSONArray getResourcesAsJsonArray(EXPTermCourse termCourse,
            Locale locale)
    {
        List<EXPTermCourseResource> resources = PortalUtils.getHt().find(
                "FROM EXPTermCourseResource e WHERE e.termCourse = ?", termCourse);
        JSONArray resourcesArray = new JSONArray();

        for (EXPTermCourseResource res : resources)
        {
            JSONObject jsonResource = new JSONObject();
            try
            {
                jsonResource.put("name",
                        LocaleUtils.isL1(locale) ? res.getName() : res.getL2Name());
                jsonResource.put("description", LocaleUtils.isL1(locale) ?
                        res.getDescription() : res.getL2Description());
                jsonResource.put("id", res.getId());
                jsonResource.put("icon", res.getFileMaterialIcon());
                jsonResource.put("typeTitle", res.getFileTypeTitle());
            }
            catch (JSONException e)
            {
                throw new RuntimeException(e);
            }
            resourcesArray.put(jsonResource);
        }
        return resourcesArray;
    }

    public static void removeTermCourseCoordinator(Integer termCourseCoordinatorId)
    {
        if (termCourseCoordinatorId != null)
        {
            PortalUtils.getJt()
                    .execute("delete from exp_term_course_coordinator where id="
                            + termCourseCoordinatorId);
        }
    }

    public static void removeTermCourseFaculty(Integer facultyId)
    {
        if (facultyId != null)
        {
            PortalUtils.getJt()
                    .execute("delete from exp_term_course_faculty where id="
                            + facultyId);
        }
    }
}
