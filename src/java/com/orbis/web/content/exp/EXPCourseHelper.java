package com.orbis.web.content.exp;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.df.DFHelper;
import com.orbis.portal.OrbisHqlResultSet;
import com.orbis.portal.PortalUtils;
import com.orbis.search.SearchModel;
import com.orbis.search.criteria.CriteriaGroup;
import com.orbis.search.criteria.CriteriaModel;
import com.orbis.search.criteria.CriteriaQuestion;
import com.orbis.search.entity.Entity;
import com.orbis.utils.DBUtils;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.QueryBuilder;
import com.orbis.utils.StringUtils;
import com.orbis.utils.query.WhereFragment;
import com.orbis.web.content.exp.course.EXPCourseLabel;
import com.orbis.web.content.grid.GridFilter;
import com.orbis.web.content.grid.NameValuePair;
import com.orbis.web.site.SiteElement;

import jakarta.servlet.http.HttpServletRequest;

public class EXPCourseHelper
{
    private static final EXPCourse defaultInstance = newInstance();

    public static EXPCourse newInstance()
    {
        return new EXPCourse();
    }

    public static EXPCourse getDefaultInstance()
    {
        return defaultInstance;
    }

    public static EXPCourse getCourse(HttpServletRequest request)
    {
        EXPCourse course = null;

        if (request.getAttribute("courseId") != null)
        {
            course = (EXPCourse) PortalUtils.getHt().load(EXPCourse.class,
                    (Integer) request.getAttribute("courseId"));
        }
        else if (StringUtils.isInteger(request.getParameter("courseId")))
        {
            course = (EXPCourse) PortalUtils.getHt().load(EXPCourse.class,
                    Integer.valueOf(request.getParameter("courseId")));
        }

        return course;
    }

    /**
     * Returns a where clause of active courses and programs. the alias is tc
     * 
     * @param termCourseAlias
     *            alias of the term course
     * @param currentUser
     * @param moduleId
     *            the exp module id that the course/program belongs to
     * 
     * @return
     */
    public static WhereFragment getCoursesWhereClause(String termCourseAlias,
            EXPModuleUser moduleUser, List<Integer> selectedTerms)
    {
        QueryBuilder q = new QueryBuilder();
        q.append(termCourseAlias + ".course.module.id=?",
                moduleUser.getModule().getId());

        UserDetailsImpl currentUser = moduleUser.getUser();
        if (moduleUser.isExpCoordinator())
        {
            q.append(" and ");
            q.append(new EXPTermCourseCoordinatorHqlClause(termCourseAlias,
                    currentUser));
        }
        if (moduleUser.isExpCourseCoordinator())
        {
            q.append(" and ");
            q.append(new EXPCourseCoordinatorHqlClause(termCourseAlias,
                    currentUser));

        }

        if (selectedTerms != null && !selectedTerms.isEmpty())
        {
            q.append(" and ");
            q.append(termCourseAlias + ".term.id IN "
                    + DBUtils.buildInClause(selectedTerms));
        }

        return new WhereFragment(q.getStr(), q.getParams());
    }

    public static WhereFragment getAvailableCoursesForStudentWhereClause(
            String termCourseAlias, String termAlias, EXPProfile student,
            UserDetailsImpl userLoggedIn, EXPModule module)
    {
        QueryBuilder q = new QueryBuilder();

        q.append(termCourseAlias + ".status=?", EXPTermCourse.STATUS_OPEN);

        q.append(" AND " + termAlias + ".module=?", module);

        q.append(" AND " + termAlias + ".active=true");

        q.append(" AND EXISTS (");
        q.append("   SELECT tct.id");
        q.append("   FROM EXPTermCourseType tct");
        q.append("   WHERE tct.termCourse=" + termCourseAlias);
        q.append(" )");

        q.append(" AND NOT EXISTS (");
        q.append("   SELECT tcs.id");
        q.append("   FROM EXPTermCourseStudent tcs");
        q.append("   JOIN tcs.student");
        q.append("   WHERE tcs.termCourse=" + termCourseAlias);
        q.append("   AND tcs.student.owner=?", userLoggedIn);
        q.append(" )");

        q.append(" AND " + termCourseAlias + ".registrationMode=?",
                EXPTermCourse.REGISTRATION_MODE_APPLY);

        q.append(" AND (");
        q.append(termCourseAlias + ".accessMode=?", EXPTermCourse.ACCESS_MODE_OPEN);

        q.append("   OR " + termCourseAlias + ".accessMode=?",
                EXPTermCourse.ACCESS_MODE_PERMISSION_BASED);
        q.append("   AND EXISTS (");
        q.append("     SELECT etcp.id");
        q.append("     FROM EXPTermCoursePermission etcp");
        q.append("     WHERE etcp.termCourse=" + termCourseAlias);
        q.append("     AND EXISTS (");
        q.append("       SELECT g.id");
        q.append("       FROM UserDetailsImpl u");
        q.append("       JOIN u.groups AS g");
        q.append("       WHERE g.id=etcp.permission.id");
        q.append("       AND u=?",
                student != null ? student.getOwner() : userLoggedIn);
        q.append("     )");
        q.append("   )");

        if (student != null)
        {
            q.append("   OR " + termCourseAlias + ".accessMode in (?,?)",
                    EXPTermCourse.ACCESS_MODE_WHITE_LIST,
                    EXPTermCourse.ACCESS_MODE_PERMISSION_BASED);
            q.append("   AND EXISTS (");
            q.append("     SELECT wl1.id");
            q.append("     FROM EXPTermCourseStudentWhiteList wl1");
            q.append("     WHERE wl1.termCourse=" + termCourseAlias);
            q.append("     AND wl1.student=?", student);
            q.append("   )");
        }

        q.append("   OR " + termCourseAlias + ".accessMode=?",
                EXPTermCourse.ACCESS_MODE_TAG_BASED);
        q.append("   AND EXISTS (");
        q.append("     SELECT ta.id");
        q.append("     FROM TagAssign ta");
        q.append("     WHERE ta.tag=" + termCourseAlias + ".accessTag");
        q.append("     AND ta.user=?",
                student != null ? student.getOwner() : userLoggedIn);
        q.append("   )");
        q.append(" )");

        List<Integer> termIds = PortalUtils.getHt().find(
                "select distinct t.id from EXPTermCourse tc join tc.term t where tc.status=? AND t.module=?",
                new Object[] { EXPTermCourse.STATUS_OPEN, module });

        for (Integer t : termIds)
        {
            q.append(DFHelper.getQualifierQuery(
                    DFHelper.getQualifiers(
                            (EXPTerm) PortalUtils.getHt().load(EXPTerm.class, t)),
                    termCourseAlias));
        }

        return new WhereFragment(q.getStr(), q.getParams());
    }

    public static GridFilter getNoStudentsCourseFilter(Locale locale)
    {
        return new GridFilter("noStudents",
                PortalUtils.getI18nMessage(
                        "i18n.EXPCourseHelper.CourseProg7800585228543676", locale),
                GridFilter.GRID_FILTER_TYPE_CUSTOM, null,
                List.of(NameValuePair.of("type", "noStudents")),
                Collections.EMPTY_MAP);
    }

    public static GridFilter getInProgressCourseFilter(Locale locale)
    {
        return new GridFilter("inProgress",
                PortalUtils.getI18nMessage(
                        "i18n.EXPCourseHelper.CourseProg4342281179671757", locale),
                GridFilter.GRID_FILTER_TYPE_CUSTOM, null,
                List.of(NameValuePair.of("type", "inProgress")),
                Collections.EMPTY_MAP);
    }

    public static GridFilter getCompletedCourseFilter(Locale locale)
    {
        return new GridFilter("completed",
                PortalUtils.getI18nMessage(
                        "i18n.EXPCourseHelper.CourseProg6670051606114308", locale),
                GridFilter.GRID_FILTER_TYPE_CUSTOM, null,
                List.of(NameValuePair.of("type", "completed")),
                Collections.EMPTY_MAP);
    }

    public static GridFilter getOnlyProgramFilter(Locale locale)
    {
        return new GridFilter("program",
                PortalUtils.getI18nMessage(
                        "i18n.EXPCourseHelper.Programson9321637707288879", locale),
                GridFilter.GRID_FILTER_TYPE_CUSTOM, null,
                List.of(NameValuePair.of("type", "program")),
                Collections.EMPTY_MAP);
    }

    public static GridFilter getOnlyCourseFilter(Locale locale)
    {
        return new GridFilter("course",
                PortalUtils.getI18nMessage(
                        "i18n.EXPCourseHelper.Coursesonl7323961835394423", locale),
                GridFilter.GRID_FILTER_TYPE_CUSTOM, null,
                List.of(NameValuePair.of("type", "course")),
                Collections.EMPTY_MAP);
    }

    public static String getHasEnrollmentsWhereClause()
    {
        return " exists (select tcs.id from EXPTermCourseStudent tcs where tcs.termCourse.id= tc.id)";
    }

    public static String getInProgressWhereClause()
    {
        return " exists (select tcs.id from EXPTermCourseStudent tcs  where tcs.termCourse.id=tc.id and tcs.status.type<>"
                + EXPTermCourseStudentStatus.Type.COMPLETED.getValue() + ")";
    }

    public static EXPCourseLabel getCourseLabel(String courseNameAlias,
            String courseCodeAlias, String courseSectionAlias,
            String academicCourseAlias, OrbisHqlResultSet ses)
    {
        EXPCourseLabel.Builder courseLabel = EXPCourseLabel.builder();

        String sec = ses.select(courseSectionAlias);

        if (!ses.<Boolean> select(academicCourseAlias))
        {
            sec = null;
        }

        return courseLabel.name(ses.select(courseNameAlias))//
                .code(ses.select(courseCodeAlias))//
                .section(sec).build();
    }

    public static List<NameValuePair> getCourseFacultyOwnerFilterOptions(
            EXPModule module)
    {
        QueryBuilder q = new QueryBuilder();
        {
            q.append(" SELECT DISTINCT ");
            q.append("   f.user_details_id AS id, ");
            q.append("   f.preferredFirstName AS userFirstName, ");
            q.append("   f.lastName AS userLastName ");
            q.append(" FROM exp_term_course_faculty tcfm ");
            q.append("   LEFT JOIN exp_term_course tc ");
            q.append("     ON tc.id = tcfm.termCourse ");
            q.append("   LEFT JOIN user_details f ");
            q.append("     ON f.user_details_id = tcfm.facultyAdvisor ");
            q.append("   LEFT JOIN exp_course c ");
            q.append("     ON tc.course = c.id ");
            q.append(" WHERE 1=1 ");
            if (module != null)
                q.append(" AND c.module = ? ", module.getId());
        }

        return PortalUtils.getJt()
                .query(q,
                        (rs, i) -> new NameValuePair(
                                rs.getString("userFirstName") + " "
                                        + rs.getString("userLastName"),
                                Integer.toString(rs.getInt("id"))))
                .stream()
                .sorted((n, n2) -> String.CASE_INSENSITIVE_ORDER
                        .compare(n.getName(), n2.getName()))
                .collect(Collectors.toList());
    }

    public static List getExperienceCourseNames(Locale locale)
    {
        String nameProperty = LocaleUtils.isL1(locale) ? "name" : "l2Name";
        List expCourses = PortalUtils.getHt()
                .find(new QueryBuilder("select ec.").append(nameProperty)
                        .append(" from EXPCourse ec ").append(" order by ec.")
                        .append(nameProperty));
        return expCourses;
    }

    public static List<NameValuePair> getCourseTagsFilterOptions(EXPModule module,
            Locale locale)
    {
        String tagNamePrefix = LocaleUtils.isL1(locale) ? "a.tag.l1Name"
                : "a.tag.l2Name";
        String query = "SELECT DISTINCT a.tag.id, " + tagNamePrefix
                + " FROM TagAssign a JOIN a.expTermCourse etc LEFT JOIN a.tag t LEFT JOIN t.category c WHERE etc.course.module=?";
        List<Object[]> academicTags = PortalUtils.getHt().find(query, module);
        return academicTags.stream()
                .map(tag -> new NameValuePair(tag[1].toString(), tag[0].toString()))
                .collect(Collectors.toList());
    }

    public static SearchModel getCompetencyDevelopmentSearchModel(
            HttpServletRequest request, EXPTermCourse course)
    {
        Locale locale = PortalUtils.getLocale(request);
        final SiteElement se = PortalUtils.getCurrentElement(request);
        SearchModel searchModel = new SearchModel();
        searchModel.setOwnerModuleClassName(se.getContentItemClass().getName());
        searchModel.setOwnerModuleId(se.getContentItemId().toString());
        searchModel.setState(SearchModel.STATE_SHOW_RESULTS);

        searchModel.setCanSave(true);
        searchModel.setCanExport(true);
        searchModel.setCanEmail(false);
        searchModel.setCanEmailCampaign(false);
        searchModel.setCanViewCriteria(true);
        searchModel.setCanViewDetails(true);
        searchModel.setShowQuestionOrder(false);
        searchModel.setCanReturn(true);

        Entity master = getCompetencyDevelopmentMasterEntity("esesa", locale, true,
                course != null ? course.getId() : null);

        searchModel.setMasterEntity(master);

        return searchModel;
    }

    private static Entity getCompetencyDevelopmentMasterEntity(String hqlPrefix,
            Locale locale, boolean isMaster, Integer selectedTermCourse)
    {
        CriteriaModel model = getCompetencyDevelopmentCriteriaModel(hqlPrefix,
                locale);

        Entity entity = new Entity(
                PortalUtils.getMessageSource()
                        .getMessage(
                                "i18n.EXPTermCourseCompetencyDevelopmentReport.Competency5711947121188255",
                                null, locale),
                EXPStudentExperienceStepAchieved.class, "esesa", "id", model,
                "esesa.id", "asc", isMaster);

        QueryBuilder fromHql = new QueryBuilder();

        fromHql.append(" join esesa.competency c ");
        fromHql.append("join esesa.proficiencyItem pi ");
        fromHql.append("join esesa.studentExperienceStep eses ");
        fromHql.append("join esesa.studentExperienceStep.step etcts ");
        fromHql.append("join esesa.studentExperienceStep.student stcs ");
        fromHql.append("join esesa.studentExperienceStep.student.termCourse etc ");

        entity.setStaticFromHql(fromHql.toLiteralString());

        QueryBuilder whereHql = new QueryBuilder();
        whereHql.append(" and etc.id = ?", selectedTermCourse);

        entity.setStaticWhereHql(whereHql.toLiteralString());
        return entity;
    }

    private static CriteriaModel getCompetencyDevelopmentCriteriaModel(
            String hqlPrefix, Locale locale)
    {
        CriteriaModel model = new CriteriaModel();

        model.addCriteriaGroup(
                getCompetencyDevelopmentCriteriaGroup(hqlPrefix, locale));

        return model;
    }

    private static CriteriaGroup getCompetencyDevelopmentCriteriaGroup(
            String hqlPrefix, Locale locale)
    {
        CriteriaGroup detailGroup = new CriteriaGroup(
                PortalUtils.getMessageSource().getMessage(
                        "i18n.EXPCourseHelper.RecordDeta6622114462548509",
                        null, locale));
        LinkedList<CriteriaQuestion> detailQuestions = new LinkedList<>();
        CriteriaQuestion cq;

        cq = new CriteriaQuestion();
        cq.setQuestionText(
                "i18n.EXPRecordAbstractHelper.StudentID1858547741344177");
        cq.setQuestionKey(
                hqlPrefix + ".studentExperienceStep.student.student.owner.username");
        cq.setType(CriteriaQuestion.TYPE_TEXT);
        detailQuestions.add(cq);

        cq = new CriteriaQuestion();
        cq.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.StudentFirstName");
        cq.setQuestionKey(
                hqlPrefix + ".studentExperienceStep.student.student.owner.firstName");
        cq.setType(CriteriaQuestion.TYPE_TEXT);
        detailQuestions.add(cq);

        cq = new CriteriaQuestion();
        cq.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.StudentLastName");
        cq.setQuestionKey(
                hqlPrefix + ".studentExperienceStep.student.student.owner.lastName");
        cq.setType(CriteriaQuestion.TYPE_TEXT);
        detailQuestions.add(cq);

        cq = new CriteriaQuestion();
        cq.setQuestionText(
                "i18n.EXPRecordAbstractHelper.ProgramofS8848465794303944");
        cq.setQuestionKey(
                hqlPrefix + ".studentExperienceStep.student.student.owner.major1Descr");
        cq.setType(CriteriaQuestion.TYPE_TEXT);
        detailQuestions.add(cq);

        cq = new CriteriaQuestion();
        cq.setQuestionText(
                "i18n.EXPRecordAbstractHelper.ProgramCod3725596482367686");
        cq.setQuestionKey(
                hqlPrefix + ".studentExperienceStep.student.student.owner.major1Code");
        cq.setType(CriteriaQuestion.TYPE_TEXT);
        detailQuestions.add(cq);

        cq = new CriteriaQuestion();
        cq.setQuestionText(
                "i18n.EXPRecordAbstractHelper.Experience5026187443359483");
        cq.setQuestionKey(
                hqlPrefix + ".studentExperienceStep.step.tct.type." + (LocaleUtils.isL1(
                        locale) ? "name" : "l2Name"));
        cq.setType(CriteriaQuestion.TYPE_TEXT);
        detailQuestions.add(cq);

        cq = new CriteriaQuestion();
        cq.setQuestionText(
                "i18n.EXPCourseHelper.Competency1922660584174081");
        cq.setQuestionKey(hqlPrefix + ".competency." + (LocaleUtils.isL1(
                locale) ? "name" : "l2Name"));
        cq.setType(CriteriaQuestion.TYPE_TEXT);
        detailQuestions.add(cq);

        cq = new CriteriaQuestion();
        cq.setQuestionText(
                "i18n.EXPCourseHelper.Proficienc5904109815465192");
        cq.setQuestionKey(hqlPrefix + ".proficiencyItem." + (LocaleUtils.isL1(
                locale) ? "name" : "l2Name"));
        cq.setType(CriteriaQuestion.TYPE_TEXT);
        detailQuestions.add(cq);

        cq = new CriteriaQuestion();
        cq.setQuestionText(
                "i18n.EXPCourseHelper.Step0438018390500021");
        cq.setQuestionKey(
                hqlPrefix + ".studentExperienceStep.step." + (LocaleUtils.isL1(
                        locale) ? "label" : "l2Label"));
        cq.setType(CriteriaQuestion.TYPE_TEXT);
        detailQuestions.add(cq);

        detailGroup.setQuestions(detailQuestions);
        return detailGroup;
    }
}
