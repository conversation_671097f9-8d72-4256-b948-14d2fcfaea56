package com.orbis.web.content.exp;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.json.JSONArray;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import com.orbis.portal.PortalUtils;
import com.orbis.utils.Catchable;
import com.orbis.utils.JSONUtils;
import com.orbis.utils.RequestUtils;
import com.orbis.web.content.exp.placer.EXPStudentPlacementFinder;
import com.orbis.web.content.exp.placer.EXPStudentPlacementFinderOptions;
import com.orbis.web.content.exp.placer.Sort;
import com.orbis.web.content.exp.placer.ui.Placement;
import com.orbis.web.content.gmap.AddressCoord;
import com.orbis.web.content.np.NHelper;

public interface EXPPlaceStudentWithPostingController
{
    @RequestMapping("displayPlaceStudentWithPosting")
    default ModelAndView displayPlaceStudentWithPosting(
            HttpServletRequest request, HttpServletResponse response)
    {
        Locale locale = PortalUtils.getLocale(request);
        ModelAndView mv = new ModelAndView("exp/exp_placeStudentWithPosting");

        EXPStudentExperienceStep step = EXPStudentExperienceStepHelper
                .getStudentExperienceStep(request);
        mv.addObject("step", step);

        EXPExperienceType expType = step.getStudentExperience().getTct().getType();
        if (expType.isEnablePostings())
        {
            List<Map<String, Object>> tagFilters = EXPPlaceStudentWithPostingHelper
                    .getTagFilters(locale, step);
            mv.addObject("tags", tagFilters);
        }

        EXPPlaceStudentHelper.populateStudentDetails(mv, step, request);

        return mv;
    }

    default EXPStudentPlacementFinderOptions getPlacementFinderOptions(
            HttpServletRequest request)
    {
        Sort sort = Catchable.of(request.getParameter("sort"))//
                .attempt(Sort::valueOf).orElse(null);
        List<Integer> tagIds = RequestUtils.getIntParameterValues(request,
                "tagIds");
        double maximumDistanceInKilometres = RequestUtils
                .getDoubleParameter(request, "maximumDistanceInKilometres", -1);
        Locale locale = PortalUtils.getLocale(request);
        String keyword = request.getParameter("keyword");
        return EXPStudentPlacementFinderOptions.builder()//
                .tagIds(tagIds)//
                .locale(locale)//
                .sort(sort)//
                .keyword(keyword)//
                .maximumDistanceInKilometres(maximumDistanceInKilometres)//
                .build();
    }

    @RequestMapping("ajaxGetPlaceStudentPlacementsGeoData")
    default ModelAndView ajaxGetPlaceStudentPlacementsGeoData(
            HttpServletRequest request, HttpServletResponse response)
    {
        Locale locale = PortalUtils.getLocale(request);
        EXPStudentExperienceStep step = EXPStudentExperienceStepHelper
                .getStudentExperienceStep(request);
        EXPStudentPlacementFinderOptions options = getPlacementFinderOptions(
                request);
        EXPStudentPlacementFinder finder = new EXPStudentPlacementFinder(options);
        JSONArray postingGeoData = finder.find(step)//
                .stream()//
                .map(Placement::getPosting)//
                .map(p -> {
                    AddressCoord geolocation = p.getGeolocation();
                    if (geolocation == null)
                    {
                        return null;
                    }
                    return new EXPPostingGeoDataBuilder()//
                            .id(p.getId())//
                            .name(p.getName())//
                            .latitude(geolocation.getLatitude())//
                            .longitude(geolocation.getLongitude())//
                            .location(geolocation.getPostalCode())//
                            .locale(locale)//
                            .experienceType(p.getType().getName())//
                            .build();
                })//
                .filter(Objects::nonNull)//
                .collect(JSONUtils.toJSONArray());
        return NHelper.AJAXResponse(postingGeoData.toString());
    }

}
