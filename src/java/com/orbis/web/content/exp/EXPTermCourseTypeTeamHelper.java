package com.orbis.web.content.exp;

import java.util.Date;

import com.orbis.portal.PortalUtils;

public final class EXPTermCourseTypeTeamHelper
{
    private EXPTermCourseTypeTeamHelper()
    {
    }

    public static boolean hasLeader(EXPTermCourseTypeTeam team)
    {
        return PortalUtils.getHt().findInt(
                "SELECT COUNT(*) FROM EXPTermCourseTypeTeamStudent teamStudent WHERE teamStudent.team=?",
                team.getId()) > 0;
    }

    /**
     * Joins the student and the team and returns the membership.<br>
     * <br>
     * If the student is unable to join the team (i.e is a member of a different
     * team of the same term course type), null will be returned. <br>
     * <br>
     * If the student is already a member of the team, the existing membership
     * will be returned.
     * 
     * @param team
     *            {@link EXPTermCourseTypeTeam}
     * @param student
     *            {@link EXPTermCourseStudent}
     * @return Joins the student and the team and returns the membership.
     */
    public static EXPTermCourseTypeTeamStudent joinTeam(EXPTermCourseTypeTeam team,
            EXPTermCourseStudent student)
    {
        if (team == null)
            return null;
        if (team.getId() == null)
            return null;
        if (student == null)
            return null;
        if (student.getId() == null)
            return null;
        if (!student.getStudent().getOwner().isEnabled())
            return null;
        if (student.getStudent().getOwner().isDeleted())
            return null;

        EXPTermCourseType type = team.getType();

        EXPTermCourseTypeTeamStudent existingMembership = EXPTermCourseStudentHelper
                .getTeamMember(student, type);

        // has an existing membership
        if (existingMembership != null)
        {
            // already a member
            if (existingMembership.hasTeam(team))
                return existingMembership;
            // is a member of another team
            return null;
        }

        EXPTermCourseTypeTeamStudent membership = new EXPTermCourseTypeTeamStudent();
        {
            membership.setTeam(team);
            membership.setStudent(student);
            membership.setDateCreated(new Date());
            if (!hasLeader(team))
            {
                membership.setTeamLeader(true);
            }
        }
        PortalUtils.getHt().save(membership);

        EXPStudentExperienceStep teamStep = EXPTermCourseStudentHelper
                .getFormGroupStep(student, type);

        EXPStudentExperienceStepHelper.finishStudentExpStep(teamStep);

        return membership;
    }

    public static EXPTermCourseTypeTeamStudent getLeader(EXPTermCourseTypeTeam team)
    {
        return getLeader(team.getId());
    }

    public static EXPTermCourseTypeTeamStudent getLeader(Integer teamId)
    {
        return PortalUtils.getHt().<EXPTermCourseTypeTeamStudent> findFirst(
                "FROM EXPTermCourseTypeTeamStudent s WHERE s.team = ? AND s.teamLeader = true",
                teamId).orElse(null);
    }

    public static void revokeLeadership(EXPTermCourseTypeTeam team)
    {
        revokeLeadership(team.getId());
    }

    /**
     * Revokes the team leader's leadership
     * 
     * @param teamId
     *            {@link EXPTermCourseTypeTeam} ID
     */
    public static void revokeLeadership(Integer teamId)
    {
        PortalUtils.getJt().update(
                "UPDATE EXP_TERM_COURSE_TYPE_TEAM_STUDENT SET teamLeader = 0 WHERE teamLeader = 1 AND team = ?",
                new Object[] { teamId });
    }

}
