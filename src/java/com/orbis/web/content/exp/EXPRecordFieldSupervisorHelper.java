package com.orbis.web.content.exp;

import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.Map;

import com.orbis.acegi.providers.dao.hibernate.PersonGroup;
import com.orbis.acegi.providers.dao.hibernate.PersonGroupHelper;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalLog;
import com.orbis.portal.PortalUtils;
import com.orbis.search.criteria.CriteriaGroup;
import com.orbis.search.criteria.CriteriaModel;
import com.orbis.search.criteria.CriteriaQuestion;
import com.orbis.search.entity.Entity;
import com.orbis.utils.QueryBuilder;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.portal.DeletionNodeHelper;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotNull;

public class EXPRecordFieldSupervisorHelper
{
    private EXPRecordFieldSupervisorHelper()
    {
    }

    public static EXPRecordFieldSupervisor newInstance()
    {
        return new EXPRecordFieldSupervisor();
    }

    public static EXPRecordFieldSupervisor getRecordFieldSupervisor(
            HttpServletRequest request)
    {
        EXPRecordFieldSupervisor recordFieldSupervisor = null;

        if (request.getAttribute("recordFieldSupervisorId") != null)
        {
            recordFieldSupervisor = (EXPRecordFieldSupervisor) PortalUtils.getHt()
                    .load(EXPRecordFieldSupervisor.class, (Integer) request
                            .getAttribute("recordFieldSupervisorId"));
        }
        else if (StringUtils
                .isInteger(request.getParameter("recordFieldSupervisorId")))
        {
            recordFieldSupervisor = (EXPRecordFieldSupervisor) PortalUtils.getHt()
                    .load(EXPRecordFieldSupervisor.class, Integer.valueOf(
                            request.getParameter("recordFieldSupervisorId")));
        }

        return recordFieldSupervisor;
    }

    public static void addRecordFieldSupervisor(@NotNull
    EXPRecord record, @NotNull
    UserDetailsImpl fieldSupervisor, UserDetailsImpl userLoggedIn)
    {
        EXPRecordFieldSupervisor recordFieldSupervisor = newInstance();
        recordFieldSupervisor.setFieldSupervisor(fieldSupervisor);
        recordFieldSupervisor.setRecord(record);
        PortalUtils.getHt().save(recordFieldSupervisor);
        PortalUtils.createPortalLog(
                PortalLog.PL_PLACEMENT_RECORD_FIELD_SUPERVISOR_ADDED,
                fieldSupervisor.getFullName() + " added as field supervisor",
                record, userLoggedIn);
    }

    public static void deleteRecordFieldSupervisor(
            EXPRecordFieldSupervisor recordFieldSupervisor,
            UserDetailsImpl userLoggedIn)
    {
        DeletionNodeHelper.deleteContentItem(recordFieldSupervisor);
        PortalUtils.createPortalLog(
                PortalLog.PL_PLACEMENT_RECORD_FIELD_SUPERVISOR_ADDED,
                recordFieldSupervisor.getFieldSupervisor().getFullName()
                        + " removed as field supervisor",
                recordFieldSupervisor.getRecord(), userLoggedIn);
    }

    static Map<String, String> getFieldSupervisorOptionChoices(EXPModule module,
            boolean includeOldSupervisors)
    {
        PersonGroup g = PersonGroupHelper.getForGroupName(
                PersonGroupHelper.EXPERIENTIAL_EDUCATION_FIELD_SUPERVISOR);

        QueryBuilder q = new QueryBuilder();
        q.append(" select u.USER_DETAILS_ID userId,");
        q.append("   concat(u.preferredFirstName, ' ', u.lastName) name");
        q.append(" from user_details u");
        q.append(" where ");
        q.append(UserDetailsHelper.getActiveUserSqlWhereFragment("u"));
        q.append(" and exists (");
        q.append("   select 1");
        q.append("   from user_details_groups g");
        q.append("   where g.userDetailsId=u.USER_DETAILS_ID");
        q.append("   and g.userGroupId=?", g.getId());
        q.append(" )");

        if (includeOldSupervisors)
        {
            q.append(" or exists (");
            q.append("   select 1");
            q.append("   from exp_record_field_supervisor rfs");
            q.append("   join exp_record r on r.id=rfs.record");
            q.append("   join exp_student_experience_step ses");
            q.append("     on ses.id=r.studentStep");
            q.append("   join exp_term_course_student tcs on tcs.id=ses.student");
            q.append("   join exp_term_course tc on tc.id=tcs.termCourse");
            q.append("   join exp_course c on c.id=tc.course");
            q.append("   where rfs.fieldSupervisor=u.USER_DETAILS_ID");
            q.append("   and c.module=?", module.getId());
            q.append(" )");
        }

        q.append(" order by name");

        Map<String, String> fieldSupervisorMap = new LinkedHashMap<>();

        PortalUtils.getJt().query(q, rs -> {
            fieldSupervisorMap.put(String.valueOf(rs.getInt("userId")),
                    rs.getString("name"));
        });

        return fieldSupervisorMap;
    }

    static Entity getExpRecordFieldSupervisorEntity(EXPModule module, String label)
    {
        CriteriaModel model = new CriteriaModel();
        CriteriaGroup cg = new CriteriaGroup(label);
        LinkedList<CriteriaQuestion> questions = new LinkedList<>();

        CriteriaQuestion cq = new CriteriaQuestion();
        cq.setQuestionText(label);
        cq.setType(CriteriaQuestion.TYPE_CHOICE_MULTI);
        cq.setOptionChoices(getFieldSupervisorOptionChoices(module, true));
        cq.setQuestionKey("rfs.fieldSupervisor.id");
        questions.add(cq);
        cg.setQuestions(questions);
        model.getCriteriaGroups().add(cg);

        Entity entity = new Entity(label, EXPRecordFieldSupervisor.class, "rfs",
                "id", model, "rfs.fieldSupervisor.lastName", "asc", false);
        return entity;
    }

}
