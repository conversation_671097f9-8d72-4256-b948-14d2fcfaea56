package com.orbis.web.content.exp;

import java.util.Optional;

import com.orbis.portal.PortalUtils;
import com.orbis.utils.QueryBuilder;
import com.orbis.web.content.exp.step.systemActivity.config.ExpSystemActivityStepCriterion;

public class EXPTypeWorkflowTemplateSystemActivityStepPropagator
        extends EXPTypeWorkflowTemplateStepBasicPropagator
{

    @Override
    public EXPTermCourseTypeStep propagate(EXPTermCourseTypeStep templateStep,
            EXPTermCourseType propagateTo)
    {

        Integer templateStepId = templateStep.getId();

        EXPTermCourseTypeStep propagate = super.propagate(templateStep,
                propagateTo);

        Optional<ExpSystemActivityStepCriterion> templateCriterion = PortalUtils
                .getHt()
                .findFirst("from ExpSystemActivityStepCriterion where step=?",
                        templateStepId);
        templateCriterion.ifPresent(criterion -> {
            Integer templateCriterionId = criterion.getId();
            criterion.setId(null);
            criterion.setStep(propagate);
            PortalUtils.getHt().save(criterion);

            QueryBuilder qb = new QueryBuilder();
            qb.append(
                    "insert into exp_system_activity_step_criterion_category (criterion, contentItemClass, contentItemId)");
            qb.append(" (select ?, contentItemClass, contentItemId",
                    criterion.getId());
            qb.append(" from exp_system_activity_step_criterion_category");
            qb.append(" where criterion = ?)", templateCriterionId);
            PortalUtils.getJt().update(qb);
        });

        return propagate;
    }
}
