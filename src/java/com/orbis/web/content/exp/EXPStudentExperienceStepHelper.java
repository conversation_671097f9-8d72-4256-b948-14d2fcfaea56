package com.orbis.web.content.exp;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.web.servlet.ModelAndView;

import com.google.common.collect.Lists;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.df.DFHelper;
import com.orbis.portal.OrbisHqlResultSet;
import com.orbis.portal.PortalLog;
import com.orbis.portal.PortalUtils;
import com.orbis.qf.QFQualifierEntity;
import com.orbis.qf.QFQualifierEntityHelper;
import com.orbis.qf.QFStudentEntityHelper;
import com.orbis.qf.QFStudentQualificationsHql;
import com.orbis.security.OrbisSecurityUtils;
import com.orbis.security.OrbisSecurityUtils.UnauthorizedAccessType;
import com.orbis.utils.DBUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.EmailMessage;
import com.orbis.utils.EmailUtils;
import com.orbis.utils.QueryBuilder;
import com.orbis.utils.RequestUtils;
import com.orbis.utils.StringUtils;
import com.orbis.utils.query.Select;
import com.orbis.utils.query.Where;
import com.orbis.utils.query.WhereFragment;
import com.orbis.web.content.acrm.competency.Competenciable;
import com.orbis.web.content.acrm.competency.CompetencyAchievedHelper;
import com.orbis.web.content.acrm.competency.ConfigLevel;
import com.orbis.web.content.acrm.reflection.config.AcrmReflectionConfig;
import com.orbis.web.content.exp.EXPStudentExperienceStep.Status;
import com.orbis.web.content.exp.studentExperienceStep.workflow.EXPStudentExperienceStepWorkflow;
import com.orbis.web.content.gmap.AddressCoord;
import com.orbis.web.content.grid.GridFilter;
import com.orbis.web.content.grid.NameConversionParameters;
import com.orbis.web.content.grid.NameValuePair;
import com.orbis.web.content.os.OSOrder;
import com.orbis.web.content.os.OSOrderHelper;

import jakarta.persistence.NoResultException;
import jakarta.persistence.Query;
import jakarta.servlet.http.HttpServletRequest;

public final class EXPStudentExperienceStepHelper
{
    private EXPStudentExperienceStepHelper()
    {
    }

    public static int getTeamLeaderRecordStepId(HttpServletRequest request)
    {
        int teamLeaderRecordStepId = (int) PortalUtils.getHt().findFirst(
                "select ses.id from EXPStudentExperienceStep ses where ses.step.type=? and exists (select tctts.id from EXPTermCourseTypeTeamStudent tctts where tctts.teamLeader=true and tctts.student=ses.student and tctts.team.id=?)",
                new Object[] {
                        EXPTermCourseTypeStep.Type.CREATE_A_RECORD.getValue(),
                        Integer.valueOf(request.getParameter("teamId")) })
                .orElseGet(() -> RequestUtils.getIntParameterOrAttribute(request,
                        "studentStepId", 0));
        return teamLeaderRecordStepId;
    }

    public static EXPStudentExperienceStep getTeamLeaderRecordStep(
            HttpServletRequest request)
    {
        return (EXPStudentExperienceStep) PortalUtils.getHt().load(
                EXPStudentExperienceStep.class, getTeamLeaderRecordStepId(request));
    }

    public static EXPStudentExperienceStep getStudentExperienceStepForSlot(
            Integer slotId)
    {
        return (EXPStudentExperienceStep) PortalUtils.getHt().findFirst(
                "from EXPStudentExperienceStep ses where ses.attendedSlot.id=?",
                slotId).orElse(null);
    }

    public static void resetAppointmentStepForSlot(Integer slotId)
    {
        EXPStudentExperienceStep ses = getStudentExperienceStepForSlot(slotId);
        if (ses != null)
        {
            EXPHelper.resetPendingStartedForOtherSteps(ses);
            ses.setAttendedSlot(null);
            PortalUtils.getHt().saveOrUpdate(ses);
        }
    }

    public static EXPStudentExperienceStep getStudentExperienceStepFromOSOrder(
            OSOrder osOrder)
    {
        EXPStudentExperienceStep studentStep = OSOrderHelper
                .getStudentExperienceStepFromOSOrder(osOrder);
        return studentStep;
    }

    public static EXPStudentExperienceStep getStudentExperienceStep(
            HttpServletRequest request)
    {
        EXPStudentExperienceStep studentStep = null;
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (request.getAttribute("studentStepId") != null)
        {
            studentStep = (EXPStudentExperienceStep) PortalUtils.getHt().load(
                    EXPStudentExperienceStep.class,
                    (Integer) request.getAttribute("studentStepId"));
        }
        else if (StringUtils.isInteger(request.getParameter("studentStepId")))
        {
            studentStep = (EXPStudentExperienceStep) PortalUtils.getHt().load(
                    EXPStudentExperienceStep.class,
                    Integer.valueOf(request.getParameter("studentStepId")));
        }

        if (userLoggedIn == null)
        {
            studentStep = null;
        }
        else if (studentStep != null && !isVisibleToUser(studentStep, userLoggedIn))
        {

            OrbisSecurityUtils.logUnauthorizedAccess(userLoggedIn, studentStep,
                    PortalLog.PL_UNAUTHORIZED_ACCESS_ATTEMPT_EXP_STUDENT_STEP);

            OrbisSecurityUtils.disableNonStaffUser(request, userLoggedIn,
                    UnauthorizedAccessType.Entity);

        }

        return studentStep;
    }

    public static Optional<EXPStudentExperienceStep> getStudentExperienceStep(
            int studentId, int stepType, EXPTermCourseType termCourseType)
    {
        return PortalUtils.getHt().findFirst(
                "select eses from EXPStudentExperienceStep eses "
                        + "join eses.step etcts join etcts.tct etct "
                        + "where eses.owner.id = ? and etcts.type = ? "
                        + "and etct.id = ? ",
                new Object[] { studentId, stepType, termCourseType.getId() });
    }

    private static boolean isVisibleToUser(EXPStudentExperienceStep studentStep,
            UserDetailsImpl user)
    {
        boolean userIsAllowed = false;
        EXPModuleUser moduleUser = EXPModuleUser.getInstance(user,
                studentStep.getStep().getTct().getType().getModule());

        if (user.equals(studentStep.getStudent().getStudent().getOwner()))
        {
            userIsAllowed = true;
        }
        else if (PortalUtils.getJt().exists(
                "select 1 from exp_term_course_coordinator etcc where etcc.termCourse=? and etcc.uzer=?",
                studentStep.getStudent().getTermCourse().getId(), user.getId()))
        {
            userIsAllowed = true;
        }
        else if (moduleUser.isExpAdminOrCoordinatorOrFacultyMember())
        {
            userIsAllowed = true;
        }
        else if (EXPRecordIndustryPartnerHelper
                .isIndustryPartnerForStudentExperience(user,
                        studentStep.getStudentExperience()))
        {
            userIsAllowed = true;
        }

        return userIsAllowed;
    }

    public static void logStudentStepStatusOverrideByAdmin(
            EXPStudentExperienceStep studentStep, UserDetailsImpl user,
            String eventDataCode, String eventType, Locale locale)
    {
        PortalLog log = new PortalLog();
        log.setExpTermCourseStudent(studentStep.getStudent());
        log.setPortalUser(user);
        log.setEventData(PortalUtils.getMessageSource().getMessage(eventDataCode,
                new Object[] {
                        EXPHelper.getStepLabel(studentStep, locale.getLanguage()),
                        studentStep.getStep().getTct().getTermCourse()
                                .getTermCourseName(),
                        studentStep.getStudent().getStudent().getOwner()
                                .getFullNameWithUsername(),
                        user.getFullNameWithUsername() },
                locale));
        log.setEventDate(new Date());
        log.setI18nEventType(eventType);
        PortalUtils.getHt().saveOrUpdate(log);
    }

    public static EXPStudentExperienceStep getStudentStepForEXPModelKeyAndRecord(
            String expModelKey, EXPRecord record)
    {
        EXPStudentExperienceStep studentStep = null;
        int stepTypeIndex = EXPTermCourseTypeStepHelper
                .getStepTypeBasedOnExpModelKey(expModelKey);

        List<EXPStudentExperienceStep> studentStepResults = PortalUtils.getHt()
                .find("FROM EXPStudentExperienceStep s"
                        + " WHERE s.studentExperience = ? AND s.step.type = ?",
                        new Object[] {
                                record.getStudentStep().getStudentExperience(),
                                stepTypeIndex });
        if (!studentStepResults.isEmpty())
        {
            studentStep = studentStepResults.get(0);

        }
        return studentStep;
    }

    public static boolean hasRecord(EXPStudentExperienceStep studentStep)
    {
        return PortalUtils.getHt().findInt(
                "SELECT COUNT(r.id) FROM EXPRecord r WHERE r.studentStep.id=?",
                studentStep.getId()) > 0;
    }

    public static int countPreviousStepsToComplete(EXPStudentExperienceStep step)
    {
        List<Integer> statuses = getCurrentStepStatuses();
        statuses.add(EXPStudentExperienceStep.Status.PENDING.getValue());

        QueryBuilder q = new QueryBuilder();
        q.append(" select count(ses.id)");
        q.append(" from exp_student_experience_step ses");
        q.append(" join exp_term_course_type_step tcts on ses.step=tcts.id");
        q.append(" where ses.studentExperience = ?",
                step.getStudentExperience().getId());
        q.append(" and tcts.stepOrder < ?", step.getStep().getStepOrder());
        q.append(" and ");
        q.append(DBUtils.getInWhereClause("ses.status", statuses));
        q.append(" and tcts.optional <> 1");

        return PortalUtils.getJt().queryForInt(q);
    }

    public static void updateStatus(EXPStudentExperienceStep step,
            EXPStudentExperienceStep.Status status)
    {
        updateStatus(step, status, null);
    }

    public static void updateStatus(EXPStudentExperienceStep step,
            EXPStudentExperienceStep.Status status, UserDetailsImpl userLoggedIn)
    {
        EXPStudentExperienceStep.Status oldStatus = step.getStatusEnum();

        status.apply(step, userLoggedIn);
        PortalUtils.getHt().update(step);

        // EXPStudentExperienceStepLogFactory logFactory =
        // status.getLogFactory();
        // PortalLog pl = logFactory.create(step);
        // PortalUtils.getHt().save(pl);

        EXPStudentExperienceStepWorkflow workflow = step.getStep().getTypeEnum()
                .getExperienceStepWorkflow();
        workflow.afterStatusChange(oldStatus, status, step);

        updateCompetencyAssignments(step, oldStatus);
    }

    public static EXPExperienceTypeProfile getExperienceTypeProfile(
            EXPStudentExperienceStep step)
    {
        return EXPProfileHelper.getExperienceTypeProfile(
                step.getStudentExperience().getStudent().getStudent(),
                step.getStudentExperience().getTct().getType());
    }

    public static EXPExperienceTypeProfile getEnabledExperienceTypeProfile(
            EXPStudentExperienceStep step)
    {
        if (step.getStudentExperience().getTct().getType().isEnableProfiles())
        {
            return getExperienceTypeProfile(step);
        }
        return null;
    }

    public static WhereFragment getStudentRecordStepsWhereFragment(
            String termCourseTypeStepAlias, String termCourseStudentStatusAlias,
            String userAlias)
    {
        Where where = new Where();

        where.add(termCourseTypeStepAlias + ".type=?",
                EXPTermCourseTypeStep.Type.CREATE_A_RECORD.getValue());

        where.add(EXPTermCourseStudentHelper.getApprovedEnrollmentsWhereFragment(
                termCourseStudentStatusAlias, userAlias));

        return where.join();
    }

    public static WhereFragment getStudentRecordStepsSqlWhereFragment(
            String termCourseTypeStepAlias, String termCourseStudentStatusAlias,
            String userAlias)
    {
        Where where = new Where();

        where.add(termCourseTypeStepAlias + ".type=?",
                EXPTermCourseTypeStep.Type.CREATE_A_RECORD.getValue());

        where.add(EXPTermCourseStudentHelper.getApprovedEnrollmentsSqlWhereFragment(
                termCourseStudentStatusAlias, userAlias));

        return where.join();
    }

    public static List<EXPStudentExperienceStep> getUnqualifiedQuestionnaires(
            EXPExperienceType type)
    {
        QueryBuilder q = new QueryBuilder();
        q.append("select ses");
        q.append(" from EXPStudentExperienceStep ses");
        q.append(" join ses.step tcts");
        q.append(" join tcts.tct tct");
        q.append(" join tct.termCourse tc");
        q.append(" join tc.term t");
        q.append(" join ses.studentExperience.student tcs");
        q.append(" join tcs.status tcss");
        q.append(" join ses.owner u");
        q.append(" where ses.status=?",
                EXPStudentExperienceStep.Status.STARTED.getValue());
        q.append(" and tcts.type=?",
                EXPTermCourseTypeStep.Type.QUESTIONNAIRE_WITH_QUALIFIERS
                        .getValue());
        q.append(" and tct.type=?", type);
        q.append(" and ").append(EXPTermCourseStudentHelper
                .getActiveEnrollmentsWhereFragment("tcss", "u"));
        q.append(" and ").append(
                EXPTermCourseHelper.getActiveTermCourseWhereFragment("tc", "t"));

        List<EXPStudentExperienceStep> steps = PortalUtils.getHt().find(q);

        Map<EXPTermCourseTypeStep, List<Object[]>> qualifiers = new HashMap<>();

        for (EXPStudentExperienceStep step : steps)
        {
            qualifiers.computeIfAbsent(step.getStep(), DFHelper::getQualifiers);
        }

        steps.removeIf(studExpStep -> DFHelper.doesAnswerQualify(studExpStep,
                qualifiers.get(studExpStep.getStep())));

        return steps;
    }

    public static void createStudentExperienceSteps(int orderInTct,
            int termCourseId, int termCourseTypeId)
    {
        String now = DateUtils.getCurrentDateClause();

        Select select = new Select();
        select.add("tcs.id");
        select.add("tcts.id");
        select.add("se.id");
        select.add(new QueryBuilder(
                "case when se.status=? then ? when tcts.stepOrder=1 and se.status=? then ? else ? end",
                EXPStudentExperience.Status.COMPLETED.getValue(),
                Status.FINISHED.getValue(),
                EXPStudentExperience.Status.IN_PROGRESS.getValue(),
                Status.STARTED.getValue(), Status.PENDING.getValue()));
        select.add(new QueryBuilder(
                "case when se.status=? or (tcts.stepOrder=1 and se.status=?) then "
                        + now + " end",
                EXPStudentExperience.Status.COMPLETED.getValue(),
                EXPStudentExperience.Status.IN_PROGRESS.getValue()));
        select.add(new QueryBuilder("case when se.status=? then " + now + " end",
                EXPStudentExperience.Status.COMPLETED.getValue()));
        select.add("0");
        select.add("p.owner");

        QueryBuilder q = new QueryBuilder();
        q.append(" from exp_term_course_type_step tcts");
        q.append(" join exp_student_experience se on se.tct=tcts.tct");
        q.append(" join exp_term_course_student tcs on tcs.id=se.student");
        q.append(" join exp_student_profile p on p.id=tcs.studentProfile");
        q.append(" where tcs.termCourse=?", termCourseId);
        if (termCourseTypeId != -1)
        {
            q.append(" and tcts.tct=?", termCourseTypeId);
        }
        if (orderInTct != -1)
        {
            q.append(" and se.orderInTct=?", orderInTct);
        }
        q.append(
                " and not exists (select 1 from exp_student_experience_step ses where ses.studentExperience=se.id and ses.step=tcts.id)");

        QueryBuilder insertInto = DBUtils.getInsertIntoDFAnswerEntityQuery(
                "exp_student_experience_step",
                Lists.newArrayList("student", "step", "studentExperience", "status",
                        "dateStarted", "dateCompleted", "hoursTracked", "owner"),
                select, q);

        PortalUtils.getJt().update(insertInto);
    }

    public static JSONArray getStudentStepsGeoData(List<Integer> studentStepIds,
            HttpServletRequest request)
    {
        return EXPStudentExperienceStepGeoDataQuery.builder()//
                .studentStepIds(studentStepIds)//
                .request(request)//
                .build()//
                .find();
    }

    public static JSONObject getGeoData(EXPStudentExperienceStep step,
            HttpServletRequest request)
    {
        EXPTermCourseStudent tcs = step.getStudentExperience().getStudent();
        UserDetailsImpl user = tcs.getStudent().getOwner();
        AddressCoord geolocation = user.getGeolocation();
        if (geolocation == null)
        {
            return null;
        }
        Locale locale = PortalUtils.getLocale(request);
        return new EXPStudentExperienceStepGeoDataBuilder()//
                .id(step.getId())//
                .tcsId(tcs.getId())//
                .user(user, request)//
                .termCourse(tcs.getTermCourse(), locale)//
                .tct(step.getStep().getTct(), locale)//
                .locale(locale)//
                .build();
    }

    public static GridFilter getCompletedTermCourseProgressFilter(Locale locale)
    {
        GridFilter f = new GridFilter("stepProgress",
                PortalUtils.getMessageSource().getMessage(
                        "i18n.EXPStudentExperienceStepHelper.Completed1783031600870887",
                        null, locale),
                "custom", "stepProgress",
                Arrays.asList(NameValuePair.of("type", "complete")),
                Collections.EMPTY_MAP);
        return f;
    }

    public static GridFilter getIncompleteTermCourseProgressFilter(Locale locale)
    {
        GridFilter f = new GridFilter("stepProgress",
                PortalUtils.getMessageSource().getMessage(
                        "i18n.EXPStudentExperienceStepHelper.Incomplete0187180561789702",
                        null, locale),
                "custom", "stepProgress",
                Arrays.asList(NameValuePair.of("type", "incomplete")),
                Collections.EMPTY_MAP);
        return f;
    }

    /**
     * This can be used to determine if the experience still has incomplete steps
     * and can also be used to determine if there is a step in progress.
     * 
     * @param exp
     *            The experience to search in
     * @return If no steps are in progress ({@link Status#STARTED},
     *         {@link Status#PENDING_APPROVAL}, {@link Status#APPROVED}, or
     *         {@link Status#DECLINED}), then the first pending step is returned;
     *         otherwise returns the step that is currently in progress.
     */
    public static EXPStudentExperienceStep getNextStudentExperienceStep(
            EXPStudentExperienceStep step)
    {
        int stepOrder = step.getStep().getStepOrder();

        try {
            return (EXPStudentExperienceStep) PortalUtils
                    .getHt().execute(s -> {
                        QueryBuilder q = new QueryBuilder();
                        q.append(" select ses");
                        q.append(" from EXPStudentExperienceStep ses");
                        q.append(" join ses.step tcts");
                        q.append(" where ");
                        q.append(getUnfinishedStudentStepsWhereClause("ses",
                                step.getStudentExperience()));
                        q.append(" and ((tcts.stepOrder < ? and tcts.optional <> 1)",
                                stepOrder);
                        q.append(" or tcts.stepOrder > ?)", stepOrder);
                        q.append(" order by ses.status, tcts.stepOrder");

                        Query query = PortalUtils.getHt().createQuery(s, q);

                        query.setMaxResults(1);

                        return query.getSingleResult();
                    });
        }catch (NoResultException e) {
            return null;
        }
    }

    public static WhereFragment getUnfinishedStudentStepsWhereClause(
            String studentStepAlias, EXPStudentExperience exp)
    {
        Where where = new Where();

        where.add(studentStepAlias + ".studentExperience.id=?", exp.getId());
        where.add(studentStepAlias + ".status != ?",
                EXPStudentExperienceStep.Status.FINISHED.getValue());

        return where.join();
    }

    public static void finishStudentExpStep(EXPStudentExperienceStep step)
    {
        finishStudentExpStep(step, null);
    }

    public static void finishStudentExpStep(EXPStudentExperienceStep step,
            UserDetailsImpl userLoggedIn)
    {
        updateStatus(step, EXPStudentExperienceStep.Status.FINISHED, userLoggedIn);

        startStudentNextStep(step);
    }

    public static void startStudentNextStep(EXPStudentExperienceStep step)
    {
        EXPStudentExperience exp = step.getStudentExperience();

        EXPTypeWorkflowTemplate template = exp.getTct().getWorkflowTemplate();
        boolean enableStepCompletionInAnyOrder = template != null
                && template.isEnableStepCompletionInAnyOrder();
        if (!enableStepCompletionInAnyOrder
                || !hasStartedStudentExperienceStep(exp))
        {
            EXPStudentExperienceStep studentNextStep = getNextStudentExperienceStep(
                    step);

            if (studentNextStep != null)
            {
                if (canStepBeStarted(studentNextStep))
                {
                    startStudentExpStep(studentNextStep);
                }
            }
            else if (EXPWorkLogHelper.doesExperienceMeetWorkLogRequirements(exp))
            {
                EXPStudentExperienceHelper.completeExperience(exp);
            }
        }
    }

    public static void startStudentExpStep(EXPStudentExperienceStep studentStep)
    {
        EXPStudentExperienceStep.Status nextStatus = findStepNextStatus(
                studentStep);
        if (nextStatus != null)
        {
            updateStatus(studentStep, nextStatus);
        }
        else if (studentStep.getStatus() == EXPStudentExperienceStep.Status.PENDING
                .getValue())
        {
            updateStatus(studentStep, EXPStudentExperienceStep.Status.STARTED);
        }

        EXPStudentExperience exp = studentStep.getStudentExperience();
        if (exp.getStatus() == EXPStudentExperience.Status.PENDING.getValue())
        {
            exp.setStatus(EXPStudentExperience.Status.IN_PROGRESS.getValue());
            PortalUtils.getHt().saveOrUpdate(exp);

            // PortalUtils.getHt().save(
            // new EXPStudentExperienceInProgressLogFactory().create(exp));
        }

        EXPTermCourseStudent tcs = exp.getStudent();
        if (tcs.getStatus().getType() == EXPTermCourseStudentStatus.Type.PENDING
                .getValue())
        {
            tcs.setStatus(EXPTermCourseStudentStatusHelper
                    .getActiveStatus(tcs.getStudent().getModule()));
            PortalUtils.getHt().saveOrUpdate(tcs);

            // PortalUtils.getHt().save(new
            // EXPTermCourseStudentStatusLogFactory()
            // .create(studentStep.getStudent()));
        }

        if (studentStep.getStatus() == Status.FINISHED.getValue()
                || studentStep.getStep().isOptional())
        {
            startStudentNextStep(studentStep);
        }
    }

    @SuppressWarnings("unchecked")
    public static EXPStudentExperienceStep.Status findStepNextStatus(
            EXPStudentExperienceStep step)
    {
        EXPStudentExperienceStep.Status status = null;
        String expModelKey = EXPTermCourseTypeStepHelper
                .getExpModelKeyBasedOnStepType(step.getStep().getType());
        int previousStepsCount = EXPStudentExperienceStepHelper
                .countPreviousStepsToComplete(step);
        EXPTypeWorkflowTemplate workflowTemplate = step.getStep().getTct()
                .getWorkflowTemplate();
        boolean enableStepCompletionInAnyOrder = workflowTemplate != null
                && workflowTemplate.isEnableStepCompletionInAnyOrder();
        if (previousStepsCount > 0 && !enableStepCompletionInAnyOrder)
        {
            status = EXPStudentExperienceStep.Status.PENDING;
        }
        else if (StringUtils.isNotEmpty(expModelKey))
        {
            Class<?> recordFormClass = EXPRecordAbstractHelper
                    .getRecordFormClass(expModelKey);
            List<Object[]> recordsStatusStats = PortalUtils.getHt()
                    .find("select r.status, count(r.status) from "
                            + recordFormClass.getSimpleName()
                            + " r where r.studentStep.id=? group by r.status",
                            new Object[] { step.getId() });

            if (recordsStatusStats.isEmpty())
            {
                status = EXPStudentExperienceStep.Status.STARTED;
            }
            else
            {
                Map<Integer, Integer> resultMap = recordsStatusStats.stream()
                        .collect(Collectors.toMap(item -> (Integer) item[0],
                                item -> (Integer) item[1]));
                boolean isMainRecord = recordFormClass
                        .isAssignableFrom(EXPRecord.class);
                if (resultMap.get(EXPRecordAbstract.STATUS_PENDING) != null
                        && !isMainRecord)
                {
                    status = EXPStudentExperienceStep.Status.PENDING_APPROVAL;

                }
                else if (resultMap.get(EXPRecordAbstract.STATUS_APPROVED) != null
                        || resultMap.get(EXPRecordAbstract.STATUS_INFO_REQ) != null
                        || isMainRecord)
                {
                    status = EXPStudentExperienceStep.Status.STARTED;
                }
                else
                {
                    status = EXPStudentExperienceStep.Status.DECLINED;
                }
            }
        }
        else if (step.getStep().getTypeEnum().isReflection())
        {
            AcrmReflectionConfig reflectionConfig = step.getStep()
                    .getReflectionConfigurableSupport()
                    .getReflectionConfig(step.getStep());
            if ((!step.getReflectionRecordableSupport().canDisableAutoApproved()
                    || reflectionConfig.isAutoApproveReflections())
                    && reflectionConfig.isAllOnlySystemReflectionEnabled())
            {
                status = EXPStudentExperienceStep.Status.FINISHED;
            }
        }
        return status;
    }

    public static WhereFragment getCurrentStepWhereClause(String studentStepAlias)
    {
        return DBUtils.getInWhereClause(studentStepAlias + ".status",
                getCurrentStepStatuses());
    }

    /**
     * The current step is the step that is currently in progress
     */
    public static List<Integer> getCurrentStepStatuses()
    {
        List<Integer> ret = new ArrayList<>();

        ret.add(EXPStudentExperienceStep.Status.STARTED.getValue());

        ret.add(EXPStudentExperienceStep.Status.PENDING_APPROVAL.getValue());

        return ret;
    }

    public static List<ConfigLevel> getCompetencyConfigs(
            EXPStudentExperienceStep studentStep)
    {
        List<ConfigLevel> configs = new ArrayList<>();

        EXPPosting posting = PortalUtils.getHt().getFirst("select er.posting " //
                + "from EXPRecord er " //
                + "where er.studentStep.studentExperience=?",
                studentStep.getStudentExperience());
        ConfigLevel postingConfigLevel = new ConfigLevel(EXPPostingCompetency.class,
                posting, true);
        if (posting != null && postingConfigLevel.hasCompetencies())
        {
            configs.add(postingConfigLevel);
        }
        else
        {
            configs.add(new ConfigLevel(EXPTermCourseTypeStepCompetency.class,
                    studentStep.getStep()));
        }

        configs.add(new ConfigLevel(EXPExperienceTypeCompetency.class,
                studentStep.getStep().getTct().getType()));
        configs.add(new ConfigLevel(EXPModuleCompetency.class,
                studentStep.getStep().getTct().getType().getModule()));

        return configs;
    }

    public static void updateCompetencyAssignments(EXPStudentExperienceStep step,
            Status oldStatus)
    {
        List<ConfigLevel> levels = EXPStudentExperienceStepHelper
                .getCompetencyConfigs(step);
        int competencyUseCase = CompetencyAchievedHelper
                .getCompetencyUsecase(levels);
        Status status = step.getStatusEnum();

        if ((Status.STARTED.equals(oldStatus)
                || Status.PENDING_APPROVAL.equals(oldStatus)
                || Status.FINISHED.equals(oldStatus))
                && !status.equals(Status.STARTED)
                && !status.equals(Status.PENDING_APPROVAL)
                && !status.equals(Status.FINISHED))
        {
            CompetencyAchievedHelper.unassignCompetencies(
                    EXPStudentExperienceStepAchieved.class, step);
        }
        if (competencyUseCase == Competenciable.COMP_ASSIGNED
                && !step.getStep().isEntityCompetencyTracking())
        {
            int count = CompetencyAchievedHelper.getAchievedCompetenciesCount(step);
            if (count == 0
                    && (status.equals(Status.STARTED)
                            || status.equals(Status.PENDING_APPROVAL)
                            || status.equals(Status.FINISHED))
                    && !oldStatus.equals(Status.STARTED)
                    && !oldStatus.equals(Status.PENDING_APPROVAL)
                    && !oldStatus.equals(Status.FINISHED))
            {
                CompetencyAchievedHelper.assignCompetencies(
                        EXPStudentExperienceStepAchieved.class, step,
                        EXPStudentExperienceStepHelper.getCompetencyConfigs(step));
            }
        }
    }

    public static List<String> getStudentsEmailsList(List<Integer> stepIds)
    {
        String ids = DBUtils.buildInClause(stepIds);
        return PortalUtils.getHt().find(
                "select distinct s.student.student.owner.emailAddress from EXPStudentExperienceStep s join s.student.student.owner where s.id in "
                        + ids);
    }

    public static void testQualifiersStepAndUpdateStatus(
            EXPStudentExperienceStep step)
    {
        EXPTermCourseTypeStep termCourseTypeStep = step.getStep();
        if (termCourseTypeStep.getType() == EXPTermCourseTypeStep.Type.QUALIFIERS
                .getValue())
        {
            if (QFQualifierEntityHelper.testStudent(termCourseTypeStep, step))
            {
                EXPStudentExperienceStepHelper.finishStudentExpStep(step);
            }
            else if (QFQualifierEntityHelper
                    .testStudentExceptWaitingForApproval(termCourseTypeStep, step))
            {
                EXPStudentExperienceStepHelper.updateStatus(step,
                        EXPStudentExperienceStep.Status.PENDING_APPROVAL);
            }
        }
    }

    public static int countQualifiersPendingApproval(EXPStudentExperienceStep step)
    {
        int count = 0;

        EXPTermCourseTypeStep termCourseTypeStep = step.getStep();
        if (termCourseTypeStep.getType() == EXPTermCourseTypeStep.Type.QUALIFIERS
                .getValue())
        {
            QueryBuilder query = new QueryBuilder()
                    .append("select count(sq.id) from ")
                    .append(QFStudentQualificationsHql.getFromClauseFragment("q",
                            "qt", "sq", "u"))
                    .append(" where ")
                    .append(QFStudentQualificationsHql.getWhereClause(
                            termCourseTypeStep, step, true, "q", "qt", "sq"));
            count = PortalUtils.getHt().findInt(query);
        }

        return count;
    }

    public static void approveAllStudentQualifications(
            EXPStudentExperienceStep step)
    {
        updateAllStudentQualificationsStatus(step, true);
    }

    public static void resetToPendingAllStudentQualifications(
            EXPStudentExperienceStep step)
    {
        updateAllStudentQualificationsStatus(step, false);
    }

    private static void updateAllStudentQualificationsStatus(
            EXPStudentExperienceStep step, boolean approve)
    {
        EXPTermCourseTypeStep termCourseTypeStep = step.getStep();
        if (termCourseTypeStep.getType() == EXPTermCourseTypeStep.Type.QUALIFIERS
                .getValue())
        {
            QueryBuilder query = new QueryBuilder()
                    .append("update sq set approved=" + (approve ? "1" : "0"))
                    .append(" from qf_student_qualification sq")
                    .append(" join qf_qualification_test qt on sq.qualificationTest = qt.id")
                    .append(" join qf_qualifier q on qt.qualifier = q.id")
                    .append(" where sq." + step.getStudentQualificationField()
                            + "=?", step.getId())
                    .append(" and qt."
                            + termCourseTypeStep.getQualificationTestField() + "=?",
                            termCourseTypeStep.getId())
                    .append(" and q.requiresApproval=1");
            PortalUtils.getJt().update(query);
        }
    }

    public static void refreshQualifiersStepStatus(HttpServletRequest request,
            ModelAndView mv)
    {
        QFQualifierEntity qualifierEntity = QFQualifierEntityHelper
                .getQualifierEntity(request);
        if (qualifierEntity instanceof EXPTermCourseTypeStep)
        {
            EXPStudentExperienceStep studentStep = (EXPStudentExperienceStep) QFStudentEntityHelper
                    .getStudentEntity(request);
            testQualifiersStepAndUpdateStatus(studentStep);
            request.setAttribute("tcsId", studentStep.getStudent().getId());
        }
    }

    public static OrbisHqlResultSet getBlockingGateStep(OrbisHqlResultSet step,
            List<OrbisHqlResultSet> allSteps)
    {
        OrbisHqlResultSet blockingGateStep = null;

        int stepIndex = allSteps.indexOf(step);
        for (int i = stepIndex - 1; i >= 0; i--)
        {
            OrbisHqlResultSet sesStep = allSteps.get(i);
            OrbisHqlResultSet tctStep = (OrbisHqlResultSet) sesStep.get("step");
            Integer stepType = (Integer) tctStep.get("type");
            boolean stepIsOptional = (boolean) tctStep.getOrDefault("optional",
                    false);
            boolean stepIsFinished = sesStep.get("status")
                    .equals(EXPStudentExperienceStep.Status.FINISHED.getValue());
            if (EXPTermCourseTypeStep.Type.of(stepType).isGateStep()
                    && !stepIsFinished && !stepIsOptional)
            {
                blockingGateStep = sesStep;
                break;
            }
        }

        return blockingGateStep;
    }

    public static boolean areAllPreviousStepsCompleted(OrbisHqlResultSet step,
            List<OrbisHqlResultSet> allSteps)
    {
        boolean allPreviousStepsAreCompleted = true;

        int stepIndex = allSteps.indexOf(step);
        for (int i = stepIndex - 1; i >= 0; i--)
        {
            OrbisHqlResultSet sesStep = allSteps.get(i);
            OrbisHqlResultSet tctStep = (OrbisHqlResultSet) sesStep.get("step");
            boolean stepIsOptional = (boolean) tctStep.getOrDefault("optional",
                    false);
            boolean stepIsFinished = sesStep.get("status")
                    .equals(EXPStudentExperienceStep.Status.FINISHED.getValue());
            if (!stepIsFinished && !stepIsOptional)
            {
                allPreviousStepsAreCompleted = false;
                break;
            }
        }

        return allPreviousStepsAreCompleted;
    }

    private static boolean canStepBeStarted(EXPStudentExperienceStep studentStep)
    {
        boolean stepCanBeStarted = true;

        EXPTypeWorkflowTemplate workflowTemplate = studentStep.getStep().getTct()
                .getWorkflowTemplate();
        if (workflowTemplate != null
                && workflowTemplate.isEnableStepCompletionInAnyOrder())
        {
            EXPTermCourseTypeStep.Type stepType = studentStep.getStep()
                    .getTypeEnum();

            if (stepType.isGateStep() && !studentStep.getStep().isOptional())
            {
                stepCanBeStarted = areAllPreviousStepsCompleted(studentStep);
            }
            else
            {
                stepCanBeStarted = isStepBlockedByGateStep(studentStep);
            }

            if (stepCanBeStarted && stepType.isRelatedToSubrecordForm())
            {
                EXPRecord mainRecord = EXPRecordHelper.getExperienceMainRecord(
                        studentStep.getStudentExperience().getId());
                stepCanBeStarted = mainRecord != null;
            }
        }

        return stepCanBeStarted;
    }

    private static boolean isStepBlockedByGateStep(
            EXPStudentExperienceStep studentStep)
    {
        EXPStudentExperience studentExperience = studentStep.getStudentExperience();
        EXPTermCourseTypeStep tctStep = studentStep.getStep();
        List<Integer> gateStepTypes = Arrays
                .stream(EXPTermCourseTypeStep.Type.values())
                .filter(EXPTermCourseTypeStep.Type::isGateStep)
                .map(EXPTermCourseTypeStep.Type::getValue)
                .collect(Collectors.toList());

        QueryBuilder blockingGateStepQuery = new QueryBuilder()
                .append(" select count(ses.id)")
                .append(" from EXPStudentExperienceStep ses")
                .append(" join ses.step tcts") //
                .append(" where ")
                .append(getUnfinishedStudentStepsWhereClause("ses",
                        studentExperience))
                .append(" and tcts.type in " + DBUtils.buildInClause(gateStepTypes))
                .append(" and tcts.stepOrder < ?", tctStep.getStepOrder())
                .append(" and tcts.optional <> 1");
        return ((Integer) PortalUtils.getHt().getFirst(blockingGateStepQuery)) == 0;
    }

    private static boolean areAllPreviousStepsCompleted(
            EXPStudentExperienceStep studentStep)
    {
        EXPStudentExperience studentExperience = studentStep.getStudentExperience();
        EXPTermCourseTypeStep tctStep = studentStep.getStep();

        QueryBuilder unfinishedPreviousStepsQuery = new QueryBuilder()
                .append(" select count(ses.id)")
                .append(" from EXPStudentExperienceStep ses")
                .append(" join ses.step tcts") //
                .append(" where ")
                .append(getUnfinishedStudentStepsWhereClause("ses",
                        studentExperience))
                .append(" and tcts.stepOrder < ?", tctStep.getStepOrder())
                .append(" and tcts.optional <> 1");
        return ((Integer) PortalUtils.getHt()
                .getFirst(unfinishedPreviousStepsQuery)) == 0;
    }

    private static boolean hasStartedStudentExperienceStep(
            EXPStudentExperience studentExperience)
    {
        QueryBuilder startedStepsQuery = new QueryBuilder()
                .append(" select count(ses.id) from EXPStudentExperienceStep ses")
                .append(" join ses.step tcts")
                .append(" where ses.studentExperience=?", studentExperience)
                .append(" and ses.status = ?", Status.STARTED.getValue())
                .append(" and tcts.optional <> 1");
        return ((Integer) PortalUtils.getHt().getFirst(startedStepsQuery)) > 0;
    }

    public static void populateStepStatusesCounts(OrbisHqlResultSet step)
    {
        int stepId = step.select("id");
        List<OrbisHqlResultSet> statusesByStep = PortalUtils.getHt()
                .f("select s.status, count(s.id) from EXPStudentExperienceStep s " +
                        "where s.step.id=? group by s.status", stepId);
        int totalStatusesCount = PortalUtils.getHt()
                .findInt("select count(s.id) from EXPStudentExperienceStep s " +
                        "where s.step.id=?", stepId);

        step.put("total", totalStatusesCount);
        statusesByStep.forEach(rs -> step.put(Status.of(rs.select("status"))
                .name().toLowerCase(), rs.select("id.count")));
    }

    public static void setStatusStartedForNextStepAfterDeletedStep(
            EXPTermCourseTypeStep tcts)
    {
        // For all student steps that are in progress on the step being
        // deleted, start the next step if it is pending
        QueryBuilder qb = new QueryBuilder();
        qb.append(" select nextSes");
        qb.append(" FROM EXPStudentExperienceStep thisSes,");
        qb.append(" EXPStudentExperienceStep nextSes");
        qb.append(" WHERE ");
        qb.append(EXPStudentExperienceStepHelper
                .getCurrentStepWhereClause("thisSes"));
        qb.append(" AND nextSes.status = ?",
                EXPStudentExperienceStep.Status.PENDING.getValue());
        qb.append(" AND thisSes.step = ?", tcts);
        qb.append(" and nextSes.studentExperience=thisSes.studentExperience");
        qb.append(" and not exists (");
        qb.append("   select midSes.id");
        qb.append("   from EXPStudentExperienceStep midSes");
        qb.append("   where thisSes.step.stepOrder < midSes.step.stepOrder");
        qb.append("   and midSes.step.stepOrder < nextSes.step.stepOrder");
        qb.append("   and midSes.studentExperience=thisSes.studentExperience");
        qb.append(" )");
        List<EXPStudentExperienceStep> studentNextSteps = PortalUtils.getHt()
                .find(qb);

        for (EXPStudentExperienceStep nextStep : studentNextSteps)
        {
            EXPStudentExperienceStepHelper.updateStatus(nextStep,
                    EXPStudentExperienceStep.Status.STARTED);
        }
    }

    public static int getRecordOrFinishedStepCount(EXPTermCourseType tct)
    {
        return PortalUtils.getHt().findInt(
                "select count(ss.id) from EXPRecord r right join r.studentStep ss " +
                        "where ss.step.tct=? and (ss.status=? or r.id is not null)",
                tct, EXPStudentExperienceStep.Status.FINISHED.getValue());
    }

    public static List<Integer> getActiveTctStepIds(int tctId)
    {
        QueryBuilder q = new QueryBuilder();
        q.append(
                " SELECT DISTINCT tcts.id FROM EXPStudentExperienceStep ses JOIN ses.step tcts WHERE ses.status != ?",
                EXPStudentExperienceStep.Status.PENDING.getValue());
        q.append(" AND tcts.tct.id = ?", tctId);

        return PortalUtils.getHt().find(q);
    }

    public static Map<Integer, String> getStepStatusToLabelMap(Locale locale)
    {
        Map<Integer, String> stepStatusMap = new HashMap<>();
        stepStatusMap.put(EXPStudentExperienceStep.Status.PENDING.getValue(),
                PortalUtils.getMessageSource().getMessage(
                        "i18n.EXPController.Pending4560273966240058", null,
                        locale));
        stepStatusMap.put(EXPStudentExperienceStep.Status.STARTED.getValue(),
                PortalUtils.getMessageSource().getMessage(
                        "i18n.EXPController.Started8195700617433138", null,
                        locale));
        stepStatusMap.put(EXPStudentExperienceStep.Status.FINISHED.getValue(),
                PortalUtils.getMessageSource().getMessage(
                        "i18n.EXPController.Finished0827715257221685", null,
                        locale));
        stepStatusMap.put(EXPStudentExperienceStep.Status.DECLINED.getValue(),
                PortalUtils.getMessageSource().getMessage(
                        "i18n.EXPController.Declined9630151344694056", null,
                        locale));
        stepStatusMap.put(
                EXPStudentExperienceStep.Status.PENDING_APPROVAL.getValue(),
                PortalUtils.getMessageSource().getMessage(
                        "i18n.EXPController.PendingApp7984860181327967", null,
                        locale));
        return stepStatusMap;
    }

    public static String convertStatusToLabel(NameConversionParameters params)
    {
        final Object key = params.getKey();
        final Locale locale = params.getLocale();

        final String statusLabel;
        if (key instanceof Integer && locale != null)
        {
            statusLabel = EXPStudentExperienceStepHelper.getStepStatusToLabelMap(
                    locale).get((Integer) key);
        }
        else
        {
            statusLabel = "";
        }

        return statusLabel;
    }

    public static List<OrbisHqlResultSet> getStudentEmailAddressAndUsernameByTermCourseTypeStepId(
            int termCourseTypeStepId, boolean sendReminderToStarted,
            boolean sendReminderToDeclined)
    {
        return PortalUtils.getHt().f(
                "select s.student.student.owner.emailAddress, s.student.student.owner.username "
                        + "from EXPStudentExperienceStep s where s.step.id=? "
                        + (!sendReminderToStarted ? " and s.status != 1 " : "")
                        + (!sendReminderToDeclined ? " and s.status != 4 " : ""),
                termCourseTypeStepId);
    }

    public static void sendRemindEmailForStudentsOnStep(UserDetailsImpl sender,
            String subject, String email, Integer tctsId,
            boolean sendReminderToStarted, boolean sendReminderToDeclined)
    {
        List<OrbisHqlResultSet> studentEmailsAndUsernames = EXPStudentExperienceStepHelper
                .getStudentEmailAddressAndUsernameByTermCourseTypeStepId(tctsId,
                        sendReminderToStarted, sendReminderToDeclined);

        for (OrbisHqlResultSet entry : studentEmailsAndUsernames)
        {
            String studentEmailAddress = entry
                    .select("student.student.owner.emailAddress");
            String username = entry.select("student.student.owner.username");

            EmailUtils.sendAndLog1Email(new EmailMessage(sender.getEmailAddress(),
                    sender, studentEmailAddress, username, subject, email, email,
                    null, false, false));
        }
    }

    public static List<EXPStudentExperienceStep> getStudentExperienceSteps(
            EXPStudentExperience studentExperience)
    {
        return PortalUtils.getHt()
                .find("from EXPStudentExperienceStep step " +
                        "where step.studentExperience=?", studentExperience);
    }

    public static List<EXPStudentExperienceStep> getPreviousCompetencySteps(
            EXPStudentExperienceStep currentStudentStep)
    {
        List<EXPStudentExperienceStep> steps = EXPStudentExperienceStepHelper
                .getStudentExperienceSteps(
                        currentStudentStep.getStudentExperience());
        return steps.stream()
                .filter(step -> !step.getId().equals(currentStudentStep.getId()))
                .filter(step -> step.getStep().getCompetencyUsecase() != Competenciable.COMP_DISABLED)
                .filter(step -> CompetencyAchievedHelper
                        .getAchievedCompetenciesCount(step) > 0)
                .collect(Collectors.toList());
    }

    public static int getCompetencyCountOnExperienceExceptCurrentStep(
            EXPStudentExperienceStep currentStudentStep)
    {
        int count = 0;
        List<EXPStudentExperienceStep> competencySteps = getPreviousCompetencySteps(
                currentStudentStep);
        for (EXPStudentExperienceStep step : competencySteps)
        {
            EXPTermCourseTypeStep tcts = step.getStep();
            boolean supervisorCompetenciesEnabled =
                    EXPTermCourseTypeStep.Type.COMPETENCY.getValue() == tcts.getType()
                    && tcts.isEnableCompetencySupervisor();

            count += supervisorCompetenciesEnabled ?
                    CompetencyAchievedHelper
                            .getAchievedCompetenciesCountIncludeSupervisor(step)
                    : CompetencyAchievedHelper.getAchievedCompetenciesCount(step);
        }
        return count;
    }
}
