package com.orbis.web.content.exp.step.systemActivity;

import static com.orbis.portal.PortalUtils.getHt;
import static com.orbis.portal.PortalUtils.getJt;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import org.springframework.jdbc.core.PreparedStatementCreator;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.web.servlet.ModelAndView;

import com.orbis.portal.PortalUtils;
import com.orbis.utils.CollectionUtils;
import com.orbis.utils.DBUtils;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.QueryBuilder;
import com.orbis.web.content.FlatContentItem;
import com.orbis.web.content.ec.outlook.ECOutlook;
import com.orbis.web.content.ec.outlook.ECOutlookCoop;
import com.orbis.web.content.ec.outlook.ECOutlookHelper;
import com.orbis.web.content.ec.outlook.ECOutlookNPEmploymentRecord;
import com.orbis.web.content.exp.EXPStudentExperienceStep;
import com.orbis.web.content.exp.EXPTermCourseTypeStepHelper;
import com.orbis.web.content.exp.step.systemActivity.config.ExpSystemActivityStepCriterion;
import com.orbis.web.content.exp.step.systemActivity.config.ExpSystemActivityStepCriterionCategory;
import com.orbis.web.content.exp.step.systemActivity.config.ExpSystemActivityType;
import com.orbis.web.content.exp.step.systemActivity.resolver.EXPStepSystemActivityCCRDataResolver;
import com.orbis.web.content.exp.step.systemActivity.resolver.EXPStepSystemActivityCoopDataResolver;
import com.orbis.web.content.exp.step.systemActivity.resolver.EXPStepSystemActivityDataResolver;
import com.orbis.web.content.exp.step.systemActivity.resolver.EXPStepSystemActivityEventDataResolver;
import com.orbis.web.content.exp.step.systemActivity.resolver.EXPStepSystemActivityExpDataResolver;
import com.orbis.web.content.exp.step.systemActivity.resolver.EXPStepSystemActivityMentorshipDataResolver;
import com.orbis.web.content.exp.step.systemActivity.resolver.EXPStepSystemActivityProgramDataResolver;
import com.orbis.web.content.exp.step.systemActivity.resolver.EXPStepSystemActivitySADataResolver;
import com.orbis.web.content.sa.SAHelper;
import com.orbis.web.content.sa.SAModule;
import com.orbis.web.site.SiteElement;

public final class EXPStepSystemActivityHelper
{

    private EXPStepSystemActivityHelper()
    {
    }

    private static final Map<String, EXPStepSystemActivityDataResolver> resolvers;

    static
    {
        resolvers = new HashMap<>();
        resolvers.put(ExpSystemActivityType.PROGRAM.getOutlookUUID(),
                new EXPStepSystemActivityProgramDataResolver());
        resolvers.put(ExpSystemActivityType.MENTORSHIP.getOutlookUUID(),
                new EXPStepSystemActivityMentorshipDataResolver());
        resolvers.put(ExpSystemActivityType.CO_CURRICULAR.getOutlookUUID(),
                new EXPStepSystemActivityCCRDataResolver());
        resolvers.put(ExpSystemActivityType.CO_OP.getOutlookUUID(),
                new EXPStepSystemActivityCoopDataResolver());
        resolvers.put(ExpSystemActivityType.EVENT.getOutlookUUID(),
                new EXPStepSystemActivityEventDataResolver());
        resolvers.put(ExpSystemActivityType.APPOINTMENT.getOutlookUUID(),
                new EXPStepSystemActivitySADataResolver());
        resolvers.put(ExpSystemActivityType.EXPERIENTIAL_EDUCATION.getOutlookUUID(),
                new EXPStepSystemActivityExpDataResolver());
    }

    public static EXPStepSystemActivityDataResolver getDataResolver(
            String outlookUUID)
    {
        return resolvers.get(outlookUUID);
    }

    public static void saveExpRequireSystemActivityStepCriterion(ECOutlook outlook,
            Integer moduleId, Integer tctStepId, List<String> categoryIds)
    {

        final Integer criterionId;
        Optional<ExpSystemActivityStepCriterion> criterion = getHt().findFirst(
                "from ExpSystemActivityStepCriterion where step=?", tctStepId);
        if (criterion.isPresent())
        {
            ExpSystemActivityStepCriterion get = criterion.get();
            criterionId = get.getId();
            if (!get.getOutlookUUID().equals(outlook.getUuid())
                    || get.getModule().getContentItemId() != moduleId)
            {
                get.setOutlookUUID(outlook.getUuid());
                get.setModule(FlatContentItem.of(outlook.getModuleClass().getName(),
                        moduleId));
            }
            getHt().saveOrUpdate(get);
        }
        else
        {
            KeyHolder keyHolder = new GeneratedKeyHolder();
            getJt().update(new PreparedStatementCreator()
            {
                public PreparedStatement createPreparedStatement(
                        Connection connection) throws SQLException
                {
                    PreparedStatement ps = connection.prepareStatement(
                            "INSERT INTO exp_system_activity_step_criterion (outlookUUID, step, contentItemClass, contentItemId) values (?,?,?,?)",
                            new String[] { "id" });
                    ps.setString(1, outlook.getUuid());
                    ps.setInt(2, tctStepId);
                    ps.setString(3, outlook.getModuleClass().getName());
                    ps.setInt(4, moduleId);
                    return ps;
                }
            }, keyHolder);
            criterionId = keyHolder.getKey().intValue();
        }
        getJt().update(
                "DELETE FROM exp_system_activity_step_criterion_category WHERE criterion=?",
                new Object[] { criterionId });

        boolean isCoop = outlook instanceof ECOutlookCoop
                || outlook instanceof ECOutlookNPEmploymentRecord;
        getJt().orbisBatchUpdate(
                "INSERT INTO exp_system_activity_step_criterion_category (criterion, contentItemClass, contentItemId) values (?,?,?)",
                categoryIds, (ps, item, i) -> {
                    ps.setInt(1, criterionId);
                    if (!isCoop)
                    {
                        ps.setString(2, outlook.getCategoryClass().getName());
                        ps.setInt(3, Integer.parseInt(item));
                    }
                    else
                    {
                        ps.setString(2, item);
                        ps.setInt(3, 1);
                    }
                });

    }

    public static Map<String, Object> populateSystemActivityStepView(
            ModelAndView mv, List<Integer> activitySteps, Integer ownerId)
    {
        List<Map<String, Object>> types = PortalUtils.getJt().queryForList(
                "select distinct outlookUUID, step from exp_system_activity_step_criterion where step in"
                        + DBUtils.buildInClause(activitySteps));
        Map<String, List<Integer>> outlookUUIDStepMap = types.stream()
                .collect(Collectors.groupingBy(
                        item -> (String) item.get("outlookUUID"),
                        Collectors.mapping(item -> (Integer) item.get("step"),
                                Collectors.toList())));
        Map<Integer, EXPStepSystemActivityDataResolver.ActivityProgress> activityProgress = new HashMap<>();
        outlookUUIDStepMap.entrySet().forEach(entry -> {
            EXPStepSystemActivityDataResolver dataResolver = EXPStepSystemActivityHelper
                    .getDataResolver(entry.getKey());
            if (dataResolver != null)
            {
                activityProgress.putAll(dataResolver
                        .getActivityProgress(entry.getValue(), ownerId));
            }
        });
        Map<Object, Object> activityTypes = types.stream().collect(Collectors
                .toMap(item -> item.get("step"), item -> item.get("outlookUUID")));

        if (mv != null)
        {
            mv.addObject("activityTypes", activityTypes);
            mv.addObject("activityProgress", activityProgress);
        }

        return CollectionUtils.<String, Object> mapBuilder()
                .put("activityTypes", activityTypes)
                .put("activityProgress", activityProgress).build();
    }

    @SuppressWarnings("unchecked")
    public static void populateExpSystemActivityStepSelectedCategories(
            ModelAndView mv, ECOutlook outlook, String criterionId)
    {
        List<ExpSystemActivityStepCriterionCategory> c = PortalUtils.getHt().find(
                "select c from ExpSystemActivityStepCriterionCategory c join c.criterion cc where  c.criterion.id=? and cc.outlookUUID=?",
                new Object[] { Integer.valueOf(criterionId), outlook.getUuid() });
        mv.addObject("selectedCategories",
                c.stream().map(ExpSystemActivityStepCriterionCategory::getCategory)
                        .map(fc -> outlook instanceof ECOutlookCoop
                                ? fc.getContentItemClass()
                                : fc.getContentItemId())
                        .collect(Collectors.toList()));
    }

    @SuppressWarnings("unchecked")
    public static void populateSystemActivityStepAvailAppointments(
            EXPStudentExperienceStep studentStep, ModelAndView mv, Locale locale)
    {
        String nameProperty = LocaleUtils.isL1(locale) ? "name" : "l2Name";
        mv.addObject("studentStep", studentStep);

        Optional<ExpSystemActivityStepCriterion> first = getHt().findFirst(
                "from ExpSystemActivityStepCriterion c where c.module.contentItemClass=? and c.step.id=?",
                new Object[] { SAModule.class.getName(),
                        studentStep.getStep().getId() });
        if (first.isPresent())
        {
            ExpSystemActivityStepCriterion criterion = first.get();
            Integer moduleId = criterion.getModule().getContentItemId();
            List<Object[]> memberProviders = SAHelper
                    .getMemberProviders(criterion.getId());
            String[] providers = new String[memberProviders.size()];
            for (int i = 0; i < memberProviders.size(); i++)
            {
                providers[i] = memberProviders.get(i)[0].toString();
            }
            SAModule saModule = (SAModule) PortalUtils.getSiteManager()
                    .getElementsByTypeAndContentItemId("saController", moduleId)
                    .stream().findFirst()
                    .map(se -> ((SiteElement) se).getContentItem()).orElse(null);

            mv.addObject("studentStep", studentStep);
            QueryBuilder qb = new QueryBuilder();
            qb.append("select t." + nameProperty + " type");
            qb.append(" from exp_system_activity_step_criterion_category cc");
            qb.append(" left join sa_type t on cc.contentItemId = t.id");
            qb.append(" where cc.criterion =?", criterion.getId());
            List<Map<String, Object>> list = getJt().queryForList(qb);
            List<String> saTypes = list.stream()
                    .map(item -> (String) item.get("type"))
                    .collect(Collectors.toList());

            if (saTypes.isEmpty())
            {
                Optional<ECOutlook> outlook = EXPTermCourseTypeStepHelper
                        .getSystemActivityTaskOutlooks(
                                ExpSystemActivityType.APPOINTMENT.getOutlookUUID());
                saTypes = outlook
                        .map(o -> ECOutlookHelper.getOutlookCategoriesForModule(o,
                                moduleId))
                        .stream().flatMap(Collection::stream)
                        .map(item -> (String) item[1]).collect(Collectors.toList());
            }

            if (saModule != null)
            {
                mv.addObject("availSlots",
                        SAHelper.getAvailableOpenAppointmentsForCustomer(saModule,
                                studentStep.getOwner(), " order by s.fromDate",
                                saTypes.toArray(new String[0]), providers).stream()
                                .limit(10).collect(Collectors.toList()));
            }
            if (!saTypes.isEmpty())
            {
                mv.addObject("typeFilter", saTypes.get(0));
            }
        }

    }

}
