package com.orbis.web.content.exp;

import java.util.Date;

import org.hibernate.annotations.ColumnDefault;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.df.DFAnswerEntity;
import com.orbis.df.DFModel;
import com.orbis.portal.PortalUtils;
import com.orbis.qf.QFQualifierEntity;
import com.orbis.qf.QFStudentEntity;
import com.orbis.web.content.acrm.competency.CompetencyAchievable;
import com.orbis.web.content.acrm.competency.CompetencyAchievableWorkflow;
import com.orbis.web.content.acrm.reflection.record.assign.AcrmReflectionRecordEXPStudentExperienceStep;
import com.orbis.web.content.acrm.reflection.record.recordable.AcrmReflectionRecordable;
import com.orbis.web.content.grid.NameConversionParameters;
import com.orbis.web.content.os.OSOrder;
import com.orbis.web.content.sa.SASlot;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;

@Access(value = AccessType.FIELD)
public class EXPStudentExperienceStep extends DFAnswerEntity implements
        AcrmReflectionRecordable<EXPStudentExperienceStep, AcrmReflectionRecordEXPStudentExperienceStep>,
        CompetencyAchievable, QFStudentEntity
{
    private static final long serialVersionUID = 4373177164961510002L;

    private EXPTermCourseStudent student;

    private EXPTermCourseTypeStep step;

    private EXPStudentExperience studentExperience;

    private Date dateTocAccepted;

    @ColumnDefault("0")
    private int status;

    private Date dateCompleted;

    private Date dateApproved;

    private Date dateStarted;

    private Date dateDeclined;

    private UserDetailsImpl approvedBy;

    private UserDetailsImpl declinedBy;

    private OSOrder stepPurchaseOrder;

    private SASlot attendedSlot;

    @ColumnDefault("0")
    private double hoursTracked;

    @ColumnDefault("0")
    private boolean supervisorCompetenciesEmailSent;

    /***
     * set to true if the admin marked the step as completed even if the
     * required hours/entries have not been completed
     */
    @ColumnDefault("0")
    private boolean overrideCompletion;

    public EXPTermCourseStudent getStudent()
    {
        return student;
    }

    public void setStudent(EXPTermCourseStudent student)
    {
        this.student = student;
    }

    @Override
    public String getTableName()
    {
        return "exp_student_experience_step";
    }

    public EXPTermCourseTypeStep getStep()
    {
        return step;
    }

    public void setStep(EXPTermCourseTypeStep step)
    {
        this.step = step;
    }

    public Status getStatusEnum()
    {
        return Status.of(status);
    }

    public int getStatus()
    {
        return status;
    }

    public void setStatus(int status)
    {
        this.status = status;
    }

    public Date getDateCompleted()
    {
        return dateCompleted;
    }

    public void setDateCompleted(Date dateCompleted)
    {
        this.dateCompleted = dateCompleted;
    }

    public Date getDateApproved()
    {
        return dateApproved;
    }

    public void setDateApproved(Date dateApproved)
    {
        this.dateApproved = dateApproved;
    }

    public Date getDateStarted()
    {
        return dateStarted;
    }

    public void setDateStarted(Date dateStarted)
    {
        this.dateStarted = dateStarted;
    }

    public Date getDateDeclined()
    {
        return dateDeclined;
    }

    public void setDateDeclined(Date dateDeclined)
    {
        this.dateDeclined = dateDeclined;
    }

    public UserDetailsImpl getApprovedBy()
    {
        return approvedBy;
    }

    public void setApprovedBy(UserDetailsImpl approvedBy)
    {
        this.approvedBy = approvedBy;
    }

    public UserDetailsImpl getDeclinedBy()
    {
        return declinedBy;
    }

    public void setDeclinedBy(UserDetailsImpl declinedBy)
    {
        this.declinedBy = declinedBy;
    }

    public Date getDateTocAccepted()
    {
        return dateTocAccepted;
    }

    public void setDateTocAccepted(Date dateTocAccepted)
    {
        this.dateTocAccepted = dateTocAccepted;
    }

    public OSOrder getStepPurchaseOrder()
    {
        return stepPurchaseOrder;
    }

    public void setStepPurchaseOrder(OSOrder stepPurchaseOrder)
    {
        this.stepPurchaseOrder = stepPurchaseOrder;
    }

    public SASlot getAttendedSlot()
    {
        return attendedSlot;
    }

    public void setAttendedSlot(SASlot attendedSlot)
    {
        this.attendedSlot = attendedSlot;
    }

    public String getStepInstructions()
    {
        return step.getStepInstructions();
    }

    public static String convertStatus(NameConversionParameters params)
    {
        final String ret;
        final Object key = params.getKey();

        if (key instanceof Integer && ((Integer) key) >= 0 && ((Integer) key) <= 4)
        {
            ret = PortalUtils.getMessageSource().getMessage(
                    "i18n.exp.common.expStudentExperienceStep.status." + key, null,
                    params.getLocale());
        }
        else
        {
            ret = "Unknown";
        }

        return ret;
    }

    public double getHoursTracked()
    {
        return hoursTracked;
    }

    public void setHoursTracked(double hoursTracked)
    {
        this.hoursTracked = hoursTracked;
    }

    public boolean isOverrideCompletion()
    {
        return overrideCompletion;
    }

    public void setOverrideCompletion(boolean overrideCompletion)
    {
        this.overrideCompletion = overrideCompletion;
    }

    public EXPStudentExperience getStudentExperience()
    {
        return studentExperience;
    }

    public void setStudentExperience(EXPStudentExperience studentExperience)
    {
        this.studentExperience = studentExperience;
    }

    @Override
    public EXPStudentExperienceStepSupport getReflectionRecordableSupport()
    {
        if (step.getTypeEnum() == EXPTermCourseTypeStep.Type.STUDENT_REFLECTION)
            return new EXPStudentExperienceStepSupport();
        return null;
    }

    /**
     * The status of a step
     * 
     * <AUTHOR>
     *
     */
    public enum Status
    {
        PENDING
        {
            @Override
            public int getValue()
            {
                return 0;
            }

            @Override
            public void apply(EXPStudentExperienceStep step,
                    UserDetailsImpl userLoggedIn)
            {
                super.apply(step, userLoggedIn);
                step.setDateApproved(null);
                step.setDateCompleted(null);
                step.setDateStarted(null);
            }

            @Override
            public EXPStudentExperienceStepLogFactory getLogFactory()
            {
                return new EXPStudentExperienceStepPendingLogFactory();
            }
        },
        STARTED
        {
            @Override
            public int getValue()
            {
                return 1;
            }

            @Override
            public void apply(EXPStudentExperienceStep step,
                    UserDetailsImpl userLoggedIn)
            {
                super.apply(step, userLoggedIn);
                step.setDateStarted(new Date());
                step.setDateCompleted(null);
            }

            @Override
            public EXPStudentExperienceStepLogFactory getLogFactory()
            {
                return new EXPStudentExperienceStepStartedLogFactory();
            }
        },
        FINISHED
        {
            @Override
            public int getValue()
            {
                return 2;
            }

            @Override
            public void apply(EXPStudentExperienceStep step,
                    UserDetailsImpl userLoggedIn)
            {
                super.apply(step, userLoggedIn);
                step.setDateCompleted(new Date());
                if (step.getDateStarted() == null)
                {
                    step.setDateStarted(new Date());
                }
                if (step.getDateApproved() == null)
                {
                    step.setDateApproved(new Date());
                }
                if (step.getApprovedBy() == null)
                {
                    step.setApprovedBy(userLoggedIn);
                }
            }

            @Override
            public EXPStudentExperienceStepLogFactory getLogFactory()
            {
                return new EXPStudentExperienceStepCompletedLogFactory();
            }
        },
        APPROVED
        {
            @Override
            public int getValue()
            {
                return 3;
            }

            @Override
            public void apply(EXPStudentExperienceStep step,
                    UserDetailsImpl userLoggedIn)
            {
                super.apply(step, userLoggedIn);
                step.setDateApproved(new Date());
                step.setDateCompleted(null);
                step.setApprovedBy(userLoggedIn);
            }

            @Override
            public EXPStudentExperienceStepLogFactory getLogFactory()
            {
                return new EXPStudentExperienceStepApprovedLogFactory();
            }
        },
        DECLINED
        {
            @Override
            public int getValue()
            {
                return 4;
            }

            @Override
            public void apply(EXPStudentExperienceStep step,
                    UserDetailsImpl userLoggedIn)
            {
                super.apply(step, userLoggedIn);
                step.setDateCompleted(null);
                step.setDateDeclined(new Date());
                step.setDeclinedBy(userLoggedIn);
            }

            @Override
            public EXPStudentExperienceStepLogFactory getLogFactory()
            {
                return new EXPStudentExperienceStepDeclinedLogFactory();
            }
        },
        PENDING_APPROVAL
        {
            @Override
            public int getValue()
            {
                return 5;
            }

            @Override
            public void apply(EXPStudentExperienceStep step,
                    UserDetailsImpl userLoggedIn)
            {
                super.apply(step, userLoggedIn);
                step.setDateCompleted(null);
            }

            @Override
            public EXPStudentExperienceStepLogFactory getLogFactory()
            {
                return new EXPStudentExperienceStepPendingApprovalLogFactory();
            }
        };

        public abstract int getValue();

        public abstract EXPStudentExperienceStepLogFactory getLogFactory();

        public void apply(EXPStudentExperienceStep step)
        {
            apply(step, null);
        }

        public void apply(EXPStudentExperienceStep step,
                UserDetailsImpl userLoggedIn)
        {
            step.setStatus(getValue());
        }

        public static Status of(int status)
        {
            for (Status s : values())
                if (status == s.getValue())
                    return s;
            throw new IllegalArgumentException("Unknown status");
        }
    }

    @Override
    public String getCompetencyAchievedColumnName()
    {
        return "studentExperienceStep";
    }

    @Override
    public String getCompetencyAchievedDiscriminatorName()
    {
        return "com.orbis.web.content.exp.EXPStudentExperienceStepAchieved";
    }

    @Override
    public UserDetailsImpl getCompetencyOwner()
    {
        return student.getStudent().getOwner();
    }

    @Override
    public String getStudentQualificationField()
    {
        return "studentExperienceStep";
    }

    @Override
    public UserDetailsImpl getUser()
    {
        return getOwner();
    }

    @Override
    public QFQualifierEntity getQualifierEntity()
    {
        return step;
    }

    @Override
    public CompetencyAchievableWorkflow<? extends CompetencyAchievable> getCompetencyAchievableWorkflow()
    {
        return step.getTypeEnum() == EXPTermCourseTypeStep.Type.COMPETENCY
                ? new EXPCompetencyStepWorkflow()
                : CompetencyAchievable.super.getCompetencyAchievableWorkflow();
    }

    public boolean isSupervisorCompetenciesEmailSent()
    {
        return supervisorCompetenciesEmailSent;
    }

    public void setSupervisorCompetenciesEmailSent(
            boolean supervisorCompetenciesEmailSent)
    {
        this.supervisorCompetenciesEmailSent = supervisorCompetenciesEmailSent;
    }

    @Override
    @Access(AccessType.PROPERTY)
    public Date getDateCreated()
    {
        return super.getDateCreated();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public Date getDateUpdated()
    {
        return super.getDateUpdated();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public DFModel getDfModel()
    {
        return super.getDfModel();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public UserDetailsImpl getCreatedBy()
    {
        return super.getCreatedBy();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public UserDetailsImpl getUpdatedBy()
    {
        return super.getUpdatedBy();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public UserDetailsImpl getOwner()
    {
        return super.getOwner();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public String getLocale()
    {
        return super.getLocale();
    }
}
