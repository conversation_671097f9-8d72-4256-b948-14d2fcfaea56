package com.orbis.web.content.exp;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.web.servlet.ModelAndView;

import com.google.common.collect.Lists;
import com.orbis.portal.OrbisHqlResultSet;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.FlashMessageUtils;
import com.orbis.utils.QueryBuilder;
import com.orbis.utils.RequestUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.acrm.competency.CompetencyAnticipatedHelper;
import com.orbis.web.content.acrm.competency.ConfigLevel;
import com.orbis.web.content.ec.outlook.ECOutlook;
import com.orbis.web.content.ec.outlook.ECOutlookCCR;
import com.orbis.web.content.ec.outlook.ECOutlookCoop;
import com.orbis.web.content.ec.outlook.ECOutlookEXPGrouping;
import com.orbis.web.content.ec.outlook.ECOutlookEvent;
import com.orbis.web.content.ec.outlook.ECOutlookMentorship;
import com.orbis.web.content.ec.outlook.ECOutlookPTProgram;
import com.orbis.web.content.ec.outlook.ECOutlookSASlot;
import com.orbis.web.content.exp.termCourseTypeStep.creator.EXPTermCourseTypeStepCreator;
import com.orbis.web.content.exp.termCourseTypeStep.creator.EXPTermCourseTypeStepCreatorOptions;
import com.orbis.web.content.portal.DeletionNodeHelper;

import jakarta.servlet.http.HttpServletRequest;

public final class EXPTermCourseTypeStepHelper
{
    private EXPTermCourseTypeStepHelper()
    {

    }

    public static int getStepTypeBasedOnExpModelKey(String expModelKey)
    {
        int stepTypeIndex;

        if ("erec".equals(expModelKey))
        {
            stepTypeIndex = EXPTermCourseTypeStep.Type.CREATE_A_RECORD.getValue();
        }
        else if ("ejrnl".equals(expModelKey))
        {
            stepTypeIndex = EXPTermCourseTypeStep.Type.STUDENT_JOURNAL.getValue();
        }
        else if ("stev".equals(expModelKey))
        {
            stepTypeIndex = EXPTermCourseTypeStep.Type.STUDENT_EVALUATION
                    .getValue();
        }
        else if ("eeval".equals(expModelKey))
        {
            stepTypeIndex = EXPTermCourseTypeStep.Type.INDUSTRY_PARTNER_EVALUATION
                    .getValue();
        }
        else if ("facev".equals(expModelKey))
        {
            stepTypeIndex = EXPTermCourseTypeStep.Type.FACULTY_EVALUATION
                    .getValue();
        }
        else if ("etrac".equals(expModelKey))
        {
            stepTypeIndex = EXPTermCourseTypeStep.Type.TRACK_HOURS.getValue();
        }
        else
        {
            stepTypeIndex = EXPTermCourseTypeStep.Type.CUSTOM1.getValue()
                    + (Integer.parseInt(expModelKey.substring(4)) - 1);
        }
        return stepTypeIndex;
    }

    public static String getExpModelKeyBasedOnStepType(int stepTypeIndex)
    {
        String expModelKey = "";

        if (stepTypeIndex == EXPTermCourseTypeStep.Type.CREATE_A_RECORD.getValue())
        {
            expModelKey = "erec";
        }
        else if (stepTypeIndex == EXPTermCourseTypeStep.Type.STUDENT_JOURNAL
                .getValue())
        {
            expModelKey = "ejrnl";
        }
        else if (stepTypeIndex == EXPTermCourseTypeStep.Type.STUDENT_EVALUATION
                .getValue())
        {
            expModelKey = "stev";
        }
        else if (stepTypeIndex == EXPTermCourseTypeStep.Type.INDUSTRY_PARTNER_EVALUATION
                .getValue())
        {
            expModelKey = "eeval";
        }
        else if (stepTypeIndex == EXPTermCourseTypeStep.Type.FACULTY_EVALUATION
                .getValue())
        {
            expModelKey = "facev";
        }
        else if (stepTypeIndex == EXPTermCourseTypeStep.Type.TRACK_HOURS.getValue())
        {
            expModelKey = "etrac";
        }
        else if (stepTypeIndex == EXPTermCourseTypeStep.Type.CUSTOM1.getValue())
        {
            expModelKey = "etcf1";
        }
        else if (stepTypeIndex == EXPTermCourseTypeStep.Type.CUSTOM2.getValue())
        {
            expModelKey = "etcf2";
        }
        else if (stepTypeIndex == EXPTermCourseTypeStep.Type.CUSTOM3.getValue())
        {
            expModelKey = "etcf3";
        }
        else if (stepTypeIndex == EXPTermCourseTypeStep.Type.CUSTOM4.getValue())
        {
            expModelKey = "etcf4";
        }
        else if (stepTypeIndex == EXPTermCourseTypeStep.Type.CUSTOM5.getValue())
        {
            expModelKey = "etcf5";
        }
        else if (stepTypeIndex == EXPTermCourseTypeStep.Type.CUSTOM6.getValue())
        {
            expModelKey = "etcf6";
        }
        return expModelKey;
    }

    public static EXPTermCourseTypeStep getTCTStep(HttpServletRequest request)
    {
        EXPTermCourseTypeStep step = null;

        if (StringUtils.isInteger(request.getParameter("tctStepId")))
        {
            step = (EXPTermCourseTypeStep) PortalUtils.getHt().load(
                    EXPTermCourseTypeStep.class,
                    Integer.valueOf(request.getParameter("tctStepId")));
        }
        else if (request.getAttribute("tctStepId") != null)
        {
            step = (EXPTermCourseTypeStep) PortalUtils.getHt().load(
                    EXPTermCourseTypeStep.class,
                    (Integer) request.getAttribute("tctStepId"));
        }

        return step;
    }

    public static Map<Integer, String> getEnabledExperienceStepLabels(
            EXPModule module, Locale locale)
    {
        List<EXPTermCourseTypeStep.Type> enabledTypes = EXPTermCourseTypeStep.Type
                .getEnabledTypes(module).stream()
                .sorted(Comparator.comparing(t -> t.getLabel(module, locale)))
                .collect(Collectors.toList());
        Map labels = new LinkedHashMap();
        for (EXPTermCourseTypeStep.Type type :enabledTypes)
        {
            labels.put(type.getValue(), type.getLabel(module, locale));
        }
        return labels;
    }

    public static void demoteFinalReflectionStep(EXPTermCourseType tct)
    {
        PortalUtils.getJt().update(
                " UPDATE exp_term_course_type_step SET finalReflection = 0 WHERE tct = ? AND type = ? AND finalReflection = 1 ",
                new Object[] { tct.getId(),
                        EXPTermCourseTypeStep.Type.STUDENT_REFLECTION.getValue() });
    }

    public static void demoteFinalReflectionStep(EXPTypeWorkflowTemplate template)
    {
        PortalUtils.getJt().update(
                " UPDATE exp_term_course_type_step SET finalReflection = 0 WHERE template = ? AND type = ? AND finalReflection = 1 ",
                new Object[] { template.getId(),
                        EXPTermCourseTypeStep.Type.STUDENT_REFLECTION.getValue() });
    }

    public static boolean hasFinalReflectionStepSibling(EXPTermCourseTypeStep step)
    {
        EXPTermCourseType tct = step.getTct();
        EXPTypeWorkflowTemplate template = step.getTemplate();
        if (tct != null)
            return EXPTermCourseTypeHelper.hasFinalReflectionStep(tct);
        return EXPTypeWorkflowTemplateHelper.hasFinalReflectionStep(template);
    }

    public static void reorderTctSteps(final List qIds, boolean isUnlocked)
    {
        PortalUtils.getTransactionTemplate().execute(status -> {
            int firstChangedStepOrder = 0;
            Integer tctId = 0;
            Integer workflowId = 0;
            boolean hasGroupStep = false;
            boolean pastGroupStep = false;
            for (int i = 0; i < qIds.size(); i++)
            {
                EXPTermCourseTypeStep s = null;
                try
                {
                    s = (EXPTermCourseTypeStep) PortalUtils.getHt().load(
                            EXPTermCourseTypeStep.class, (Integer) qIds.get(i));
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }

                if (s == null)
                {
                    status.setRollbackOnly();
                    break;
                }

                if (s.getTct() != null && tctId == 0 && workflowId == 0)
                {
                    tctId = s.getTct().getId();
                    hasGroupStep = PortalUtils.getHt().findInt(
                            "select count(*) from EXPTermCourseTypeStep s where s.tct.id=? and s.type=?",
                            tctId, EXPTermCourseTypeStep.Type.FORM_A_GROUP
                                    .getValue()) == 1;
                }
                else if (s.getTemplate() != null && tctId == 0 && workflowId == 0)
                {
                    workflowId = s.getTemplate().getId();
                    hasGroupStep = PortalUtils.getHt().findInt(
                            "select count(*) from EXPTermCourseTypeStep s where s.template.id=? and s.type=?",
                            workflowId, EXPTermCourseTypeStep.Type.FORM_A_GROUP
                                    .getValue()) == 1;
                }
                if (s.getType() == EXPTermCourseTypeStep.Type.FORM_A_GROUP
                        .getValue())
                {
                    pastGroupStep = true;
                }
                if (hasGroupStep && !pastGroupStep
                        && s.getType() == EXPTermCourseTypeStep.Type.CREATE_A_RECORD
                                .getValue())
                {
                    status.setRollbackOnly();
                    break;
                }

                if (s.getStepOrder() != (i + 1))
                {
                    Integer activeSteps = 0;

                    activeSteps += (Integer) PortalUtils.getHt().find(
                            "SELECT COUNT(s.id) FROM EXPStudentExperienceStep s JOIN s.step WHERE s.status != ? AND s.step.tct = ? AND (s.step.stepOrder <= ? AND s.step.stepOrder >= ? OR s.step.stepOrder >= ? AND s.step.stepOrder <= ?)",
                            new Object[] {
                                    EXPStudentExperienceStep.Status.PENDING
                                            .getValue(),
                                    s.getTct(), i + 1, s.getStepOrder(), i + 1,
                                    s.getStepOrder() })
                            .get(0);

                    if (activeSteps != 0 && !isUnlocked)
                    {
                        status.setRollbackOnly();
                        break;
                    }
                    else if (activeSteps != 0 && firstChangedStepOrder == 0)
                    {
                        firstChangedStepOrder = i + 1;
                    }

                    s.setStepOrder(i + 1);
                    PortalUtils.getHt().update(s);
                }
            }
            if (tctId != 0)
            {
                List<EXPStudentExperienceStep> studentSteps = PortalUtils.getHt()
                        .find("select ses from EXPStudentExperienceStep ses where ses.status=? and ses.step.tct.id=? and ses.step.stepOrder >= ?",
                                new Object[] {
                                        EXPStudentExperienceStep.Status.STARTED
                                                .getValue(),
                                        tctId, firstChangedStepOrder });
                for (EXPStudentExperienceStep studentStep : studentSteps)
                {
                    EXPHelper.resetPendingStartedForOtherSteps(studentStep);
                }
            }

            return true;
        });
    }

    public static EXPTermCourseTypeStep getTermCourseTypeStep(
            HttpServletRequest request)
    {
        Integer tctsId = RequestUtils.getInteger(request, "tctsId");
        if (tctsId != null)
        {
            return (EXPTermCourseTypeStep) PortalUtils.getHt()
                    .get(EXPTermCourseTypeStep.class, tctsId);
        }
        return null;
    }

    public static Set<EXPTermCourseTypeStep> getCompetenciedTermCourseTypeSteps(
            EXPModule module)
    {
        return new HashSet(PortalUtils.getHt().find(" select distinct tcts " //
                + " from EXPTermCourseTypeStepCompetency tctsc " //
                + " join tctsc.tcts tcts " //
                + " left join tcts.tct tct" //
                + " left join tct.type tctType" //
                + " left join tcts.template template " //
                + " left join template.type templateType" //
                + " where tctType.module=? " //
                + " or templateType.module=? ", new Object[] { module, module }));
    }

    public static Integer getCompetenciedTermCourseTypeStepsCount(EXPModule module)
    {
        return PortalUtils.getHt().findInteger(" select count(distinct tcts) " //
                + " from EXPTermCourseTypeStepCompetency tctsc " //
                + " join tctsc.tcts tcts " //
                + " left join tcts.tct tct" //
                + " left join tct.type tctType" //
                + " left join tcts.template template " //
                + " left join template.type templateType" //
                + " where tctType.module=? " //
                + " or templateType.module=? ", new Object[] { module, module });
    }

    public static Set<EXPTermCourseTypeStep> getCompetenciedTermCourseTypeSteps(
            EXPExperienceType experienceType)
    {
        return new HashSet(PortalUtils.getHt().find(" select distinct tcts " //
                + " from EXPTermCourseTypeStepCompetency tctsc " //
                + " join tctsc.tcts tcts " //
                + " left join tcts.tct tct" //
                + " left join tcts.template template " //
                + " where tct.type=? " //
                + " or template.type=? ",
                new Object[] { experienceType, experienceType }));
    }

    public static Integer getCompetenciedTermCourseTypeStepsCount(
            EXPExperienceType experienceType)
    {
        return PortalUtils.getHt().findInteger(" select count(distinct tcts) " //
                + " from EXPTermCourseTypeStepCompetency tctsc " //
                + " join tctsc.tcts tcts " //
                + " left join tcts.tct tct" //
                + " left join tcts.template template " //
                + " where tct.type=? " //
                + " or template.type=? ",
                new Object[] { experienceType, experienceType });
    }

    public static void populateTermCourseTypeStepCompetenciesConfigData(
            HttpServletRequest request, ModelAndView mv)
    {
        EXPTermCourseTypeStep tcts = getTermCourseTypeStep(request);
        if (tcts != null)
        {
            EXPExperienceType type = tcts.getTemplate() != null
                    ? tcts.getTemplate().getType()
                    : tcts.getTct().getType();

            List<ConfigLevel> ancestors = new ArrayList<>();

            ancestors.add(new ConfigLevel(EXPExperienceTypeCompetency.class, type));
            ancestors.add(
                    new ConfigLevel(EXPModuleCompetency.class, type.getModule()));

            mv.addAllObjects(
                    CompetencyAnticipatedHelper.populateCompetenciableEditForm(
                            EXPTermCourseTypeStepCompetency.class, tcts,
                            ancestors));

            mv.addObject("tcts", tcts);
        }
    }

    public static List<ECOutlook> getSystemActivityTaskOutlooks()
    {
        return Lists.newArrayList(new ECOutlookPTProgram(),
                new ECOutlookMentorship(), new ECOutlookCCR(), new ECOutlookCoop(),
                new ECOutlookEvent(), new ECOutlookSASlot(),
                new ECOutlookEXPGrouping(true));
    }

    public static Optional<ECOutlook> getSystemActivityTaskOutlooks(
            String outlookUUID)
    {
        return getSystemActivityTaskOutlooks().stream()
                .filter(o -> o.getUuid().equals(outlookUUID)).findFirst();
    }

    public static void saveStepCompetencyConfigCommon(HttpServletRequest request,
            EXPTermCourseTypeStep termCourseTypeStep)
    {
        boolean finalCompetencyStep = RequestUtils.getBooleanParameter(request,
                "finalCompetencyStep", false);
        if (finalCompetencyStep && !termCourseTypeStep.isFinalCompetencyStep())
        {
            QueryBuilder qb = new QueryBuilder();
            qb.append(
                    "update exp_term_course_type_step set finalCompetencyStep=0 where");

            if (termCourseTypeStep.getTct() != null)
            {
                qb.append(" tct=?", termCourseTypeStep.getTct().getId());
            }
            else
            {
                qb.append(" template = ?",
                        termCourseTypeStep.getTemplate().getId());
            }
            PortalUtils.getJt().update(qb);
        }
        boolean entityCompetencyTracking = RequestUtils.getBooleanParameter(request,
                "entityCompetencyTracking", false);

        termCourseTypeStep.setFinalCompetencyStep(
                finalCompetencyStep && !entityCompetencyTracking);

        termCourseTypeStep.setEntityCompetencyTracking(entityCompetencyTracking);
        CompetencyAnticipatedHelper.processCompetenciableEditForm(request,
                EXPTermCourseTypeStepCompetency.class, termCourseTypeStep);
    }

    public static EXPTermCourseTypeStep getCreateRecordStep(
            EXPTermCourseType termCourseType)
    {
        return (EXPTermCourseTypeStep) PortalUtils.getHt().findFirst(
                "SELECT s FROM EXPTermCourseTypeStep s WHERE s.tct = ? AND s.type = "
                        + EXPTermCourseTypeStep.Type.CREATE_A_RECORD.getValue(),
                termCourseType).orElse(null);
    }

    public static List<OrbisHqlResultSet> getStepsByTct(EXPTermCourseType type)
    {
        QueryBuilder query = new QueryBuilder();
        query.append(
                "select s.id, s.type, s.stepOrder, s.label, s.l2Label, s.optional from EXPTermCourseTypeStep s");
        query.append(" where s.tct=?", type);
        query.append(" order by s.stepOrder");
        return PortalUtils.getHt().f(query);
    }

    public static Map<Integer, List<OrbisHqlResultSet>> getStepsByTypes(
            List<EXPTermCourseType> termCourseTypes)
    {
        Map<Integer, List<OrbisHqlResultSet>> stepsByTypeMap = new HashMap<>();

        for (EXPTermCourseType type : termCourseTypes)
        {
            List<OrbisHqlResultSet> steps = getTctStepsWithStatusStats(
                    type);
            stepsByTypeMap.put(type.getId(), steps);
        }
        return stepsByTypeMap;
    }

    public static List<OrbisHqlResultSet> getTctStepsWithStatusStats(
            EXPTermCourseType type)
    {
        List<OrbisHqlResultSet> steps = EXPTermCourseTypeStepHelper.getStepsByTct(
                type);

        // Add EXPStudentExperienceStep statuses to step
        for (OrbisHqlResultSet step : steps)
        {
            List<Integer> activeStepIds = EXPStudentExperienceStepHelper
                    .getActiveTctStepIds(
                    type.getId());

            if (activeStepIds.contains(step.<Integer> select("id")))
            {
                step.put("locked", true);
            }

            EXPStudentExperienceStepHelper.populateStepStatusesCounts(step);
        }
        return steps;
    }

    public static List<Integer> getUsedStepTypeIdsInTermCourseType(
            EXPTermCourseType tct)
    {
        return PortalUtils.getHt().find(
                "SELECT s.type FROM EXPTermCourseTypeStep s WHERE s.tct.id = ? "
                        + " GROUP BY s.type HAVING COUNT(s.id) > 0",
                tct.getId());
    }

    public static void addNewTCTStep(EXPQuestionInterface questionInterface,
            HttpServletRequest request, EXPTermCourseTypeStep.Type type,
            EXPTermCourseType tct)
    {
        if (type.isSingleton()
                && EXPTermCourseTypeStepHelper.isStepExist(tct, type))
        {
            FlashMessageUtils.error(request,
                    "i18n.EXPController.Thestephas0720423411959237");
        }
        else if (type.isTrackHours() && tct.getWorkflowTemplate() != null
                && tct.getWorkflowTemplate().isEnableTimeTracker())
        {
            FlashMessageUtils.error(request,
                    "i18n.EXPController.Thisstepca5188135558982024");
        }
        else
        {
            EXPTermCourseTypeStepCreator creator = type.getCreator();
            EXPTermCourseTypeStepCreatorOptions options = EXPTermCourseTypeStepCreatorOptions
                    .builder()//
                    .type(type.getValue())//
                    .tct(tct)//
                    .questionInterface(questionInterface)//
                    .build();
            EXPTermCourseTypeStep s = creator.create(options);

            EXPHelper.createStudentExperienceStepsForStep(s);
        }
    }

    public static boolean deleteTermCourseTypeStep(EXPTermCourseTypeStep tcts)
    {
        boolean success;

        QueryBuilder q = new QueryBuilder();
        q.append(
                "UPDATE exp_term_course_type_step SET stepOrder = stepOrder - 1 WHERE stepOrder > ?",
                tcts.getStepOrder());

        if (tcts.getTct() != null)
        {
            q.append(" AND tct = ?", tcts.getTct().getId());
        }
        else
        {
            q.append(" AND template = ?", tcts.getTemplate().getId());
        }
        PortalUtils.getJt().update(q);

        EXPStudentExperienceStepHelper.setStatusStartedForNextStepAfterDeletedStep(
                tcts);

        success = DeletionNodeHelper.deleteContentItem(tcts) > 0;
        return success;
    }

    public static boolean isStepExist(EXPTermCourseType tct,
            EXPTermCourseTypeStep.Type type)
    {
        return PortalUtils.getJt().exists(
                "SELECT 1 FROM exp_term_course_type_step s WHERE s.tct = ? AND s.type = ?",
                tct.getId(), type.getValue());
    }
}
