package com.orbis.web.content.exp.placer.ui;

import java.util.List;

import com.orbis.web.content.exp.EXPIndustryPartner;
import com.orbis.web.content.exp.EXPPostingHelper;
import com.orbis.web.content.gmap.AddressCoord;

public class Posting
{
    private final int id;

    private final String name;

    private final String synopsis;

    private final ExperienceType type;

    private final AddressCoord geolocation;

    private final boolean isOCC;

    private List<EXPIndustryPartner> industryPartners;

    public Posting(int id, String name, String synopsis, ExperienceType type,
            AddressCoord geolocation, boolean isOCC)
    {
        this.id = id;
        this.name = name;
        this.synopsis = synopsis;
        this.type = type;
        this.geolocation = geolocation;
        this.isOCC = isOCC;
    }

    public int getId()
    {
        return id;
    }

    public String getName()
    {
        return name;
    }

    public String getSynopsis()
    {
        return synopsis;
    }

    public AddressCoord getGeolocation()
    {
        return geolocation;
    }

    public ExperienceType getType()
    {
        return type;
    }

    public List<EXPIndustryPartner> getIndustryPartners()
    {
        if (industryPartners == null)
            industryPartners = EXPPostingHelper.getIndustryPartners(id);
        return industryPartners;
    }

    public boolean getIsOCC()
    {
        return isOCC;
    }
}
