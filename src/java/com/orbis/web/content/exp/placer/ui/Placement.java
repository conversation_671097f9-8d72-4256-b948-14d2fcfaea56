package com.orbis.web.content.exp.placer.ui;

import java.util.Comparator;

import com.orbis.utils.StringUtils;
import com.orbis.web.content.gmap.AddressCoord;

public class Placement
{
    public static final Comparator<Placement> NAME_ORDER = (firstPlacement,
            secondPlacement) -> StringUtils.compareTo(
                    firstPlacement.getPosting().getName(),
                    secondPlacement.getPosting().getName());

    public static final Comparator<Placement> APPLIED_ORDER = (firstPlacement,
            secondPlacement) -> {
        if (firstPlacement.isApplied() && !secondPlacement.isApplied())
            return -1;
        if (!firstPlacement.isApplied() && secondPlacement.isApplied())
            return 1;
        return 0;
    };

    public static final Comparator<Placement> INTEREST_ORDER = (firstPlacement,
            secondPlacement) -> {
        if (firstPlacement.isInterested() && !secondPlacement.isInterested())
            return -1;
        if (!firstPlacement.isInterested() && secondPlacement.isInterested())
            return 1;
        return 0;
    };

    public static final Comparator<Placement> RANKING_ORDER = (firstPlacement,
            secondPlacement) -> {
        if (firstPlacement.getRanking() == 0 && secondPlacement.getRanking() != 0)
            return 1;
        if (firstPlacement.getRanking() != 0 && secondPlacement.getRanking() == 0)
            return -1;
        return Integer.compare(firstPlacement.getRanking(),
                secondPlacement.getRanking());
    };

    private final Posting posting;

    private final int ranking;

    private final boolean applied;

    private final boolean interested;

    private Placement(Builder builder)
    {
        posting = builder.getPosting();
        ranking = builder.getRanking();
        applied = builder.isApplied();
        interested = builder.isInterested();
    }

    /**
     * Returns a builder for {@link Placement}
     * 
     * @return A builder for {@link Placement}
     */
    public static Builder builder()
    {
        return new Builder();
    }

    public Posting getPosting()
    {
        return posting;
    }

    public int getRanking()
    {
        return ranking;
    }

    public boolean isApplied()
    {
        return applied;
    }

    public boolean isInterested()
    {
        return interested;
    }

    /**
     * Builder for {@link Placement}
     */
    public static class Builder implements com.orbis.utils.Builder<Placement>
    {
        private Posting posting;

        private int ranking;

        private boolean applied;

        private boolean interested;

        @Override
        public Placement build()
        {
            return new Placement(this);
        }

        public Posting getPosting()
        {
            return posting;
        }

        public Builder posting(Posting posting)
        {
            this.posting = posting;
            return this;
        }

        public int getRanking()
        {
            return ranking;
        }

        public Builder ranking(int ranking)
        {
            this.ranking = ranking;
            return this;
        }

        public boolean isApplied()
        {
            return applied;
        }

        public Builder applied(boolean applied)
        {
            this.applied = applied;
            return this;
        }

        public boolean isInterested()
        {
            return interested;
        }

        public Builder interested(boolean interested)
        {
            this.interested = interested;
            return this;
        }
    }

    public static class DistanceOrder implements Comparator<Placement>
    {
        private final AddressCoord origin;

        public DistanceOrder(AddressCoord origin)
        {
            this.origin = origin;
        }

        public AddressCoord getOrigin()
        {
            return origin;
        }

        @Override
        public int compare(Placement firstPlacement, Placement secondPlacement)
        {
            if (origin == null)
                return 0;
            AddressCoord p1Location = firstPlacement.getPosting().getGeolocation();
            AddressCoord p2Location = secondPlacement.getPosting().getGeolocation();
            if (p1Location == null && p2Location == null)
                return 0;
            if (p1Location == null)
                return 1;
            if (p2Location == null)
                return -1;
            return Double.compare(p1Location.distanceInKm(origin),
                    p2Location.distanceInKm(origin));
        }
    }
}
