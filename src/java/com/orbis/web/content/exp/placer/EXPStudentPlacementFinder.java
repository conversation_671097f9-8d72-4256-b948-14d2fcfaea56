package com.orbis.web.content.exp.placer;

import java.util.List;
import java.util.stream.Collectors;

import com.orbis.utils.AddressCoordWithinDistance;
import com.orbis.web.content.exp.EXPStudentExperienceStep;
import com.orbis.web.content.exp.placer.ui.Placement;
import com.orbis.web.content.gmap.AddressCoord;

public class EXPStudentPlacementFinder
{
    private EXPStudentPlacementFinderOptions options;

    public EXPStudentPlacementFinder(EXPStudentPlacementFinderOptions options)
    {
        this.options = options;
    }

    public List<Placement> find(EXPStudentExperienceStep step)
    {
        List<Placement> placements = findPlacements(step);
        sortPlacements(step, placements);
        return placements;
    }

    private List<Placement> findPlacements(EXPStudentExperienceStep step)
    {
        List<Placement> ptos = queryForPlacements(step);

        double maximumDistanceInKilometres = options
                .getMaximumDistanceInKilometres();
        if (maximumDistanceInKilometres > 0)
        {
            AddressCoord studentLocation = step.getStudentExperience().getStudent()
                    .getStudent().getOwner().getGeolocation();
            if (studentLocation != null)
            {
                AddressCoordWithinDistance withinDistance = new AddressCoordWithinDistance(
                        studentLocation, maximumDistanceInKilometres);
                ptos = ptos.stream()
                        .filter(placement -> withinDistance
                                .test(placement.getPosting().getGeolocation()))
                        .collect(Collectors.toList());
            }
        }
        return ptos;
    }

    private void sortPlacements(EXPStudentExperienceStep step,
            List<Placement> placements)
    {
        Sort sort = options.getSort();
        if (sort != null)
            placements.sort(sort.comparator(step));
    }

    private List<Placement> queryForPlacements(EXPStudentExperienceStep step)
    {
        return getPlacementQuery(step).find();
    }

    private PlacementQuery getPlacementQuery(EXPStudentExperienceStep step)
    {
        return new PlacementQuery(step, options.getTagIds(), options.getKeyword(),
                options.getLocale());
    }
}
