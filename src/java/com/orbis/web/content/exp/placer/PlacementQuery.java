package com.orbis.web.content.exp.placer;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;
import java.util.Locale;

import com.orbis.portal.PortalUtils;
import com.orbis.utils.QueryBuilder;
import com.orbis.utils.StringUtils;
import com.orbis.utils.query.SQL;
import com.orbis.utils.query.WhereFragment;
import com.orbis.web.content.exp.EXPStudentExperienceStep;
import com.orbis.web.content.exp.placer.ui.ExperienceType;
import com.orbis.web.content.exp.placer.ui.Placement;
import com.orbis.web.content.exp.placer.ui.Posting;
import com.orbis.web.content.gmap.AddressCoord;
import com.orbis.web.content.gmap.GoogleMapHelper;

public class PlacementQuery implements SQL
{
    private final Locale locale;

    private final EXPStudentExperienceStep step;

    private final List<Integer> tagIds;

    private final String keyword;

    public PlacementQuery(EXPStudentExperienceStep step, List<Integer> tagIds,
            String keyword, Locale locale)
    {
        this.step = step;
        this.tagIds = tagIds;
        this.locale = locale;
        this.keyword = keyword;
    }

    private QueryBuilder getSelect()
    {
        QueryBuilder sql = new QueryBuilder();
        {
            sql.append(" p.id AS pId, ");
            sql.append(StringUtils.getStringForLocale("p.postingName",
                    "p.l2PostingName", locale));
            sql.append("   AS pName, ");
            sql.append(StringUtils.getStringForLocale("p.postingSynopsis",
                    "p.l2PostingSynopsis", locale));
            sql.append("   AS pSynopsis, ");
            sql.append(" p.address1 AS pAddress1, ");
            sql.append(" p.city AS pCity, ");
            sql.append(" p.province AS pProvince, ");
            sql.append(" p.country AS pCountry, ");
            sql.append(" p.postalCode AS pPostalCode, ");
            sql.append(StringUtils.getStringForLocale("expt.name", "expt.l2Name",
                    locale));
            sql.append("   AS exptName, ");
            sql.append(" CASE ");
            sql.append("   WHEN ( ");
            sql.append("     SELECT COUNT(*) ");
            sql.append("     FROM EXP_APPLICATION pq_a ");
            sql.append("     WHERE pq_a.studentStep = ? ", step.getId());
            sql.append("       AND pq_a.interest = 0 ");
            sql.append("       AND pq_a.termOpening = pto.id ");
            sql.append("   ) > 0 ");
            sql.append("     THEN 1 ");
            sql.append("   ELSE 0 ");
            sql.append(" END AS applied, ");
            sql.append(" CASE ");
            sql.append("   WHEN ( ");
            sql.append("     SELECT COUNT(*) ");
            sql.append("     FROM EXP_APPLICATION pq_a ");
            sql.append("     WHERE pq_a.studentStep = ?", step.getId());
            sql.append("       AND pq_a.interest = 1 ");
            sql.append("       AND pq_a.termOpening = pto.id ");
            sql.append("   ) > 0 ");
            sql.append("     THEN 1 ");
            sql.append("   ELSE 0 ");
            sql.append(" END AS interested, ");
            sql.append(" ( ");
            sql.append("   SELECT TOP 1 pq_a.studentRanking ");
            sql.append("   FROM EXP_APPLICATION pq_a ");
            sql.append("   WHERE pq_a.studentStep = ? ", step.getId());
            sql.append("     AND pq_a.termOpening = pto.id ");
            sql.append(" ) AS ranking ");
        }
        return sql;
    }

    private QueryBuilder getFrom()
    {
        QueryBuilder sql = new QueryBuilder();
        {
            sql.append(" EXP_POSTING p ");
            sql.append("   JOIN EXP_POSTING_TERM_OPENING pto ");
            sql.append("     ON pto.posting = p.id ");
            sql.append("       AND pto.numberOfPositions > 0 ");
            sql.append("   JOIN EXP_TYPE expt ");
            sql.append("     ON expt.id = p.type ");
        }
        return sql;
    }

    private QueryBuilder getQuery()
    {
        QueryBuilder hql = new QueryBuilder();
        {
            hql.append(" SELECT ");
            hql.append(getSelect());
            hql.append(" FROM ");
            hql.append(getFrom());
            hql.append(" WHERE ");
            hql.append(getWhere());
        }
        return hql;
    }

    private WhereFragment getWhere()
    {
        return new EXPStudentPlacementClauseBuilder()//
                .step(step)//
                .tagIds(tagIds)//
                .keyword(keyword)//
                .build()//
                .getClause();
    }

    public List<Placement> find()
    {
        return PortalUtils.getJt().query(getQuery(), (rs, i) -> {
            return Placement.builder()//
                    .ranking(rs.getInt("ranking"))
                    .interested(rs.getBoolean("interested"))//
                    .applied(rs.getBoolean("applied"))//
                    .posting(getPosting(rs))//
                    .build();
        });
    }

    private Posting getPosting(ResultSet rs) throws SQLException
    {
        return new Posting(//
                rs.getInt("pId"), //
                rs.getString("pName"), //
                rs.getString("pSynopsis"), //
                getExpType(rs), //
                getGeolocation(rs), false);
    }

    private ExperienceType getExpType(ResultSet rs) throws SQLException
    {
        return new ExperienceType(rs.getString("exptName"));
    }

    private AddressCoord getGeolocation(ResultSet rs) throws SQLException
    {
        return GoogleMapHelper.getAddressCoord(//
                GoogleMapHelper.getAddressForGeoCoding(//
                        rs.getString("pAddress1"), //
                        rs.getString("pCity"), //
                        rs.getString("pProvince"), //
                        rs.getString("pPostalCode"), //
                        rs.getString("pCountry")//
                )//
        );
    }

    public Locale getLocale()
    {
        return locale;
    }
}