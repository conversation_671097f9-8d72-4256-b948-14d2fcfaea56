package com.orbis.web.content.exp.placer;

import java.util.List;
import java.util.Locale;

public class EXPStudentPlacementFinderOptions
{
    private final Locale locale;

    private final List<Integer> tagIds;

    private final double maximumDistanceInKilometres;

    private final Sort sort;

    private final String keyword;

    private EXPStudentPlacementFinderOptions(Builder builder)
    {
        locale = builder.locale;
        tagIds = builder.tagIds;
        maximumDistanceInKilometres = builder.maximumDistanceInKilometres;
        sort = builder.sort;
        keyword = builder.keyword;
    }

    /**
     * Returns a builder for {@link EXPStudentPlacementFinderOptions}
     * 
     * @return A builder for {@link EXPStudentPlacementFinderOptions}
     */
    public static Builder builder()
    {
        return new Builder();
    }

    public Sort getSort()
    {
        return sort;
    }

    public List<Integer> getTagIds()
    {
        return tagIds;
    }

    public double getMaximumDistanceInKilometres()
    {
        return maximumDistanceInKilometres;
    }

    public Locale getLocale()
    {
        return locale;
    }

    public String getKeyword()
    {
        return keyword;
    }

    /**
     * Builder for {@link EXPStudentPlacementFinderOptions}
     */
    public static class Builder
            implements com.orbis.utils.Builder<EXPStudentPlacementFinderOptions>
    {
        private Locale locale;

        private List<Integer> tagIds;

        private double maximumDistanceInKilometres = -1;

        private Sort sort;

        private String keyword;

        @Override
        public EXPStudentPlacementFinderOptions build()
        {
            return new EXPStudentPlacementFinderOptions(this);
        }

        public List<Integer> getTagIds()
        {
            return tagIds;
        }

        public Builder tagIds(List<Integer> tagIds)
        {
            this.tagIds = tagIds;
            return this;
        }

        public Sort getSort()
        {
            return sort;
        }

        public Builder sort(Sort sort)
        {
            this.sort = sort;
            return this;
        }

        public double getMaximumDistanceInKilometres()
        {
            return maximumDistanceInKilometres;
        }

        public Builder maximumDistanceInKilometres(
                double maximumDistanceInKilometres)
        {
            this.maximumDistanceInKilometres = maximumDistanceInKilometres;
            return this;
        }

        public Locale getLocale()
        {
            return locale;
        }

        public Builder locale(Locale locale)
        {
            this.locale = locale;
            return this;
        }

        public String getKeyword()
        {
            return keyword;
        }

        public Builder keyword(String keyword)
        {
            this.keyword = keyword;
            return this;
        }
    }
}
