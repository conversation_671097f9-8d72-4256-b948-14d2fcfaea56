package com.orbis.web.content.exp.placer;

import java.util.Comparator;

import com.orbis.web.content.exp.EXPStudentExperienceStep;
import com.orbis.web.content.exp.placer.ui.Placement;
import com.orbis.web.content.gmap.AddressCoord;

public enum Sort
{
    NAME
    {
        @Override
        public Comparator<Placement> comparator(EXPStudentExperienceStep step)
        {
            return Placement.NAME_ORDER;
        }
    },
    DISTANCE
    {
        @Override
        public Comparator<Placement> comparator(EXPStudentExperienceStep step)
        {
            AddressCoord studentLocation = step.getStudentExperience().getStudent()
                    .getStudent().getOwner().getGeolocation();
            return new Placement.DistanceOrder(studentLocation);
        }
    },
    COMPATIBILITY
    {
        @Override
        public Comparator<Placement> comparator(EXPStudentExperienceStep step)
        {
            AddressCoord studentLocation = step.getStudentExperience().getStudent()
                    .getStudent().getOwner().getGeolocation();
            return Placement.RANKING_ORDER//
                    .thenComparing(Placement.APPLIED_ORDER)//
                    .thenComparing(Placement.INTEREST_ORDER)//
                    .thenComparing(new Placement.DistanceOrder(studentLocation))
                    .thenComparing(Placement.NAME_ORDER);
        }
    };

    public abstract Comparator<Placement> comparator(EXPStudentExperienceStep step);
}
