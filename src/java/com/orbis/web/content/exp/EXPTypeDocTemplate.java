package com.orbis.web.content.exp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import com.orbis.web.content.doc.DocHelper;
import org.hibernate.annotations.ColumnDefault;
import org.json.JSONArray;
import org.json.JSONObject;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.df.DFAnswerEntity;
import com.orbis.df.DFHelper;
import com.orbis.docTemp.DocTemplate;
import com.orbis.docTemp.DocTemplateHelper;
import com.orbis.docTemp.DocumentModel;
import com.orbis.docTemp.DocumentModelItem;
import com.orbis.email.EmailRecipient;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.JSONUtils;
import com.orbis.utils.LocaleUtils;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.servlet.http.HttpServletRequest;

@Access(value = AccessType.FIELD)
public class EXPTypeDocTemplate extends DocTemplate
{
    private static final long serialVersionUID = 1931826838512686487L;

    public static final int TYPE_OTHER = 0;

    public static final int TYPE_ENROLLMENT = 1;

    public static final int TYPE_PLACEMENT = 2;

    public static final int TYPE_PLACEMENT_AND_SUBS = 3;

    public static final int TAX_CREDIT_LETTER = 4;

    private EXPExperienceType experienceType;

    @Override
    protected Map<Integer, String> generateTypeMap(Locale locale)
    {
        Map<Integer, String> typeMap = new LinkedHashMap<>();
        typeMap.put(TYPE_OTHER,
                new I18nLabel("i18n.EXPTypeDocTemplate.Other6820066960915914")
                        .getTranslation(locale));
        typeMap.put(TYPE_ENROLLMENT,
                new I18nLabel("i18n.EXPTypeDocTemplate.Enrollment6644150601692679")
                        .getTranslation(locale));
        typeMap.put(TYPE_PLACEMENT,
                new I18nLabel("i18n.EXPTypeDocTemplate.Placement9899614235116599")
                        .getTranslation(locale));
        typeMap.put(TYPE_PLACEMENT_AND_SUBS,
                new I18nLabel("i18n.EXPTypeDocTemplate.PlacementA1343330511092541")
                        .getTranslation(locale));
        typeMap.put(TAX_CREDIT_LETTER,
                new I18nLabel("i18n.EXPTypeDocTemplate.TaxCreditL2906034454737318")
                        .getTranslation(locale));
        return typeMap;
    }

    @Override
    public int[] getTypesWithoutDefault()
    {
        return new int[] { TYPE_OTHER };
    }

    @Override
    public String getAdditionalHql(JSONObject additionalParams) throws Exception
    {
        String hql;
        if (additionalParams.has("courseId"))
        {
            String sb = " exists (select tct.id from EXPTermCourseType tct" +
                    " join tct.termCourse.course c where tct.type=t.experienceType" +
                    " and c.id=" + additionalParams.getInt("courseId") +
                    ")";
            hql = sb;
        }
        else
        {
            hql = " t.experienceType = " + additionalParams.getInt("expTypeId");
        }
        return hql;
    }

    @Override
    public void setAdditionalData(JSONObject additionalParams) throws Exception
    {
        EXPExperienceType experienceType = PortalUtils.getHt()
                .load(EXPExperienceType.class,
                        additionalParams.getInt("expTypeId"));
        this.setExperienceType(experienceType);
    }

    @Override
    public String getTableName()
    {
        return "exp_type_doc_template";
    }

    @Override
    public List<DocumentModel> populateDocumentData(JSONObject additionalParams,
            Locale locale, List<Integer> itemIds, String[] fieldsUsed,
            UserDetailsImpl userLoggedIn) throws Exception
    {
        final List<DocumentModel> documentModels = new LinkedList<>();
        UserDetailsImpl user = PortalUtils.getHt()
                .load(UserDetailsImpl.class, additionalParams.getInt("userId"));
        if (this.getType() == TYPE_PLACEMENT_AND_SUBS)
        {
            DocumentModel documentModel = new DocumentModel();
            EXPExperienceType type = PortalUtils.getHt().load(
                    EXPExperienceType.class, additionalParams.getInt("expTypeId"));
            JSONArray modelKeys = additionalParams.has("templateRecords")
                    ? additionalParams.getJSONObject("templateRecords")
                    .getJSONArray(getId().toString())
                    : additionalParams.getJSONArray("modelKeys");
            EXPRecord record = PortalUtils.getHt().load(EXPRecord.class,
                    additionalParams.getInt("recordId"));
            populateExpTemplateDocModelCommon(documentModel, record);
            List<Object> questionAnswerMap = new ArrayList<>();
            for (String modelKey : JSONUtils.toStringArray(modelKeys))
            {
                EXPRecordFormModelAbstract expRecordFormModelAbtract = EXPRecordFormModelAbstractHelper
                        .getEXPRecordFormModelAbtractForKeyAndType(modelKey, type);
                List<Object> items = new ArrayList<>();
                if (modelKey.equals("erec"))
                {
                    DFHelper.populateModel(expRecordFormModelAbtract.getDFModel(),
                            false, record, userLoggedIn);
                    items.add(DocTemplateHelper.getMappedDFFormattedFields(record,
                            expRecordFormModelAbtract, locale));
                    questionAnswerMap.add(getDocumentModelFromRecord(userLoggedIn,
                            record, expRecordFormModelAbtract, locale));
                    questionAnswerMap.add(
                            DocTemplateHelper.getAdditionalWorkflowDocumentModelFromRecord(
                                    userLoggedIn, record, locale));
                }
                else
                {
                    List<DFAnswerEntity> subRecordList = EXPRecordAbstractHelper
                            .getSubRecordForms(user, modelKey, record);
                    for (DFAnswerEntity subRecord : subRecordList)
                    {
                        DFHelper.populateModel(
                                expRecordFormModelAbtract.getDFModel(), false,
                                record, userLoggedIn);
                        items.add(DocTemplateHelper.getMappedDFFormattedFields(
                                subRecord, expRecordFormModelAbtract, locale));
                        questionAnswerMap.add(
                                getDocumentModelFromRecord(userLoggedIn, subRecord,
                                        expRecordFormModelAbtract, locale));
                        questionAnswerMap.add(
                                DocTemplateHelper.getAdditionalWorkflowDocumentModelFromRecord(
                                        userLoggedIn, subRecord, locale));
                    }
                    documentModel.addItem(new DocumentModelItem(modelKey, items));
                }
            }
            documentModel.addItem(new DocumentModelItem("rec", questionAnswerMap));
            documentModels.add(documentModel);
        }
        else if (this.getType() == TYPE_ENROLLMENT)
        {
            DocumentModel docModel = new DocumentModel();
            EXPCourse course = PortalUtils.getHt().load(EXPCourse.class,
                    additionalParams.getInt("courseId"));
            docModel.addItem(new DocumentModelItem("user", user));
            docModel.addItem(
                    new DocumentModelItem("experienceType", experienceType));
            docModel.addItem(new DocumentModelItem("course", course));
            documentModels.add(docModel);
        }
        else
        {
            DocumentModel documentModel = new DocumentModel();
            EXPRecord record = PortalUtils.getHt().load(EXPRecord.class,
                    additionalParams.getInt("recordId"));
            populateExpTemplateDocModelCommon(documentModel, record);
            documentModels.add(documentModel);
        }

        return documentModels;
    }

    private void populateExpTemplateDocModelCommon(DocumentModel documentModel,
            EXPRecord record)
    {
        UserDetailsImpl user = record.getStudentStep().getStudent().getStudent()
                .getOwner();
        DocHelper.overrideFirstName(user);

        documentModel.addItem(new DocumentModelItem("user", user));
        documentModel.addItem(new DocumentModelItem("record", record));
        documentModel.addItem(new DocumentModelItem("experienceType",
                record.getStudentStep().getStep().getTct().getType()));
        documentModel.addItem(new DocumentModelItem("course", record
                .getStudentStep().getStep().getTct().getTermCourse().getCourse()));
        List<UserDetailsImpl> supervisors = PortalUtils.getHt().find(
                "select s.fieldSupervisor from EXPRecordFieldSupervisor s where record=?",
                record);
        documentModel
                .addItem(new DocumentModelItem("fieldSupervisors", supervisors));
        List<UserDetailsImpl> advisors = PortalUtils.getHt().find(
                "select fa.facultyAdvisor from EXPTermCourseFacultyAdvisor fa where fa.termCourse =?",
                record.getStudentStep().getStep().getTct().getTermCourse());
        documentModel.addItem(new DocumentModelItem("facultyAdvisors", advisors));

    }

    private Map<String, Object> getDocumentModelFromRecord(
            UserDetailsImpl userLoggedIn, DFAnswerEntity record,
            EXPRecordFormModelAbstract expRecordFormModelAbstract, Locale locale)
            throws Exception
    {
        DFHelper.populateModel(expRecordFormModelAbstract.getDFModel(), false,
                record, userLoggedIn);
        Map<String, Object> rec = new HashMap<>();
        rec.put(DocTemplateHelper.NOT_DATASOURCE, true);
        rec.put("recordLabel",
                LocaleUtils.getDefaultLocale().equals(locale)
                        ? expRecordFormModelAbstract.getLabel()
                        : expRecordFormModelAbstract.getL2Label());
        Map<String, Map<String, String>> qaMap = DocTemplateHelper
                .getMappedDFQuestionFormattedAnswers(record,
                        expRecordFormModelAbstract, userLoggedIn, locale);
        rec.put("fields", new LinkedList(qaMap.entrySet()));
        return rec;
    }

    public EXPExperienceType getExperienceType()
    {
        return experienceType;
    }

    public void setExperienceType(EXPExperienceType experienceType)
    {
        this.experienceType = experienceType;
    }

    @Override
    public boolean isEmailable()
    {
        return true;
    }

    @Override
    public List<EmailRecipient> getEmailRecipients(JSONObject additionalParams,
            HttpServletRequest request) throws Exception
    {
        List<EmailRecipient> recipients = new ArrayList<>();
        Integer postingId = additionalParams.getInt("userId");
        UserDetailsImpl user = PortalUtils.getHt()
                .load(UserDetailsImpl.class, postingId);

        EmailRecipient recipient = new EmailRecipient();
        recipient.setEmail(user.getEmail());
        recipient.setUsername(user.getUsername());
        recipient.setName(user.getFullName());
        recipients.add(recipient);

        return recipients;
    }

    @Override
    public String getAfterEmailAction(JSONObject additionalParams)
    {
        String ret = "displayHome";

        try
        {
            if (additionalParams.has("recordId")
                    && additionalParams.get("recordId") != null)
            {
                ret = "displayRecord";
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return ret;
    }

    @Override
    public JSONObject getSample(JSONObject additionalParams) throws Exception
    {
        JSONObject ret;
        if (this.getType() == TYPE_ENROLLMENT)
        {
            ret = DocTemplateHelper.loadDocTemplateSample("exp_enrollment");
        }
        else if (this.getType() == TYPE_PLACEMENT_AND_SUBS)
        {
            ret = DocTemplateHelper
                    .loadDocTemplateSample("exp_placement_and_subrecords");
        }
        else
        {
            ret = DocTemplateHelper.loadDocTemplateSample("exp_template");
        }
        return ret;
    }

    @Override
    public String getActionsGroupLabel(String orbisLocale)
    {
        Locale locale = LocaleUtils.toLocale(orbisLocale);
        String actionsGroupLabel = super.getActionsGroupLabel(locale);
        String experienceTypeName = LocaleUtils.isL1(locale)
                ? experienceType.getName()
                : experienceType.getL2Name();
        return experienceTypeName + ": " + actionsGroupLabel;
    }

    @Override
    @Access(AccessType.PROPERTY)
    public String getName()
    {
        return super.getName();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public String getL2Name()
    {
        return super.getL2Name();
    }

    @Override
    @Access(AccessType.PROPERTY)
    @ColumnDefault("0")
    public int getType()
    {
        return super.getType();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public String getDocument()
    {
        return super.getDocument();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public String getL2Document()
    {
        return super.getL2Document();
    }

    @Override
    @Access(AccessType.PROPERTY)
    @ColumnDefault("0")
    public boolean isDefaultForType()
    {
        return super.isDefaultForType();
    }
}
