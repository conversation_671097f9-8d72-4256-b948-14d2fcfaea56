package com.orbis.web.content.cc;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.beanutils.PropertyUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.context.MessageSource;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.web.bind.ServletRequestBindingException;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.mvc.multiaction.ParameterMethodNameResolver;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.orbis.acegi.providers.dao.hibernate.PersonGroup;
import com.orbis.acegi.providers.dao.hibernate.PersonGroupHelper;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.df.DFCategory;
import com.orbis.df.DFHelper;
import com.orbis.df.DFModel;
import com.orbis.df.DFQuestion;
import com.orbis.df.DFSubController;
import com.orbis.docTemp.DocTemplateHelper;
import com.orbis.email.EmailModel;
import com.orbis.email.EmailRecipient;
import com.orbis.jqgrid.JQGridHelper;
import com.orbis.jqgrid.JQGridModel;
import com.orbis.jqgrid.JQGridSubController;
import com.orbis.portal.CommandQueryTemplate;
import com.orbis.portal.OrbisHqlResultSet;
import com.orbis.portal.OrbisRepost;
import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalUtils;
import com.orbis.question2.QuestionUserType;
import com.orbis.report.ReportSubController;
import com.orbis.search.SearchHelper;
import com.orbis.search.SearchSubControllerFactory;
import com.orbis.utils.ArrayUtils;
import com.orbis.utils.ChartUtils;
import com.orbis.utils.CollectionUtils;
import com.orbis.utils.DBUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.EmailMessage;
import com.orbis.utils.EmailUtils;
import com.orbis.utils.FlashMessageUtils;
import com.orbis.utils.HtmlUtils;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.I18nMessageList;
import com.orbis.utils.JSONUtils;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.LookupUtils;
import com.orbis.utils.NumberUtils;
import com.orbis.utils.ParamsMap;
import com.orbis.utils.QueryBuilder;
import com.orbis.utils.RequestModuleUser;
import com.orbis.utils.RequestUtils;
import com.orbis.utils.StringUtils;
import com.orbis.utils.ValidationData;
import com.orbis.utils.query.Select;
import com.orbis.web.OrbisInteractionController;
import com.orbis.web.content.ContentItem;
import com.orbis.web.content.acrm.AcrmHelper;
import com.orbis.web.content.acrm.AcrmRegQuestionVisibilityHelper;
import com.orbis.web.content.acrm.AcrmRegistrationAnswer;
import com.orbis.web.content.acrm.AcrmRegistrationController;
import com.orbis.web.content.acrm.AcrmRegistrationModule;
import com.orbis.web.content.acrm.AcrmRegistrationQuestion;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.acrm.competency.AchievedCompetencyEditController;
import com.orbis.web.content.acrm.competency.Competenciable;
import com.orbis.web.content.acrm.competency.CompetencyAchievedHelper;
import com.orbis.web.content.acrm.competency.CompetencyAnticipatedHelper;
import com.orbis.web.content.acrm.competency.ConfigLevel;
import com.orbis.web.content.acrm.dataImport.AcrmDataImportHelper;
import com.orbis.web.content.acrm.dataImport.ImportControllerInterface;
import com.orbis.web.content.acrm.dataImport.ImportUsecase;
import com.orbis.web.content.acrm.dataImport.ImportUsecaseFactory;
import com.orbis.web.content.acrm.reflection.AcrmReflectionHelper;
import com.orbis.web.content.acrm.reflection.config.AcrmReflectionConfig;
import com.orbis.web.content.acrm.reflection.config.configurable.AcrmReflectionConfigurableHelper;
import com.orbis.web.content.acrm.reflection.controller.AcrmReflectionRecordController;
import com.orbis.web.content.acrm.reflection.controller.AcrmReflectionRecordControllerHelper;
import com.orbis.web.content.acrm.reflection.record.AcrmReflectionRecord;
import com.orbis.web.content.acrm.reflection.record.AcrmReflectionRecordHelper;
import com.orbis.web.content.acrm.reflection.record.assign.AcrmReflectionRecordAssign;
import com.orbis.web.content.acrm.reflection.record.assign.AcrmReflectionRecordAssignHelper;
import com.orbis.web.content.acrm.reflection.record.assign.AcrmReflectionRecordCCRecordPosition;
import com.orbis.web.content.acrm.reflection.reflectable.AcrmReflectable;
import com.orbis.web.content.cc.reportPlugins.CCActivityReport;
import com.orbis.web.content.cc.reportPlugins.CCPeriodReport;
import com.orbis.web.content.cc.reportPlugins.CCPositionReport;
import com.orbis.web.content.cc.reportPlugins.CCRecordPositionReport;
import com.orbis.web.content.cc.reportPlugins.CCRecordReport;
import com.orbis.web.content.cc.reportPlugins.CCSummaryReport;
import com.orbis.web.content.cc.reportPlugins.CCValidatorReport;
import com.orbis.web.content.coop.CoopTerm;
import com.orbis.web.content.ec.ECReflectionControllerInterface;
import com.orbis.web.content.ec.outlook.ECOutlookHelper;
import com.orbis.web.content.ec.publish.PublishHelper;
import com.orbis.web.content.ec.reflection.ECReflectionHelper;
import com.orbis.web.content.ev.domain.GlobalEvent;
import com.orbis.web.content.grid.CcRecordPositionActivitiesHql;
import com.orbis.web.content.grid.CcRecordPositionApprovedValidations10DaysHql;
import com.orbis.web.content.grid.CcRecordPositionApprovedValidationsNoReflectionHql;
import com.orbis.web.content.grid.CcRecordPositionDeclinedValidations10DaysHql;
import com.orbis.web.content.grid.CcRecordPositionPendingExternalValidationsHql;
import com.orbis.web.content.grid.CcRecordPositionPendingValidations10DaysHql;
import com.orbis.web.content.grid.CcRecordPositionPositionsHql;
import com.orbis.web.content.grid.CcRecordPositionStudentsWithAtleastOnePositionHql;
import com.orbis.web.content.grid.CcRecordPositionValidationsHql;
import com.orbis.web.content.grid.DataViewerState;
import com.orbis.web.content.grid.GridCheckHelper;
import com.orbis.web.content.grid.GridFilter;
import com.orbis.web.content.grid.GridHelper;
import com.orbis.web.content.grid.GridOptions;
import com.orbis.web.content.grid.GridQuery;
import com.orbis.web.content.grid.GridQueryFactory;
import com.orbis.web.content.grid.NameValuePair;
import com.orbis.web.content.interaction.InteractionEngageableControllerInterface;
import com.orbis.web.content.interaction.InteractionEngagementActivity.TYPE;
import com.orbis.web.content.interaction.InteractionEngagementActivityCCPosition;
import com.orbis.web.content.interaction.InteractionEngagementHelper;
import com.orbis.web.content.interaction.InteractionHelper;
import com.orbis.web.content.interaction.InteractionHelper.INTERACTION_TYPE;
import com.orbis.web.content.nes.NESHelper;
import com.orbis.web.content.np.NHelper;
import com.orbis.web.content.portal.DeletionNodeHelper;
import com.orbis.web.content.pt.PTHelper;
import com.orbis.web.content.pt.PTProgram;
import com.orbis.web.content.pt.PTProgramEnrollment;
import com.orbis.web.site.SiteController;
import com.orbis.web.site.SiteElement;
import com.orbis.web.site.SiteManager;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RequestMapping("ccController")
public class CCController extends OrbisInteractionController<CCModule>
        implements ECReflectionControllerInterface,
        InteractionEngageableControllerInterface, ImportControllerInterface,
        AchievedCompetencyEditController, AcrmReflectionRecordController
{
    private CCSearchInterface ccSearchInterface = new CCSearchInterface(this);

    private CCGridInterface ccGridInterface;

    private String parchmentUrl;

    CCDFQuestionInterface ccDFQuestionInterface = new CCDFQuestionInterface(this);

    private static List<QuestionUserType> defaultQuestionUserTypes;

    public CCController(SiteManager siteManager)
    {
        super(siteManager);
        setMethodNameResolver(
                new ParameterMethodNameResolver("action", "displayHome"));
    }

    static
    {
        defaultQuestionUserTypes = new ArrayList<>();
        QuestionUserType t = null;

        t = new QuestionUserType();
        t.setUserTypeLabel("Admin");
        t.setUserTypeKey(CCHelper.CC_ROLE_ADMIN);
        defaultQuestionUserTypes.add(t);

        t = new QuestionUserType();
        t.setUserTypeLabel("Unit Leader");
        t.setUserTypeKey(CCHelper.CC_ROLE_ACTIVITY_DIRECTOR);
        defaultQuestionUserTypes.add(t);

        t = new QuestionUserType();
        t.setUserTypeLabel("Activity Owner");
        t.setUserTypeKey(CCHelper.CC_ROLE_ACTIVITY_OWNER);
        defaultQuestionUserTypes.add(t);

        t = new QuestionUserType();
        t.setUserTypeLabel("Staff");
        t.setUserTypeKey(CCHelper.CC_ROLE_STAFF);
        defaultQuestionUserTypes.add(t);

        t = new QuestionUserType();
        t.setUserTypeLabel("Validator");
        t.setUserTypeKey(CCHelper.CC_ROLE_VALIDATOR);
        defaultQuestionUserTypes.add(t);

        t = new QuestionUserType();
        t.setUserTypeLabel(PersonGroupHelper.STUDENT);
        t.setUserTypeKey(CCHelper.CC_ROLE_STUDENT);
        defaultQuestionUserTypes.add(t);

        t = new QuestionUserType();
        t.setUserTypeLabel(PersonGroupHelper.ALUMNI);
        t.setUserTypeKey(CCHelper.CC_ROLE_ALUMNI);
        defaultQuestionUserTypes.add(t);

        t = new QuestionUserType();
        t.setUserTypeLabel("Anonymous");
        t.setUserTypeKey(CCHelper.CC_ROLE_ANONYMOUS);
        defaultQuestionUserTypes.add(t);
    }

    public void setParchmentUrl(String parchmentUrl)
    {
        this.parchmentUrl = parchmentUrl;
    }

    @RequestMapping("question")
    public ModelAndView question(HttpServletRequest request,
            HttpServletResponse response)
    {
        return DFSubController
                .getInstance(ccDFQuestionInterface, getServletContext())
                .processRequest(request, response);
    }

    /**
     * Default request handling method
     */
    @Override
    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "displayHome")
    public ModelAndView displayHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        initializePermissions();

        ModelAndView mv = null;

        CCModule module = getModule(request);
        String currentRole = CCHelper.getCurrentUserRole(module,
                PortalUtils.getUserLoggedIn(request));
        Map<String, Boolean> roles = CCHelper
                .getUserRoles(PortalUtils.getUserLoggedIn(request), module);

        if (!StringUtils.isEmpty(currentRole)
                || CCHelper.getUserRoleListFromMap(roles).size() == 1)
        {
            String role = !StringUtils.isEmpty(currentRole) ? currentRole
                    : CCHelper.getUserRoleListFromMap(roles).get(0);

            if (CCHelper.CC_ROLE_ADMIN.equals(role))
            {
                mv = displayAdminHome(request, response);
            }
            else if (CCHelper.CC_ROLE_STAFF.equals(role))
            {
                mv = displayStaffHome(request, response);
            }
            else if (CCHelper.CC_ROLE_VALIDATOR.equals(role))
            {
                mv = displayMyValidations(request, response);
            }
            else if (CCHelper.CC_ROLE_STUDENT.equals(role)
                    || CCHelper.CC_ROLE_ALUMNI.equals(role))
            {
                mv = displayMyRecord(request, response);
            }
            else if (CCHelper.CC_ROLE_ACTIVITY_DIRECTOR.equals(role))
            {
                mv = displayActivityDirectorHome(request, response);
            }
            else if (CCHelper.CC_ROLE_ACTIVITY_OWNER.equals(role))
            {
                mv = displayActivityOwnerHome(request, response);
            }
            else
            {
                mv = new ModelAndView("cc/cc_noPermission");
            }
        }
        else if (CCHelper.getUserRoleListFromMap(roles).size() > 1)
        {
            mv = new ModelAndView("cc/cc_multiRoleHome");
        }
        else
        {
            mv = new ModelAndView("cc/cc_noPermission");
        }

        return mv;
    }

    /**
     * UCSD use-case
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("requestOfficialRecord")
    public ModelAndView requestOfficialRecord(HttpServletRequest request,
            HttpServletResponse response)
    {
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
        ModelAndView mv = getRedirectView(parchmentUrl);
        return mv;
    }

    @RequestMapping("loadAjaxProgram")
    public ModelAndView loadAjaxProgram(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        JSONObject ret = new JSONObject();
        List<Object[]> foundPosition = getHt().find(
                CCHelper.getNodeSelectorSelect("ccp")
                        + " from CCPosition ccp where ccp.id = ?",
                Integer.valueOf(request.getParameter("positionId")));
        if (!foundPosition.isEmpty())
        {
            List shortlist = PortalUtils.getHt().find(
                    "select rpc.position.id from CCRecordPositionShortlist rpc where rpc.record=?",
                    CCHelper.getCCRecord(getModule(request),
                            PortalUtils.getUserLoggedIn(request)));
            ret = CCHelper.nodeSelectorJsonPosition(shortlist,
                    foundPosition.get(0));
        }

        return jsonObjectResponse(ret);
    }

    @RequestMapping("displayMassUpdateActivity")
    public ModelAndView displayMassUpdateActivity(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityMassUpdate");
        CCActivityQuestionModel modelInstance = CCActivityQuestionModelHelper
                .getActivityQuestionModel(getModule(request));
        CCHelper.populateActivityQuestions(mv, getModule(request), null,
                PortalUtils.getUserLoggedIn(request), true);

        DFHelper.populateMassUpdatePage(mv, request, modelInstance);

        List<Object> idsFromGrid = super.getGridSelectedIds(request);

        String idsFromRequest = request.getParameter("selectedIds");

        if (!idsFromGrid.isEmpty())
        {
            mv.addObject("selectedIds", idsFromGrid);
        }
        else
        {
            mv.addObject("selectedIds", idsFromRequest);
        }

        return mv;
    }

    @RequestMapping("massUpdateActivity")
    public ModelAndView massUpdateActivity(HttpServletRequest request,
            HttpServletResponse response)
    {
        new MassUpdateCCActivityThread(request, getModule(request)).start();
        ModelAndView mv = displayMassUpdateActivity(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.CCController.MassUpdate5938418337356527")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    @RequestMapping("displayMassUpdatePosition")
    public ModelAndView displayMassUpdatePosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_positionMassUpdate");
        CCPositionQuestionModel modelInstance = CCPositionQuestionModelHelper
                .getPositionQuestionModel(getModule(request));
        CCHelper.populatePositionQuestions(mv, getModule(request), null,
                PortalUtils.getUserLoggedIn(request), false);

        DFHelper.populateMassUpdatePage(mv, request, modelInstance);

        List<Object> idsFromGrid = super.getGridSelectedIds(request);

        String idsFromRequest = request.getParameter("selectedIds");

        if (!idsFromGrid.isEmpty())
        {
            mv.addObject("selectedIds", idsFromGrid);
        }
        else
        {
            mv.addObject("selectedIds", idsFromRequest);
        }

        return mv;
    }

    @RequestMapping("displayMassUpdatePositionValidator")
    public ModelAndView displayMassUpdatePositionValidator(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_positionMassUpdate_validators");

        List<Object> idsFromGrid = super.getGridSelectedIds(request);

        String idsFromRequest = request.getParameter("selectedIds");

        if (!idsFromGrid.isEmpty())
        {
            mv.addObject("selectedIds", idsFromGrid);
        }
        else
        {
            mv.addObject("selectedIds", idsFromRequest);
        }

        return mv;
    }

    @RequestMapping("displayMassUpdatePositionVisibility")
    public ModelAndView displayMassUpdatePositionVisibility(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_positionMassUpdate_visibility");

        List<Object> idsFromGrid = super.getGridSelectedIds(request);

        String idsFromRequest = request.getParameter("selectedIds");

        if (!idsFromGrid.isEmpty())
        {
            mv.addObject("selectedIds", idsFromGrid);
        }
        else
        {
            mv.addObject("selectedIds", idsFromRequest);
        }

        return mv;
    }

    @RequestMapping("massUpdatePosition")
    public ModelAndView massUpdatePosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        new MassUpdateCCPositionThread(request, getModule(request)).start();
        ModelAndView mv = displayMassUpdatePosition(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.CCController.MassUpdate0004574785287217")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    @RequestMapping("fixRecordPositions")
    public ModelAndView fixRecordPositions(HttpServletRequest request,
            HttpServletResponse response)
    {
        return displayHome(request, response);
    }

    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupAllStudents")
    public ModelAndView lookupAllStudents(HttpServletRequest request,
            HttpServletResponse response)
    {
        return AcrmHelper.lookupUsers(request.getParameter("term").trim(),
                new String[] { PersonGroupHelper.STUDENT }, UserDetailsImpl.class,
                "u", "u", 10, 1, false, null);
    }

    @RequestMapping("displayCreateRecord")
    public ModelAndView displayCreateRecord(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_massCreateRecords");

        return mv;
    }

    @RequestMapping("createRecords")
    public ModelAndView createRecords(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        CCModule module = getModule(request);
        int recordAlreadyExists = 0;
        int recordsCreated = 0;
        int creationTotal = 0;
        int invalidUserCount = 0;

        if (!StringUtils.isEmpty(request.getParameter("userIds")))
        {
            String[] usernames = request.getParameter("userIds").split("\r\n");
            for (int i = 0; i < usernames.length; i++)
            {
                String username = usernames[i];
                if (null != username)
                {
                    List<CCRecord> record = getHt().find(
                            "from CCRecord r where r.owner.username=? and r.module=?",
                            new Object[] { username.trim(), module });
                    List<UserDetailsImpl> userCheck = getHt().find(
                            "from UserDetailsImpl u where u.username=?", username);
                    if (!record.isEmpty() && !userCheck.isEmpty())
                    {
                        recordAlreadyExists++;
                    }
                    else if (!userCheck.isEmpty())
                    {
                        Integer userId = UserDetailsHelper
                                .getUserIdByUsername(username);
                        CCHelper.getOrAddCCRecord(module,
                                UserDetailsHelper.getUserByUserId(userId));
                        recordsCreated++;
                        creationTotal++;
                    }
                    else
                    {
                        invalidUserCount++;
                    }
                }
            }
        }

        ModelAndView mv;

        if (creationTotal > 0)
        {
            mv = displayHome(request, response);
        }
        else
        {
            mv = displayCreateRecord(request, response);
        }
        String successMessage = Integer.valueOf(creationTotal)
                + " Student(s) successfully enrolled";
        if (invalidUserCount > 0)
        {
            successMessage += "<br>" + Integer.valueOf(invalidUserCount)
                    + " IDs provided were not valid";
        }
        if (recordAlreadyExists > 0)
        {
            successMessage += "<br>" + Integer.valueOf(recordAlreadyExists)
                    + " IDs provided already had a record";
        }
        mv.addObject("invalidUserCount", invalidUserCount);
        mv.addObject("createdCount", Integer.valueOf(recordsCreated));
        mv.addObject("creationTotal", Integer.valueOf(creationTotal));
        mv.addObject("successMessage", successMessage);
        return mv;
    }

    @RequestMapping("displayStudentHome")
    public ModelAndView displayStudentHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        Locale locale = PortalUtils.getLocale(request);
        boolean isLocaleL1 = LocaleUtils.isL1(locale);

        CCHelper.updateCurrentUserRole(
                userLoggedIn.isStudent() ? CCHelper.CC_ROLE_STUDENT
                        : CCHelper.CC_ROLE_ALUMNI,
                userLoggedIn);

        ModelAndView mv = new ModelAndView("cc/cc_studentHome");

        String selectClauseFragment = "p.id, p.activity.id, p."
                + (isLocaleL1 ? "title" : "l2Title") + ", p.activity.activity, p."
                + (isLocaleL1 ? "description" : "l2Description") + ", p.activity."
                + (isLocaleL1 ? "description" : "l2Description") + ", p.startDate";
        String whereClauseFragment = "p.activity.period.module.id = ? AND p.status = ? "
                + "AND ? BETWEEN p.startDate AND p.endDate ";

        CCRecord record = CCHelper.getCCRecord(module, userLoggedIn);

        List<Object[]> shortlistedPositionData = PortalUtils.getHt().find(
                "SELECT " + selectClauseFragment
                        + " FROM CCRecordPositionShortlist rps "
                        + "LEFT JOIN rps.position p WHERE " + whereClauseFragment
                        + " AND rps.record.id = ? ORDER BY p.startDate",
                new Object[] { module.getId(), CCPosition.APPROVAL_STATUS_APPROVED,
                        new Date(), record != null ? record.getId() : -1 });

        mv.addObject("shortlistedPositionData", shortlistedPositionData.subList(0,
                Math.min(shortlistedPositionData.size(), 20)));

        populateRecord(mv, module, userLoggedIn, true, request);

        List ccModules = new ArrayList();
        ccModules.add(module);
        CCHelper.populateCCPublicSiteElement(mv, ccModules, request);

        return mv;
    }

    @RequestMapping("loadRecentPositionsTable")
    public ModelAndView loadRecentPositionsTable(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        ModelAndView mv = getShortCircuitView("cc/cc_recentPositionsTable");
        Locale locale = PortalUtils.getLocale(request);
        boolean isLocaleL1 = LocaleUtils.isL1(locale);
        String selectClauseFragment = "p.id, p.activity.id, p."
                + (isLocaleL1 ? "title" : "l2Title") + ", p.activity.activity, p."
                + (isLocaleL1 ? "description" : "l2Description") + ", p.activity."
                + (isLocaleL1 ? "description" : "l2Description")
                + ", p.startDate , rps.dateCreated";
        String whereClauseFragment = "p.activity.period.activeStudent = true AND p.activity.period.module.id = ? AND p.status = ? "
                + "AND p.createdDate > ? ";

        List<Object[]> positionData = PortalUtils.getHt().find(
                "SELECT " + selectClauseFragment
                        + " FROM CCRecordPositionShortlist rps "
                        + "RIGHT JOIN rps.position p WHERE " + whereClauseFragment
                        + " AND p.enabled=? ORDER BY p.startDate",
                new Object[] { module.getId(), CCPosition.APPROVAL_STATUS_APPROVED,
                        DateUtils.subtractDays(new Date(), 90), true });

        mv.addObject("positionData",
                positionData.subList(0, Math.min(positionData.size(), 20)));

        return mv;
    }

    @RequestMapping("displayActivityDirectorHome")
    public ModelAndView displayActivityDirectorHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (!CCHelper.isActivityDirector(userLoggedIn, module))
        {
            return new ModelAndView("cc/cc_noPermission");
        }

        CCHelper.updateCurrentUserRole(CCHelper.CC_ROLE_ACTIVITY_DIRECTOR,
                userLoggedIn);

        ModelAndView mv = new ModelAndView("cc/cc_activityDirectorHome");

        CCHelper.populatePeriodSelection(request, mv, module);

        return mv;
    }

    @RequestMapping("displayActivityOwnerHome")
    public ModelAndView displayActivityOwnerHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (!CCHelper.isActivityOwner(userLoggedIn, module))
        {
            return new ModelAndView("cc/cc_noPermission");
        }
        CCHelper.updateCurrentUserRole(CCHelper.CC_ROLE_ACTIVITY_OWNER,
                userLoggedIn);

        ModelAndView mv = new ModelAndView("cc/cc_activityOwnerHome");

        String filterClause = getPositionVisibilityClauseForCurrentUser(module,
                request);

        mv.addObject("pendingPositions",
                CCHelper.getCCPositions(module, "select count(p)",
                        "p.approvedBy is null and " + filterClause).get(0));

        mv.addObject("activePositions", CCHelper.getCCPositions(module,
                "select count(p)",
                "p.approvedBy is not null and p.enabled=1 and " + filterClause)
                .get(0));

        mv.addObject("inactivePositions", CCHelper.getCCPositions(module,
                "select count(p)",
                "p.approvedBy is not null and p.enabled=false and " + filterClause)
                .get(0));

        mv.addObject("rpModule", CCHelper.getCCRequestPositionModule(module));

        return mv;
    }

    @RequestMapping("displayStaffHome")
    public ModelAndView displayStaffHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (!CCHelper.isStaff(userLoggedIn, module))
        {
            return new ModelAndView("cc/cc_noPermission");
        }

        CCHelper.updateCurrentUserRole(CCHelper.CC_ROLE_STAFF, userLoggedIn);

        ModelAndView mv = new ModelAndView("cc/cc_staffHome");

        String filterClause = getPositionVisibilityClauseForCurrentUser(module,
                request);

        mv.addObject("pendingPositions",
                CCHelper.getCCPositions(module, "select count(p)",
                        "p.approvedBy is null and " + filterClause).get(0));

        mv.addObject("activePositions", CCHelper.getCCPositions(module,
                "select count(p)",
                "p.approvedBy is not null and p.enabled=1 and " + filterClause)
                .get(0));

        mv.addObject("inactivePositions", CCHelper.getCCPositions(module,
                "select count(p)",
                "p.approvedBy is not null and p.enabled=false and " + filterClause)
                .get(0));

        mv.addObject("rpModule", CCHelper.getCCRequestPositionModule(module));

        return mv;
    }

    private String getPositionVisibilityClauseForCurrentUser(CCModule module,
            HttpServletRequest request)
    {
        Integer currentUserId = PortalUtils.getUserLoggedIn(request).getId();
        String currentRole = CCHelper.getCurrentUserRole(module,
                PortalUtils.getUserLoggedIn(request));

        String hql = "(p.id = -999)";

        if (CCHelper.CC_ROLE_STAFF.equals(currentRole))
        {
            hql = "(p.createdBy.id=" + currentUserId
                    + " or p.activity.createdBy.id=" + currentUserId
                    + " or p.activity.currentOwner.id=" + currentUserId + ")";
        }
        else if (CCHelper.CC_ROLE_ACTIVITY_OWNER.equals(currentRole))
        {
            hql = "(p.activity.currentOwner.id=" + currentUserId + ")";
        }

        return hql;
    }

    @RequestMapping("displayMyActivitiesAndPositions")
    public ModelAndView displayMyActivitiesAndPositions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activitiesAndPositions");
        mv.addObject("positionType", request.getParameter("positionType"));
        return mv;
    }

    @RequestMapping("ajaxLoadActivitiesAndPositionsTable")
    public ModelAndView ajaxLoadActivitiesAndPositionsTable(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_activitiesAndPositionsTable");
        CCModule module = getModule(request);
        String positionType = request.getParameter("positionType");
        String filterClause = getPositionVisibilityClauseForCurrentUser(module,
                request);

        List positions = new ArrayList();
        if (StringUtils.isEmpty(positionType) || "all".equals(positionType))
        {
            positions = CCHelper.getCCPositions(module, "select p", filterClause);
        }
        else if ("pending".equals(positionType))
        {
            positions = CCHelper.getCCPositions(module, "select p",
                    "p.approvedBy is null and " + filterClause);
        }
        else if ("active".equals(positionType))
        {
            positions = CCHelper.getCCPositions(module, "select p",
                    "p.approvedBy is not null and p.enabled=1 and " + filterClause);
        }
        else if ("inactive".equals(positionType))
        {
            positions = CCHelper.getCCPositions(module, "select p",
                    "p.approvedBy is not null and p.enabled=false and "
                            + filterClause);
        }
        mv.addObject("positions", positions);

        return mv;
    }

    @RequestMapping("displayAddNewActivity")
    public ModelAndView displayAddNewActivity(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_addActivity");
        CCModule module = getModule(request);
        Locale locale = PortalUtils.getLocale(request);
        String nameParam = LocaleUtils.isL1(locale) ? "name" : "l2Name";
        String visibleClause = null;
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);

        CCHelper.populateForAddingPosition(mv, module, locale, user);

        visibleClause = CCHelper.limitPeriodToActiveClause(module, user);

        String periodSql = "select p.id, p." + nameParam + " as name"
                + " from cc_period p where p.module=?" + visibleClause;

        mv.addObject("activePeriods",
                getJt().queryForList(periodSql, new Object[] { module.getId() }));
        mv.addObject("selectedPeriodId",
                RequestUtils.getIntParameterOrAttribute(request, "periodId", null));
        mv.addObject("position", new CCPosition());
        mv.addObject("backToTimePeriod", request.getParameter("backToTimePeriod"));
        mv.addObject("module", module);

        return mv;
    }

    @RequestMapping("ajaxLoadActivityDetailsEdit")
    public ModelAndView ajaxLoadActivityDetailsEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        return CCHelper.ajaxLoadActivityDetailsEdit(request, module);
    }

    @RequestMapping("displayAddNewPosition")
    public ModelAndView displayAddNewPosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_addNewPosition");
        CCModule module = getModule(request);
        Locale locale = PortalUtils.getLocale(request);
        String nameParam = LocaleUtils.isL1(request) ? "name" : "l2Name";
        String visibleClause = null;
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);

        CCHelper.populateForAddingPosition(mv, module, locale, user);

        visibleClause = CCHelper.limitPeriodToActiveClause(module, user);

        String periodSql = "select p.id, p." + nameParam + " as name"
                + " from cc_period p where p.module=?" + visibleClause;

        mv.addObject("activePeriods",
                getJt().queryForList(periodSql, new Object[] { module.getId() }));
        mv.addObject("selectedPeriodId",
                RequestUtils.getIntParameterOrAttribute(request, "periodId", null));
        mv.addObject("position", new CCPosition());
        mv.addObject("backToTimePeriod", request.getParameter("backToTimePeriod"));
        mv.addObject("module", module);
        Optional<Integer> titleQuestion = getHt().findFirst(
                "select dfq.id from DFQuestion dfq where dfq.category.model=? and dfq.answerField1='title'",
                new Object[] { CCPositionQuestionModelHelper
                        .getPositionQuestionModel(module).getDFModel() });
        titleQuestion.ifPresent(qId -> mv.addObject("titleQuestionField",
                "question_" + qId.toString()));
        mv.addObject("externalValidationEnabled",
                module.isEnableExternalValidations());

        return mv;
    }

    @RequestMapping("displayAddNewPositionWizard")
    public ModelAndView displayAddNewPositionWizard(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_addNewPositionWizard");

        CCActivity activity = CCHelper.getCCActivity(request);
        CCModule module = getModule(request);
        mv.addObject("ccModule", module);

        if (activity != null)
        {
            mv.addObject("activity", activity);
            List categoryOptions = CCRequestPositionHelper.getSelectOptions(module,
                    activity.getPeriod().getId(), activity.getCategory(),
                    activity.getOrganization(), activity.getDepartment(),
                    "category");
            List organizationOptions = CCRequestPositionHelper.getSelectOptions(
                    module, activity.getPeriod().getId(), activity.getCategory(),
                    activity.getOrganization(), activity.getDepartment(),
                    "organization");
            List departmentOptions = CCRequestPositionHelper.getSelectOptions(
                    module, activity.getPeriod().getId(), activity.getCategory(),
                    activity.getOrganization(), activity.getDepartment(),
                    "department");
            List activityOptions = CCRequestPositionHelper.getSelectOptions(module,
                    activity.getPeriod().getId(), activity.getCategory(),
                    activity.getOrganization(), activity.getDepartment(),
                    "activityId");
            mv.addObject("categoryOptions", categoryOptions);
            mv.addObject("organizationOptions", organizationOptions);
            mv.addObject("departmentOptions", departmentOptions);
            mv.addObject("activityOptions", activityOptions);
        }

        CCHelper.populateAddNewPositionWizard(module, mv, request);

        return mv;
    }

    @RequestMapping("ajaxLoadActivityDetailsView")
    public ModelAndView ajaxLoadActivityDetailsView(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView(
                "cc/cc_requestActivityDetailsViewAjax");
        CCModule module = getModule(request);
        CCActivity a = CCHelper.getCCActivity(request);

        CCHelper.populateActivityQuestions(mv, module, a,
                PortalUtils.getUserLoggedIn(request), false);

        try
        {
            AcrmHelper.processTagAssign(request, mv,
                    JSONUtils.newJSONObject("activityId", a.getId()), a, true);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        mv.addObject("activity", a);

        return mv;
    }

    @RequestMapping("ajaxLoadSelectOptions")
    public ModelAndView ajaxLoadSelectOptions(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        List options = CCRequestPositionHelper.getSelectOptions(module,
                RequestUtils.getIntParameter(request, "periodId", -1),
                request.getParameter("category"),
                request.getParameter("organization"),
                request.getParameter("department"), request.getParameter("level"));
        List<Object> results = new ArrayList<Object>(options);
        results = results.stream().distinct().collect(Collectors.toList());
        JSONArray ret = StringUtils.convertListToJSONArray(results);

        return jsonArrayResponse(ret);
    }

    @RequestMapping("checkForExistingActivity")
    public ModelAndView checkForExistingActivity(HttpServletRequest request,
            HttpServletResponse response)
    {
        boolean duplicate = CCRequestPositionHelper
                .checkForExistingActivity(request, getModule(request));

        return jsonBooleanResponse("duplicate", duplicate);
    }

    @RequestMapping("checkForDuplicatePosition")
    public ModelAndView checkForDuplicatePosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        boolean duplicate = CCRequestPositionHelper.checkForDuplicatePositionTitle(
                getModule(request), request.getParameter("posTitle"), request);

        return jsonBooleanResponse("duplicate", duplicate);
    }

    @RequestMapping("ajaxSearchCCNodes")
    public ModelAndView ajaxSearchCCNodes(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        return jsonObjectResponse(CCHelper.getNextCCNode(request,
                getModule(request),
                StringUtils.isEmpty(request.getParameter("accessToAllPositions"))));
    }

    /**
     * Validate record position
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("validateRecordPosition")
    public ModelAndView validateRecordPosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCRecordPosition recordPosition = CCHelper.getCCRecordPosition(request);
        String newStatus = CCRecordPositionHelper.validateRecordPosition(request,
                recordPosition);

        ModelAndView mv = null;

        String returnTo = request.getParameter("returnTo");

        if (CCRecordPosition.STATUS_APPROVED.equalsIgnoreCase(newStatus)
                && !recordPosition.isShowOnPdf())
        {
            PublishHelper.publishCard(recordPosition, recordPosition.getOwner());
            recordPosition.setShowOnPdf(true);
            PortalUtils.getHt().update(recordPosition);
        }

        if (returnTo == null)
        {
            mv = displayHome(request, response);
        }
        else if (returnTo.equals("record"))
        {
            mv = displayRecordPositionEdit(request, response);
        }
        else if (returnTo.equals("pending"))
        {
            mv = displayRecordPositionEdit(request, response);
        }
        else if (returnTo.equals("validatorDetails"))
        {
            mv = displayValidatorDetailsPendingTab(request, response);
        }
        else if (returnTo.equals("myValidations"))
        {
            mv = displayMyValidations(request, response);
        }
        else if (returnTo.equals("timeTracking"))
        {
            mv = displayPendingValidationsWithTimeTracking(request, response);
        }
        else if (returnTo.equals("recordList"))
        {
            mv = displayRecord(request, response);
        }

        if (mv != null)
        {
            if ("Approved".equalsIgnoreCase(newStatus))
            {
                mv.addObject("successMessage",
                        new I18nLabel(
                                "i18n.CCController.RecordPosi7941984419696567")
                                .getTranslation(PortalUtils.getLocale(request)));
            }
            else if ("Declined".equalsIgnoreCase(newStatus))
            {
                mv.addObject("successMessage",
                        new I18nLabel(
                                "i18n.CCController.RecordPosi8523844525986428")
                                .getTranslation(PortalUtils.getLocale(request)));
            }
        }

        return mv;
    }

    @RequestMapping("bulkValidateRecordPositions")
    public ModelAndView bulkValidateRecordPositions(HttpServletRequest request,
            HttpServletResponse response)
    {

        final CCModule module = getModule(request);
        final UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        final String newStatus = request.getParameter("newStatus");

        List<Integer> ids = getGridSelectedIds(request);

        String errorMessage = CCRecordPositionHelper.bulkValidatePositionsCommon(
                request, module, userLoggedIn, newStatus, ids);

        ModelAndView mv = null;

        if (request.getParameter("returnToMyValidations") != null)
        {
            mv = displayMyValidations(request, response);
        }
        else if (request.getParameter("returnToValidatorDetails") != null)
        {
            mv = displayValidatorDetailsPendingTab(request, response);
        }
        else if (request.getParameter("returnToValidatorHome") != null)
        {
            mv = displayHome(request, response);
        }
        else if (request.getParameter("returnToDeclinedValidations10Days") != null)
        {
            mv = displayRecordPositionDeclinedValidations10Days(request, response);
        }
        else if (request.getParameter("returnToPendingExternalValidations") != null)
        {
            mv = displayRecordPositionPendingExternalValidations(request, response);
        }
        else if (request.getParameter("returnToValidations") != null)
        {
            mv = displayRecordPositionValidations(request, response);
        }
        else if (request.getParameter("returnToPendingValidations10Days") != null)
        {
            mv = displayRecordPositionPendingValidations10Days(request, response);
        }
        else
        {
            mv = displayRecordPositionValidations(request, response);
        }

        if (!StringUtils.isEmpty(errorMessage))
        {
            mv.addObject("errorMessage", errorMessage);
        }
        else if ("Approved".equalsIgnoreCase(newStatus))
        {
            mv.addObject("successMessage",
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.0recordpos1588104568405815",
                            PortalUtils.getLocale(request), ids.size()));
        }
        else if ("Declined".equalsIgnoreCase(newStatus))
        {
            mv.addObject("successMessage",
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.0recordpos9912594553784346",
                            PortalUtils.getLocale(request), ids.size()));
        }

        return mv;
    }

    @RequestMapping("bulkValidateRecordPositionsWithTimeTracking")
    public ModelAndView bulkValidateRecordPositionsWithTimeTracking(
            HttpServletRequest request, HttpServletResponse response)
    {

        final CCModule module = getModule(request);
        final UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        final String newStatus = request.getParameter("newStatus");

        List<Integer> ids = RequestUtils.getIntParameterValues(request, "rpIds");

        String errorMessage = CCRecordPositionHelper.bulkValidatePositionsCommon(
                request, module, userLoggedIn, newStatus, ids);

        ModelAndView mv = displayPendingValidationsWithTimeTracking(request,
                response);

        if (!StringUtils.isEmpty(errorMessage))
        {
            FlashMessageUtils.error(request, errorMessage);
        }
        else if ("Approved".equalsIgnoreCase(newStatus))
        {
            FlashMessageUtils.success(request,
                    "i18n.CCController.0recordpos1588104568405815", ids.size());
        }
        else if ("Declined".equalsIgnoreCase(newStatus))
        {
            FlashMessageUtils.success(request,
                    "i18n.CCController.0recordpos9912594553784346", ids.size());
        }

        return mv;
    }

    @RequestMapping("bulkEmailValidators")
    public ModelAndView bulkEmailValidators(HttpServletRequest request,
            HttpServletResponse response)
    {
        List<Integer> ids = super.getGridSelectedIds(request);

        String errorMessage = "";
        String keepErrorOpen = "";

        if (!ids.isEmpty())
        {
            CCModule module = getModule(request);
            UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

            List<String> rpsNoValidator = sendPublicValidationEmail(ids, module,
                    userLoggedIn, null);
            if (!rpsNoValidator.isEmpty())
            {
                errorMessage = PortalUtils.getI18nMessage(
                        "i18n.CCController.Thefollowi1900297678161678",
                        PortalUtils.getLocale(request));
                errorMessage += "<br>";
                for (String rpDescription : rpsNoValidator)
                {
                    errorMessage += rpDescription + "<br>";
                }
                keepErrorOpen = "true";
            }
        }
        else
        {
            errorMessage = PortalUtils.getI18nMessage(
                    "i18n.CCController.Pleasesele5666575349839795",
                    PortalUtils.getLocale(request));
        }

        ModelAndView mv = null;

        if (request.getParameter("returnToMyValidations") != null)
        {
            mv = displayMyValidations(request, response);
        }
        else if (request.getParameter("returnToValidatorDetails") != null)
        {
            mv = displayValidatorDetailsPendingTab(request, response);
        }
        else if (request.getParameter("returnToValidatorHome") != null)
        {
            mv = displayHome(request, response);
        }
        else if (request.getParameter("returnToTimeTracking") != null)
        {
            mv = displayPendingValidationsWithTimeTracking(request, response);
        }
        else if (request.getParameter("returnToDeclinedValidations10Days") != null)
        {
            mv = displayRecordPositionDeclinedValidations10Days(request, response);
        }
        else if (request.getParameter("returnToPendingExternalValidations") != null)
        {
            mv = displayRecordPositionPendingExternalValidations(request, response);
        }
        else if (request.getParameter("returnToValidations") != null)
        {
            mv = displayRecordPositionValidations(request, response);
        }
        else if (request.getParameter("returnToPendingValidations10Days") != null)
        {
            mv = displayRecordPositionPendingValidations10Days(request, response);
        }
        else
        {
            mv = displayRecordPositionValidations(request, response);
        }

        if (!StringUtils.isEmpty(errorMessage))
        {
            mv.addObject("errorMessage", errorMessage);
            mv.addObject("errorTimeOpen", keepErrorOpen);
        }
        else
        {
            FlashMessageUtils.success(request,
                    "i18n.CCController.Emailsareb1307887131271515");
        }

        return mv;
    }

    /**
     * Sends out the validation emails to the validators of the record-positions.
     *
     * @param ids
     * @param module
     * @param userLoggedIn
     * @param externalValidationEmails
     * @return a list of record-positions that have no validator, and so no email
     *         was sent out
     */
    private List<String> sendPublicValidationEmail(List<Integer> ids,
            CCModule module, UserDetailsImpl userLoggedIn,
            Map<Integer, List<Object[]>> externalValidationEmails)
    {
        List<String> rpsNoValidator = new ArrayList<>();
        List<OrbisHqlResultSet> rps = getHt().f(
                "select rp.id, rp.confirmationCode, rp.position.id, rp.position.title, rp.position.l2Title, rp.position.activity.activity, rp.position.activity.l2Activity, rp.record.owner.firstAndLastName, rp.position.externalPosition from CCRecordPosition rp where rp.id in"
                        + DBUtils.buildInClause(ids));
        for (OrbisHqlResultSet rp : rps)
        {

            boolean externalPosition = rp.select("position.externalPosition");

            final List<Object[]> validatorEmails;
            Integer id = rp.select("id");
            if (externalPosition && id != null && (externalValidationEmails != null
                    && externalValidationEmails.containsKey(id)))
            {
                validatorEmails = externalValidationEmails.get(id);
            }
            else if (externalPosition)
            {
                String s = getExternalEmail(module, (CCRecordPosition) PortalUtils
                        .getHt().load(CCRecordPosition.class, id));
                List<Object[]> o = Lists.newArrayList();
                o.add(new Object[] { s, null });
                validatorEmails = o;
            }
            else
            {
                validatorEmails = getHt().find(
                        "select validator.emailAddress, validator.username, validator.userLocale, validator.preferredFirstName, validator.lastName from CCPositionValidator cpv join cpv.validator validator where cpv.position.id="
                                + rp.select("position.id"));
            }

            CCPublicModule publicModule = CCHelper.getCCPublicModule(module);
            String siteUrl = PortalUtils.getSiteBaseUrl();

            if (validatorEmails != null && !validatorEmails.isEmpty())
            {
                for (Object[] recipientData : validatorEmails)
                {
                    boolean isL1Locale = LocaleUtils.isL1(
                            (String) ArrayUtils.getValueAtIndex(recipientData, 2));
                    Locale emailLocale = isL1Locale ? LocaleUtils.getDefaultLocale()
                            : LocaleUtils.getSecondaryLocale();

                    String subject;
                    if (externalPosition)
                    {
                        subject = module.getExternalPositionEmailSubject();
                    }
                    else
                    {
                        subject = isL1Locale ? module.getValidatorSubject()
                                : module.getL2ValidatorSubject();
                    }

                    String body;
                    if (externalPosition)
                    {
                        body = module.getExternalPositionEmailBody();
                    }
                    else
                    {
                        body = isL1Locale ? module.getValidatorBody()
                                : module.getL2ValidatorBody();
                    }

                    body += "<br /><br />"
                            + PortalUtils.getI18nMessage(
                                    "i18n.CCController.StudentNam0620128514475826",
                                    emailLocale)
                            + " " + rp.select("record.owner.firstAndLastName");
                    body += "<br />" + PortalUtils.getI18nMessage(
                            "i18n.CCController.Position7437931649048924",
                            emailLocale) + " |ACTIVITY| - |POSITION|";

                    if (publicModule != null && (externalPosition
                            || module.isIncludeOpportunityLink()))
                    {
                        body += "<br /><br /><a href='https://" + siteUrl
                                + publicModule.getSiteElementPath()
                                + "?action=displayRecordPosition&rpId=" + id
                                + "&confirmationCode="
                                + rp.select("confirmationCode")
                                + "&validatorUsername=" + recipientData[1] + "'>"
                                + PortalUtils.getI18nMessage(
                                        "i18n.CCController.ClickHere4047580196270051",
                                        emailLocale)
                                + "</a> "
                                + PortalUtils.getI18nMessage(
                                        "i18n.CCController.tovalidate8501075169797387",
                                        emailLocale);

                    }

                    subject = CCHelper.processEmailTokens(subject,
                            ArrayUtils.getValueAtIndex(recipientData, 3, ""),
                            ArrayUtils.getValueAtIndex(recipientData, 4, ""),
                            isL1Locale ? rp.select("position.activity.activity")
                                    : rp.select("position.activity.l2Activity"),
                            isL1Locale ? rp.select("position.title")
                                    : rp.select("position.l2Title"),
                            null, null, null);

                    body = CCHelper.processEmailTokens(body,
                            ArrayUtils.getValueAtIndex(recipientData, 3, ""),
                            ArrayUtils.getValueAtIndex(recipientData, 4, ""),
                            isL1Locale ? rp.select("position.activity.activity")
                                    : rp.select("position.activity.l2Activity"),
                            isL1Locale ? rp.select("position.title")
                                    : rp.select("position.l2Title"),
                            null, null, null);

                    EmailUtils.sendAndLog1Email(new EmailMessage(
                            module.getFromEmailForValidators(), userLoggedIn,
                            (String) recipientData[0], (String) recipientData[1],
                            subject, body, body, null, false, false));
                }
            }
            else
            {
                rpsNoValidator.add(rp.select("position.activity.activity") + " - "
                        + rp.select("position.title") + " - "
                        + rp.select("record.owner.firstAndLastName"));
            }
        }
        return rpsNoValidator;
    }

    @RequestMapping("displayAdvancedSearch")
    public ModelAndView displayAdvancedSearch(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_advancedSearch");
        CCModule module = getModule(request);
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        String visibleClause = "";
        if (CCHelper.isAdmin(userLoggedIn, module)
                || CCHelper.isStaff(userLoggedIn, module)
                || CCHelper.isActivityDirector(userLoggedIn, module))
        {
            visibleClause = " and p.active = true ";
        }
        else if (userLoggedIn.isStudent() || CCHelper.isAlumni(userLoggedIn))
        {
            visibleClause = " and p.activeStudent = true ";
        }
        else if (CCHelper.isValidator(userLoggedIn))
        {
            visibleClause = " and p.activeVal = true ";
        }
        List<CCPeriod> periods = getHt()
                .find("from CCPeriod p where p.module=?" + visibleClause, module);
        List<JSONObject> periodsForDataViewerFilter = periods.stream().map(p -> {
            JSONObject ret = new JSONObject();

            try
            {
                ret.put("value", p.getId());
                ret.put("label", p.getName());
            }
            catch (JSONException e)
            {
                e.printStackTrace();
            }

            return ret;
        }).collect(Collectors.toList());

        List<Integer> activePeriods = periods.stream()
                .filter(CCPeriod::isActiveStudent).map(CCPeriod::getId)
                .collect(Collectors.toList());

        if (activePeriods.size() > 0)
        {
            mv.addObject("activePeriodId",
                    activePeriods.get(activePeriods.size() - 1));
        }

        mv.addObject("ccPeriods", periods);
        mv.addObject("periodsForDataViewerFilter",
                JSONUtils.toJSON(periodsForDataViewerFilter));

        String mode = request.getParameter("mode");
        if (mode == null)
        {
            mode = "viewDetails";
        }
        mv.addObject("mode", mode);

        if ("addToRecord".equals(mode))
        {
            String studentId = request.getParameter("studentId");
            if (StringUtils.isEmpty(studentId))
            {
                studentId = (String) request.getAttribute("studentId");
            }
            mv.addObject("studentId", studentId);

            mv.addObject("myRecordMode",
                    userLoggedIn.isStudent() || CCHelper.isAlumni(userLoggedIn));
        }

        mv.addObject("shortlistOnly", request.getParameter("shortlistOnly"));
        return mv;
    }

    @Override
    @RequestMapping("jqGrid")
    public ModelAndView jqGrid(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = JQGridSubController.getInstance(getCcGridInterface())
                .processRequest(request, response);
        return mv;
    }

    /**
     * Returns a "grid UI" that lists either "pending" or "approved" or "denied"
     * CCPositions. The type of CCPosition listed is determined by the "mode"
     * parameter.
     */
    @RequestMapping("displayCCRequests")
    public ModelAndView displayCCRequests(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_ccRequests");
        Locale locale = PortalUtils.getLocale(request);
        CCModule module = getModule(request);

        final GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName("ajaxLoadCCRequests");
        options.setExportMethodName("exportCCRequestsGrid");
        options.setGridLoadOnDocumentReady(true);

        GridHelper.addGridSupport(request, mv, "cc_ccRequests", options);

        mv.addObject("requestsSelectedCount", 0);

        // GET 'MODE'
        String mode = request.getParameter("mode");
        if (mode == null)
        {
            mode = (String) request.getAttribute("mode");
        }
        if (mode == null)
        {
            mode = "pending";
        }

        String departmentLabelBilingual = LocaleUtils.isL1(locale)
                ? module.getDepartmentLabel()
                : module.getL2DepartmentLabel();
        if ((mode.equalsIgnoreCase("pending") || mode.equalsIgnoreCase("approved"))
                && !StringUtils.isEmpty(departmentLabelBilingual))
        {
            mv.addObject("directorsIndex", 14);
        }

        mv.addObject("mode", mode);
        mv.addObject("isShowLevelCategory", module.isShowLevelCategory());
        mv.addObject("isShowLevelOrganization", module.isShowLevelOrganization());
        mv.addObject("isShowLevelDepartment", module.isShowLevelDepartment());

        if (!StringUtils.isEmpty(departmentLabelBilingual))
        {
            mv.addObject("departmentLabel", departmentLabelBilingual);
        }
        mv.addObject("periodId", request.getParameter("periodId"));
        mv.addObject("activePeriodsCount",
                CCHelper.getActivePeriods(module, locale).size());
        mv.addObject("filter", request.getParameter("filter"));

        return mv;
    }

    @RequestMapping("ajaxLoadCCRequests")
    public ModelAndView ajaxLoadCCRequests(HttpServletRequest request,
            HttpServletResponse response)
    {
        return CCHelper.ajaxLoadCCRequests(request);
    }

    @RequestMapping("gridCheckToggleAllCCRequests")
    public ModelAndView gridCheckToggleAllCCRequests(HttpServletRequest request,
            HttpServletResponse response)
    {
        Locale locale = PortalUtils.getLocale(request);
        CCModule module = CCHelper
                .getCCModule(Integer.valueOf(request.getParameter("moduleId")));

        // GET 'MODE'
        String mode = request.getParameter("mode");
        if (mode == null)
        {
            mode = (String) request.getAttribute("mode");
        }
        if (mode == null)
        {
            mode = "pending";
        }

        List<Integer> filteredIds = new ArrayList<>();
        String whereClause = CCHelper.getCCRequestWhereHql(module, mode,
                "last10days".equals(request.getParameter("filter")), request) + " "
                + GridHelper.getGridFiltersWhereClause(request);

        if ((mode.equalsIgnoreCase("pending") || mode.equalsIgnoreCase("approved"))
                && !StringUtils.isEmpty(
                        LocaleUtils.isL1(locale) ? module.getDepartmentLabel()
                                : module.getL2DepartmentLabel())
                && !StringUtils.isEmpty(request.getParameter("directorsIndex")))
        {
            Integer directorsIndex = Integer.valueOf(
                    request.getParameter("directorsIndex"));
            String hql = "select p.activity.id, p.id";
            for (int i = 2; i < directorsIndex; i++)
            {
                hql += ", p.id";
            }
            hql += " from CCPosition p where " + whereClause;
            List<Object[]> ids = PortalUtils.getHt().find(hql);

            if (!ids.isEmpty())
            {
                hql = "select ad.activity.id, ad.directors.name from CCActivityDirectors ad where ad.activity.id in (select p.activity.id from CCPosition p where "
                        + whereClause + ")";
                List<Object[]> directors = PortalUtils.getHt().find(hql);
                directors.add(0, new Object[] { 0, 0 });
                ids = ArrayUtils.mergeArrays(ids, directors);

                GridHelper.virtualFilter(request, ids, PortalUtils.nullMv(),
                        new Integer[] { directorsIndex });

                for (Object[] d : ids)
                {
                    filteredIds.add((Integer) d[1]);
                }
            }
        }
        else
        {
            filteredIds = PortalUtils.getHt()
                    .find("select p.id from CCPosition p where " + whereClause);
        }

        return GridCheckHelper.gridCheckToggleAll(request, filteredIds);
    }

    @RequestMapping("massApprovePositions")
    public ModelAndView massApprovePositions(HttpServletRequest request,
            HttpServletResponse response)
    {
        try
        {
            UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);

            JSONArray selectedIds = null;
            if (!StringUtils.isEmpty(request.getParameter("gridInstanceId")))
            {
                selectedIds = new JSONArray(getGridSelectedIds(request));
            }
            else
            {
                selectedIds = new JSONArray(request.getParameter("selectedIds"));
            }

            for (int i = 0; i < selectedIds.length(); i++)
            {
                CCPosition position = (CCPosition) getHt().load(CCPosition.class,
                        selectedIds.getInt(i));

                if (position != null)
                {
                    position.setRequestModule(null);
                    position.setDeniedReason(null);
                    position.setDateDisabled(null);
                    position.setEnabled(true);
                    CCHelper.updatePositionApprovalStatus(position,
                            CCPosition.APPROVAL_STATUS_APPROVED, user);

                    if (position.getActivity().getStatus() == 0)
                    {
                        CCHelper.updateActivityStatus(position.getActivity(), user);
                    }
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        ModelAndView mv = displayHome(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.CCController.Positionsa4086378475129602")
                        .getTranslation(PortalUtils.getLocale(request)));

        return mv;
    }

    @RequestMapping("exportCCRequestsGrid")
    public ModelAndView exportCCRequestsGrid(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = new ModelAndView("");
        Locale locale = PortalUtils.getLocale(request);
        CCModule module = CCHelper
                .getCCModule(Integer.valueOf(request.getParameter("moduleId")));

        String mode = request.getParameter("mode");
        if (mode == null)
        {
            mode = (String) request.getAttribute("mode");
        }

        String selectClause = CCHelper.getCCRequestsSelectClause(mode,
                module.isShowLevelCategory(), module.isShowLevelOrganization(),
                module.isShowLevelDepartment());
        String whereClause = CCHelper.getCCRequestWhereHql(module, mode,
                "last10days".equals(request.getParameter("filter")), request);
        List<Object[]> data = GridHelper.runQuery(request, mv,
                " from CCPosition p where " + whereClause, selectClause, "p.id");

        List<String> gridLabels = new ArrayList<>();
        gridLabels.add("Requested On");
        if (mode.equalsIgnoreCase("approved"))
        {
            gridLabels.add("Approved On");
            gridLabels.add("Approved By First Name");
            gridLabels.add("Approved By Last Name");
        }
        else if (mode.equalsIgnoreCase("denied"))
        {
            gridLabels.add("Denied On");
            gridLabels.add("Denied By First Name");
            gridLabels.add("Denied By Last Name");
        }
        if (module.isShowLevelCategory())
        {
            gridLabels.add("Category");
        }
        if (module.isShowLevelOrganization())
        {
            gridLabels.add("Organization");
        }
        if (module.isShowLevelDepartment())
        {
            gridLabels.add("Department");
        }
        gridLabels.add("Activity");
        if (mode.equalsIgnoreCase("pending"))
        {
            gridLabels.add("Position Title");
        }
        else
        {
            gridLabels.add("Position");
        }
        String departmentLabelBilingual = LocaleUtils.isL1(locale)
                ? module.getDepartmentLabel()
                : module.getL2DepartmentLabel();
        if ((mode.equalsIgnoreCase("pending") || mode.equalsIgnoreCase("approved"))
                && !StringUtils.isEmpty(departmentLabelBilingual))
        {
            gridLabels.add(departmentLabelBilingual);

            List<Object[]> directors = PortalUtils.getHt().find(
                    "select ad.activity.id, ad.directors.name from CCActivityDirectors ad where ad.activity.id in (select p.activity.id from CCPosition p where "
                            + whereClause + ")");
            directors.add(0, new Object[] { 0, 0 });
            data = ArrayUtils.mergeArrays(data, directors);
        }

        int size = data.get(0).length;
        List<Object[]> finalData = new LinkedList<>();
        for (Object[] d : data)
        {
            finalData.add(Arrays.copyOfRange(d, 2, size));
        }

        return GridHelper.exportAndLogGrid(finalData, "Position Requests",
                gridLabels.toArray(new String[gridLabels.size()]), null, request);
    }

    @RequestMapping("displayEmailUser")
    public ModelAndView displayEmailUser(HttpServletRequest request,
            HttpServletResponse response)
    {
        Map<String, String> additionalParams = new HashMap<>();
        Map<String, String> emailModelParams = new HashMap<>();
        emailModelParams.put("onCancelled", "displayHome");
        emailModelParams.put("onCompleted", "displayHome");
        emailModelParams.put("action", "sendEmailCommon");
        EmailModel emailModel = getEmailModel(request, emailModelParams,
                additionalParams);

        return displayEmailer(request, response, emailModel);
    }

    @Override
    public EmailModel getEmailModel(HttpServletRequest request,
            Map emailModelParams, Map additionalParams)
    {
        JSONArray selectedIds = new JSONArray(getGridSelectedIds(request));
        List results;
        String type = request.getParameter("type");
        if (StringUtils.equals(type, "pendingValidations")
                || StringUtils.equals(type, "Pending"))
        {
            results = getHt().find(
                    "SELECT distinct rp.record.owner.emailAddress, rp.record.owner.preferredFirstName, rp.record.owner.lastName from CCRecordPosition rp join rp.record join rp.record.owner where rp.id in "
                            + DBUtils.buildInClause(selectedIds)
                            + " order by rp.record.owner.preferredFirstName, rp.record.owner.lastName");
        }
        else
        {
            results = getHt().find(
                    "select distinct p.requestorEmail, p.requestorFirstName, p.requestorLastName"
                            + " from CCPosition p where p.id in "
                            + DBUtils.buildInClause(selectedIds)
                            + " order by p.requestorFirstName, p.requestorLastName");
        }

        List<EmailRecipient> emailRecipients = new ArrayList<>();

        for (Iterator i = results.iterator(); i.hasNext();)
        {
            Object[] row = (Object[]) i.next();
            EmailRecipient r = new EmailRecipient();

            if (EmailUtils.isValidEmailAddress((String) row[0]))
            {
                r.setEmail((String) row[0]);
            }

            String firstName = (String) row[1];
            String lastName = (String) row[2];
            r.setUsername(NHelper.getUsernameByNameAndEmail(firstName, lastName,
                    (String) row[0]));
            r.setName(firstName + " " + lastName);

            r.setLocale(
                    LocaleUtils.toLocale(NHelper.getUserLocale((String) row[0])));

            emailRecipients.add(r);
        }

        EmailModel emailModel = new EmailModel();

        if (!emailRecipients.isEmpty())
        {
            emailModel.setEmailRecipients(emailRecipients);
        }

        setEmailModelParams(emailModelParams, additionalParams, emailModel,
                request);

        return emailModel;
    }

    @RequestMapping("displayApprovedRequest")
    public ModelAndView displayApprovedRequest(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        Locale locale = PortalUtils.getLocale(request);

        CCModule ccModule = getModule(request);
        CCPosition ccPosition = CCHelper.getCCPosition(request);
        if (ccPosition != null)
        {
            mv = new ModelAndView("cc/cc_approvedRequest");
            mv.addObject("ccPosition", ccPosition);

            mv.addObject("validators", getHt().find(
                    "from CCPositionValidator pv where pv.position=? order by pv.validator.lastName",
                    ccPosition));

            CCHelper.populatePositionQuestions(mv, getModule(request), ccPosition,
                    PortalUtils.getUserLoggedIn(request), false);

            CCHelper.populateCCOutcomes_editMode(mv, ccModule, ccPosition, false,
                    request);

            mv.addObject("activePeriods",
                    CCHelper.getActivePeriods(ccModule, locale));
        }
        else
        {
            mv = displayHome(request, response);
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.CCController.Positionno2273724291484361")
                            .getTranslation(PortalUtils.getLocale(request)));
        }
        return mv;
    }

    @RequestMapping("displayRecordPositionValidators")
    public ModelAndView displayRecordPositionValidators(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordPositionValidators");
        String periodId = request.getParameter("periodId");
        request.setAttribute("periodId", periodId);
        mv.addObject("periodId", periodId);

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName("ajaxLoadRecordPositionValidators");
        final Map<String, String> gridAdditionalParams = new HashMap<>();
        gridAdditionalParams.put("periodId", periodId);
        options.setGridAdditionalParams(gridAdditionalParams);
        options.setGridCheckToggleAllMethod("gridCheckToggleAll");

        String type = request.getParameter("type");
        if (null != type && !"".equals(type))
        {
            Locale locale = PortalUtils.getLocale(request);
            if ("validatorsWithPending".equals(type))
            {
                options.setFiltersToApplyOnLoad(
                        Arrays.asList(CCHelper.getPendingValidatorsFilter(locale)));
            }
            else if ("validatorsWithApproved".equals(type))
            {
                options.setFiltersToApplyOnLoad(Arrays
                        .asList(CCHelper.getApprovedValidatorsFilter(locale)));
            }
            else if ("validatorsWithDeclined".equals(type))
            {
                options.setFiltersToApplyOnLoad(Arrays
                        .asList(CCHelper.getDeclinedValidatorsFilter(locale)));
            }

            gridAdditionalParams.put("type", type);
        }

        GridHelper.addGridSupport(request, mv, "cc_recordPositionValidators",
                options);

        return mv;
    }

    @RequestMapping("ajaxLoadRecordPositionValidators")
    public ModelAndView ajaxLoadRecordPositionValidators(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_recordPositionValidatorsAjax");
        Locale locale = PortalUtils.getLocale(request);

        final GridQuery query = GridQueryFactory
                .newInstance("cc_recordPositionValidators", request, mv);

        List<GridFilter> customFilters = new ArrayList<>();
        customFilters.add(CCHelper.getPendingValidatorsFilter(locale));
        customFilters.add(CCHelper.getApprovedValidatorsFilter(locale));
        customFilters.add(CCHelper.getDeclinedValidatorsFilter(locale));
        query.setCustomFilters(customFilters);

        GridHelper.runQuery(query);
        return mv;
    }

    @RequestMapping("displayRecordPositionValidations")
    public ModelAndView displayRecordPositionValidations(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordPositionValidations");
        String periodId = request.getParameter("periodId");
        request.setAttribute("periodId", periodId);
        mv.addObject("periodId", periodId);

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName("ajaxLoadRecordPositionValidations");
        final Map<String, String> gridAdditionalParams = new HashMap<>();
        gridAdditionalParams.put("periodId", periodId);
        options.setGridAdditionalParams(gridAdditionalParams);
        options.setGridCheckToggleAllMethod("gridCheckToggleAll");

        String type = request.getParameter("type");
        if (null != type && !"".equals(type))
        {
            Locale locale = PortalUtils.getLocale(request);
            if ("pendingValidations".equals(type))
            {
                options.setFiltersToApplyOnLoad(Arrays
                        .asList(CCHelper.getPendingValidationsFilter(locale)));
            }
            else if ("approvedValidations".equals(type))
            {
                options.setFiltersToApplyOnLoad(Arrays
                        .asList(CCHelper.getApprovedValidationsFilter(locale)));
            }
            else if ("declinedValidations".equals(type))
            {
                options.setFiltersToApplyOnLoad(Arrays
                        .asList(CCHelper.getDeclinedValidationsFilter(locale)));
            }

            gridAdditionalParams.put("type", type);
            mv.addObject("type", type);
        }

        GridHelper.addGridSupport(request, mv, "cc_recordPositionValidations",
                options);

        return mv;
    }

    @RequestMapping("ajaxLoadRecordPositionValidations")
    public ModelAndView ajaxLoadRecordPositionValidations(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView(
                "cc/cc_recordPositionValidationsAjax");
        Locale locale = PortalUtils.getLocale(request);

        final GridQuery query = GridQueryFactory
                .newInstance("cc_recordPositionValidations", request, mv);

        List<GridFilter> customFilters = new ArrayList<>();
        customFilters.add(CCHelper.getPendingValidationsFilter(locale));
        customFilters.add(CCHelper.getApprovedValidationsFilter(locale));
        customFilters.add(CCHelper.getDeclinedValidationsFilter(locale));
        query.setCustomFilters(customFilters);

        GridHelper.runQuery(query);
        return mv;
    }

    @RequestMapping("displayRecordPositionApprovedValidationsNoReflection")
    public ModelAndView displayRecordPositionApprovedValidationsNoReflection(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "cc/cc_recordPositionApprovedValidationsNoReflection");
        String periodId = request.getParameter("periodId");
        request.setAttribute("periodId", periodId);
        mv.addObject("periodId", periodId);

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName(
                "ajaxLoadRecordPositionApprovedValidationsNoReflection");
        final Map<String, String> gridAdditionalParams = new HashMap<>();
        gridAdditionalParams.put("periodId", periodId);
        options.setGridAdditionalParams(gridAdditionalParams);

        GridHelper.addGridSupport(request, mv,
                "cc_recordPositionApprovedValidationsNoReflection", options);

        return mv;
    }

    @RequestMapping("ajaxLoadRecordPositionApprovedValidationsNoReflection")
    public ModelAndView ajaxLoadRecordPositionApprovedValidationsNoReflection(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView(
                "cc/cc_recordPositionApprovedValidationsNoReflectionAjax");
        GridHelper.runQuery(GridQueryFactory.newInstance(
                "cc_recordPositionApprovedValidationsNoReflection", request, mv));
        return mv;
    }

    @RequestMapping("displayRecordPositionActivities")
    public ModelAndView displayRecordPositionActivities(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordPositionActivities");
        String periodId = request.getParameter("periodId");
        if (periodId == null)
        {
            periodId = (String) request.getAttribute("periodId");
        }
        request.setAttribute("periodId", periodId);
        mv.addObject("periodId", periodId);

        List<NameValuePair> statusList = new ArrayList();
        statusList.add(new NameValuePair(PortalUtils.getI18nMessage(
                "i18n.CCController.Pending0055847662714788",
                PortalUtils.getLocale(request)), "0"));
        statusList.add(new NameValuePair(PortalUtils.getI18nMessage(
                "i18n.CCController.Active6302213816404740",
                PortalUtils.getLocale(request)), "1"));
        statusList.add(new NameValuePair(PortalUtils.getI18nMessage(
                "i18n.CCController.Disabled1232614656378444",
                PortalUtils.getLocale(request)), "2"));
        mv.addObject("activityStatusOptions", statusList);

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName("ajaxLoadRecordPositionActivities");
        final Map<String, String> gridAdditionalParams = new HashMap<>();
        gridAdditionalParams.put("periodId", periodId);
        options.setGridAdditionalParams(gridAdditionalParams);
        options.setGridCheckToggleAllMethod("gridCheckToggleAll");

        String type = request.getParameter("type");
        if (type == null)
        {
            type = (String) request.getAttribute("type");
        }
        if (null != type && !"".equals(type))
        {
            Locale locale = PortalUtils.getLocale(request);
            if ("pendingActivities".equals(type))
            {
                options.setFiltersToApplyOnLoad(
                        Arrays.asList(CCHelper.getPendingActivitiesFilter(locale)));
            }
            else if ("activeActivities".equals(type))
            {
                options.setFiltersToApplyOnLoad(
                        Arrays.asList(CCHelper.getActiveActivitiesFilter(locale)));
            }
            else if ("inactiveActivities".equals(type))
            {
                options.setFiltersToApplyOnLoad(Arrays
                        .asList(CCHelper.getInactiveActivitiesFilter(locale)));
            }

            gridAdditionalParams.put("type", type);
        }
        mv.addObject("type", type);

        GridHelper.addGridSupport(request, mv, "cc_recordPositionActivities",
                options);

        return mv;
    }

    @RequestMapping("ajaxLoadRecordPositionActivities")
    public ModelAndView ajaxLoadRecordPositionActivities(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_recordPositionActivitiesAjax");

        final GridQuery query = GridQueryFactory
                .newInstance("cc_recordPositionActivities", request, mv);
        Locale locale = PortalUtils.getLocale(request);

        List<GridFilter> customFilters = new ArrayList<>();
        customFilters.add(CCHelper.getPendingActivitiesFilter(locale));
        customFilters.add(CCHelper.getActiveActivitiesFilter(locale));
        customFilters.add(CCHelper.getInactiveActivitiesFilter(locale));
        query.setCustomFilters(customFilters);

        GridHelper.runQuery(query);

        return mv;
    }

    @RequestMapping("displayRecordPositionPositions")
    public ModelAndView displayRecordPositionPositions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordPositionPositions");
        String periodId = request.getParameter("periodId");
        request.setAttribute("periodId", periodId);
        mv.addObject("periodId", periodId);

        List<NameValuePair> statusList = new ArrayList();
        statusList.add(new NameValuePair(PortalUtils.getI18nMessage(
                "i18n.cc_recordPositionPendingActivitiesAjax.Pending2388423983135578",
                PortalUtils.getLocale(request)), "0"));
        statusList.add(new NameValuePair(PortalUtils.getI18nMessage(
                "i18n.cc_recordPositionPendingPositionsAjax.Approved6289361581281130",
                PortalUtils.getLocale(request)), "1"));
        statusList.add(new NameValuePair(PortalUtils.getI18nMessage(
                "i18n.cc_recordPositionPendingPositionsAjax.Declined9211403076461636",
                PortalUtils.getLocale(request)), "2"));
        mv.addObject("statusOptions", statusList);

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName("ajaxLoadRecordPositionPositions");
        final Map<String, String> gridAdditionalParams = new HashMap<>();
        gridAdditionalParams.put("periodId", periodId);
        options.setGridAdditionalParams(gridAdditionalParams);
        options.setGridCheckToggleAllMethod("gridCheckToggleAll");

        String type = request.getParameter("type");
        if (null != type && !"".equals(type))
        {
            Locale locale = PortalUtils.getLocale(request);
            if ("pendingPositions".equals(type))
            {
                options.setFiltersToApplyOnLoad(
                        Arrays.asList(CCHelper.getPendingPositionsFilter(locale)));
            }
            else if ("approvedAndVisiblePositions".equals(type))
            {
                options.setFiltersToApplyOnLoad(Arrays.asList(
                        CCHelper.getApprovedAndVisiblePositionsFilter(locale)));
            }
            else if ("approvedAndNotVisiblePositions".equals(type))
            {
                options.setFiltersToApplyOnLoad(Arrays.asList(
                        CCHelper.getApprovedAndNotVisiblePositionsFilter(locale)));
            }
            else if ("declinedPositions".equals(type))
            {
                options.setFiltersToApplyOnLoad(
                        Arrays.asList(CCHelper.getDeclinedPositionsFilter(locale)));
            }

            gridAdditionalParams.put("type", type);
        }

        GridHelper.addGridSupport(request, mv, "cc_recordPositionPositions",
                options);

        return mv;
    }

    @RequestMapping("ajaxLoadRecordPositionPositions")
    public ModelAndView ajaxLoadRecordPositionPositions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_recordPositionPositionsAjax");

        final GridQuery query = GridQueryFactory
                .newInstance("cc_recordPositionPositions", request, mv);
        Locale locale = PortalUtils.getLocale(request);

        List<GridFilter> customFilters = new ArrayList<>();
        customFilters.add(CCHelper.getPendingPositionsFilter(locale));
        customFilters.add(CCHelper.getApprovedAndVisiblePositionsFilter(locale));
        customFilters.add(CCHelper.getApprovedAndNotVisiblePositionsFilter(locale));
        customFilters.add(CCHelper.getDeclinedPositionsFilter(locale));
        query.setCustomFilters(customFilters);

        GridHelper.runQuery(query);

        return mv;
    }

    @RequestMapping("displayRecordPositionStudentsWithAtleastOnePosition")
    public ModelAndView displayRecordPositionStudentsWithAtleastOnePosition(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "cc/cc_recordPositionStudentsWithAtleastOnePosition");
        String periodId = request.getParameter("periodId");
        request.setAttribute("periodId", periodId);
        mv.addObject("periodId", periodId);

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName(
                "ajaxLoadRecordPositionStudentsWithAtleastOnePosition");
        final Map<String, String> gridAdditionalParams = new HashMap<>();
        gridAdditionalParams.put("periodId", periodId);
        options.setGridAdditionalParams(gridAdditionalParams);
        // options.setGridCheckToggleAllMethod("gridCheckToggleAll");

        GridHelper.addGridSupport(request, mv,
                "cc_recordPositionStudentsWithAtleastOnePosition", options);

        return mv;
    }

    @RequestMapping("ajaxLoadRecordPositionStudentsWithAtleastOnePosition")
    public ModelAndView ajaxLoadRecordPositionStudentsWithAtleastOnePosition(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView(
                "cc/cc_recordPositionStudentsWithAtleastOnePositionAjax");
        GridHelper.runQuery(GridQueryFactory.newInstance(
                "cc_recordPositionStudentsWithAtleastOnePosition", request, mv));

        return mv;
    }

    @RequestMapping("displayRecordPositionApprovedValidationsZeroCompetencies")
    public ModelAndView displayRecordPositionApprovedValidationsZeroCompetencies(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "cc/cc_recordPositionApprovedValidationsZeroCompetencies");
        String periodId = request.getParameter("periodId");
        request.setAttribute("periodId", periodId);
        mv.addObject("periodId", periodId);

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName(
                "ajaxLoadRecordPositionApprovedValidationsZeroCompetencies");
        final Map<String, String> gridAdditionalParams = new HashMap<>();
        gridAdditionalParams.put("periodId", periodId);
        options.setGridAdditionalParams(gridAdditionalParams);

        GridHelper.addGridSupport(request, mv,
                "cc_recordPositionApprovedValidationsZeroCompetencies", options);

        return mv;
    }

    @RequestMapping("ajaxLoadRecordPositionApprovedValidationsZeroCompetencies")
    public ModelAndView ajaxLoadRecordPositionApprovedValidationsZeroCompetencies(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView(
                "cc/cc_recordPositionApprovedValidationsZeroCompetenciesAjax");
        GridHelper.runQuery(GridQueryFactory.newInstance(
                "cc_recordPositionApprovedValidationsZeroCompetencies", request,
                mv));

        return mv;
    }

    @RequestMapping("displayRecordPositionPendingExternalValidations")
    public ModelAndView displayRecordPositionPendingExternalValidations(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "cc/cc_recordPositionPendingExternalValidations");
        String periodId = request.getParameter("periodId");
        request.setAttribute("periodId", periodId);
        mv.addObject("periodId", periodId);

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName(
                "ajaxLoadRecordPositionPendingExternalValidations");
        final Map<String, String> gridAdditionalParams = new HashMap<>();
        gridAdditionalParams.put("periodId", periodId);
        options.setGridAdditionalParams(gridAdditionalParams);
        options.setGridCheckToggleAllMethod("gridCheckToggleAll");

        GridHelper.addGridSupport(request, mv,
                "cc_recordPositionPendingExternalValidations", options);

        return mv;
    }

    @RequestMapping("ajaxLoadRecordPositionPendingExternalValidations")
    public ModelAndView ajaxLoadRecordPositionPendingExternalValidations(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView(
                "cc/cc_recordPositionPendingExternalValidationsAjax");
        GridHelper.runQuery(GridQueryFactory.newInstance(
                "cc_recordPositionPendingExternalValidations", request, mv));

        return mv;
    }

    @RequestMapping("displayRecordPositionPendingValidations10Days")
    public ModelAndView displayRecordPositionPendingValidations10Days(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "cc/cc_recordPositionPendingValidations10Days");
        String periodId = request.getParameter("periodId");
        request.setAttribute("periodId", periodId);
        mv.addObject("periodId", periodId);

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName(
                "ajaxLoadRecordPositionPendingValidations10Days");
        final Map<String, String> gridAdditionalParams = new HashMap<>();
        gridAdditionalParams.put("periodId", periodId);
        options.setGridAdditionalParams(gridAdditionalParams);
        options.setGridCheckToggleAllMethod("gridCheckToggleAll");

        GridHelper.addGridSupport(request, mv,
                "cc_recordPositionPendingValidations10Days", options);

        return mv;
    }

    @RequestMapping("ajaxLoadRecordPositionPendingValidations10Days")
    public ModelAndView ajaxLoadRecordPositionPendingValidations10Days(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView(
                "cc/cc_recordPositionPendingValidations10DaysAjax");
        GridHelper.runQuery(GridQueryFactory.newInstance(
                "cc_recordPositionPendingValidations10Days", request, mv));

        return mv;
    }

    @RequestMapping("displayRecordPositionApprovedValidations10Days")
    public ModelAndView displayRecordPositionApprovedValidations10Days(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "cc/cc_recordPositionApprovedValidations10Days");
        String periodId = request.getParameter("periodId");
        request.setAttribute("periodId", periodId);
        mv.addObject("periodId", periodId);

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName(
                "ajaxLoadRecordPositionApprovedValidations10Days");
        final Map<String, String> gridAdditionalParams = new HashMap<>();
        gridAdditionalParams.put("periodId", periodId);
        options.setGridAdditionalParams(gridAdditionalParams);

        GridHelper.addGridSupport(request, mv,
                "cc_recordPositionApprovedValidations10Days", options);

        return mv;
    }

    @RequestMapping("ajaxLoadRecordPositionApprovedValidations10Days")
    public ModelAndView ajaxLoadRecordPositionApprovedValidations10Days(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView(
                "cc/cc_recordPositionApprovedValidations10DaysAjax");
        GridHelper.runQuery(GridQueryFactory.newInstance(
                "cc_recordPositionApprovedValidations10Days", request, mv));

        return mv;
    }

    @RequestMapping("displayRecordPositionDeclinedValidations10Days")
    public ModelAndView displayRecordPositionDeclinedValidations10Days(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "cc/cc_recordPositionDeclinedValidations10Days");
        String periodId = request.getParameter("periodId");
        request.setAttribute("periodId", periodId);
        mv.addObject("periodId", periodId);

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName(
                "ajaxLoadRecordPositionDeclinedValidations10Days");
        final Map<String, String> gridAdditionalParams = new HashMap<>();
        gridAdditionalParams.put("periodId", periodId);
        options.setGridAdditionalParams(gridAdditionalParams);
        options.setGridCheckToggleAllMethod("gridCheckToggleAll");

        GridHelper.addGridSupport(request, mv,
                "cc_recordPositionDeclinedValidations10Days", options);

        return mv;
    }

    @RequestMapping("ajaxLoadRecordPositionDeclinedValidations10Days")
    public ModelAndView ajaxLoadRecordPositionDeclinedValidations10Days(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView(
                "cc/cc_recordPositionDeclinedValidations10DaysAjax");
        GridHelper.runQuery(GridQueryFactory.newInstance(
                "cc_recordPositionDeclinedValidations10Days", request, mv));

        return mv;
    }

    @RequestMapping("displayPendingValidationsWithTimeTracking")
    public ModelAndView displayPendingValidationsWithTimeTracking(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "cc/cc_pendingValidationsWithTimeTracking");

        CCModule module = getModule(request);

        QueryBuilder query = new QueryBuilder(
                "select rp.id, rp.dateAdded, position.id, period.name, period.l2Name, activity.activity, activity.l2Activity, position.title, position.l2Title, record.id, owner.id, owner.firstAndLastName from CCRecordPosition rp join rp.position as position join rp.position.activity as activity join rp.position.activity.period as period join rp.record as record join rp.record.owner as owner");
        query.append(" where rp.hours < position.hoursToTrack ");
        query.append(" and rp.status=? ", CCRecordPosition.STATUS_PENDING);
        query.append(" and activity.id in " + CCHelper.getValidActivitiesForUserHQL(
                PortalUtils.getUserLoggedIn(request), module, request));
        if (StringUtils.isInteger(request.getParameter("periodId")))
        {
            query.append(" and period.id=? ",
                    Integer.valueOf(request.getParameter("periodId")));
        }
        else
        {
            query.append(" and record.module=? and period.active=true ", module);
        }

        List<OrbisHqlResultSet> pendingValidations = getHt().f(query.getStr(),
                query.getParams());

        mv.addObject("pendingValidations", pendingValidations);
        populatePositionValidators(mv,
                pendingValidations.stream().map(rp -> rp.select("position.id"))
                        .map(Object::toString).map(Integer::valueOf).distinct()
                        .collect(Collectors.toList()));
        return mv;
    }

    @RequestMapping("displayMassUpdateActivityStatus")
    public ModelAndView displayMassUpdateActivityStatus(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_massUpdateActivityStatus");
        mv.addObject("selectedIds", request.getParameter("selectedIds"));
        return mv;
    }

    @RequestMapping("massUpdateActivityStatus")
    public ModelAndView massUpdateActivityStatus(HttpServletRequest request,
            HttpServletResponse response)
    {
        try
        {
            new MassUpdateCCActivityStatusThread(
                    new JSONArray(request.getParameter("selectedIds")),
                    request.getParameter("status"), request).start();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        ModelAndView mv = displayHome(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.CCController.Massupdate4396801516767611")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    @RequestMapping("clonePeriod")
    public ModelAndView clonePeriod(HttpServletRequest request,
            HttpServletResponse response)
    {

        CCPeriod newPeriod = null;

        try
        {
            CCPeriod p = CCHelper.getCCPeriod(request);
            CCModule module = p.getModule();

            // CLONE PERIOD
            newPeriod = (CCPeriod) p.clone();
            newPeriod.setModule(module);
            newPeriod.setName(p.getName() + "_clone");
            newPeriod.setL2Name(p.getL2Name() + "_clone");
            PortalUtils.getHt().save(newPeriod);
        }
        catch (Exception e)
        {

        }
        CCClonePeriodThread cloneThread = new CCClonePeriodThread();
        cloneThread.setNewPeriod(newPeriod);
        cloneThread.setRequest(request);
        cloneThread.start();

        ModelAndView mv = displayHomeModuleConfig(request, response);

        FlashMessageUtils.success(request,
                "i18n.CCController.Periodisbe0416750764357721");

        return mv;
    }

    @RequestMapping("clonePosition")
    public ModelAndView clonePosition(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = null;

        if (StringUtils.isInteger(request.getParameter("positionId"))
                && null != request.getParameterValues("periodsToCloneTo")
                && StringUtils.isInteger(request.getParameter("cloneLevel")))
        {
            CCPosition position = CCHelper.getCCPosition(request);
            CCPosition newPosition = new CCPosition();
            if (position != null)
            {
                String[] periodIds = request.getParameterValues("periodsToCloneTo");

                int cloneLevel = Integer.valueOf(request.getParameter("cloneLevel"));
                boolean includeCompetencies = RequestUtils.isChecked(request,
                        "includeCompetencies");

                for (int i = 0; i < periodIds.length; i++)
                {
                    try
                    {
                        CCPeriod period = (CCPeriod) getHt().load(CCPeriod.class,
                                Integer.valueOf(periodIds[i]));

                        if (cloneLevel >= 1)
                        {
                            CCActivity oldActivity = position.getActivity();
                            CCActivity newActivity = getActivityIfExist(oldActivity,
                                    period);

                            if (newActivity == null)
                            {
                                newActivity = (CCActivity) oldActivity.clone();
                                newActivity.setPeriod(period);
                                newActivity.setActivity(
                                        oldActivity.getActivity() + "_clone");
                                newActivity
                                        .setCreatedBy(oldActivity.getCreatedBy());
                                newActivity.setCurrentOwner(
                                        oldActivity.getCurrentOwner());
                                newActivity
                                        .setUpdatedBy(oldActivity.getUpdatedBy());
                                newActivity.setUpdatedOn(new Date());
                                newActivity.setOnRecordCount(0);
                                DFHelper.copyFileUploadsForClonedEntity(
                                        newActivity);
                                getHt().save(newActivity);

                                // CLONE ACTIVITY-DIRECTORS
                                List<CCActivityDirectors> ads = PortalUtils.getHt()
                                        .find("from CCActivityDirectors ads where ads.activity=?",
                                                oldActivity);
                                for (CCActivityDirectors ad : ads)
                                {
                                    CCActivityDirectors newAD = new CCActivityDirectors();
                                    newAD.setActivity(newActivity);
                                    newAD.setDirectors(ad.getDirectors());
                                    PortalUtils.getHt().save(newAD);
                                }
                            }

                            if (cloneLevel >= 2)
                            {
                                newPosition = (CCPosition) position.clone();
                                if (newPosition.getCreatedBy() == null)
                                {
                                    newPosition.setCreatedBy(
                                            PortalUtils.getUserLoggedIn(request));
                                }
                                if (newPosition.getCreatedDate() == null)
                                {
                                    newPosition.setCreatedDate(new Date());
                                }
                                newPosition.setOnRecordCount(0);
                                newPosition.setActivity(newActivity);
                                DFHelper.copyFileUploadsForClonedEntity(
                                        newPosition);
                                getHt().save(newPosition);
                                if (includeCompetencies)
                                {
                                    CompetencyAnticipatedHelper
                                            .cloneCompetencyConfigs(position,
                                                    newPosition);
                                }
                                if (cloneLevel >= 3)
                                {
                                    List<CCPositionValidator> validators = getHt()
                                            .find("from CCPositionValidator pv where pv.position = ?",
                                                    position);

                                    for (CCPositionValidator pv : validators)
                                    {
                                        CCPositionValidator newPV = new CCPositionValidator();
                                        newPV.setPosition(newPosition);
                                        newPV.setValidator(pv.getValidator());
                                        getHt().save(newPV);
                                    }

                                    if (cloneLevel == 4)
                                    {
                                        List<CCPositionAchievementAdmin> paas = getHt()
                                                .find("from CCPositionAchievementAdmin paa where paa.position=? ",
                                                        position);

                                        for (CCPositionAchievementAdmin paa : paas)
                                        {
                                            CCPositionAchievementAdmin newPaa = new CCPositionAchievementAdmin();
                                            newPaa.setPosition(newPosition);
                                            newPaa.setAchievement(
                                                    paa.getAchievement());
                                            getHt().save(newPaa);
                                        }

                                    }
                                }
                            }

                        }
                    }
                    catch (Exception e)
                    {
                        e.printStackTrace();
                    }
                }

                if (!StringUtils.isEmpty(request.getParameter("goTo")))
                {
                    if ("positionOverview".equals(request.getParameter("goTo")))
                    {
                        mv = displayPositionOverview(request, response);
                        mv.addObject("clonedPosition", newPosition);
                    }
                }
                else
                {
                    request.setAttribute("positionId", position.getId());
                    mv = displayPositionOverview(request, response);
                    mv.addObject("successMessage",
                            PortalUtils.getI18nMessage(
                                    "i18n.CCController.Thispositi7479987020237596",
                                    PortalUtils.getLocale(request)));
                }
            }
        }

        if (mv == null)
        {
            if (StringUtils.isInteger(request.getParameter("positionId")))
            {
                request.setAttribute("positionId",
                        Integer.valueOf(request.getParameter("positionId")));
                mv = displayPositionOverview(request, response);
                mv.addObject("errorMessage",
                        PortalUtils.getI18nMessage(
                                "i18n.CCController.Cloningthi5151083587628097",
                                PortalUtils.getLocale(request)));
            }
            else
            {
                mv = displayHome(request, response);
                mv.addObject("errorMessage",
                        PortalUtils.getI18nMessage(
                                "i18n.CCController.Cloningact7462324168786560",
                                PortalUtils.getLocale(request)));
            }
        }

        return mv;
    }

    @RequestMapping("cloneActivity")
    public ModelAndView cloneActivity(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = null;
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (StringUtils.isInteger(request.getParameter("activityId"))
                && null != request.getParameterValues("periodsToCloneTo"))
        {
            CCActivity oldActivity = CCHelper.getCCActivity(request);
            CCActivity newActivity = new CCActivity();

            String[] periodIds = request.getParameterValues("periodsToCloneTo");

            for (int i = 0; i < periodIds.length; i++)
            {
                try
                {
                    CCPeriod period = (CCPeriod) getHt().load(CCPeriod.class,
                            Integer.valueOf(periodIds[i]));

                    newActivity = (CCActivity) oldActivity.clone();
                    newActivity.setPeriod(period);
                    newActivity.setActivity(oldActivity.getActivity() + "_clone");
                    newActivity.setCreatedBy(oldActivity.getCreatedBy());
                    newActivity.setCurrentOwner(oldActivity.getCurrentOwner());
                    newActivity.setUpdatedBy(oldActivity.getUpdatedBy());
                    newActivity.setUpdatedOn(new Date());
                    newActivity.setOnRecordCount(0);
                    DFHelper.copyFileUploadsForClonedEntity(newActivity);
                    getHt().save(newActivity);
                    CCActivityHelper.cloneTags(oldActivity, newActivity,
                            userLoggedIn);
                    // CLONE ACTIVITY-DIRECTORS
                    List<CCActivityDirectors> ads = PortalUtils.getHt().find(
                            "from CCActivityDirectors ads where ads.activity=?",
                            oldActivity);
                    for (CCActivityDirectors ad : ads)
                    {
                        CCActivityDirectors newAD = new CCActivityDirectors();
                        newAD.setActivity(newActivity);
                        newAD.setDirectors(ad.getDirectors());
                        PortalUtils.getHt().save(newAD);
                    }
                    List<CCPosition> positions = getHt().find(
                            "from CCPosition p where p.activity=? and requestDenied is null",
                            new Object[] { oldActivity });
                    for (CCPosition oldPosition : positions)
                    {
                        CCPosition newPosition = (CCPosition) oldPosition.clone();
                        newPosition.setOnRecordCount(0);
                        newPosition.setActivity(newActivity);
                        DFHelper.copyFileUploadsForClonedEntity(newPosition);
                        getHt().save(newPosition);

                        CCHelper.clonePositionLearningOutcomes(oldPosition,
                                newPosition);

                        CCHelper.clonePositionAchivementAdmins(oldPosition,
                                newPosition);
                        CCHelper.clonePositionValidators(oldPosition, newPosition);
                        CompetencyAnticipatedHelper
                                .cloneCompetencyConfigs(oldPosition, newPosition);
                    }

                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }

            if (!StringUtils.isEmpty(request.getParameter("goTo")))
            {
                if ("activityOverview".equals(request.getParameter("goTo")))
                {
                    mv = displayActivityOverview(request, response);
                    mv.addObject("clonedActivity", newActivity);
                }
            }
            else
            {
                request.setAttribute("positionId", oldActivity.getId());
                mv = displayActivityOverview(request, response);
                mv.addObject("successMessage",
                        new I18nLabel(
                                "i18n.CCController.ThisPositi6172036949713621")
                                .getTranslation(PortalUtils.getLocale(request)));
            }

        }

        if (null == mv)
        {
            if (StringUtils.isInteger(request.getParameter("positionId")))
            {
                request.setAttribute("positionId",
                        Integer.valueOf(request.getParameter("positionId")));
                mv = displayPositionOverview(request, response);
                mv.addObject("errorMessage",
                        new I18nLabel(
                                "i18n.CCController.Cloningthi9113133317526932")
                                .getTranslation(PortalUtils.getLocale(request)));
            }
            else
            {
                mv = displayHome(request, response);
                mv.addObject("errorMessage",
                        new I18nLabel(
                                "i18n.CCController.Cloningact6631578062876864")
                                .getTranslation(PortalUtils.getLocale(request)));
            }
        }

        return mv;
    }

    private CCActivity getActivityIfExist(CCActivity activity, CCPeriod period)
    {
        CCActivity ret = null;
        List<CCActivity> results = getHt().find(
                "from CCActivity a where a.period = ? and a.category = ? and a.organization = ? and a.department = ? and a.activity = ?",
                new Object[] { period, activity.getCategory(),
                        activity.getOrganization(), activity.getDepartment(),
                        activity.getActivity() });
        if (!results.isEmpty())
        {
            ret = results.get(0);
        }
        return ret;
    }

    @RequestMapping("displayValidatorDetailsPendingTab")
    public ModelAndView displayValidatorDetailsPendingTab(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_validatorDetailsPendingTab");

        UserDetailsImpl validator = CCHelper.getValidator(request);

        if (validator == null)
        {
            mv = displayHome(request, response);
            mv.addObject("validatorLookupError", Boolean.TRUE);
        }
        else
        {
            CCHelper.populateValidatorData(request, mv, validator,
                    getModule(request));
        }

        mv.addObject("acrmUser", validator);
        mv.addObject("returnToValidatorDetails", Boolean.TRUE);

        SiteElement se = NHelper
                .getAcrmCareerSiteElement(PortalUtils.getUserLoggedIn(request));
        mv.addObject("acrmFullPath", se != null ? se.getFullPath() + ".htm" : "");

        return mv;
    }

    @RequestMapping("displayValidatorDetailsApprovedTab")
    public ModelAndView displayValidatorDetailsApprovedTab(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_validatorDetailsApprovedTab");

        UserDetailsImpl validator = CCHelper.getValidator(request);

        if (validator == null)
        {
            mv = displayHome(request, response);
            mv.addObject("validatorLookupError", Boolean.TRUE);
        }
        else
        {
            CCHelper.populateValidatorData(request, mv, validator,
                    getModule(request));
        }

        mv.addObject("acrmUser", validator);
        mv.addObject("returnToValidatorDetails", Boolean.TRUE);

        SiteElement se = NHelper
                .getAcrmCareerSiteElement(PortalUtils.getUserLoggedIn(request));
        mv.addObject("acrmFullPath", se != null ? se.getFullPath() + ".htm" : "");

        return mv;
    }

    @RequestMapping("displayValidatorDetailsDeclinedTab")
    public ModelAndView displayValidatorDetailsDeclinedTab(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_validatorDetailsDeclinedTab");

        UserDetailsImpl validator = CCHelper.getValidator(request);

        if (validator == null)
        {
            mv = displayHome(request, response);
            mv.addObject("validatorLookupError", Boolean.TRUE);
        }
        else
        {
            CCHelper.populateValidatorData(request, mv, validator,
                    getModule(request));
        }

        mv.addObject("acrmUser", validator);
        mv.addObject("returnToValidatorDetails", Boolean.TRUE);

        SiteElement se = NHelper
                .getAcrmCareerSiteElement(PortalUtils.getUserLoggedIn(request));
        mv.addObject("acrmFullPath", se != null ? se.getFullPath() + ".htm" : "");

        return mv;
    }

    @RequestMapping("displayValidatorDetailsActivitesTab")
    public ModelAndView displayValidatorDetailsActivitesTab(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_validatorDetailsActivitiesTab");

        UserDetailsImpl validator = CCHelper.getValidator(request);

        if (validator == null)
        {
            mv = displayHome(request, response);
            mv.addObject("validatorLookupError", Boolean.TRUE);
        }
        else
        {
            request.setAttribute("positionStats", true);
            CCHelper.populateValidatorData(request, mv, validator,
                    getModule(request));
        }

        mv.addObject("acrmUser", validator);
        mv.addObject("returnToValidatorDetails", Boolean.TRUE);

        SiteElement se = NHelper
                .getAcrmCareerSiteElement(PortalUtils.getUserLoggedIn(request));
        mv.addObject("acrmFullPath", se != null ? se.getFullPath() + ".htm" : "");

        return mv;
    }

    /**
     * Display my validations
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayMyValidations")
    public ModelAndView displayMyValidations(HttpServletRequest request,
            HttpServletResponse response)
    {
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (!CCHelper.isValidator(userLoggedIn))
        {
            return new ModelAndView("cc/cc_noPermission");
        }

        CCHelper.updateCurrentUserRole(CCHelper.CC_ROLE_VALIDATOR,
                PortalUtils.getUserLoggedIn(request));

        ModelAndView mv = new ModelAndView("cc/cc_validatorHomePendingTab");

        CCHelper.populateValidatorData(request, mv, userLoggedIn,
                getModule(request));
        mv.addObject("returnToMyValidations", Boolean.TRUE);

        if (request.getParameter("createdPosition") != null)
        {
            FlashMessageUtils.success(request,
                    "i18n.CCController.Positioncr7081835331571678");
        }

        return mv;
    }

    @RequestMapping("displayMyValidationsApprovedTab")
    public ModelAndView displayMyValidationsApprovedTab(HttpServletRequest request,
            HttpServletResponse response)
    {
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (!CCHelper.isValidator(userLoggedIn))
        {
            return new ModelAndView("cc/cc_noPermission");
        }

        CCHelper.updateCurrentUserRole(CCHelper.CC_ROLE_VALIDATOR,
                PortalUtils.getUserLoggedIn(request));

        ModelAndView mv = new ModelAndView("cc/cc_validatorHomeApprovedTab");

        CCHelper.populateValidatorData(request, mv, userLoggedIn,
                getModule(request));
        mv.addObject("returnToMyValidations", Boolean.TRUE);

        return mv;
    }

    @RequestMapping("displayMyValidationsDeclinedTab")
    public ModelAndView displayMyValidationsDeclinedTab(HttpServletRequest request,
            HttpServletResponse response)
    {
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (!CCHelper.isValidator(userLoggedIn))
        {
            return new ModelAndView("cc/cc_noPermission");
        }

        CCHelper.updateCurrentUserRole(CCHelper.CC_ROLE_VALIDATOR,
                PortalUtils.getUserLoggedIn(request));

        ModelAndView mv = new ModelAndView("cc/cc_validatorHomeDeclinedTab");

        CCHelper.populateValidatorData(request, mv, userLoggedIn,
                getModule(request));
        mv.addObject("returnToMyValidations", Boolean.TRUE);

        return mv;
    }

    @RequestMapping("displayMyValidationsActivitiesTab")
    public ModelAndView displayMyValidationsActivitiesTab(
            HttpServletRequest request, HttpServletResponse response)
    {
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (!CCHelper.isValidator(userLoggedIn))
        {
            return new ModelAndView("cc/cc_noPermission");
        }

        CCHelper.updateCurrentUserRole(CCHelper.CC_ROLE_VALIDATOR,
                PortalUtils.getUserLoggedIn(request));

        ModelAndView mv = new ModelAndView("cc/cc_validatorHomeActivitiesTab");
        request.setAttribute("positionStats", true);
        CCHelper.populateValidatorData(request, mv, userLoggedIn,
                getModule(request));
        mv.addObject("returnToMyValidations", Boolean.TRUE);

        return mv;
    }

    /**
     * Display approved validations
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayApprovedValidationWithId")
    public ModelAndView displayApprovedValidationWithId(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = displayRecordPositionEdit(request, response);

        return mv;
    }

    @RequestMapping("displayRecordWithId")
    public ModelAndView displayRecordWithId(HttpServletRequest request,
            HttpServletResponse response)
    {
        List<CCRecord> record = getHt().find("from CCRecord r where r.id=? ",
                Integer.valueOf(request.getParameter("recordId")));
        int studentId = record.get(0).getOwner().getId();
        request.setAttribute("studentId", studentId);
        ModelAndView mv = displayRecord(request, response);

        return mv;
    }

    /**
     * Display declined validations
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayDeclinedValidations")
    public ModelAndView displayDeclinedValidations(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_declinedValidations");
        List<CCRecordPosition> declinedValidations = getHt().find(
                "from CCRecordPosition p where p.position.activity.period.module=? and p.position.activity.period=? and p.status='Declined'",
                new Object[] { getModule(request), CCHelper.getCCPeriod(request) });
        mv.addObject("declinedValidations", declinedValidations);
        populatePositionValidators(mv,
                declinedValidations.stream().map(rp -> rp.getPosition().getId())
                        .distinct().collect(Collectors.toList()));
        return mv;
    }

    @RequestMapping("displayModuleEdit")
    public ModelAndView displayModuleEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_moduleEdit");
        SiteElement siteElement = NHelper
                .getFirstSiteElementByType("ccRecordLookupController");

        if (siteElement != null)
        {
            mv.addObject("CCRecordLookupController", true);
        }

        CCModule module = getModule(request);
        AcrmReflectionConfig conf = module.getReflectionConfigurableSupport()
                .getReflectionConfig(module);
        mv.addObject("reflectionsEnabled",
                conf != null && conf.isAnyReflectionEnabled());

        return mv;
    }

    /**
     * Display my record
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayMyRecord")
    public ModelAndView displayMyRecord(HttpServletRequest request,
            HttpServletResponse response)
    {
        return displayMyRecord2(request, response);
    }

    @RequestMapping("displayMyRecord2")
    public ModelAndView displayMyRecord2(HttpServletRequest request,
            HttpServletResponse response)
    {
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (!userLoggedIn.isStudent() && !CCHelper.isAlumni(userLoggedIn))
        {
            return new ModelAndView("cc/cc_noPermission");
        }

        CCHelper.updateCurrentUserRole(
                userLoggedIn.isStudent() ? CCHelper.CC_ROLE_STUDENT
                        : CCHelper.CC_ROLE_ALUMNI,
                userLoggedIn);

        ModelAndView mv = new ModelAndView("cc/cc_record2");
        populateRecord(mv, getModule(request), userLoggedIn, true, request);

        mv.addObject("fromCards",
                RequestUtils.getBooleanParameter(request, "fromCards", true));

        mv.addObject("outlookConfig",
                ECOutlookHelper.getOutlookConfigurationMap().containsKey("ccr")
                        ? ECOutlookHelper.getOutlookConfigurationMap().get("ccr")
                        : null);

        return mv;
    }

    // Unused but retained for now as we may want to resurrect some
    // functionality that was here
    @Deprecated
    @RequestMapping("displayMyOldRecord")
    public ModelAndView displayMyOldRecord(HttpServletRequest request,
            HttpServletResponse response)
    {
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (!userLoggedIn.isStudent() && !CCHelper.isAlumni(userLoggedIn))
        {
            return new ModelAndView("cc/cc_noPermission");
        }

        CCHelper.updateCurrentUserRole(
                userLoggedIn.isStudent() ? CCHelper.CC_ROLE_STUDENT
                        : CCHelper.CC_ROLE_ALUMNI,
                userLoggedIn);

        ModelAndView mv = new ModelAndView("cc/cc_record");
        CCModule ccModule = getModule(request);
        populateRecord(mv, ccModule, userLoggedIn, true, request);

        mv.addObject("myRecordMode", "true");

        final Map<String, String> additionalParams = Maps.newHashMap();

        additionalParams.put("moduleId", ccModule.getId().toString());
        additionalParams.put("recordId",
                request.getAttribute("recordId").toString());
        additionalParams.put("myRecordMode", "true");

        final GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName("ajaxLoadRecordPositions");
        options.setGridLoadOnDocumentReady(true);

        options.setGridAdditionalParams(additionalParams);

        String mode = request.getParameter("mode");

        if (mode != null)
        {
            List gridFilters = new ArrayList();

            if ("approved".equals(mode))
            {
                gridFilters
                        .add(CCRecordPositionsHql.getCCRecordPositionsGridFilters(
                                PortalUtils.getLocale(request)).get(0));
            }
            else if ("competencies".equals(mode))
            {
                gridFilters
                        .add(CCRecordPositionsHql.getCCRecordPositionsGridFilters(
                                PortalUtils.getLocale(request)).get(1));
            }
            else if ("pending".equals(mode))
            {
                gridFilters
                        .add(CCRecordPositionsHql.getCCRecordPositionsGridFilters(
                                PortalUtils.getLocale(request)).get(2));
            }
            else if ("declined".equals(mode))
            {
                gridFilters
                        .add(CCRecordPositionsHql.getCCRecordPositionsGridFilters(
                                PortalUtils.getLocale(request)).get(3));
            }
            else if ("reflection".equals(mode))
            {
                gridFilters
                        .add(CCRecordPositionsHql.getCCRecordPositionsGridFilters(
                                PortalUtils.getLocale(request)).get(4));
            }
            else if ("noReflection".equals(mode))
            {
                gridFilters
                        .add(CCRecordPositionsHql.getCCRecordPositionsGridFilters(
                                PortalUtils.getLocale(request)).get(5));
            }

            options.setFiltersToApplyOnLoad(gridFilters);

            mv.addObject("mode", mode);
        }

        GridHelper.addGridSupport(request, mv, "cc_recordPositions", options);

        return mv;
    }

    @RequestMapping("displayRecommended")
    public ModelAndView displayRecommended(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recommendedForYou");
        return mv;
    }

    /**
     * Display record
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayRecord")
    public ModelAndView displayRecord(HttpServletRequest request,
            HttpServletResponse response)
    {
        return displayRecord2(request, response);
    }

    // TODO remove me
    @Deprecated
    @RequestMapping("displayOldRecord")
    public ModelAndView displayOldRecord(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_record");
        populateCCRecordPage(mv, request, response);

        final Map<String, String> additionalParams = Maps.newHashMap();

        additionalParams.put("moduleId", getModule(request).getId().toString());
        additionalParams.put("recordId",
                request.getAttribute("recordId").toString());
        additionalParams.put("myRecordMode", "false");

        final GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName("ajaxLoadRecordPositions");
        options.setGridLoadOnDocumentReady(true);

        options.setGridAdditionalParams(additionalParams);

        String mode = request.getParameter("mode");

        if (mode != null)
        {
            List gridFilters = new ArrayList();

            if ("approved".equals(mode))
            {
                gridFilters
                        .add(CCRecordPositionsHql.getCCRecordPositionsGridFilters(
                                PortalUtils.getLocale(request)).get(0));
            }
            else if ("competencies".equals(mode))
            {
                gridFilters
                        .add(CCRecordPositionsHql.getCCRecordPositionsGridFilters(
                                PortalUtils.getLocale(request)).get(1));
            }
            else if ("pending".equals(mode))
            {
                gridFilters
                        .add(CCRecordPositionsHql.getCCRecordPositionsGridFilters(
                                PortalUtils.getLocale(request)).get(2));
            }
            else if ("declined".equals(mode))
            {
                gridFilters
                        .add(CCRecordPositionsHql.getCCRecordPositionsGridFilters(
                                PortalUtils.getLocale(request)).get(3));
            }

            options.setFiltersToApplyOnLoad(gridFilters);
        }

        GridHelper.addGridSupport(request, mv, "cc_recordPositions", options);

        if (request.getParameter("loadRecord") != null)
        {
            mv.addObject("loadRecord", request.getParameter("loadRecord"));
        }

        return mv;
    }

    @RequestMapping("displayRecord2")
    public ModelAndView displayRecord2(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv;
        if (StringUtils.equalsAny(
                CCHelper.getCurrentUserRole(getModule(request),
                        PortalUtils.getUserLoggedIn(request)),
                CCHelper.CC_ROLE_STUDENT, CCHelper.CC_ROLE_ALUMNI))
        {
            mv = displayMyRecord2(request, response);
        }
        else
        {
            mv = new ModelAndView("cc/cc_record2");
            populateCCRecordPage(mv, request, response);

            CCModule ccModule = getModule(request);
            List<CCModule> ccModules = new ArrayList();
            ccModules.add(ccModule);
            CCHelper.populateCCPublicSiteElement(mv, ccModules, request);
        }

        return mv;
    }

    @RequestMapping("ajaxLoadRecordPositions")
    public ModelAndView ajaxLoadRecordPositions(HttpServletRequest request,
            HttpServletResponse response)
    {
        return CCHelper.ajaxLoadRecordPositions(request);
    }

    private void populateCCRecordPage(ModelAndView mv, HttpServletRequest request,
            HttpServletResponse response)
    {
        mv.addObject("impersonatorSe", NHelper.getImpersonatorSiteElement(request));

        String studentId = "";
        if (request.getAttribute("studentId") != null)
        {
            studentId = request.getAttribute("studentId").toString();
            mv.addObject("fromCC", true);
        }
        else
        {
            studentId = request.getParameter("studentId");
        }

        boolean recordFound = true;
        try
        {
            UserDetailsImpl user = (UserDetailsImpl) getHt()
                    .load(UserDetailsImpl.class, Integer.valueOf(studentId));
            AcrmRegistrationModule regModule = AcrmRegistrationController
                    .getRegistrationModule(user.getPrimaryGroup());
            mv.addObject("registrationModule", regModule);

            List<AcrmRegistrationQuestion> headerQuestions = PortalUtils.getHt()
                    .find("from AcrmRegistrationQuestion q where q.module=? and q.showInCCRHeader=true and q.active=true order by q.questionOrderCC",
                            regModule);
            List<AcrmRegistrationAnswer> headerAnswers = AcrmHelper
                    .prepareAnswersForQuestions(headerQuestions, request, user,
                            false);
            mv.addObject("moduleType", "ccr");
            mv.addObject("headerAnswers", headerAnswers);
            populateRecord(mv, getModule(request), user, false, request);
        }
        catch (Exception e)
        {
            e.printStackTrace();
            recordFound = false;
        }
        if (!recordFound)
        {
            mv = displayHome(request, response);
            mv.addObject("recordLookupError", Boolean.TRUE);
        }
        mv.addObject("myRecordMode", "false");
        InteractionHelper.populateInteractionActionsBar(mv, request);
    }

    /**
     * Delete record position entry
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("deleteRecordPositionEntry")
    public ModelAndView deleteRecordPositionEntry(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        return CCHelper.deleteRecordPositionEntry(request, response);
    }

    /**
     * Save record position entry
     *
     * @param request
     * @param response
     * @return
     * @throws JSONException
     */
    @RequestMapping("saveRecordPositionEntry")
    public ModelAndView saveRecordPositionEntry(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        return CCHelper.saveRecordPositionEntry(request, response);
    }

    @RequestMapping("ajaxUpdateRecordPositionTotalHours")
    public ModelAndView ajaxUpdateRecordPositionTotalHours(
            HttpServletRequest request, HttpServletResponse response)
            throws JSONException
    {
        return CCHelper.ajaxUpdateRecordPositionTotalHours(request, response);
    }

    /**
     * Display record position
     *
     * @param request
     * @param response
     * @return
     */
    @OrbisRepost
    @RequestMapping("displayRecordPositionEdit")
    public ModelAndView displayRecordPositionEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        CCRecordPosition rp = getRecordPosition(request);

        ModelAndView mv = new ModelAndView("cc/cc_recordPositionEdit");

        CCRecordPositionHelper.populateMVForRecordPositionEdit(mv, request, module,
                rp);
        CCHelper.populatePositionNodes(mv, rp.getPosition(), module,
                PortalUtils.getLocale(request));

        RequestUtils.repopulateMV(request, mv, "rpId", "limitedEdit", "recordId");

        return mv;
    }

    @OrbisRepost
    @RequestMapping("ajaxDisplayRecordPositionEdit")
    public ModelAndView ajaxDisplayRecordPositionEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        CCRecordPosition rp = getRecordPosition(request);

        ModelAndView mv = getShortCircuitView("cc/cc_recordPosition");

        CCRecordPositionHelper.populateMVForRecordPositionEdit(mv, request, module,
                rp);
        CCHelper.populatePositionNodes(mv, rp.getPosition(), module,
                PortalUtils.getLocale(request));
        mv.addObject("fromCards",
                RequestUtils.getBooleanParameter(request, "fromCards", false));
        return mv;
    }

    @OrbisRepost
    @RequestMapping("ajaxDisplayRecordActivity")
    public ModelAndView ajaxDisplayRecordActivity(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        CCActivity a = CCHelper.getCCActivity(request);

        ModelAndView mv = getShortCircuitView("cc/cc_activityOverview_student");

        CCHelper.populateActivityCommon(a, module, request, mv);
        Set<String> keysToPopulate = new HashSet<>();
        keysToPopulate.add("pendingValidations");
        keysToPopulate.add("approvedValidations");
        keysToPopulate.add("declinedValidations");
        keysToPopulate.add("pendingPositions");
        keysToPopulate.add("approvedPositions");
        keysToPopulate.add("declinedPositions");
        keysToPopulate.add("activePositions");
        keysToPopulate.add("disabledPositions");
        keysToPopulate.add("totalPositions");
        populateDashboardStats(mv, module, a, null, request,
                keysToPopulate);

        CCHelper.populateActivityQuestions(mv, module, a,
                PortalUtils.getUserLoggedIn(request), false);

        List<CCPosition> positions = PortalUtils.getHt().find(
                "from CCPosition p where p.activity=? and p.approvedBy is not null and p.enabled=true order by p.title",
                new Object[] { a });
        mv.addObject("activityPositions", positions);
        mv.addObject("numActivityPositions", positions.size());

        mv.addObject("fromCards",
                RequestUtils.getBooleanParameter(request, "fromCards", false));
        mv.addObject("comingFrom", request.getParameter("comingFrom"));

        SiteElement se = NHelper.getAcrmCareerSiteElement(
                PortalUtils.getUserLoggedIn(request));
        mv.addObject("acrmFullPath",
                se != null ? se.getFullPath() + ".htm" : "");

        mv.addObject("competenciesDisabled", module
                .getCompetencyUsecase() == Competenciable.COMP_DISABLED);
        return mv;
    }

    private CCRecordPosition getRecordPosition(HttpServletRequest request)
    {
        CCRecordPosition rp;
        if (RequestUtils.getBooleanParameter(request, "fromExternalPostion", false))
        {
            UserDetailsImpl student = getStudentForRecordPosition(request);
            CCRecord record = CCHelper.getOrAddCCRecord(getModule(request),
                    student);
            CCPosition position = CCHelper.getCCPosition(request);
            rp = CCHelper.getOrAddCCRecordPosition(record, position, request,
                    false);
            bindExternalPositionQuestions(request, getModule(request), rp);
            PortalUtils.getHt().saveOrUpdate(rp);
        }
        else
        {
            rp = CCHelper.getCCRecordPosition(request);
        }
        return rp;
    }

    /**
     * Display time tracking content
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayRecordPositionTracking")
    public ModelAndView displayRecordPositionTracking(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = new ModelAndView("cc/cc_timeTracking");

        if (request.getParameter("shortCircuit") != null)
        {
            mv.addObject(SiteController.SHORT_CIRCUIT_ATTRIBUTE_NAME, Boolean.TRUE);
        }

        int recordPos = Integer.parseInt(request.getParameter("recordPosition"));

        CCRecordPosition rp = (CCRecordPosition) PortalUtils.getHt().load(
                CCRecordPosition.class,
                Integer.valueOf(request.getParameter("recordPosition")));

        String hql = "select rpe from CCRecordPositionEntry rpe where rpe.recordPosition=?";
        List<CCRecordPositionEntry> rpes = PortalUtils.getHt().find(hql, recordPos);

        mv.addObject("rpes", rpes);
        mv.addObject("rp", rp);
        mv.addObject("limitedEdit", request.getParameter("limitedEdit"));
        mv.addObject("myRecordMode", request.getParameter("myRecordMode"));
        mv.addObject("datepickerCutoff", DateUtils.now());

        return mv;
    }

    public static void bindExternalPositionQuestions(HttpServletRequest request,
            CCModule module, CCRecordPosition rp)
    {
        CCExternalPositionQuestionModel ccExternalPostingQuestionModel = CCExternalPositionQuestionModelHelper
                .getExternalPositionQuestionModel(module);
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
        DFHelper.bindAnswers(ccExternalPostingQuestionModel, rp, request, user,
                true);
    }

    private ModelAndView displayRecordPositionExternalEdit(
            HttpServletRequest request, HttpServletResponse response,
            CCPosition position, UserDetailsImpl student)
    {
        CCModule module = getModule(request);
        CCRecordPosition rp = CCRecordPositionHelper.newInstance(request);
        ModelAndView mv = new ModelAndView("cc/cc_recordExternalPositionEdit");

        mv.addObject("ccModule", module);

        if (position != null)
        {

            CCExternalPositionQuestionModel model = CCExternalPositionQuestionModelHelper
                    .getExternalPositionQuestionModel(module);
            if (position.isExternalPosition())
            {
                rp.setPosition(position);
                boolean canWrite = CCRecordPositionHelper
                        .canWriteCCExternalRecordPosition(rp);
                DFHelper.populateModel(model.getDFModel(), canWrite, rp,
                        PortalUtils.getUserLoggedIn(request));
                mv.addObject("dfModel", model.getDFModel());
                mv.addObject("fromExternalPostion", true);
                mv.addObject("record", CCHelper.getCCRecord(module, student));
                mv.addObject("position", position);
            }

        }

        mv.addObject("rp", rp);
        return mv;
    }

    /**
     * Delete position from record
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("deleteRecordPosition")
    public ModelAndView deleteRecordPosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        try
        {
            CCRecordPosition rp = CCHelper.getCCRecordPosition(request);

            if (rp != null)
            {
                CCRecord record = rp.getRecord();

                CCHelper.updatePositionOnRecordCount(rp.getPosition());
                CCHelper.updateActivityOnRecordCount(
                        rp.getPosition().getActivity());
                DFHelper.deleteFileUploadsForDeletedEntity(rp);

                QueryBuilder q = new QueryBuilder();
                q.append(
                        "delete cra from acrm_competency_record_assessment cra join acrm_competency_record cr on cr.id=cra.record where cr.recordPosition=?;",
                        rp.getId());
                q.append(
                        "delete from acrm_competency_record where recordPosition=?;",
                        rp.getId());

                PortalUtils.getJt().update(q);

                DeletionNodeHelper.deleteContentItem(rp);

                record.setDateUpdated(new Date());
                PortalUtils.getHt().update(record);
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            /*
             * Don't try to do anything here. If this exception occurred it's
             * probably because the user tried to refresh his page after already
             * calling this action-method. Therefore the best response is just to
             * return the user back to the record-page, as if nothing bad happened.
             */
        }

        return isNotMyRecordMode(request) ? displayRecord(request, response)
                : displayMyRecord(request, response);
    }

    /**
     * Add position to a student's record
     */
    @RequestMapping("addPositionToRecord")
    public ModelAndView addPositionToRecord(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        UserDetailsImpl user = getStudentForRecordPosition(request);

        ModelAndView mv = null;
        String returnTo = request.getParameter("returnTo");

        CCPosition position = CCHelper.getCCPosition(request);
        if (user == null || !user.isEnabled() || user.isDeleted())
        {
            if ("cc_positionParticipants".equals(returnTo))
            {
                mv = displayPositionParticipants(request, response);
            }
            else if ("activity".equals(returnTo)
                    && CCHelper.getCCActivity(request) != null
                    && CCHelper.canView(CCHelper.getCCActivity(request), request))
            {
                mv = displayActivityParticipants(request, response);
            }
            else
            {
                mv = displayHome(request, response);
            }
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.CCController.InvalidUse7612345587524735")
                            .getTranslation(PortalUtils.getLocale(request)));
        }
        else
        {
            boolean foundPosition = false;
            CCRecordPosition rp = null;
            try
            {
                CCRecord record = CCHelper.getOrAddCCRecord(module, user);

                if (StringUtils
                        .isEmpty(request.getParameter("submitingAchievements"))
                        && CCHelper.recordHasPosition(record, position))
                {
                    if ("cc_positionParticipants".equals(returnTo))
                    {
                        mv = displayPositionParticipants(request, response);
                    }
                    else if ("activity".equals(returnTo)
                            && CCHelper.getCCActivity(request) != null
                            && CCHelper.canView(CCHelper.getCCActivity(request),
                                    request))
                    {
                        mv = displayActivityParticipants(request, response);
                    }
                    else
                    {
                        request.setAttribute("studentId", user.getId().toString());
                        mv = displayRecord(request, response);
                    }

                    mv.addObject("errorMessage",
                            new I18nLabel(
                                    "i18n.CCController.Studentalr5226640476239350")
                                    .getTranslation(
                                            PortalUtils.getLocale(request)));
                    return mv;
                }
                if (position != null && record != null
                        && !position.isExternalPosition())
                {
                    rp = CCHelper.getOrAddCCRecordPosition(record, position,
                            request, true);

                    if (!StringUtils
                            .isEmpty(request.getParameter("submitingAchievements"))
                            || !((module.isTrackAchievements()
                                    || module.isTrackLearningOutcomes())
                                    && module.isStudentSelectLOA()))
                    {
                        CCHelper.updateCCLearningOutcomeAchievement(module, rp,
                                request);
                    }

                    CCRecordPositionShortlist shortlist = CCHelper
                            .getRecordPositionShortlist(record, position);
                    if (shortlist != null)
                    {
                        DeletionNodeHelper.deleteContentItem(shortlist);
                    }

                    request.setAttribute("recordPosition", rp);
                    foundPosition = true;
                }
                else if (position != null && position.isExternalPosition())
                {
                    foundPosition = true;
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }

            if (foundPosition)
            {
                String submitingAchievements = request
                        .getParameter("submitingAchievements");
                if (StringUtils.isEmpty(submitingAchievements)
                        && (module.isTrackAchievements()
                                || module.isTrackLearningOutcomes())
                        && module.isStudentSelectLOA())
                {
                    mv = displayAddRecord(request, response);
                }
                else
                {
                    if ("cc_positionParticipants".equals(returnTo))
                    {
                        mv = displayPositionParticipants(request, response);
                    }
                    else if ("activity".equals(returnTo)
                            && CCHelper.getCCActivity(request) != null
                            && CCHelper.canView(CCHelper.getCCActivity(request),
                                    request))
                    {
                        mv = displayActivityParticipants(request, response);
                    }
                    else if (position != null && position.isExternalPosition())
                    {
                        mv = displayRecordPositionExternalEdit(request, response,
                                position, user);
                    }
                    else
                    {
                        mv = displayRecordPositionEdit(request, response);
                    }
                }

                if (rp != null && null == rp.getPosition().getProgram()
                        && module.isEmailValidator()
                        && !StringUtils.isEmpty(module.getFromEmailForValidators())
                        && (!module.isTrackLearningOutcomes()
                                || !module.isStudentSelectLOA()
                                || "true".equals(submitingAchievements)))
                {
                    CCPublicModule publicModule = CCHelper
                            .getCCPublicModule(module);
                    String siteUrl = PortalUtils.getSiteBaseUrl();
                    UserDetailsImpl userLoggedIn = PortalUtils
                            .getUserLoggedIn(request);

                    List<Object[]> validatorEmails = getHt().find(
                            "select validator.emailAddress, validator.username, validator.userLocale, validator.preferredFirstName, validator.lastName from CCPositionValidator cpv join cpv.validator validator where cpv.position.id="
                                    + request.getParameter("positionId"));

                    if (validatorEmails != null)
                    {
                        for (Object[] recipientData : validatorEmails)
                        {
                            boolean isL1Locale = LocaleUtils
                                    .isL1((String) recipientData[2]);
                            Locale userLocale = isL1Locale
                                    ? LocaleUtils.getDefaultLocale()
                                    : LocaleUtils.getSecondaryLocale();

                            String subject = isL1Locale
                                    ? module.getValidatorSubject()
                                    : module.getL2ValidatorSubject();
                            String body = isL1Locale ? module.getValidatorBody()
                                    : module.getL2ValidatorBody();

                            body += "<br><br>" + PortalUtils.getI18nMessage(
                                    "i18n.CCController.StudentNam8020909994070773",
                                    userLocale) + " "
                                    + rp.getRecord().getOwner().getFullName();
                            body += "<br>" + PortalUtils.getI18nMessage(
                                    "i18n.CCController.Position7977516700478454",
                                    userLocale) + " |ACTIVITY| - |POSITION|";
                            if (publicModule != null
                                    && (position.isExternalPosition()
                                            || module.isIncludeOpportunityLink()))
                            {
                                body += "<br><a href='https://" + siteUrl
                                        + publicModule.getSiteElementPath()
                                        + "?action=displayRecordPositionEdit&rpId="
                                        + rp.getId() + "&confirmationCode="
                                        + rp.getConfirmationCode()
                                        + "&validatorUsername=" + recipientData[1]
                                        + "'>"
                                        + PortalUtils.getI18nMessage(
                                                "i18n.CCController.ClickHere6620392297721320",
                                                userLocale)
                                        + "</a> "
                                        + PortalUtils.getI18nMessage(
                                                "i18n.CCController.tovalidate7966403442155985",
                                                userLocale);
                            }

                            subject = CCHelper.processEmailTokens(subject,
                                    (String) recipientData[3],
                                    (String) recipientData[4],
                                    isL1Locale
                                            ? rp.getPosition().getActivity()
                                                    .getActivity()
                                            : rp.getPosition().getActivity()
                                                    .getL2Activity(),
                                    isL1Locale ? rp.getPosition().getTitle()
                                            : rp.getPosition().getL2Title(),
                                    null, null, null);

                            body = CCHelper.processEmailTokens(body,
                                    (String) recipientData[3],
                                    (String) recipientData[4],
                                    isL1Locale
                                            ? rp.getPosition().getActivity()
                                                    .getActivity()
                                            : rp.getPosition().getActivity()
                                                    .getL2Activity(),
                                    isL1Locale ? rp.getPosition().getTitle()
                                            : rp.getPosition().getL2Title(),
                                    null, null, null);

                            EmailUtils.sendAndLog1Email(new EmailMessage(
                                    module.getFromEmailForValidators(),
                                    userLoggedIn, (String) recipientData[0],
                                    (String) recipientData[1], subject, body, body,
                                    null, false, false));
                        }
                    }
                }

                if (rp != null && null == rp.getPosition().getProgram()
                        && StringUtils.getBooleanValue(
                                request.getParameter("sendEmailToParticipant")))
                {
                    CCHelper.sendParticipantEmail(rp, module,
                            PortalUtils.getUserLoggedIn(request));
                }
            }
            else
            {
                if ("cc_positionParticipants".equals(returnTo))
                {
                    mv = displayPositionParticipants(request, response);
                }
                else if ("activity".equals(returnTo)
                        && CCHelper.getCCActivity(request) != null && CCHelper
                                .canView(CCHelper.getCCActivity(request), request))
                {
                    mv = displayActivityParticipants(request, response);
                }
                else
                {
                    mv = isNotMyRecordMode(request)
                            ? displayRecord(request, response)
                            : displayMyRecord(request, response);
                }
                mv.addObject("errorMessage",
                        new I18nLabel(
                                "i18n.CCController.Positionno0100221737591680")
                                .getTranslation(PortalUtils.getLocale(request)));
            }
        }

        return mv;
    }

    private UserDetailsImpl getStudentForRecordPosition(HttpServletRequest request)
    {
        UserDetailsImpl user = null;

        if (StringUtils.isInteger(request.getParameter("acrmUserId")))
        {
            user = UserDetailsHelper.getUserByUserId(
                    Integer.valueOf(request.getParameter("acrmUserId")));
        }
        else if (StringUtils.isInteger(request.getParameter("studentId")))
        {
            user = (UserDetailsImpl) getHt().load(UserDetailsImpl.class,
                    Integer.valueOf(request.getParameter("studentId")));
        }
        else
        {
            user = PortalUtils.getUserLoggedIn(request);
        }
        return user;
    }

    @RequestMapping("displayAddRecord")
    public ModelAndView displayAddRecord(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_addRecord");
        Locale locale = PortalUtils.getLocale(request);
        CCModule module = getModule(request);
        CCPosition pos = CCHelper.getCCPosition(request);
        CCHelper.populateCCOutcomes_editMode(mv, module, pos, true, request);
        CCHelper.populatePositionQuestions(mv, getModule(request), pos,
                PortalUtils.getUserLoggedIn(request), true);
        mv.addObject("directors",
                getHt().find(
                        "from CCDirectors d where d.module=? order by d."
                                + (LocaleUtils.isL1(locale) ? "name" : "l2Name"),
                        module));
        mv.addObject("rp", CCRecordPositionHelper.newInstance(request));

        mv.addObject("returnTo", request.getParameter("returnTo"));
        mv.addObject("acrmUserId", request.getParameter("acrmUserId"));
        mv.addObject("positionId", request.getParameter("positionId"));
        mv.addObject("myRecordMode", request.getParameter("myRecordMode"));
        mv.addObject("studentId", request.getParameter("studentId"));
        mv.addObject("addingPosition", request.getParameter("addingPosition"));
        mv.addObject("ccModule", module);
        return mv;
    }

    /**
     * Default request handling method
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayPeriods")
    public ModelAndView displayPeriods(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_periods");
        CCModule module = getModule(request);

        List periods = getHt().find(
                "from CCPeriod c where c.module=? order by c.startDate DESC, c.id DESC",
                module);
        mv.addObject("periods", periods);

        mv.addObject("nowDate", new Date());

        int periodsMissingDatesCount = PortalUtils.getJt().queryForInt(
                "SELECT COUNT(ccp.id) FROM cc_period ccp WHERE ccp.module = ? "
                        + "AND (ccp.startDate IS NULL OR ccp.endDate IS NULL)",
                new Object[] { module.getId() });

        mv.addObject("periodsMissingDatesCount",
                Integer.valueOf(periodsMissingDatesCount));

        Map periodStats = new HashMap();
        for (Iterator iterator = periods.iterator(); iterator.hasNext();)
        {
            CCPeriod c = (CCPeriod) iterator.next();
            Integer aCount = (Integer) getHt()
                    .find("select count(p) from CCActivity p where p.period=?", c)
                    .get(0);
            Integer pCount = (Integer) getHt().find(
                    "select count(p) from CCRecordPosition p where p.position.activity.period=? and p.status='Pending'",
                    c).get(0);
            Integer approvedCount = (Integer) getHt().find(
                    "select count(p) from CCRecordPosition p where p.position.activity.period=? and p.status='Approved'",
                    c).get(0);
            Integer declinedCount = (Integer) getHt().find(
                    "select count(p) from CCRecordPosition p where p.position.activity.period=? and p.status='Declined'",
                    c).get(0);
            Integer validatorCount = (Integer) getHt().find(
                    "select count(distinct p.validator.id) from CCPositionValidator p where p.position.activity.period=?",
                    c).get(0);
            Integer categoryCount = (Integer) getHt().find(
                    "select count(distinct p.category) from CCActivity p where p.period=?",
                    c).get(0);
            Integer organizationCount = (Integer) getHt().find(
                    "select count(distinct p.organization) from CCActivity p where p.period=?",
                    c).get(0);
            Integer departmentCount = (Integer) getHt().find(
                    "select count(distinct p.department) from CCActivity p where p.period=?",
                    c).get(0);
            periodStats.put(c,
                    new Object[] { aCount, pCount, approvedCount, declinedCount,
                            validatorCount, categoryCount, organizationCount,
                            departmentCount });
        }
        mv.addObject("periodStats", periodStats);
        mv.addObject("module", module);
        return mv;
    }

    @RequestMapping("displayEmailPublicUpdateLinks")
    public ModelAndView displayEmailPublicUpdateLinks(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        Locale locale = PortalUtils.getLocale(request);
        Map<String, String> additionalParams = new HashMap<>();
        Map<String, String> emailModelParams = new HashMap<>();
        emailModelParams.put("onCancelled", "displayPeriods");
        emailModelParams.put("onCompleted", "displayPeriods");
        emailModelParams.put("action", "sendPublicUpdateLinks");
        emailModelParams.put("subject",
                LocaleUtils.isL1(locale) ? module.getPublicUpdateEmailSubject()
                        : module.getL2PublicUpdateEmailSubject());
        emailModelParams.put("body",
                LocaleUtils.isL1(locale) ? module.getPublicUpdateEmailBody()
                        : module.getL2PublicUpdateEmailBody());
        EmailModel emailModel = getPublicUpdateLinksEmailModel(request,
                emailModelParams, additionalParams);
        emailModel.setFromAddress(module.getFromEmailForPublicUpdateEmail());

        return displayEmailer(request, response, emailModel);
    }

    public EmailModel getPublicUpdateLinksEmailModel(HttpServletRequest request,
            Map emailModelParams, Map additionalParams)
    {
        CCPeriod period = CCHelper.getCCPeriod(request);
        List results = getHt().find(
                "select distinct a.currentOwner.emailAddress, a.currentOwner.preferredFirstName, a.currentOwner.lastName, a.currentOwner.username, a.id, a.confirmationCode, a.currentOwner.userLocale, a.activity, COALESCE(a.l2Activity, a.activity)"
                        + " from CCActivity a where a.period=?"
                        + " order by a.currentOwner.preferredFirstName, a.currentOwner.lastName",
                period.getId());
        List<EmailRecipient> emailRecipients = new ArrayList<>();

        for (Object result : results)
        {
            Object[] row = (Object[]) result;
            EmailRecipient r = new EmailRecipient();

            if (EmailUtils.isValidEmailAddress((String) row[0]))
            {
                r.setEmail((String) row[0]);
            }

            String firstName = (String) row[1];
            String lastName = (String) row[2];
            r.setUsername((String) row[3]);
            r.setName(firstName + " " + lastName);
            r.setEntityLink(":" + row[4] + ":" + row[5] + ":" + firstName + ":"
                    + lastName + ":" + row[6] + ":"
                    + row[7].toString().replace(":", " ") + ":"
                    + row[8].toString().replace(":", " "));
            r.setLocale(LocaleUtils.toLocale((String) row[6]));
            emailRecipients.add(r);
        }

        EmailModel emailModel = new EmailModel();

        if (!emailRecipients.isEmpty())
        {
            emailModel.setEmailRecipients(emailRecipients);
        }

        setEmailModelParams(emailModelParams, additionalParams, emailModel,
                request);

        return emailModel;
    }

    @RequestMapping("displaySendEmailFromActivityGraph")
    public ModelAndView displaySendEmailFromActivityGraph(
            HttpServletRequest request, HttpServletResponse response)
    {
        List<Integer> ids = super.getGridSelectedIds(request);
        CCModule module = getModule(request);
        Map<String, String> additionalParams = new HashMap<>();
        Map<String, String> emailModelParams = new HashMap<>();
        additionalParams.put("periodId", request.getParameter("periodId"));
        additionalParams.put("type", request.getParameter("type"));
        emailModelParams.put("onCancelled", "displayRecordPositionActivities");
        emailModelParams.put("onCompleted", "displayRecordPositionActivities");
        emailModelParams.put("action", "sendEmailFromActivityGraph");
        emailModelParams.put("subject", module.getActivityGraphEmailSubject());
        emailModelParams.put("l2Subject", module.getL2ActivityGraphEmailSubject());
        emailModelParams.put("body", module.getActivityGraphEmailBody());
        emailModelParams.put("l2Body", module.getL2ActivityGraphEmailBody());
        List<Object[]> activities = getHt().find(
                "select a.activity, a.l2Activity, a.currentOwner.emailAddress, a.currentOwner.preferredFirstName, a.currentOwner.lastName, a.currentOwner.username, a.currentOwner.userLocale  from CCActivity as a  where a.id in"
                        + DBUtils.buildInClause(ids));

        List<EmailRecipient> emailRecipients = new ArrayList<>();

        for (Object[] activity : activities)
        {
            if (EmailUtils.isValidEmailAddress((String) activity[2]))
            {
                EmailRecipient r = new EmailRecipient();
                r.setEmail((String) activity[2]);
                r.setLocale(LocaleUtils.toLocale((String) activity[6]));
                r.setUsername((String) activity[5]);
                r.setName((String) activity[3] + (String) activity[4]);
                r.setEntityLink((String) activity[0] + ":" + (String) activity[1]
                        + ":" + (String) activity[3] + ":" + (String) activity[4]
                        + ":" + (String) activity[6]);
                emailRecipients.add(r);
            }
        }
        EmailModel emailModel = new EmailModel();
        if (!emailRecipients.isEmpty())
        {
            emailModel.setEmailRecipients(emailRecipients);
        }

        setEmailModelParams(emailModelParams, additionalParams, emailModel,
                request);
        emailModel.setFromAddress(module.getFromEmailForActivityGraph());
        ModelAndView mv = displayEmailer(request, response, emailModel);
        mv.addObject("periodId", request.getParameter("periodId"));
        mv.addObject("type", request.getParameter("type"));
        return mv;
    }

    @RequestMapping("sendEmailFromActivityGraph")
    public ModelAndView sendEmailFromActivityGraph(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        EmailModel emailModel = updateEmailModel(request);
        I18nMessageList errors = validateUpdatedEmailModel(emailModel);

        if (errors.isEmpty())
        {
            for (EmailRecipient r : emailModel.getEmailRecipients())
            {
                String[] entityLink = r.getEntityLink().split(":");

                boolean isL1 = LocaleUtils.isL1(entityLink[4]);

                String subject = CCHelper.processEmailTokens(
                        isL1 ? emailModel.getSubject() : emailModel.getL2Subject(),
                        entityLink[2], entityLink[3],
                        isL1 ? entityLink[0] : entityLink[1], null, null, null,
                        null);

                String body = CCHelper.processEmailTokens(
                        isL1 ? emailModel.getBody() : emailModel.getL2Body(),
                        entityLink[2], entityLink[3],
                        isL1 ? entityLink[0] : entityLink[1], null, null, null,
                        null);

                EmailUtils.sendAndLog1Email(new EmailMessage(
                        emailModel.getFromAddress(),
                        PortalUtils.getUserLoggedIn(request), r.getEmail(),
                        r.getUsername(), subject, body, body, null, false, false));

            }
            request.setAttribute("periodId",
                    emailModel.getAdditionalParams().get("periodId"));
            request.setAttribute("type",
                    emailModel.getAdditionalParams().get("type"));
            mv = onEmailCompleted(request, response, emailModel);

            mv.addObject("emailSent", true);
        }
        else
        {
            // PARAMS ARE INVALID - RETURN TO PAGE

            mv = new ModelAndView("email/emailer_home");
            mv.addObject("emailSent", Boolean.FALSE);
            mv.addObject("errors", errors);
            mv.addObject("emailModel", emailModel);
        }

        return mv;
    }

    @RequestMapping("sendPublicUpdateLinks")
    public ModelAndView sendPublicUpdateLinks(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        EmailModel emailModel = updateEmailModel(request);

        I18nMessageList errors = validateUpdatedEmailModel(emailModel);

        if (errors.isEmpty())
        {
            List<Integer> selectedActivityIds = new ArrayList<>();
            // PARAMS ARE VALID - SEND THE EMAIL!
            UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
            CCModule module = getModule(request);
            CCPublicModule publicModule = CCHelper.getCCPublicModule(module);

            for (EmailRecipient r : emailModel.getEmailRecipients())
            {
                if (r.isSelected() && r.isValidEmail())
                {
                    String[] entityLink = r.getEntityLink().split(":");

                    boolean isL1Locale = LocaleUtils.isL1(entityLink[5]);
                    Locale userLocale = isL1Locale ? LocaleUtils.getDefaultLocale()
                            : LocaleUtils.getSecondaryLocale();
                    String tempBody = emailModel.getBody() + "<br /><br />";

                    if (publicModule != null)
                    {
                        String pageUrl = PortalUtils
                                .getPageUrl(publicModule.getSiteElement());
                        tempBody += "<a href='" + pageUrl
                                + "?action=displayActivityEdit&activityId="
                                + entityLink[1] + "&confirmationCode="
                                + entityLink[2] + "'>"
                                + PortalUtils.getI18nMessage(
                                        "i18n.CCController.ClickHere2704027865950505",
                                        userLocale)
                                + "</a> "
                                + PortalUtils.getI18nMessage(
                                        "i18n.CCController.tovalidate8242136690684810",
                                        userLocale);
                    }

                    if (request.getParameter("emailSignature") != null
                            && !StringUtils.isEmpty(
                                    request.getParameter("emailSignature")))
                    {
                        tempBody += "<br /><br />"
                                + request.getParameter("emailSignature");
                    }
                    tempBody = CCHelper.processEmailTokens(tempBody, entityLink[3],
                            entityLink[4],
                            isL1Locale ? entityLink[6] : entityLink[7], null, null,
                            null, null);

                    String subject = CCHelper.processEmailTokens(
                            emailModel.getSubject(), entityLink[3], entityLink[4],
                            isL1Locale ? entityLink[6] : entityLink[7], null, null,
                            null, null);

                    EmailUtils.sendAndLog1Email(new EmailMessage(
                            emailModel.getFromAddress(), user, r.getEmail(),
                            r.getUsername(), subject, tempBody,
                            HtmlUtils.stripHtml(tempBody), null, false, false));
                    selectedActivityIds.add(NumberUtils.asInteger(entityLink[1]));
                }
            }

            getJt().execute(
                    "update cc_activity set ownerUpdateRequested=1 where id in "
                            + DBUtils.buildInClause(selectedActivityIds));
            getJt().execute(
                    "update cc_position set ownerUpdateRequested=1 where activity in "
                            + DBUtils.buildInClause(selectedActivityIds));

            mv = onEmailCompleted(request, response, emailModel);

            mv.addObject("emailSent", true);

        }
        else
        {
            // PARAMS ARE INVALID - RETURN TO PAGE

            mv = new ModelAndView("email/emailer_home");
            mv.addObject("emailSent", Boolean.FALSE);
            mv.addObject("errors", errors);
            mv.addObject("emailModel", emailModel);
        }

        return mv;
    }

    @RequestMapping("displayPeriodFixMissingDates")
    public ModelAndView displayPeriodFixMissingDates(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_periodFixMissingDates");

        CCModule module = getModule(request);

        List<CoopTerm> periods = PortalUtils.getHt().find(
                "FROM CCPeriod ccp WHERE ccp.module.id = ? AND (ccp.startDate IS NULL OR ccp.endDate IS NULL)",
                new Object[] { module.getId() });

        mv.addObject("periods", periods);

        return mv;
    }

    @RequestMapping("fixPeriodMissingDates")
    public ModelAndView fixPeriodMissingDates(HttpServletRequest request,
            HttpServletResponse response)
    {
        int[] periodIds = RequestUtils.getIntParameters(request, "periodIds");

        PortalUtils.getJt().batchUpdate(
                "UPDATE cc_period SET startDate = ?, endDate = ? WHERE id = ?",
                new BatchPreparedStatementSetter()
                {
                    @Override
                    public int getBatchSize()
                    {
                        return periodIds.length;
                    }

                    @Override
                    public void setValues(PreparedStatement ps, int i)
                            throws SQLException
                    {
                        int periodId = periodIds[i];
                        ps.setTimestamp(1, new java.sql.Timestamp(DateUtils
                                .getDatepickerVal(request, "startDate" + periodId)
                                .getTime()));
                        ps.setTimestamp(2,
                                new java.sql.Timestamp(
                                        DateUtils
                                                .getEndDate(DateUtils
                                                        .getDatepickerVal(request,
                                                                "endDate"
                                                                        + periodId))
                                                .getTime()));
                        ps.setInt(3, periodId);
                    }

                });

        FlashMessageUtils.success(request, periodIds
                + new I18nLabel("i18n.CCController.0periodsta2727276602615535")
                        .getTranslation(PortalUtils.getLocale(request)));

        return displayPeriods(request, response);
    }

    @RequestMapping("toggleTpActive")
    public ModelAndView toggleTpActive(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        JSONObject ret = new JSONObject();
        String toggleWhat = request.getParameter("toggleWhat");

        CCPeriod p = (CCPeriod) getHt().load(CCPeriod.class,
                Integer.valueOf(request.getParameter("tpId")));

        if (toggleWhat.equalsIgnoreCase("admin"))
        {
            p.setActive(!p.isActive());
            if (!p.isActive())
            {
                p.setActiveStudent(false);
                p.setActiveVal(false);
                p.setActiveRequests(false);
            }
            ret.put("toggledTo", p.isActive());
        }
        else if (toggleWhat.equalsIgnoreCase("student"))
        {
            p.setActiveStudent(!p.isActiveStudent());
            if (p.isActiveStudent())
            {
                p.setActive(true);
            }
            ret.put("toggledTo", p.isActiveStudent());
        }
        else if (toggleWhat.equalsIgnoreCase("val"))
        {
            p.setActiveVal(!p.isActiveVal());
            if (p.isActiveVal())
            {
                p.setActive(true);
            }
            ret.put("toggledTo", p.isActiveVal());
        }
        else if (toggleWhat.equalsIgnoreCase("request"))
        {
            p.setActiveRequests(!p.isActiveRequests());
            if (p.isActiveRequests())
            {
                p.setActive(true);
            }
            ret.put("toggledTo", p.isActiveRequests());
        }
        else if (toggleWhat.equalsIgnoreCase("search"))
        {
            p.setSearchInCatalogue(!p.isSearchInCatalogue());
            ret.put("toggledTo", p.isSearchInCatalogue());
        }

        getHt().update(p);

        return jsonObjectResponse(ret);
    }

    /**
     * Display period edit
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayPeriodEdit")
    public ModelAndView displayPeriodEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_periodEdit");
        CCPeriod r = CCHelper.getCCPeriod(request);
        mv.addObject("period", r);

        return mv;
    }

    @RequestMapping("saveModule")
    public ModelAndView saveModule(HttpServletRequest request,
            HttpServletResponse response)
    {
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
        CCModule module = getModule(request);
        Locale locale = PortalUtils.getLocale(request);

        // GENERAL CONFIGS...
        module.setName(request.getParameter("moduleName"));
        module.setL2Name(StringUtils.getValueForL2Fields(
                request.getParameter("l2ModuleName"),
                request.getParameter("moduleName")));
        module.setStudentsCanAdd(request.getParameter("studentsCanAdd") != null
                && request.getParameter("studentsCanAdd").equals("on"));
        module.setStudentsNeedAdd(request.getParameter("studentsNeedAdd") != null
                && request.getParameter("studentsNeedAdd").equals("on"));
        module.setShowRecommended(request.getParameter("showRecommended") != null
                && request.getParameter("showRecommended").equals("on"));
        String enableOptionalRole = request.getParameter("enableOptionalRole");
        module.setEnableStaffRole("enableStaffRole".equals(enableOptionalRole));
        module.setEnableActivityOwnerRole(
                "enableActivityOwnerRole".equals(enableOptionalRole));

        module.setActivityOwnersCanEdit(
                null != request.getParameter("activityOwnersCanEdit"));
        module.setEnableLeadershipPoints(
                request.getParameter("enableLeadershipPoints") != null && request
                        .getParameter("enableLeadershipPoints").equals("on"));
        module.setEnableTimeTracking(
                request.getParameter("enableTimeTracking") != null
                        && request.getParameter("enableTimeTracking").equals("on"));
        module.setEnableClubs(request.getParameter("enableClubs") != null
                && request.getParameter("enableClubs").equals("on"));
        module.setEnableActivityDirectors(
                request.getParameter("enableActivityDirectors") != null && request
                        .getParameter("enableActivityDirectors").equals("on"));

        module.setConnectToPT(request.getParameter("connectToPT") != null
                && request.getParameter("connectToPT").equals("on"));

        module.setConnectToEvents(request.getParameter("connectToEvents") != null
                && request.getParameter("connectToEvents").equals("on"));

        module.setPublicRecordDetails(
                request.getParameter("publicRecordDetails") != null && request
                        .getParameter("publicRecordDetails").equals("on"));

        module.setEnableExternalValidations(RequestUtils
                .getBooleanParameter(request, "enableExternalValidations", false));

        module.setPublicRecordValidMessage(
                request.getParameter("publicRecordValidMessage"));
        module.setL2PublicRecordValidMessage(StringUtils.getValueForL2Fields(
                request.getParameter("l2PublicRecordValidMessage"),
                request.getParameter("publicRecordValidMessage")));

        // ACTIVITY LEVEL CONFIGS...
        boolean currentIsShowCategory = module.isShowLevelCategory();
        module.setShowLevelCategory(
                request.getParameter("showLevelCategory") != null);
        module.setShowLevelOrganization(
                request.getParameter("showLevelOrganization") != null);
        module.setShowLevelDepartment(
                request.getParameter("showLevelDepartment") != null);

        module.setEnableReflectionComment(!StringUtils
                .isEmpty(request.getParameter("enableReflectionComment")));
        module.setEnableShortlist(request.getParameter("enableShortlist") != null);
        module.setAllowPrintingWithNoCompLO(
                request.getParameter("allowPrintingWithNoCompLO") != null);
        module.setAllowStudentPrint(
                !StringUtils.isEmpty(request.getParameter("allowStudentPrint")));
        module.setAllowExperiencesWithoutReflection(RequestUtils.isChecked(request,
                "allowExperiencesWithoutReflection"));
        module.setAllowExperiencesWithoutMeetingMinimumCompetencies(
                RequestUtils.isChecked(request,
                        "allowExperiencesWithoutMeetingMinimumCompetencies"));
        module.setAllowExperiencesWithoutMeetingMinimumHours(RequestUtils
                .isChecked(request, "allowExperiencesWithoutMeetingMinimumHours"));

        module.setStudentCanChangeCompetencyAfterRecordApproval(
                RequestUtils.isChecked(request,
                        "studentCanChangeCompetencyAfterRecordApproval"));

        module.setStudentCanChangeReflectionAfterRecordApproval(
                RequestUtils.isChecked(request,
                        "studentCanChangeReflectionAfterRecordApproval"));

        module.setStudentCanChangeHoursAfterRecordApproval(RequestUtils
                .isChecked(request, "studentCanChangeHoursAfterRecordApproval"));

        module.setStudentCanUpdateDeclinedPositions(RequestUtils.isChecked(request,
                "studentCanUpdateDeclinedPositions"));

        if (user.isOrbisInDevelopment() && user.isRoot())
        {
            module.setEnableSpiralRobot(!StringUtils
                    .isEmpty(request.getParameter("enableSpiralRobot")));
        }

        module.setEnableSpiralRobotForStudents(!StringUtils
                .isEmpty(request.getParameter("enableSpiralRobotForStudents")));

        getHt().update(module);

        DFModel model = CCPositionQuestionModelHelper
                .getPositionQuestionModel(module).getDFModel();

        if (module.isEnableLeadershipPoints())
        {
            createLeadershipOrTimeQuestion(module, "leadership", locale);
        }
        else
        {
            List<DFQuestion> questions = getHt().find(
                    "from DFQuestion q where q.answerField1 = ? and q.category.model = ?",
                    new Object[] { "points", model });
            if (!questions.isEmpty())
            {
                DFHelper.deleteQuestion(questions.get(0), CCPosition.class);
            }
        }

        if (module.isEnableTimeTracking())
        {
            createLeadershipOrTimeQuestion(module, "time", locale);
        }
        else
        {
            List<DFQuestion> questions = getHt().find(
                    "from DFQuestion q where q.answerField1 = ? and q.category.model = ?",
                    new Object[] { "hoursToTrack", model });
            if (!questions.isEmpty())
            {
                DFHelper.deleteQuestion(questions.get(0), CCPosition.class);
            }
        }

        if (module.isShowLevelCategory() != currentIsShowCategory)
        {
            ECOutlookHelper.recreateConfiguredOutlooks();
        }
        request.setAttribute("successMessage", "Module configurations saved.");
        return displayModuleEdit(request, response);
    }

    /**
     * Save period
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("savePeriod")
    public ModelAndView savePeriod(HttpServletRequest request,
            HttpServletResponse response)
    {

        // dumpRequestParameters(request);

        CCPeriod r = CCHelper.getCCPeriod(request);
        if (r == null)
        {
            r = new CCPeriod();
        }
        r.setModule(getModule(request));
        r.setActive(!StringUtils.isEmpty(request.getParameter("active")));
        r.setSearchInCatalogue(RequestUtils.getBooleanParameter(request,
                "searchInCatalogue", false));
        r.setActiveStudent(
                !StringUtils.isEmpty(request.getParameter("activeStudent")));
        r.setActiveVal(!StringUtils.isEmpty(request.getParameter("activeVal")));
        r.setActiveRequests(
                !StringUtils.isEmpty(request.getParameter("activeRequests")));
        r.setName(request.getParameter("name"));
        r.setL2Name(StringUtils.getValueForL2Fields(request.getParameter("l2Name"),
                request.getParameter("name")));
        r.setStartDate(DateUtils.getDatepickerVal(request, "startDate"));
        r.setEndDate(DateUtils.getDatepickerVal(request, "endDate"));
        r.setPublicUpdatesCloseDate(
                DateUtils.getDatepickerVal(request, "publicUpdatesCloseDate"));
        getHt().saveOrUpdate(r);

        ModelAndView mv = displayPeriods(request, response);
        mv.addObject("saveTimeSuccess", Boolean.TRUE);
        return mv;
    }

    /**
     * Delete period
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("deletePeriod")
    public ModelAndView deletePeriod(HttpServletRequest request,
            HttpServletResponse response)
    {
        PortalUtils.getJt().update("delete from cc_period where id = ?",
                new Object[] { request.getParameter("periodId") });

        ModelAndView mv = displayPeriods(request, response);
        mv.addObject("deleteTimeSuccess", Boolean.TRUE);
        return mv;
    }

    @RequestMapping("addActivityAndPosition")
    public ModelAndView addActivityAndPosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        try
        {
            guardAgainstMissingValidatorGroup();
        }
        catch (CCException cce)
        {
            cce.printStackTrace();
            return getCCExceptionAjaxResponse(cce);
        }

        CCPeriod period = (CCPeriod) getHt().load(CCPeriod.class,
                Integer.valueOf(request.getParameter("periodId")));
        CCActivity activity = CCRequestPositionHelper
                .saveNewActivityRequest(request, getModule(request), period);

        CCHelper.saveNewPosition(getModule(request), activity,
                PortalUtils.getUserLoggedIn(request), request);

        request.setAttribute("periodId", period.getId());

        ModelAndView mv = displayAddNewActivity(request, response);

        mv.addObject("success", true);

        return mv;
    }

    @RequestMapping("savePositionRequest")
    public ModelAndView savePositionRequest(HttpServletRequest request,
            HttpServletResponse response) throws CCException
    {
        try
        {
            guardAgainstMissingValidatorGroup();
        }
        catch (CCException cce)
        {
            cce.printStackTrace();
            return getCCExceptionAjaxResponse(cce);
        }

        CCActivity activity = CCHelper.getCCActivity(request);
        CCModule module = getModule(request);
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        String currentUserRole = CCHelper.getCurrentUserRole(module, userLoggedIn);
        if (activity == null)
        {
            if (module.isStudentsCanAdd()
                    || (!CCHelper.CC_ROLE_ANONYMOUS.equals(currentUserRole)
                            && !CCHelper.CC_ROLE_STUDENT.equals(currentUserRole)
                            && !CCHelper.CC_ROLE_ALUMNI.equals(currentUserRole)))
            {
                CCPeriod period = PortalUtils.getHt().load(CCPeriod.class,
                        Integer.valueOf(request.getParameter("periodId")));
                activity = CCRequestPositionHelper.saveNewActivityRequest(request,
                        module, period);
            }
            else
            {
                throw new CCException(CCException.TYPE.INSUFFICIENT_PERMISSIONS);
            }
        }

        CCPosition newPosition = CCHelper.saveNewPosition(module, activity,
                PortalUtils.getUserLoggedIn(request), request);

        request.setAttribute("positionId", newPosition.getId());
        String successMessage = PortalUtils.getI18nMessage(
                "i18n.CCController.Positioncr0255842056923752", request);

        ModelAndView mv = displayPositionOverview(request, response);

        FlashMessageUtils.success(request, successMessage);

        return mv;
    }

    @RequestMapping("deleteCCRequest")
    public ModelAndView deleteCCRequest(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        CCPosition ccp = CCHelper.getCCPosition(request);
        if (ccp != null)
        {
            PortalUtils.getJt().update("delete from cc_pla where position = ?",
                    new Object[] { ccp.getId() });
            PortalUtils.getJt().update("delete from cc_paa where position = ?",
                    new Object[] { ccp.getId() });
            getHt().delete(ccp);
        }
        return displayCCRequests(request, response);
    }

    @RequestMapping("deleteActivity")
    public ModelAndView deleteActivity(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCHelper.deleteCCActivity(CCHelper.getCCActivity(request));

        ModelAndView mv = null;

        if ("cc_periodActivities".equals(request.getParameter("returnTo"))
                && StringUtils.isInteger(request.getParameter("periodId")))
        {
            request.setAttribute("periodId",
                    Integer.valueOf(request.getParameter("periodId")));
            mv = displayPeriodActivities(request, response);
        }
        else
        {
            mv = displayHome(request, response);
        }

        mv.addObject("successMessage",
                PortalUtils.getI18nMessage(
                        "i18n.CCController.Activityde5201999246649204",
                        PortalUtils.getLocale(request)));

        return mv;
    }

    /**
     * Display Learning outcomes
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayLearningOutcomes")
    public ModelAndView displayLearningOutcomes(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_learningOutcomes");
        Locale locale = PortalUtils.getLocale(request);
        List los = getHt().find(
                "from CCLearningOutcome c where c.module=? order by "
                        + (LocaleUtils.isL1(locale) ? "c.name" : "c.l2Name"),
                getModule(request));
        Map<CCLearningOutcome, Map<List<Object[]>, Boolean>> loAchievements = new HashMap<>();
        String achievementFieldBilingual = LocaleUtils.isL1(locale) ? "achievement"
                : "l2Achievement";
        for (Iterator iterator = los.iterator(); iterator.hasNext();)
        {
            CCLearningOutcome c = (CCLearningOutcome) iterator.next();
            List achievements = getHt().find("select p.id, p."
                    + achievementFieldBilingual
                    + ", p.active from CCAchievement p where p.learningOutcome=? order by p."
                    + achievementFieldBilingual, c);
            Map<List<Object[]>, Boolean> tempMap = new HashMap<>();
            List positions = getHt().find(
                    "select count(*) from CCPositionLearningOutcome pla where pla.learningOutcome=?",
                    c);
            if (positions.isEmpty() && ((Integer) positions.get(0)).intValue() > 0)
            {
                tempMap.put(achievements, true);
            }
            else
            {
                tempMap.put(achievements, false);
            }
            loAchievements.put(c, tempMap);
        }
        mv.addObject("loAchievements", loAchievements);
        mv.addObject("learningOutcomes", los);

        return mv;
    }

    @RequestMapping("toggleAchievement")
    public ModelAndView toggleAchievement(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        JSONObject ret = new JSONObject();

        CCAchievement achievement = (CCAchievement) getHt()
                .load(CCAchievement.class, Integer.valueOf(request.getParameter("id")));

        CCLearningOutcome lo = (CCLearningOutcome) getHt().load(
                CCLearningOutcome.class,
                Integer.valueOf(achievement.getLearningOutcome().getId()));

        if (lo.isActive())
        {
            achievement.setActive(!achievement.isActive());
            getHt().update(achievement);
        }

        ret.put("active", achievement.isActive());
        return jsonObjectResponse(ret);
    }

    @RequestMapping("toggleLearningOutcome")
    public ModelAndView toggleLearningOutcome(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        JSONObject ret = new JSONObject();

        CCLearningOutcome lo = (CCLearningOutcome) getHt().load(
                CCLearningOutcome.class, Integer.valueOf(request.getParameter("id")));

        lo.setActive(Boolean.parseBoolean(request.getParameter("isChecked")));
        getHt().update(lo);

        List<CCAchievement> achievements = getHt().find(
                "FROM CCAchievement a WHERE a.learningOutcome.id=?", lo.getId());
        for (CCAchievement achievement : achievements)
        {
            if (lo.isActive())
            {
                achievement.setActive(true);
            }
            else
            {
                achievement.setActive(false);
            }

            getHt().update(achievement);
        }

        ret.put("active", lo.isActive());
        return jsonObjectResponse(ret);
    }

    @RequestMapping("toggleShowOnPDF")
    public ModelAndView toggleShowOnPDF(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        JSONObject ret = new JSONObject();
        CCRecordPosition rp = CCHelper.getCCRecordPosition(request);
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (rp != null)
        {
            boolean isPublished = rp.isShowOnPdf();
            if (isPublished)
            {
                PublishHelper.unpublishCard(rp, rp.getOwner());
                rp.setShowOnPdf(false);

                getHt().update(rp);
                ret.put("active", rp.isShowOnPdf());
            }
            else
            {
                ret = CCHelper.publishCCRecordPossition(rp, userLoggedIn,
                        PortalUtils.getLocale(request));
            }
        }

        return jsonObjectResponse(ret);
    }

    /**
     * Display learning outcome edit
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayLearningOutcomeEdit")
    public ModelAndView displayLearningOutcomeEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_learningOutcomeEdit");
        CCLearningOutcome r = getCCLearningOutcome(request);
        mv.addObject("learningOutcome", r);
        return mv;
    }

    /**
     * Save learningOutcome
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("saveLearningOutcome")
    public ModelAndView saveLearningOutcome(HttpServletRequest request,
            HttpServletResponse response)
    {

        CCLearningOutcome r = getCCLearningOutcome(request);
        r.setModule(getModule(request));
        r.setName(request.getParameter("name"));
        r.setL2Name(StringUtils.getValueForL2Fields(request.getParameter("l2Name"),
                request.getParameter("name")));
        r.setActive(true);
        getHt().saveOrUpdate(r);

        ModelAndView mv = displayLearningOutcomes(request, response);
        mv.addObject("saveLearningSuccess", Boolean.TRUE);
        return mv;
    }

    /**
     * Delete learningOutcome
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("deleteLearningOutcome")
    public ModelAndView deleteLearningOutcome(HttpServletRequest request,
            HttpServletResponse response)
    {

        PortalUtils.getJt().update("delete from cc_lo where id = ?",
                new Object[] { request.getParameter("learningOutcomeId") });

        ModelAndView mv = displayLearningOutcomes(request, response);
        mv.addObject("deleteLearningSuccess", Boolean.TRUE);
        return mv;
    }

    /**
     * Display achievement edit
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayAchievementEdit")
    public ModelAndView displayAchievementEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_achievementEdit");
        CCLearningOutcome lo = getCCLearningOutcome(request);
        mv.addObject("learningOutcome", lo);
        CCAchievement r = getCCAchievement(request);
        mv.addObject("achievement", r);

        if (r.getId() != null)
        {
            mv.addObject("hasPositionAchievementAdmin", (Integer) getHt().find(
                    "select count(*) from CCPositionAchievementAdmin a where a.achievement=?",
                    r).get(0) > 0);
        }
        return mv;
    }

    /**
     * Save achievement
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("saveAchievement")
    public ModelAndView saveAchievement(HttpServletRequest request,
            HttpServletResponse response)
    {

        CCLearningOutcome r = getCCLearningOutcome(request);
        CCAchievement a = getCCAchievement(request);
        a.setLearningOutcome(r);
        a.setAchievement(request.getParameter("achievement"));
        a.setL2Achievement(StringUtils.getValueForL2Fields(
                request.getParameter("l2Achievement"),
                request.getParameter("achievement")));
        a.setDescription(request.getParameter("description"));
        a.setL2Description(StringUtils.getValueForL2Fields(
                request.getParameter("l2Description"),
                request.getParameter("description")));
        getHt().saveOrUpdate(a);

        ModelAndView mv = displayLearningOutcomes(request, response);
        mv.addObject("saveAchievementSuccess", Boolean.TRUE);
        return mv;
    }

    @RequestMapping("deleteAchievement")
    public ModelAndView deleteAchievement(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCAchievement a = getCCAchievement(request);
        getHt().delete(a);
        ModelAndView mv = displayLearningOutcomes(request, response);
        mv.addObject("deleteAchievementSuccess", Boolean.TRUE);
        return mv;
    }

    @RequestMapping("addPositionToActivity")
    public ModelAndView addPositionToActivity(HttpServletRequest request,
            HttpServletResponse response)
    {
        Locale locale = PortalUtils.getLocale(request);

        CCActivity a = CCHelper.getCCActivity(request);
        CCPosition p = CCHelper.getCCPosition(request);

        Boolean isSuccess = Boolean.FALSE, duplicateFound = Boolean.FALSE;
        if (a != null && p != null)
        {
            try
            {
                CCHelper.guardAgainstDuplicatePosition(a, p.getTitle());
            }
            catch (CCException e)
            {
                p.setTitle(p.getTitle() + "_" + p.getId());
                p.setL2Title(p.getL2Title() + "_" + p.getId());
                duplicateFound = Boolean.TRUE;
            }

            p.setActivity(a);
            getHt().saveOrUpdate(p);
            isSuccess = Boolean.TRUE;
        }
        ModelAndView mv = displayActivityPositions(request, response);
        mv.addObject("moveSuccess", isSuccess);
        mv.addObject("duplicateFound", duplicateFound);
        mv.addObject("newTitle",
                p != null
                        ? (LocaleUtils.isL1(locale) ? p.getTitle() : p.getL2Title())
                        : "");
        return mv;
    }

    @RequestMapping("deletePositionFromActivity")
    public ModelAndView deletePositionFromActivity(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCPosition p = CCHelper.getCCPosition(request);

        boolean deleteSuccess = false;

        if (p != null)
        {
            deleteSuccess = CCHelper.delete(p);
        }

        ModelAndView mv = displayActivityPositions(request, response);
        if (deleteSuccess)
        {
            mv.addObject("successMessage",
                    new I18nLabel("i18n.CCController.Positionde0038932414531479")
                            .getTranslation(PortalUtils.getLocale(request)));
        }
        else
        {
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.CCController.Failedtode6220569713736204")
                            .getTranslation(PortalUtils.getLocale(request)));
        }

        return mv;
    }

    @RequestMapping("deletePosition")
    public ModelAndView deletePosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        boolean deleteSuccess = false;
        Integer activityId = null;

        try
        {
            CCPosition p = CCHelper.getCCPosition(request);
            if (p != null)
            {
                activityId = p.getActivity().getId();

                deleteSuccess = CCHelper.delete(p);
            }
        }
        catch (Exception e)
        {
            deleteSuccess = false;
        }

        if (deleteSuccess)
        {
            request.setAttribute("activityId", activityId);
            mv = displayActivityOverview(request, response);
            mv.addObject("successMessage",
                    new I18nLabel("i18n.CCController.CoCurricul7571706252036014")
                            .getTranslation(PortalUtils.getLocale(request)));
        }
        else
        {
            mv = displayPositionOverview(request, response);
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.CCController.Failedtode2714745468989867")
                            .getTranslation(PortalUtils.getLocale(request)));
        }

        return mv;
    }

    /**
     * Display activity edit
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayActivityEdit")
    public ModelAndView displayActivityEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCActivity a = CCHelper.getCCActivity(request);

        if (!CCHelper.canEdit(a, request))
        {
            return new ModelAndView("cc/cc_noPermission");
        }

        ModelAndView mv = null;
        CCHelper.populateQuickStats(a);

        if (a == null)
        {
            mv = displayHome(request, response);
            mv.addObject("activityLookupError", Boolean.TRUE);
        }
        else
        {
            mv = new ModelAndView("cc/cc_activityEdit");
            CCActivityHelper.populateMVForActivityEdit(request, a, mv);
            CCHelper.populateAccessFlags(mv, a, request);
            mv.addObject("backToTimePeriod",
                    request.getParameter("backToTimePeriod"));
            String nameParam = LocaleUtils.isL1(request) ? "name" : "l2Name";
            mv.addObject("validPeriodsToCloneTo",
                    getJt().queryForList("select p.id as id, p." + nameParam
                            + " as name from cc_period p where p.module=? and p.active=1 order by p."
                            + nameParam,
                            new Object[] { getModule(request).getId() }));
        }
        return mv;
    }

    /**
     * Called by selecting a combined search result, returns the appropriate
     * ModelAndView. The ID for the record must be part of the request (ie set in
     * the JSP calling this method)
     *
     * @param request
     *            - ensure that resultCategory has been set in the jsp's form
     * @param response
     * @return
     */
    @RequestMapping("displayResult")
    public ModelAndView displayResult(HttpServletRequest request,
            HttpServletResponse response)
    {
        switch (request.getParameter("resultCategory"))
        {
            case "Student":
                return displayRecord(request, response);
            case "Validator":
                return displayValidatorDetailsPendingTab(request, response);
            case "Position":
                return displayPositionOverview(request, response);
            case "Activity":
                return displayActivityOverview(request, response);
            default:
                return null;
        }
    }

    @RequestMapping("displayActivityOverview")
    public ModelAndView displayActivityOverview(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        CCModule module = getModule(request);
        CCActivity a = CCHelper.getCCActivity(request);

        if (a == null)
        {
            mv = displayHome(request, response);
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.CCController.Activityno6124384132867820")
                            .getTranslation(PortalUtils.getLocale(request)));
        }
        else
        {
            String currentRole = CCHelper.getCurrentUserRole(module,
                    PortalUtils.getUserLoggedIn(request));

            boolean canViewActivity = CCHelper.canView(a, request);
            if (CCHelper.CC_ROLE_ADMIN.equals(currentRole)
                    || (CCHelper.CC_ROLE_ACTIVITY_DIRECTOR.equals(currentRole)
                            && canViewActivity))
            {
                mv = new ModelAndView("cc/cc_activityOverview_admin");
            }
            else if (CCHelper.CC_ROLE_STAFF.equals(currentRole)
                    || CCHelper.CC_ROLE_ACTIVITY_OWNER.equals(currentRole))
            {
                mv = new ModelAndView("cc/cc_activityOverview_staff");
            }
            else if (CCHelper.CC_ROLE_VALIDATOR.equals(currentRole))
            {
                mv = new ModelAndView("cc/cc_activityOverview_validator");
            }
            else if (CCHelper.CC_ROLE_STUDENT.equals(currentRole)
                    || CCHelper.CC_ROLE_ALUMNI.equals(currentRole)
                    || (!canViewActivity))
            {
                mv = new ModelAndView("cc/cc_activityOverview_student");
            }
            if (mv != null)
            {
                CCHelper.populateActivityCommon(a, module, request, mv);

                // POPULATE MV FOR "STATISTICS PANEL"
                Set<String> keysToPopulate = new HashSet<>();
                keysToPopulate.add("pendingValidations");
                keysToPopulate.add("approvedValidations");
                keysToPopulate.add("declinedValidations");
                keysToPopulate.add("pendingPositions");
                keysToPopulate.add("approvedPositions");
                keysToPopulate.add("declinedPositions");
                keysToPopulate.add("activePositions");
                keysToPopulate.add("disabledPositions");
                keysToPopulate.add("totalPositions");
                populateDashboardStats(mv, module, a, null, request,
                        keysToPopulate);

                // POPULATE MV FOR "ACTIVITY QUESTIONS"
                CCHelper.populateActivityQuestions(mv, module, a,
                        PortalUtils.getUserLoggedIn(request), false);

                if (CCHelper.CC_ROLE_STUDENT.equals(currentRole)
                        || CCHelper.CC_ROLE_ALUMNI.equals(currentRole))
                {
                    List<CCPosition> positions = PortalUtils.getHt().find(
                            "from CCPosition p where p.activity=? and p.approvedBy is not null and p.enabled=true order by p.title",
                            new Object[] { a });
                    mv.addObject("activityPositions", positions);
                    mv.addObject("numActivityPositions", positions.size());
                }
                else if (CCHelper.CC_ROLE_STAFF.equals(currentRole))
                {
                    mv.addObject("activityPositions", PortalUtils.getHt().find(
                            "from CCPosition p where p.activity=? order by p.title",
                            new Object[] { a }));
                }

                mv.addObject("comingFrom", request.getParameter("comingFrom"));

                SiteElement se = NHelper.getAcrmCareerSiteElement(
                        PortalUtils.getUserLoggedIn(request));
                mv.addObject("acrmFullPath",
                        se != null ? se.getFullPath() + ".htm" : "");

                mv.addObject("competenciesDisabled", module
                        .getCompetencyUsecase() == Competenciable.COMP_DISABLED);
            }
        }
        return mv;
    }

    @RequestMapping("displayActivityPositions")
    public ModelAndView displayActivityPositions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityPositions");
        CCActivity a = CCHelper.getCCActivity(request);
        CCHelper.populateActivityCommon(a, getModule(request), request, mv);

        return mv;
    }

    @RequestMapping("displayActivityValidators")
    public ModelAndView displayActivityValidators(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityValidators");
        CCActivity a = CCHelper.getCCActivity(request);
        CCHelper.populateActivityCommon(a, getModule(request), request, mv);
        return mv;
    }

    // Currently not accessible
    @RequestMapping("displayActivityUnitAdmins")
    public ModelAndView displayActivityUnitAdmins(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityUnitAdmins");
        CCActivity a = CCHelper.getCCActivity(request);
        CCHelper.populateActivityCommon(a, getModule(request), request, mv);

        return mv;
    }

    @RequestMapping("displayActivityInteractions")
    public ModelAndView displayActivityInteractions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityInteractions");
        CCActivity a = CCHelper.getCCActivity(request);
        CCHelper.populateActivityCommon(a, getModule(request), request, mv);
        InteractionHelper.populateInteractionSummaryOptions(request, mv);

        return mv;
    }

    @RequestMapping("displayActivityTags")
    public ModelAndView displayActivityTags(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityTags");
        CCActivity a = CCHelper.getCCActivity(request);
        CCHelper.populateActivityCommon(a, getModule(request), request, mv);

        JSONObject additionalParams = new JSONObject();
        try
        {
            additionalParams.put("activityId", a.getId());

            AcrmHelper.populateTagsAdminPage(request, mv, additionalParams, a, true,
                    "ccActivity");
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return mv;
    }

    @RequestMapping("displayActivityClubMembership")
    public ModelAndView displayActivityClubMembership(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityMembership");
        CCActivity activity = CCHelper.getCCActivity(request);
        CCHelper.populateActivityCommon(activity, getModule(request), request, mv);

        List<OrbisHqlResultSet> members = PortalUtils.getHt().f(
                "select rp.record.owner.id, rp.record.owner.preferredFirstName, rp.record.owner.lastName, rp.record.owner.username, rp.record.owner.yearLevel, rp.record.owner.major1Descr, rp.position.id, rp.position.title, rp.position.l2Title from CCRecordPosition rp where rp.position.activity=? and rp.status=?",
                activity, CCRecordPosition.STATUS_APPROVED);
        mv.addObject("members", members);

        return mv;
    }

    @RequestMapping("displayActivityParticipants")
    public ModelAndView displayActivityParticipants(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityParticipants");
        CCActivity a = CCHelper.getCCActivity(request);
        CCHelper.populateActivityCommon(a, getModule(request), request, mv);
        mv.addObject("recordPositions", getHt().find(
                "from CCRecordPosition rp where rp.position.activity=? order by rp.position.title, rp.record.owner.preferredFirstName, rp.record.owner.lastName",
                a));
        return mv;
    }

    @RequestMapping("displayActivityStats")
    public ModelAndView displayActivityStats(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityStats");
        CCActivity a = CCHelper.getCCActivity(request);
        CCHelper.populateQuickStats(a);
        mv.addObject("activity", a);
        return mv;
    }

    // Currently not accessible
    @RequestMapping("displayActivityAdditionalTimePeriods")
    public ModelAndView displayActivityAdditionalTimePeriods(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityAdditionalTimePeriods");
        CCActivity act = CCHelper.getCCActivity(request);
        CCHelper.populateActivityCommon(act, getModule(request), request, mv);

        mv.addObject("additionalActivities", getHt().find(
                "select c.id, c.period.name, c.createdOn from CCActivity c where c.activity=? and c.department=? and c.organization=? "
                        + "and c.category=? and c.period.id!=?",
                new Object[] { act.getActivity(), act.getDepartment(),
                        act.getOrganization(), act.getCategory(),
                        act.getPeriod().getId() }));

        return mv;
    }

    @RequestMapping("ajaxRemovePv")
    public ModelAndView ajaxRemovePv(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCPositionValidator pv = (CCPositionValidator) getHt().load(
                CCPositionValidator.class,
                Integer.valueOf(request.getParameter("pvId")));
        getHt().delete(pv);

        JSONObject ret = new JSONObject();
        try
        {
            ret.put("validatorCount", getHt().find(
                    "select count(distinct v.validator.id) from CCPositionValidator v where v.position.activity=?",
                    pv.getPosition().getActivity()).get(0));
        }
        catch (DataAccessException e)
        {
            e.printStackTrace();
        }
        catch (JSONException e)
        {
            e.printStackTrace();
        }
        return jsonObjectResponse(ret);
    }

    @RequestMapping("loadActivityPositionsValidatorsTable")
    public ModelAndView loadActivityPositionsValidatorsTable(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView(
                "cc/cc_activityPositionsValidatorsTable");
        Locale locale = PortalUtils.getLocale(request);

        CCActivity a = (CCActivity) getHt().load(CCActivity.class,
                Integer.valueOf(request.getParameter("activityId")));

        Map positionValidators = new HashMap();

        String positionsHql = "from CCPosition p where p.activity=?";
        List positions = getHt().find(positionsHql, a);
        mv.addObject("positions", positions);

        CCHelper.populatePositionsLOStats(a, "select p.id " + positionsHql, mv,
                getModule(request), locale);

        for (Iterator iterator = positions.iterator(); iterator.hasNext();)
        {
            CCPosition pos = (CCPosition) iterator.next();

            positionValidators.put(pos, getHt().find(
                    "from CCPositionValidator pv where pv.position=? order by pv.validator.lastName",
                    pos));
        }
        mv.addObject("positionValidators", positionValidators);

        mv.addObject("recordPositionCount",
                Integer.valueOf(CCHelper.getRecordCount(a)));

        return mv;
    }

    @RequestMapping("loadActivityPositionsTable")
    public ModelAndView loadActivityPositionsTable(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_activityPositionsTable");
        Locale locale = PortalUtils.getLocale(request);

        CCActivity a = (CCActivity) getHt().load(CCActivity.class,
                Integer.valueOf(request.getParameter("activityId")));

        String positionsHql = "from CCPosition p where p.activity=?";
        List positions = getHt().find(positionsHql, a);
        mv.addObject("positions", positions);

        CCHelper.populatePositionsLOStats(a, "select p.id " + positionsHql, mv,
                getModule(request), locale);

        mv.addObject("recordPositionCount",
                Integer.valueOf(CCHelper.getRecordCount(a)));

        return mv;
    }

    /**
     * Save activity Edit
     */

    @RequestMapping("saveActivityEdit")
    public ModelAndView saveActivityEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCActivity activity = CCHelper.getCCActivity(request);

        if (!CCHelper.canEdit(activity, request))
        {
            return new ModelAndView("cc/cc_noPermission");
        }

        ModelAndView mv = null;
        CCActivityHelper.saveActivityCommon(request, activity);

        request.setAttribute("activityId", activity.getId());
        mv = displayActivityEdit(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.CCController.ActivitySa6262284346321464")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    /**
     * Display time period activities
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayPeriodActivities")
    public ModelAndView displayPeriodActivities(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_periodActivities");
        CCPeriod r = CCHelper.getCCPeriod(request);
        mv.addObject("period", r);
        String sortBy = StringUtils.isEmpty(request.getParameter("sortBy"))
                ? "activity"
                : request.getParameter("sortBy");

        List activities = getHt()
                .find("from CCActivity a where a.period=? order by a." + sortBy, r);
        Map actPos = new HashMap();
        for (Iterator iterator = activities.iterator(); iterator.hasNext();)
        {
            CCActivity a = (CCActivity) iterator.next();
            actPos.put(a, getHt().find(
                    "from CCPosition p where p.activity=? order by p.title", a));

        }
        mv.addObject("activities", activities);
        mv.addObject("actPos", actPos);
        return mv;
    }

    @RequestMapping("displayCCGrid")
    public ModelAndView displayCCGrid(HttpServletRequest request,
            HttpServletResponse response)
    {
        Locale locale = PortalUtils.getLocale(request);

        ModelAndView mv = null;
        String searchType = request.getParameter("searchType").toString();

        CCPeriod r = CCHelper.getCCPeriod(request);
        JQGridModel gridModel = null;

        if (searchType.equalsIgnoreCase("validators"))
        {
            mv = new ModelAndView("cc/cc_periodValidators");
            mv.addObject("period", r);
            gridModel = CCHelper.getGridModel_CCValidators(r, locale);
        }
        else
        {
            mv = new ModelAndView("cc/cc_periodDetails");
            mv.addObject("period", r);
            mv.addObject("searchType", searchType);
            if (searchType.equalsIgnoreCase("categories"))
            {
                gridModel = CCHelper.getGridModel_CCCategories(r, locale);
            }

            if (searchType.equalsIgnoreCase("organizations"))
            {
                gridModel = CCHelper.getGridModel_CCOrganizations(r, locale);
            }

            if (searchType.equalsIgnoreCase("departments"))
            {
                gridModel = CCHelper.getGridModel_CCDepartments(r, locale);
            }
        }

        JQGridHelper.populateSearchResultsPage(mv, gridModel, request,
                SearchHelper.SEARCH_MODEL_SESSION_KEY);

        return mv;
    }

    /**
     * Display position edit
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayPositionEdit")
    public ModelAndView displayPositionEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        Locale locale = PortalUtils.getLocale(request);

        ModelAndView mv = null;
        CCPosition position = null;
        CCModule module = getModule(request);

        try
        {
            position = CCHelper.getCCPosition(request);

        }
        catch (Exception e)
        {
            mv = displayHome(request, response);
            mv.addObject("positionLookupError", Boolean.TRUE);
            return mv;
        }

        if (position == null || !CCHelper.canEdit(position, request))
        {
            return new ModelAndView("cc/cc_noPermission");
        }

        mv = new ModelAndView("cc/cc_positionEdit");
        mv.addObject("position", position);
        mv.addObject("ccModule", module);

        CCHelper.populatePositionQuestions(mv, module, position,
                PortalUtils.getUserLoggedIn(request), true);
        CCHelper.populateAccessFlags(mv, position, request);
        CCHelper.populatePositionCommon(mv, request, position);

        if (StringUtils.isEmpty(request.getParameter("activityId")))
        {
            request.setAttribute("activityId", position.getActivity().getId());
        }

        CCActivity r = CCHelper.getCCActivity(request);
        mv.addObject("activity", r);

        CCHelper.populateCCOutcomes_editMode(mv, module, position, false, request);

        if (position.getId() != null)
        {
            mv.addObject("peopleWithPosition", getHt().find(
                    "select rp.record.owner.preferredFirstName, rp.record.owner.lastName, rp.record.owner.id from CCRecordPosition rp where rp.position=? and rp.status='Approved'",
                    new Object[] { position }));
            CCHelper.populatePositionLOStats(position, mv, getModule(request),
                    locale);
        }

        CCHelper.populateQuickStats(position, mv);

        return mv;
    }

    @RequestMapping("displayPositionOverview")
    public ModelAndView displayPositionOverview(HttpServletRequest request,
            HttpServletResponse response)
    {

        MessageSource messageSource = PortalUtils.getMessageSource();
        Locale locale = PortalUtils.getLocale(request);
        ModelAndView mv;
        CCPosition position = CCHelper.getCCPosition(request);
        boolean fromCards = RequestUtils.getBooleanParameter(request, "fromCards",
                false);
        CCModule module = getModule(request);
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (position == null
                && StringUtils.isInteger(request.getParameter("activityId")))
        {
            CCActivity activity = CCHelper.getCCActivity(request);

            if (activity != null && activity.getPeriod() != null && activity
                    .getPeriod().getModule().getId().equals(module.getId()))
            {
                // ADDING A NEW POSITION!
                Date now = new Date();
                position = new CCPosition();
                position.setActivity(activity);
                position.setCreatedDate(now);
                position.setCreatedBy(userLoggedIn);
                position.setUpdatedDate(now);
                position.setUpdatedBy(userLoggedIn);
                getHt().save(position);

                position.setTitle(
                        messageSource.getMessage(
                                "i18n.ccController.NewPositionUppercase."
                                        + PortalUtils.getDefaultLocale(),
                                null, locale) + " " + position.getId());
                position.setL2Title(messageSource.getMessage(
                        "i18n.ccController.NewPositionUppercase."
                                + (PortalUtils.isSiteInMultilingualMode()
                                ? PortalUtils.getSecondaryLocale()
                                : PortalUtils.getDefaultLocale()),
                        null, locale) + " " + position.getId());
                getHt().update(position);
            }
        }

        if (position == null)
        {
            if (fromCards)
            {
                mv = getShortCircuitView("cc/cc_positionOverview");
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
            }
            else
            {
                mv = displayHome(request, response);
            }
            mv.addObject("errorMessage", messageSource.getMessage(
                    "i18n.ccController.PositionNotFound", null, locale));
        }
        else
        {
            mv = new ModelAndView("cc/cc_positionOverview");
            if (fromCards)
            {
                mv = getShortCircuitView("cc/cc_positionOverview");
            }
            mv.addObject("position", position);
            mv.addObject("shortlisted",
                    CCHelper.getRecordPositionShortlist(
                            CCHelper.getCCRecord(module, userLoggedIn),
                            position) != null);

            if (!StringUtils.isEmpty(request.getParameter("saved")))
            {
                mv.addObject("saved", true);
            }

            CCHelper.populatePositionCommon(mv, request, position);

            // POSITION QUESTIONS
            CCHelper.populatePositionQuestions(mv, getModule(request), position,
                    PortalUtils.getUserLoggedIn(request), false);

            DFModel dfModel = CCPositionQuestionModelHelper
                    .getPositionQuestionModel(module).getDFModel();
            DFHelper.populateModel(dfModel, false, position, userLoggedIn);
            mv.addObject("dfModel", dfModel);

            // QUICK STATS
            CCHelper.populateQuickStats(position, mv);

            // CHART STATS
            Set<String> keysToPopulate = new HashSet<>();
            keysToPopulate.add("pendingValidations");
            keysToPopulate.add("approvedValidations");
            keysToPopulate.add("declinedValidations");
            mv.addObject("statMap", CCHelper.getPeriodStatsMap(keysToPopulate,
                    position.getActivity().getPeriod(), null, position.getId()));
            mv.addObject("statPeriod", position.getActivity().getPeriod());
            mv.addObject("positionId", position.getId());

            // LEARNING OUTCOMES & ACHIEVEMENTS...
            populateCCOutcomes_viewMode(mv, module, position, locale);

            mv.addObject("eventCount", getHt()
                    .find("select count(*) from GlobalEvent e where e.cCPosition=?",
                            position)
                    .get(0));

            // STUDENT STUFF...
            if (userLoggedIn.isStudent() || CCHelper.isAlumni(userLoggedIn))
            {
                CCRecord record = CCHelper.getCCRecord(module, userLoggedIn);

                mv.addObject("canAddToRecord",
                        CCHelper.canAddToRecord(position, record));

                mv.addObject("recordPosition",
                        CCHelper.getCCRecordPosition(record, position));

                if (null != position.getProgram())
                {
                    mv.addObject("ptEnrollment", PTHelper
                            .getEnrollment(position.getProgram(), userLoggedIn));
                }
                mv.addObject("student", userLoggedIn);
                mv.addObject("isStudent", userLoggedIn.isStudent());
            }
            mv.addObject("backToTimePeriod",
                    request.getParameter("backToTimePeriod"));
            mv.addObject("competenciesDisabled",
                    module.getCompetencyUsecase() == Competenciable.COMP_DISABLED);
            CCHelper.populatePositionNodes(mv, position, module,
                    PortalUtils.getLocale(request));

        }

        RequestUtils.repopulateMV(request, mv, "comingFrom",
                "comingFrom_advancedSearchMode", "comingFrom_studentId");
        CCHelper.populateAccessFlags(mv, position, request);

        SiteElement se = NHelper
                .getAcrmCareerSiteElement(PortalUtils.getUserLoggedIn(request));
        mv.addObject("acrmFullPath", se != null ? se.getFullPath() + ".htm" : "");
        mv.addObject("fromCards", fromCards);

        return mv;
    }

    @RequestMapping("ajaxLoadActivityDfDetails")
    public ModelAndView ajaxLoadActivityDfDetails(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_activityDfDetails");
        CCActivity activity = CCHelper.getCCActivity(request);

        DFModel activityDFModel = CCActivityQuestionModelHelper
                .getActivityQuestionModel(getModule(request)).getDFModel();

        DFHelper.populateModel(activityDFModel, false, activity,
                PortalUtils.getUserLoggedIn(request));

        DFQuestion targetQuestionTitle = null;
        DFQuestion targetQuestionDescription = null;

        boolean isL1 = LocaleUtils.isL1(PortalUtils.getLocale(request));
        String titleField = isL1 ? "l2Activity" : "activity";
        String descriptionField = isL1 ? "l2Description" : "description";

        for (DFCategory category : activityDFModel.getCategories())
        {
            List<DFQuestion> questions = category.getQuestions();
            for (DFQuestion question : questions)
            {
                if (titleField.equals(question.getMappingKey()))
                {
                    targetQuestionTitle = question;
                }
                else if (descriptionField.equals(question.getMappingKey()))
                {
                    targetQuestionDescription = question;
                }
            }
            questions.remove(targetQuestionTitle);
            questions.remove(targetQuestionDescription);
        }

        mv.addObject("activity", activity);
        mv.addObject("dfModel", activityDFModel);

        return mv;
    }

    @RequestMapping("ajaxLoadPositionDfDetails")
    public ModelAndView ajaxLoadPositionDfDetails(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_positionDfDetails");
        CCPosition position = CCHelper.getPosition(request);

        DFModel positionDFModel = CCPositionQuestionModelHelper
                .getPositionQuestionModel(getModule(request)).getDFModel();

        DFHelper.populateModel(positionDFModel, false, position,
                PortalUtils.getUserLoggedIn(request));

        DFQuestion targetQuestionTitle = null;
        DFQuestion targetQuestionDescription = null;

        boolean isL1 = LocaleUtils.isL1(PortalUtils.getLocale(request));
        String titleField = isL1 ? "l2Title" : "title";
        String descriptionField = isL1 ? "l2Description" : "positionDesc";

        for (DFCategory category : positionDFModel.getCategories())
        {
            List<DFQuestion> questions = category.getQuestions();
            for (DFQuestion question : questions)
            {
                if (titleField.equals(question.getMappingKey()))
                {
                    targetQuestionTitle = question;
                }
                else if (descriptionField.equals(question.getMappingKey()))
                {
                    targetQuestionDescription = question;
                }
            }
            questions.remove(targetQuestionTitle);
            questions.remove(targetQuestionDescription);
        }

        mv.addObject("position", position);
        mv.addObject("dfModel", positionDFModel);

        return mv;
    }

    @RequestMapping("ajaxLoadPositionDocumentDetails")
    public ModelAndView ajaxLoadPositionDocumentDetails(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        CCPosition position = CCHelper.getPosition(request);
        UserDetailsImpl student = PortalUtils.getUserLoggedIn(request);
        CCRecord record = CCHelper.getCCRecord(getModule(request), student);
        JSONObject ret = new JSONObject();
        CCModule module = getModule(request);

        JSONObject positionNodes = CCHelper.buildQueryAndGetNodes(module, true,
                PortalUtils.getLocale(request), student,
                position.getActivity().getPeriod().getId().toString(),
                position.getActivity().getCategory(),
                position.getActivity().getOrganization(),
                position.getActivity().getDepartment(),
                position.getActivity().getId().toString(), null, null, null);

        CCRecordPosition rp = CCHelper.getCCRecordPosition(record, position);

        if (rp != null)
        {
            JSONObject rpParams = new JSONObject();
            rpParams.put("recordId", record.getId());
            rpParams.put("rpId", rp.getId());
            ret.put("rpParams", rpParams);
        }

        ret.put("canAddToRecord", CCHelper.canAddToRecord(position, record));
        ret.put("shortlisted",
                CCHelper.getRecordPositionShortlist(record, position) != null);
        ret.put("hasRp", rp != null);
        ret.put("positionsInActivity", positionNodes);
        ret.put("isShowLevelCategory", module.isShowLevelCategory());
        ret.put("isShowLevelOrganization", module.isShowLevelOrganization());
        ret.put("isShowLevelDepartment", module.isShowLevelDepartment());
        ret.put("position", position.toJsonObject(PortalUtils.getLocale(request)));
        return jsonObjectResponse(ret);
    }

    @RequestMapping("ajaxLoadPositionToSidebar")
    public ModelAndView ajaxLoadPositionToSidebar(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_positionSidebar");
        CCPosition position = CCHelper.getPosition(request);
        UserDetailsImpl student = request.getParameter("studentId") != null
                ? (UserDetailsImpl) getHt().load(UserDetailsImpl.class,
                        RequestUtils.getIntParameter(request, "studentId", -1))
                : PortalUtils.getUserLoggedIn(request);

        CCRecord record = CCHelper.getCCRecord(getModule(request), student);

        DFModel positionDFModel = CCPositionQuestionModelHelper
                .getPositionQuestionModel(getModule(request)).getDFModel();

        DFHelper.populateModel(positionDFModel, false, position,
                PortalUtils.getUserLoggedIn(request));

        mv.addObject("canAddToRecord", CCHelper.canAddToRecord(position, record));
        mv.addObject("shortlisted",
                CCHelper.getRecordPositionShortlist(record, position) != null);

        CCRecordPosition rp = CCHelper.getCCRecordPosition(request);
        if (rp == null)
        {
            rp = CCHelper.getCCRecordPosition(record, position);
        }
        mv.addObject("recordPosition", rp);

        if (position.getProgram() != null)
        {
            mv.addObject("ptEnrollment",
                    PTHelper.getEnrollment(position.getProgram(), student));
        }
        mv.addObject("student", student);
        mv.addObject("dfModel", positionDFModel);
        mv.addObject("position", position);
        return mv;
    }

    @RequestMapping("ajaxLoadPositionJson")
    public ModelAndView ajaxLoadPositionJson(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        CCPosition position = CCHelper.getPosition(request);

        JSONObject resp = new JSONObject();
        resp.put("position", position.toJsonObject(PortalUtils.getLocale(request)));
        return jsonObjectResponse(resp);
    }

    @RequestMapping("ajaxLoadActivityToSidebar")
    public ModelAndView ajaxLoadActivityToSidebar(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_activitySidebar");
        CCModule module = getModule(request);
        CCActivity a = CCHelper.getCCActivity(request);

        if (a == null)
        {
            mv = displayHome(request, response);
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.CCController.Activityno9321203800295101")
                            .getTranslation(PortalUtils.getLocale(request)));
        }
        else
        {
            mv.addObject("activity", a);
            CCHelper.populateAccessFlags(mv, a, request);

            // POPULATE MV FOR "QUICK STATS"
            CCHelper.populateQuickStats(a);

            // POPULATE MV FOR "ACTIVITY QUESTIONS"
            CCHelper.populateActivityQuestions(mv, module, a,
                    PortalUtils.getUserLoggedIn(request), false);

            String currentRole = CCHelper.getCurrentUserRole(module,
                    PortalUtils.getUserLoggedIn(request));

            if (CCHelper.CC_ROLE_STUDENT.equals(currentRole)
                    || CCHelper.CC_ROLE_ALUMNI.equals(currentRole))
            {
                List<CCPosition> positions = PortalUtils.getHt().find(
                        "from CCPosition p where p.activity=? and p.approvedBy is not null and p.enabled=true order by p.title",
                        new Object[] { a });
                mv.addObject("activityPositions", positions);
                mv.addObject("numActivityPositions", positions.size());
            }
            else if (CCHelper.CC_ROLE_STAFF.equals(currentRole)
                    || CCHelper.CC_ROLE_ADMIN.equals(currentRole))
            {
                List<CCPosition> positions = PortalUtils.getHt().find(
                        "from CCPosition p where p.activity=? order by p.title",
                        new Object[] { a });
                mv.addObject("activityPositions", positions);
                mv.addObject("numActivityPositions", positions.size());
            }

            mv.addObject("comingFrom", request.getParameter("comingFrom"));

            SiteElement se = NHelper
                    .getAcrmCareerSiteElement(PortalUtils.getUserLoggedIn(request));
            mv.addObject("acrmFullPath",
                    se != null ? se.getFullPath() + ".htm" : "");
        }
        return mv;
    }

    @RequestMapping("ajaxLoadTimeTracking")
    public ModelAndView ajaxLoadTimeTracking(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = CCHelper.ajaxLoadTimeTracking(request, response);

        mv.addObject("ccModule", getModule(request));

        return mv;
    }

    @RequestMapping("ajaxLoadReflection")
    public ModelAndView ajaxLoadReflection(HttpServletRequest request,
            HttpServletResponse response)
    {
        AcrmReflectionRecordAssign<?, ?> assign = AcrmReflectionRecordAssignHelper
                .getOrCreateReflectionRecordAssign(request);
        ParamsMap pageReloadParams = new ParamsMap(
                "displayReflectionRecord_version2");
        pageReloadParams.put("recordPositionId",
                assign.getRecordable().getId().toString());
        CCRecordPosition rp = ((AcrmReflectionRecordCCRecordPosition) assign)
                .getCcRecordPosition();
        ParamsMap backToParams = new ParamsMap("displayRecord");
        backToParams.put("studentId", rp.getOwner().getId().toString());
        ModelAndView mv = getShortCircuitView("cc/cc_reflectionAjax");
        AcrmReflectionRecordHelper.populateReflectionView(mv, assign,
                pageReloadParams, backToParams, request);
        boolean fromCards = RequestUtils.getBooleanParameter(request,
                "fromCards", false);
        mv.addObject("fromCards", fromCards);
        mv.addObject("rp", rp);
        mv.addObject("backToParamsJson",
                JSONUtils.toJSON(backToParams.getParams()).toString());
        CCReflectionHelper.populateAcrmReflectionAction(mv, rp,
                Map.of("recordPositionId", rp.getId()));
        return mv;
    }

    @RequestMapping("ajaxLoadCompetencies")
    public ModelAndView ajaxLoadCompetencies(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_recordCompetencyAjax");
        CCRecordPosition rp = CCHelper.getCCRecordPosition(request);

        CCRecordPositionHelper.populateCompetencyDependencies(mv, rp,
                PortalUtils.getUserLoggedIn(request),
                CCHelper.isSpiralRobotEnabled(getModule(request),
                        PortalUtils.getUserLoggedIn(request)),
                PortalUtils.getLocale(request));

        mv.addObject("rp", rp);

        return mv;
    }

    @RequestMapping("ajaxLoadCompetenciesEdit")
    public ModelAndView ajaxLoadCompetenciesEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_recordCompetencyAjax");
        CCRecordPosition rp = CCHelper.getCCRecordPosition(request);

        mv.addAllObjects(CompetencyAchievedHelper.populateAllAchievedCompetencies(
                rp, CCRecordPositionHelper.getCompetencyConfigs(rp), null, false));
        mv.addObject("editStudent", true);

        mv.addObject("rp", rp);

        boolean fromCards = RequestUtils.getBooleanParameter(request,
                "fromCards", false);

        mv.addObject("fromCards", fromCards);

        return mv;
    }

    @RequestMapping("displayPositionValidators")
    public ModelAndView displayPositionValidators(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        CCPosition pos = CCHelper.getCCPosition(request);
        if (pos != null)
        {
            mv = new ModelAndView("cc/cc_positionValidatorsTab");
            mv.addObject("position", pos);
            mv.addObject("validators", getHt().find(
                    "from CCPositionValidator pv where pv.position=? order by pv.validator.lastName",
                    pos));
            CCHelper.populateQuickStats(pos, mv);
            CCHelper.populateAccessFlags(mv, pos, request);
            CCHelper.populatePositionCommon(mv, request, pos);
        }
        else
        {
            mv = displayHome(request, response);
            mv.addObject("errorMessage",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.ccController.PositionNotFound", null,
                            PortalUtils.getLocale(request)));
        }

        return mv;
    }

    @RequestMapping("displayPositionParticipants")
    public ModelAndView displayPositionParticipants(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        CCPosition position = CCHelper.getCCPosition(request);
        if (position != null)
        {
            mv = new ModelAndView("cc/cc_positionParticipants");
            mv.addObject("position", position);

            mv.addObject("recordPositions", getHt().find(
                    "from CCRecordPosition rp where rp.position=? order by rp.position.title, rp.record.owner.preferredFirstName, rp.record.owner.lastName",
                    position));
            CCHelper.populateQuickStats(position, mv);
            CCHelper.populateAccessFlags(mv, position, request);
            CCHelper.populatePositionCommon(mv, request, position);
        }
        else
        {
            mv = displayHome(request, response);
            mv.addObject("errorMessage",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.ccController.PositionNotFound", null,
                            PortalUtils.getLocale(request)));
        }

        return mv;
    }

    @RequestMapping("displayPositionTags")
    public ModelAndView displayPositionTags(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = new ModelAndView("cc/cc_positionTags");

        CCPosition position = CCHelper.getCCPosition(request);

        mv.addObject("position", position);

        CCHelper.populateQuickStats(position, mv);
        CCHelper.populateAccessFlags(mv, position, request);

        JSONObject additionalParams = new JSONObject();
        additionalParams.put("posId", position.getId());

        AcrmHelper.processTagAssign(request, mv, additionalParams, position, true);

        return mv;
    }

    @RequestMapping("displayPositionLearningOutcomes")
    public ModelAndView displayPositionLearningOutcomes(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        Locale locale = PortalUtils.getLocale(request);
        CCPosition position = CCHelper.getCCPosition(request);
        if (position != null)
        {
            mv = new ModelAndView("cc/cc_positionLearningOutcomes");
            CCModule module = getModule(request);
            mv.addObject("position", position);
            populateCCOutcomes_viewMode(mv, module, position, locale);
            CCHelper.populateQuickStats(position, mv);
            CCHelper.populateAccessFlags(mv, position, request);
        }
        else
        {
            mv = displayHome(request, response);
            mv.addObject("errorMessage", PortalUtils.getMessageSource().getMessage(
                    "i18n.ccController.PositionNotFound", null, locale));
        }

        return mv;
    }

    @RequestMapping("displayPositionProgramTracking")
    public ModelAndView displayPositionProgramTracking(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        CCPosition position = CCHelper.getCCPosition(request);
        if (position != null)
        {
            mv = new ModelAndView("cc/cc_positionProgramTracking");
            mv.addObject("position", position);

            List<PTProgram> programs = getHt()
                    .find("from PTProgram p order by p.module.id, p.name");
            Map<PTProgram, CCPosition> ppMap = new LinkedHashMap<>();
            for (PTProgram p : programs)
            {
                List<CCPosition> ccp = getHt()
                        .find("from CCPosition ccp where ccp.program=?", p);

                ppMap.put(p, ccp.isEmpty() ? null : ccp.get(0));
            }
            mv.addObject("ppMap", ppMap);
            CCHelper.populateQuickStats(position, mv);
            CCHelper.populateAccessFlags(mv, position, request);
        }
        else
        {
            mv = displayHome(request, response);
            mv.addObject("errorMessage",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.ccController.PositionNotFound", null,
                            PortalUtils.getLocale(request)));
        }

        return mv;
    }

    @RequestMapping("displayPositionEventConnection")
    public ModelAndView displayPositionEventConnection(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_positionEventConnection");

        CCPosition position = CCHelper.getCCPosition(request);
        mv.addObject("position", position);

        List<GlobalEvent> events = getHt().find(
                "from GlobalEvent e where e.cCPosition=? order by e.title",
                position);
        mv.addObject("events", events);
        CCHelper.populateQuickStats(position, mv);
        CCHelper.populateAccessFlags(mv, position, request);

        return mv;
    }

    @RequestMapping("displayPositionStats")
    public ModelAndView displayPositionStats(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        Locale locale = PortalUtils.getLocale(request);

        CCModule module = getModule(request);
        CCPosition position = CCHelper.getCCPosition(request);

        if (position != null)
        {
            mv = new ModelAndView("cc/cc_positionStats");
            CCHelper.populateCCOutcomes_editMode(mv, module, position, false,
                    request);

            if (position.getId() != null)
            {
                mv.addObject("peopleWithPosition", getHt().find(
                        "select rp.record.owner.preferredFirstName, rp.record.owner.lastName, rp.record.owner.id from CCRecordPosition rp where rp.position=? and rp.status='Approved'",
                        position));
                CCHelper.populatePositionLOStats(position, mv, getModule(request),
                        locale);
            }
            mv.addObject("position", position);
        }
        else
        {
            mv = displayHome(request, response);
            mv.addObject("errorMessage", PortalUtils.getMessageSource().getMessage(
                    "i18n.ccController.PositionNotFound", null, locale));
        }

        return mv;
    }

    @RequestMapping("displayPositionAdditionalTimePeriods")
    public ModelAndView displayPositionAdditionalTimePeriods(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = null;
        CCPosition pos = CCHelper.getCCPosition(request);
        if (pos != null)
        {
            mv = new ModelAndView("cc/cc_positionAdditionalTimePeriods");
            mv.addObject("additionalPositions", getHt().find(
                    "select c.id, c.activity.period.name, c.description, c.createdDate from CCPosition c where c.activity.activity=? and c.activity.department=? and c.activity.organization=? "
                            + "and c.activity.category=? and c.title=? and c.activity.period.id!=?",
                    new Object[] { pos.getActivity().getActivity(),
                            pos.getActivity().getDepartment(),
                            pos.getActivity().getOrganization(),
                            pos.getActivity().getCategory(), pos.getTitle(),
                            pos.getActivity().getPeriod().getId() }));
            mv.addObject("position", pos);
            CCHelper.populateQuickStats(pos, mv);
        }
        else
        {
            mv = displayHome(request, response);
            mv.addObject("errorMessage",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.ccController.PositionNotFound", null,
                            PortalUtils.getLocale(request)));
        }
        return mv;
    }

    /**
     * Save position
     *
     * @param request
     * @param response
     * @return
     */

    @RequestMapping("savePosition")
    public ModelAndView savePosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCPosition p = CCHelper.getCCPosition(request);

        if (!CCHelper.canEdit(p, request))
        {
            return new ModelAndView("cc/cc_noPermission");
        }

        CCModule module = getModule(request);
        try
        {
            CCHelper.savePositionCommon(request, p, module);
        }
        catch (CCException e)
        {
            ModelAndView mv = displayPositionEdit(request, response);
            mv.addObject("positionSaved", Boolean.FALSE);
            mv.addObject("error_msg", e.getMessage());
            return mv;
        }

        // return to Position edit page.
        request.setAttribute("positionId", p.getId());
        ModelAndView mv = displayPositionEdit(request, response);
        mv.addObject("positionSaved", Boolean.TRUE);
        return mv;
    }

    @RequestMapping("savePositionStatus")
    public ModelAndView savePositionStatus(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCPosition position = CCHelper.getCCPosition(request);
        Boolean status = "1".equals(request.getParameter("status"));

        position.setEnabled(status);
        getHt().saveOrUpdate(position);
        return jsonBooleanResponse(SUCCESS_KEY, true);
    }

    @RequestMapping("savePositionStatusAndVisibility")
    public ModelAndView savePositionStatusAndVisibility(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        String currentRole = CCHelper.getCurrentUserRole(module, userLoggedIn);
        if (CCHelper.CC_ROLE_ADMIN.equals(currentRole)
                || CCHelper.CC_ROLE_ACTIVITY_DIRECTOR.equals(currentRole))
        {
            CCPosition position = CCHelper.getCCPosition(request);
            Integer approvalStatus = StringUtils
                    .toInteger(request.getParameter("approvalStatus"));
            CCHelper.updatePositionApprovalStatus(position, approvalStatus,
                    userLoggedIn);

            String requestorFirstName = position.getRequestorFirstName() != null
                    ? position.getRequestorFirstName()
                    : "";
            String requestorLastName = position.getRequestorLastName() != null
                    ? position.getRequestorLastName()
                    : "";

            if (approvalStatus == CCPosition.APPROVAL_STATUS_APPROVED)
            {
                boolean sendEmailNotification = "on".equalsIgnoreCase(
                        request.getParameter("sendApprovalEmailNotification"));

                if (sendEmailNotification)
                {
                    String to = request.getParameter("approvedTo");
                    String from = request.getParameter("approvedFrom");

                    String userLocale = NHelper.getUserLocale(to);
                    boolean isL1Locale = LocaleUtils.isL1(userLocale);

                    String subject = CCHelper.processEmailTokens(
                            request.getParameter("approvedSubject"),
                            requestorFirstName, requestorLastName,
                            isL1Locale ? position.getActivity().getActivity()
                                    : position.getActivity().getL2Activity(),
                            isL1Locale ? position.getTitle()
                                    : position.getL2Title(),
                            null, null, null);
                    String message = CCHelper.processEmailTokens(
                            request.getParameter("approvedMessage"),
                            requestorFirstName, requestorLastName,
                            isL1Locale ? position.getActivity().getActivity()
                                    : position.getActivity().getL2Activity(),
                            isL1Locale ? position.getTitle()
                                    : position.getL2Title(),
                            null, null, null);

                    String username = NHelper.getUsernameByNameAndEmail(
                            requestorFirstName, requestorLastName, to);

                    EmailUtils.sendAndLog1Email(new EmailMessage(from,
                            PortalUtils.getUserLoggedIn(request), to, username,
                            subject, message, message, null, false, false));
                }
            }
            else if (approvalStatus == CCPosition.APPROVAL_STATUS_DECLINED)
            {
                position.setDeniedReason(request.getParameter("denialReason"));
                boolean sendEmailNotification = request
                        .getParameter("sendDeclineEmailNotification") != null;

                if (sendEmailNotification)
                {
                    String to = request.getParameter("deniedTo");
                    String from = request.getParameter("deniedFrom");
                    String userLocale = NHelper.getUserLocale(to);
                    boolean isL1Locale = LocaleUtils.isL1(userLocale);
                    String subject = CCHelper.processEmailTokens(
                            request.getParameter("deniedSubject"),
                            requestorFirstName, requestorLastName,
                            isL1Locale ? position.getActivity().getActivity()
                                    : position.getActivity().getL2Activity(),
                            isL1Locale ? position.getTitle()
                                    : position.getL2Title(),
                            null, null, null);
                    String message = CCHelper.processEmailTokens(
                            request.getParameter("deniedMessage"),
                            requestorFirstName, requestorLastName,
                            isL1Locale ? position.getActivity().getActivity()
                                    : position.getActivity().getL2Activity(),
                            isL1Locale ? position.getTitle()
                                    : position.getL2Title(),
                            null, null, null);
                    String username = NHelper.getUsernameByNameAndEmail(
                            requestorFirstName, requestorLastName, to);

                    EmailUtils.sendAndLog1Email(new EmailMessage(from,
                            PortalUtils.getUserLoggedIn(request), to, username,
                            subject, message, message, null, false, false));
                }
            }

            if (position.isEnabled()
                    && !RequestUtils.isChecked(request, "visibility"))
            {
                position.setEnabled(false);
                position.setDateDisabled(DateUtils.now());
                position.setDisabledBy(userLoggedIn);
                getHt().update(position);
            }
            else if (!position.isEnabled()
                    && RequestUtils.isChecked(request, "visibility"))
            {
                position.setEnabled(true);
                position.setDateDisabled(null);
                position.setDisabledBy(null);
                getHt().update(position);
            }

            if (position.getActivity().getStatus() == 0)
            {
                CCHelper.updateActivityStatus(position.getActivity(), userLoggedIn);
            }
        }

        return displayPositionOverview(request, response);
    }

    @RequestMapping("saveRecordPositionReflection")
    public ModelAndView saveRecordPositionReflection(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        CCRecordPosition rp = CCHelper.getCCRecordPosition(request);

        boolean isSaveSuccess = false;

        if (rp != null)
        {

            boolean studentCannotEditDeclinedPosition = userLoggedIn.isStudent()
                    && !module.isStudentCanUpdateDeclinedPositions()
                    && rp.getStatus().equals(CCRecordPosition.STATUS_DECLINED);

            if (!studentCannotEditDeclinedPosition)
            {
                if (module.isEnableReflectionComment())
                {
                    rp.setReflectionComment(
                            request.getParameter("reflectionComment"));
                }

                if (!CCRecordPosition.STATUS_APPROVED.equals(rp.getStatus())
                        && Objects.equals(rp.getRecord().getOwner(), userLoggedIn))
                {
                    CCRecordPositionHelper.changeRecordPositionStatus(rp,
                            CCRecordPosition.STATUS_PENDING, userLoggedIn);
                }

                getHt().saveOrUpdate(rp);

                request.setAttribute("recordPosition", rp);

                isSaveSuccess = true;
            }
            else
            {
                FlashMessageUtils.error(request,
                        "i18n.CCController.Declinedre8025892753553765");
            }
        }

        ModelAndView mv = displayRecordPositionEdit(request, response);

        mv.addObject("rpSaved", isSaveSuccess);

        return mv;
    }

    /**
     * Save record position
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("saveRecordPosition")
    public ModelAndView saveRecordPosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        request.setAttribute("limitedEdit", request.getParameter("limitedEdit"));

        CCRecordPosition rp = CCHelper.getCCRecordPosition(request);
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        boolean invalidExternalValidatorEmail = false;
        boolean isSaveSuccess = false;

        if (rp != null)
        {

            boolean studentCannotEditDeclinedPosition = userLoggedIn.isStudent()
                    && !module.isStudentCanUpdateDeclinedPositions()
                    && rp.getStatus().equals(CCRecordPosition.STATUS_DECLINED);

            if (!studentCannotEditDeclinedPosition)
            {
                if (rp.getPosition().isExternalPosition())
                {
                    bindExternalPositionQuestions(request, module, rp);
                }

                if (module.isEnableReflectionComment())
                {
                    rp.setReflectionComment(
                            request.getParameter("reflectionComment"));
                }

                if (module.isEnableCompetencySelection() && (!userLoggedIn
                        .isStudent()
                        || (!CCRecordPosition.STATUS_APPROVED.equals(rp.getStatus())
                                || module
                                        .isStudentsChangeCompetenciesAfterApproval()))
                        && !module.isStudentsReceivePositionCompetencies())
                {
                    String compentencies = request
                            .getParameter("personalCompetenciesSelected");
                    rp.setPersonalCompetencies(compentencies);
                    if (CCRecordPosition.STATUS_NOT_SUBMITTED
                            .equals(rp.getStatus()))
                    {
                        CCRecordPositionHelper.changeRecordPositionStatus(rp,
                                CCRecordPosition.STATUS_PENDING, userLoggedIn);
                    }
                }

                getHt().saveOrUpdate(rp);

                PortalUtils.getJt()
                        .execute("update cc_record set dateUpdated = "
                                + DateUtils.getCurrentDateClause() + " where id = "
                                + rp.getRecord().getId());

                if ((!userLoggedIn.isAlumni() && !userLoggedIn.isStudent())
                        || (StringUtils.equalsAny(rp.getStatus(),
                                CCRecordPosition.STATUS_PENDING,
                                CCRecordPosition.STATUS_NOT_SUBMITTED)
                                && module.isStudentSelectLOA()))
                {
                    deletedCCRecordPositionOutcomes(module, rp);
                    CCHelper.saveCCRecordPositionOutcomes(request, module, rp);

                    if (CCRecordPosition.STATUS_NOT_SUBMITTED
                            .equals(rp.getStatus()))
                    {
                        CCRecordPositionHelper.changeRecordPositionStatus(rp,
                                CCRecordPosition.STATUS_PENDING, userLoggedIn);
                    }
                }

                if (!CCRecordPosition.STATUS_APPROVED.equals(rp.getStatus())
                        && Objects.equals(rp.getRecord().getOwner(), userLoggedIn))
                {
                    CCRecordPositionHelper.changeRecordPositionStatus(rp,
                            CCRecordPosition.STATUS_PENDING, userLoggedIn);
                }

                request.setAttribute("recordPosition", rp);

                isSaveSuccess = true;
            }
            else
            {
                FlashMessageUtils.error(request,
                        "i18n.CCController.Declinedre8025892753553765");
            }
        }

        ModelAndView mv = displayRecordPositionEdit(request, response);

        if (isSaveSuccess)
        {
            mv.addObject("successMessage",
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.RecordPosi9452170318586688",
                            PortalUtils.getLocale(request)));
        }

        if (invalidExternalValidatorEmail)
        {
            FlashMessageUtils.warn(request,
                    "i18n.CCController.Theexterna3648109482426046");
        }

        return mv;
    }

    @RequestMapping("emailExternalValidator")
    public ModelAndView emailExternalValidator(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        CCRecordPosition rp = CCHelper.getCCRecordPosition(request);
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
        boolean invalidExternalValidatorEmail = RequestUtils.getBooleanParameter(
                request, "invalidExternalValidatorEmail", false);
        boolean externalEmailInvalid = sendExternalValidatorEmailRequest(request,
                module, rp, user, invalidExternalValidatorEmail);

        ModelAndView mv = displayRecordPositionEdit(request, response);
        if (externalEmailInvalid)
        {
            FlashMessageUtils.error(request,
                    "i18n.CCController.ExternalVa4791110551348515");
        }
        else
        {
            FlashMessageUtils.success(request,
                    "i18n.CCController.Emailsentt7118886181714180");

        }
        return mv;
    }

    private boolean sendExternalValidatorEmailRequest(HttpServletRequest request,
            CCModule module, CCRecordPosition rp, UserDetailsImpl user,
            boolean invalidExternalValidatorEmail)
    {
        if (RequestUtils.getBooleanParameter(request,
                "includeEmailToExternalValidator", false))
        {
            invalidExternalValidatorEmail = sendExternalValidatorEmail(module, rp,
                    user, RequestUtils.getBooleanParameter(request,
                            "includeEmailToExternalValidator", false));
        }

        return invalidExternalValidatorEmail;
    }

    private boolean sendExternalValidatorEmail(CCModule module, CCRecordPosition rp,
            UserDetailsImpl user, boolean includeExternalValidatorEmail)
    {
        boolean invalidExternalValidatorEmail = false;
        if (CCRecordPositionHelper.canWriteCCExternalRecordPosition(rp))
        {
            String email = getExternalEmail(module, rp);
            try
            {
                if (email != null && EmailUtils.isValidEmailAddress(email))
                {
                    if ((includeExternalValidatorEmail
                            && CCRecordPosition.STATUS_DECLINED
                                    .equals(rp.getStatus()))
                            || rp.getExternalValidationDate() == null)
                    {
                        sendExternalValidationEmails(module, rp, user, email);
                    }
                }
                else
                {
                    invalidExternalValidatorEmail = true;
                }
            }
            catch (Exception e)
            {
                // TODO Auto-generated catch block
                e.printStackTrace();
            }
        }
        return invalidExternalValidatorEmail;
    }

    private String getExternalEmail(CCModule module, CCRecordPosition rp)
    {
        CCExternalPositionQuestionModel ccExternalPostingQuestionModel = CCExternalPositionQuestionModelHelper
                .getExternalPositionQuestionModel(module);
        List<DFQuestion> questions = DFHelper
                .getDFQuestions(ccExternalPostingQuestionModel.getDFModel());
        String answerField = questions.stream()
                .filter(q -> "s6".equals(q.getAnswerField1()))
                .map(qq -> qq.getAnswerField1()).findFirst().orElse("");
        String email = null;
        try
        {
            email = (String) PropertyUtils.getProperty(rp, answerField);
        }
        catch (Exception e1)
        {
            // TODO Auto-generated catch block
            e1.printStackTrace();
        }
        return email;
    }

    private void sendExternalValidationEmails(CCModule module, CCRecordPosition rp,
            UserDetailsImpl user, String email)
    {
        List<Integer> ids = Lists.newArrayList();
        ids.add(rp.getId());

        Object[] objects = new Object[] { email, null };
        List<Object[]> arr = Lists.newArrayList();
        arr.add(objects);

        Map<Integer, List<Object[]>> emails = Maps.newHashMap();
        emails.put(rp.getId(), arr);
        sendPublicValidationEmail(ids, module, user, emails);
    }

    @RequestMapping("loadAllActivePeriodNodes")
    public ModelAndView loadAllActivePeriodNodes(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {

        CCModule module = getModule(request);

        String node = request.getParameter("node");
        String value = request.getParameter("value");
        List<String> newNode = CCHelper.getActivityNodesForAllActivePeriods(node,
                value, module);
        JSONObject ret = new JSONObject();
        ret.put("newNode", newNode);
        return jsonObjectResponse(ret);
    }

    @RequestMapping("loadNodesByPeriodId")
    public ModelAndView loadNodesByPeriodId(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_requestPositionNodes");

        String periodId = request.getParameter("periodId");
        String node = request.getParameter("node");
        String nodeValue = request.getParameter("value");

        if (StringUtils.isInteger(periodId))
        {
            List nodes = CCHelper.getActivityNodesByPeriodId(node, nodeValue,
                    Integer.valueOf(periodId), getModule(request));

            mv.addObject("nodes", nodes);
        }

        return mv;
    }

    @RequestMapping("printMyRecord")
    public ModelAndView printMyRecord(HttpServletRequest request,
            HttpServletResponse response)
    {
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);

        CCHelper.logPrintActivity(user, user.getId().toString(),
                getModule(request));

        return CCHelper.generateCCReportPDF(request, user.getId().toString(),
                getDataSource());
    }

    @RequestMapping("printRecordForUser")
    public ModelAndView printRecordForUser(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        if (CCHelper.isAdmin(PortalUtils.getUserLoggedIn(request),
                getModule(request)))
        {
            CCHelper.logPrintActivity(PortalUtils.getUserLoggedIn(request),
                    request.getParameter("userId"), getModule(request));

            mv = CCHelper.generateCCReportPDF(request,
                    request.getParameter("userId"), getDataSource());
        }
        return mv;
    }

    @RequestMapping("displayPrintLog")
    public ModelAndView displayPrintLog(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordPrintLog");

        if (!StringUtils.isEmpty(request.getParameter("ownerId")))
        {
            mv.addObject(
                    "r", getHt()
                            .find("from CCRecord r where r.owner.id=?",
                                    Integer.valueOf(request.getParameter("ownerId")))
                            .get(0));
            mv.addObject("logs",
                    getHt().find(
                            "from CCRecordPrintLog l where l.ccRecord.owner.id=?",
                            Integer.valueOf(request.getParameter("ownerId"))));
        }
        return mv;
    }

    @RequestMapping("loadPositions")
    public ModelAndView loadPositions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView(
                "cc/cc_requestPositionCheckPositions");
        CCModule module = getModule(request);
        CCActivity a = CCHelper.getCCActivity(request);

        CCHelper.loadPositions(mv, request, module);

        mv.addObject("module", module);
        mv.addObject("category", a.getCategory());
        mv.addObject("organization", a.getOrganization());
        mv.addObject("department", a.getDepartment());
        mv.addObject("activity", a.getActivity());

        return mv;
    }

    /**
     * Look up users
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupUsers")
    public ModelAndView lookupUsers(HttpServletRequest request,
            HttpServletResponse response)
    {
        List<Object[]> userData = CCHelper.lookupUsers(request.getParameter("term"),
                request.getParameter("group"));
        return NHelper.lookupsAJAXResponse(userData, 0, "[2] [3] ([1])");
    }

    /**
     * Look up positions
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupPositions")
    public ModelAndView lookupPositions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        try
        {
            mv = CCHelper.lookupPositions(request, getModule(request));
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return mv;
    }

    @RequestMapping("ajaxLoadPositionsForDataViewer")
    public ModelAndView ajaxLoadPositionsForDataViewer(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        DataViewerState dvState = new DataViewerState(request);

        JSONObject resp = CCHelper.getPositionDatasForDataViewer(true, true,
                RequestUtils.getBooleanParameter(request, "shortlistOnly", false),
                getModule(request), PortalUtils.getUserLoggedIn(request), dvState);

        return jsonObjectResponse(resp);
    }

    @RequestMapping("ajaxLookupPositionsForActivity")
    public ModelAndView ajaxLookupPositionsForActivity(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        String term = request.getParameter("term");
        if (!StringUtils.isEmpty(term))
        {
            term = term.replaceAll("'", "''");
        }
        String activityId = request.getParameter("activityId");
        String currentRole = request.getParameter("currentRole");
        List<Object[]> data = new ArrayList<>();
        String ret = "";

        if (term.length() > 0)
        {
            boolean isL1 = LocaleUtils.isL1(request);
            String periodField = isL1 ? "name" : "l2Name";
            String activityField = isL1 ? "activity" : "l2Activity";
            String positionField = isL1 ? "title" : "l2Title";

            StringBuilder hql = new StringBuilder();
            hql.append("select p.id, p.activity.period." + periodField
                    + ", p.activity." + activityField + ", p." + positionField);

            hql.append(
                    " from CCPosition p join p.activity where p.activity.period.module.id=")
                    .append(module.getId());

            hql.append(" and p.activity.period.active=true and p.activity.id != ")
                    .append(activityId);

            if ("activityDirector".equals(currentRole))
            {
                hql.append(
                        " and p.activity.id in (select ad.activity.id from CCActivityDirectors ad where ad.directors.id in (select d.directors.id from CCDirector d where d.member.id=")
                        .append(PortalUtils.getUserLoggedIn(request).getId())
                        .append("))");
            }

            hql.append(" and (p.").append(positionField).append(" like '%")
                    .append(term).append("%' or p.activity.").append(activityField)
                    .append(" like '%").append(term).append("%'");

            if (module.isShowLevelDepartment())
            {
                hql.append(" or p.activity.department like '%").append(term)
                        .append("%'");
            }

            if (module.isShowLevelOrganization())
            {
                hql.append(" or p.activity.organization like '%").append(term)
                        .append("%'");
            }

            if (module.isShowLevelCategory())
            {
                hql.append(" or p.activity.category like '%").append(term)
                        .append("%' ");
            }

            hql.append(") order by p." + positionField + ", p.activity."
                    + activityField);

            data = CollectionUtils.distinctObjectArrayList(
                    PortalUtils.getHt().find(hql.toString()), 0);

            JSONArray json = new JSONArray();
            try
            {
                for (Object[] row : data)
                {
                    JSONObject j = new JSONObject();
                    j.put("id", row[0]);
                    j.put("cuId", row[3]);
                    String label = StringUtils.fillInPlaceholders("([1]) [2] - [3]",
                            row);
                    j.put("label", label);
                    j.put("value", label);
                    j.put("activity", row[2]);
                    j.put("position", row[3]);

                    json.put(j);
                }
            }
            catch (JSONException e)
            {
                json = new JSONArray();
            }

            if (json.length() > 0)
            {
                ret = json.toString();
            }
        }

        return NHelper.AJAXResponse(ret);
    }

    /**
     * Search across student/validator/activity/position simultaneously
     *
     * TODO
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupCombined")
    public ModelAndView lookupCombined(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        return CCHelper.lookupCombined(request, response, getModule(request));
    }

    /**
     * Look up activities
     *
     * @param request
     * @param response
     * @return
     */

    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupActivities")
    public ModelAndView lookupActivities(HttpServletRequest request,
            HttpServletResponse response)
    {
        return CCHelper.lookupActivities(request, response, getModule(request));
    }

    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupStudents")
    public ModelAndView lookupStudents(HttpServletRequest request,
            HttpServletResponse response)
    {
        return CCHelper.lookupStudents(request, response, getModule(request));
    }

    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupValidators")
    public ModelAndView lookupValidators(HttpServletRequest request,
            HttpServletResponse response)
    {
        return CCHelper.lookupValidators(request, response, getModule(request));
    }

    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupNewStudents")
    public ModelAndView lookupNewStudents(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        CCModule module = getModule(request);
        return CCHelper.lookupNewStudents(request, response, module);
    }

    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupNewValidators")
    public ModelAndView lookupNewValidators(HttpServletRequest request,
            HttpServletResponse response)
    {
        return CCHelper.lookupNewValidators(request, response);
    }

    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupNewUsers")
    public ModelAndView lookupNewUsers(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = null;

        if (StringUtils.isInteger(request.getParameter("idIndex")))
        {
            Integer idIndex = Integer.parseInt(request.getParameter("idIndex"));
            mv = CCHelper.lookupNewUsers(request, response, idIndex);
        }
        else
        {
            mv = CCHelper.lookupNewUsers(request, response);
        }

        return mv;
    }

    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupNewActivities")
    public ModelAndView lookupNewActivities(HttpServletRequest request,
            HttpServletResponse response)
    {
        return CCHelper.lookupNewActivities(request, response, getModule(request));
    }

    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "lookupNewPositions")
    public ModelAndView lookupNewPositions(HttpServletRequest request,
            HttpServletResponse response)
    {
        return CCHelper.lookupNewPositions(request, response, getModule(request));
    }

    @RequestMapping("loadRequestedValidatorMatches")
    public ModelAndView loadRequestedValidatorMatches(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_requestedValidatorMatches");

        String firstName = request.getParameter("firstName");
        String lastName = request.getParameter("lastName");
        String email = request.getParameter("email");
        String phone = request.getParameter("phone");
        String position = request.getParameter("position");

        QueryBuilder query = new QueryBuilder(
                "SELECT u.USER_DETAILS_ID, CONCAT(u.preferredFirstName, ' ', u.lastName) as name, u.username, u.emailAddress, u.phoneNumber, u.phoneCell, u.jobTitle, CASE WHEN EXISTS(SELECT udg.userDetailsId FROM user_details_groups udg JOIN user_group ug ON ug.id=udg.userGroupId WHERE udg.userDetailsId=u.USER_DETAILS_ID AND ug.name=?) THEN 1 ELSE 0 END AS validator, CASE WHEN u.preferredFirstName=? and u.lastName=? THEN 1 ELSE 0 END AS nameMatch",
                PersonGroupHelper.CC_VALIDATOR, firstName, lastName);
        query.appendIf(!StringUtils.isEmpty(email),
                ", CASE WHEN u.username=? or u.emailAddress=? THEN 1 ELSE 0 END AS emailMatch",
                email, email);
        query.appendIf(!StringUtils.isEmpty(phone),
                ", CASE WHEN u.phoneNumber=? or u.phoneCell=? THEN 1 ELSE 0 END AS phoneMatch",
                phone, phone);
        query.appendIf(!StringUtils.isEmpty(position),
                ", CASE WHEN u.jobTitle=? THEN 1 ELSE 0 END AS positionMatch",
                position);
        query.append(" FROM user_details u ");
        query.append(
                "WHERE u.enabled=1 AND u.deleted=0 AND ((u.preferredFirstName=? AND u.lastName=?) ",
                firstName, lastName);
        query.appendIf(!StringUtils.isEmpty(email),
                " OR u.username=? OR u.emailAddress=? ", email, email);
        query.appendIf(!StringUtils.isEmpty(phone),
                " OR u.phoneNumber=? OR u.phoneCell=? ", phone, phone);
        query.append(")");

        ResultSetExtractor matchedUserExtractor = rs -> {
            List<Map<String, Object>> ret = new ArrayList<>();
            while (rs.next())
            {
                Map<String, Object> matchingUser = new HashMap<>();
                matchingUser.put("id", rs.getInt("USER_DETAILS_ID"));
                matchingUser.put("name", rs.getString("name"));
                matchingUser.put("nameMatch", rs.getInt("nameMatch"));
                int matches = rs.getInt("nameMatch");
                matchingUser.put("username", rs.getString("username"));
                matchingUser.put("validator", rs.getInt("validator"));
                if (!StringUtils.isEmpty(email))
                {
                    matchingUser.put("email", rs.getString("emailAddress"));
                    matchingUser.put("emailMatch", rs.getInt("emailMatch"));
                    matches += rs.getInt("emailMatch");
                }
                if (!StringUtils.isEmpty(phone))
                {
                    matchingUser.put("phone", rs.getString("phoneNumber"));
                    matchingUser.put("cellphone", rs.getString("phoneCell"));
                    matchingUser.put("phoneMatch", rs.getInt("phoneMatch"));
                    matches += rs.getInt("phoneMatch");
                }
                if (!StringUtils.isEmpty(position))
                {
                    matchingUser.put("position", rs.getString("jobTitle"));
                    matchingUser.put("positionMatch", rs.getInt("positionMatch"));
                    matches += rs.getInt("positionMatch");
                }
                matchingUser.put("matches", matches);
                ret.add(matchingUser);
            }
            return ret;
        };

        Comparator<Map<String, Object>> sortByMatchCountThenName = (o1, o2) -> {
            int ret = (Integer) o2.get("matches") - (Integer) o1.get("matches");

            if (ret == 0)
            {
                String name1 = (String) o1.get("name");
                String name2 = (String) o2.get("name");
                ret = name1.compareTo(name2);
            }

            return ret;
        };

        List<Map<String, Object>> matchedUsers = (List<Map<String, Object>>) PortalUtils
                .getJt()
                .query(query.getStr(), query.getParams(), matchedUserExtractor);
        matchedUsers.sort(sortByMatchCountThenName);
        mv.addObject("matchedUsers", matchedUsers);

        mv.addObject("requestedValidatorName", firstName + " " + lastName);
        mv.addObject("requestedValidatorPosition", position);
        mv.addObject("requestedValidatorEmail", email);
        mv.addObject("requestedValidatorPhone", phone);
        mv.addObject("positionId", request.getParameter("positionId"));
        mv.addObject("currentValidatorIds", PortalUtils.getHt().find(
                "select pv.validator.id from CCPositionValidator pv where pv.position.id=?",
                request.getParameter("positionId")));

        SiteElement se = NHelper
                .getAcrmCareerSiteElement(PortalUtils.getUserLoggedIn(request));
        mv.addObject("acrmFullPath", se != null ? se.getFullPath() + ".htm" : "");

        return mv;
    }

    @RequestMapping("loadMassAssignValStep5")
    public ModelAndView loadMassAssignValStep5(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_massAssignValidatorPositions");
        String hql = "select pv.position.id, a.category, a.organization, a.department, a.activity, pv.position.title, pv.position.status"
                + " from CCPositionValidator pv join pv.position.activity a"
                + " where pv.validator.id=? and a.period.id=?";
        if (StringUtils.isInteger(request.getParameter("selectedStatuses")))
        {
            String statuses = request.getParameter("selectedStatuses");
            List<Integer> statusList = new ArrayList();
            for (int i = 0; i < statuses.length(); i++)
            {
                statusList.add(Integer.valueOf(String.valueOf(statuses.charAt(i))));
            }
            hql += " and pv.position.status in "
                    + DBUtils.buildInClause(statusList);
        }
        List positions = getHt().find(hql,
                new Object[] {
                        Integer.valueOf(request.getParameter("validatorSelected")),
                        Integer.valueOf(request.getParameter("periodId")) });

        mv.addObject("withWhat", request.getParameter("withWhat"));
        mv.addObject("positions", positions);

        return mv;
    }

    @RequestMapping("massAssignValidators")
    public ModelAndView massAssignValidators(HttpServletRequest request,
            HttpServletResponse response)
    {
        boolean massUpdateInitiated = false;

        if (request.getParameterValues("positionIds") != null)
        {
            List positions = Arrays
                    .stream(request.getParameterValues("positionIds"))
                    .filter(StringUtils::isInteger).map(Integer::parseInt)
                    .collect(Collectors.toList());
            new MassUpdateCCPositionValidatorThread(positions, request).start();
            massUpdateInitiated = true;
        }

        return displayMassValidatorsEdit(request, response)
                .addObject("massUpdateInitiated", massUpdateInitiated);
    }

    @RequestMapping("bulkAddParticipants")
    public ModelAndView bulkAddParticipants(HttpServletRequest request,
            HttpServletResponse response)
    {
        List<String> errorList = new ArrayList<>();
        CCModule module = getModule(request);

        Function<RequestModuleUser, ContentItem> function = (rmu) -> CCHelper
                .getUserCCRecord(rmu);

        List<String> ids = CCHelper.addStudentRecordToLearningExperience(request,
                errorList, module, function);

        CCPosition position = CCHelper.getCCPosition(request);

        ModelAndView mv;
        if ("activity".equals(request.getParameter("returnTo")))
        {
            mv = displayActivityParticipants(request, response);
        }
        else if (position != null && position.isExternalPosition())
        {
            UserDetailsImpl student = UserDetailsHelper
                    .getUserByUsername(ids.get(0));
            mv = displayRecordPositionExternalEdit(request, response, position,
                    student);
        }
        else
        {
            mv = displayPositionParticipants(request, response);
            mv.addObject("modified", ids.size() - errorList.size());
            mv.addObject("errorList", errorList);
            mv.addObject("nonModified", errorList.size());
        }

        return mv;
    }

    @RequestMapping("removePositionFromValidator")
    public ModelAndView removePositionFromValidator(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCPositionValidator pv = (CCPositionValidator) getHt().load(
                CCPositionValidator.class,
                Integer.valueOf(request.getParameter("pvId")));
        getHt().delete(pv);

        return jsonArrayResponse(new JSONArray());
    }

    @RequestMapping("ajaxSaveValidatorToPosition")
    public ModelAndView ajaxSaveValidatorToPosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        manageValidatorPosition(request, "create");

        return jsonArrayResponse(new JSONArray());
    }

    @RequestMapping("ajaxRemoveValidatorFromPosition")
    public ModelAndView ajaxRemoveValidatorFromPosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        manageValidatorPosition(request, "delete");
        return jsonArrayResponse(new JSONArray());
    }

    /**
     * Delete validator from position
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("deleteValidatorFromPosition")
    public ModelAndView deleteValidatorFromPosition(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        guardAgainstMissingValidatorGroup();

        CCPositionValidator pv = (CCPositionValidator) getHt().load(
                CCPositionValidator.class,
                Integer.valueOf(Integer.parseInt(request.getParameter("pvId"))));
        UserDetailsImpl validator = pv.getValidator();

        PortalUtils.getJt().update("delete from cc_position_validator where id = ?",
                new Object[] { request.getParameter("pvId") });

        Integer validatorCount = (Integer) getHt().find(
                "select count(pv) from CCPositionValidator pv where pv.validator=? ",
                validator).get(0);
        if (validatorCount.intValue() == 0)
        {
            UserDetailsHelper.removeUserGroup(validator,
                    PersonGroupHelper.CC_VALIDATOR);
            getHt().update(validator);
        }

        return displayPositionEdit(request, response);
    }

    /**
     * called when the search button is click. jumps us to the search grid.
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping("search")
    public ModelAndView search(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        return SearchSubControllerFactory.getInstance(ccSearchInterface)
                .processRequest(request, response);
    }

    /**
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("displayMassPositionEdit")
    public ModelAndView displayMassPositionEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_massEditPositions");
        CCModule ccModule = getModule(request);
        mv.addObject("ccPeriods", getHt().find(
                "FROM CCPeriod p WHERE p.module=? AND p.active=true", ccModule));
        return mv;
    }

    @RequestMapping("ajaxLoadPositions")
    public ModelAndView ajaxLoadPositions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_positionsCheckboxWidget");
        mv.addObject("positions", getHt().find(
                "select p.id, p.title from CCPosition p where p.activity=? and pendingRequest = 0 and requestDenied is null",
                CCHelper.getActivityFromSelector(getModule(request), request)));
        return mv;
    }

    /**
     * responds to an ajax call and enables or disables a number of positions in a
     * given period
     *
     * @param request
     * @param response
     * @return
     * @throws JSONException
     */
    @RequestMapping("updatePositions")
    public ModelAndView updatePositions(HttpServletRequest request,
            HttpServletResponse response)
    {
        List<Integer> positions = getPositionsForMassAssign(request, "select p.id");
        boolean massUpdateInitiated = CCHelper.massUpdatePositionVisibility(
                positions, "1".equals(request.getParameter("enable")), request);

        ModelAndView mv = displayMassPositionEdit(request, response);

        mv.addObject("massUpdateInitiated", massUpdateInitiated);

        return mv;
    }

    /**
     * @param request
     * @param response
     * @return
     * @throws JSONException
     */
    @RequestMapping("displayMassValidatorsEdit")
    public ModelAndView displayMassValidatorsEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_massEditValidators");

        mv.addObject("ccPeriods",
                getHt().find("FROM CCPeriod p WHERE p.module=? AND p.active=true",
                        getModule(request)));

        return mv;
    }

    @RequestMapping("ajaxLookupUsers")
    public ModelAndView ajaxLookupUsers(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        JSONArray ret = new JSONArray();
        String searchTerm = request.getParameter("term");

        if (!StringUtils.isEmpty(searchTerm))
        {
            StringBuilder hql = new StringBuilder();
            hql.append(
                    "select u.id, u.preferredFirstName, u.lastName, u.username, u.emailAddress from UserDetailsImpl u where u.enabled=true and u.deleted=false and u.userStatus <> 'Prospect' and ")
                    .append(LookupUtils.getUserSearchTermHqlWhereClause(searchTerm,
                            "u", null))
                    .append(" order by u.preferredFirstName, u.lastName");

            List<Object[]> data = getHt().find(hql.toString());
            data = CollectionUtils.limitList(data, 50);
            StringBuilder sb;
            for (Object[] obj : data)
            {
                sb = new StringBuilder();
                sb.append(obj[1]).append(" ").append(obj[2]).append(" (")
                        .append(obj[3]).append(") ").append(obj[4]);
                JSONObject p = new JSONObject();
                p.put("value", obj[0]);
                p.put("label", sb.toString());
                ret.put(p);
            }
        }

        return jsonArrayResponse(ret);
    }

    @RequestMapping("ajaxLoadUserDetails")
    public ModelAndView ajaxLoadUserDetails(HttpServletRequest request,
            HttpServletResponse response)
    {
        UserDetailsImpl user = NESHelper.getUser(request);

        ModelAndView mv = getShortCircuitView(
                "cc/cc_massEditValidators_userDetails");
        mv.addObject("user", user);

        return mv;
    }

    @RequestMapping("displayManagePositionQuestions")
    public ModelAndView displayManagePositionQuestions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_managePositionQuestions");
        mv.addObject("model", CCPositionQuestionModelHelper
                .getPositionQuestionModel(getModule(request)));
        return mv;
    }

    @RequestMapping("displayManageExternalPositionQuestions")
    public ModelAndView displayManageExternalPositionQuestions(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_manageExternalPositionQuestions");
        mv.addObject("model", CCExternalPositionQuestionModelHelper
                .getExternalPositionQuestionModel(getModule(request)));
        return mv;
    }

    @RequestMapping("updatePositionApprovalStatus")
    public ModelAndView updatePositionApprovalStatus(HttpServletRequest request,
            HttpServletResponse response)
    {
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        CCModule module = getModule(request);

        if (CCHelper.isAdmin(userLoggedIn, module)
                || CCHelper.isActivityDirector(userLoggedIn, module)
                || CCHelper.isStaff(userLoggedIn, module))
        {
            CCPosition position = CCHelper.getCCPosition(request);
            if (position != null)
            {
                CCHelper.updatePositionApprovalStatus(position,
                        StringUtils
                                .toInteger(request.getParameter("approvalStatus")),
                        userLoggedIn);

                String to = position.getRequestorEmail();
                String from = module.getFromEmailForRequest();

                if (!StringUtils.isEmpty(to) && !StringUtils.isEmpty(from))
                {
                    String username = NHelper.getUsernameByNameAndEmail(
                            position.getRequestorFirstName(),
                            position.getRequestorLastName(), to);
                    String userLocale = NHelper.getUserLocale(to);
                    boolean isL1Locale = LocaleUtils.isL1(userLocale);

                    String subject = CCHelper.processEmailTokens(
                            isL1Locale ? module.getApproveSubjectForRequest()
                                    : module.getL2ApproveSubjectForRequest(),
                            position.getRequestorFirstName(),
                            position.getRequestorLastName(),
                            isL1Locale ? position.getActivity().getActivity()
                                    : position.getActivity().getL2Activity(),
                            isL1Locale ? position.getTitle()
                                    : position.getL2Title(),
                            null, null, null);
                    String message = CCHelper.processEmailTokens(
                            isL1Locale ? module.getApproveEmailForRequest()
                                    : module.getL2ApproveEmailForRequest(),
                            position.getRequestorFirstName(),
                            position.getRequestorLastName(),
                            isL1Locale ? position.getActivity().getActivity()
                                    : position.getActivity().getL2Activity(),
                            isL1Locale ? position.getTitle()
                                    : position.getL2Title(),
                            null, null, null);

                    EmailUtils.sendAndLog1Email(new EmailMessage(from,
                            PortalUtils.getUserLoggedIn(request), to, username,
                            subject, message, message, null, false, false));
                }
            }
        }

        ModelAndView mv = displayPositionOverview(request, response);
        return mv;
    }

    private void createLeadershipOrTimeQuestion(CCModule module,
            String leadershipOrTime, Locale locale)
    {
        DFModel model = CCPositionQuestionModelHelper
                .getPositionQuestionModel(module).getDFModel();
        List<DFCategory> categories = getHt().find(
                "from DFCategory c where c.model = ? order by c.position", model);
        if (!categories.isEmpty())
        {
            DFCategory category = categories.get(0);
            int index = ((Integer) getHt().find(
                    "select count(q.id) from DFQuestion q where q.category = ?",
                    category).get(0)).intValue() + 1;
            if (leadershipOrTime.equalsIgnoreCase("leadership"))
            {
                createLeadershipPtsQuestion(index, category, module, model, locale);
            }
            else if (leadershipOrTime.equalsIgnoreCase("time"))
            {
                createTimeTrackingPtsQuestion(index, category, module, model,
                        locale);
            }
        }
    }

    private void createTimeTrackingPtsQuestion(int index, DFCategory category,
            CCModule module, DFModel model, Locale locale)
    {
        if (module.isEnableTimeTracking() && ((Integer) getHt().find(
                "select count(q.id) from DFQuestion q where "
                        + " q.answerField1 = ? and q.category.model = ? ",
                new Object[] { "hoursToTrack", model }).get(0)).intValue() == 0)
        {
            DFQuestion question = new DFQuestion();
            question.setQuestionText(
                    new I18nLabel("i18n.ccQuestion.questionText.hoursToTrack"));
            question.setAnswerField1("hoursToTrack");
            question.setType(DFQuestion.TYPE_INTEGER);
            question.setMin(0);
            question.setMax(40);
            question.setDisplayType(DFQuestion.NUMBER_DISPLAY_DROPDOWN);
            question.setPosition(index);
            question.setCategory(category);
            getHt().save(question);

            PortalUtils.getJt().execute(
                    "insert into df_question_role (dfQuestion, dfRole, canWrite, canRead, canSearch, canShowInResults, canShowInReports) select "
                            + question.getId()
                            + ", r.id, r.defaultWrite, r.defaultRead, r.defaultSearch, r.defaultShowInResults, r.defaultShowInReports from df_role r where r.model="
                            + model.getId());
        }
    }

    private void createLeadershipPtsQuestion(int index, DFCategory category,
            CCModule module, DFModel model, Locale locale)
    {
        if (module.isEnableLeadershipPoints() && ((Integer) getHt().find(
                "select count(q.id) from DFQuestion q where "
                        + " q.answerField1 = ? and q.category.model = ? ",
                new Object[] { "points", model }).get(0)).intValue() == 0)
        {
            DFQuestion question = new DFQuestion();
            question.setQuestionText(
                    new I18nLabel("i18n.ccQuestion.questionText.LeadershipPoints"));
            question.setAnswerField1("points");
            question.setType(DFQuestion.TYPE_INTEGER);
            question.setMin(0);
            question.setMax(10);
            question.setDisplayType(DFQuestion.NUMBER_DISPLAY_DROPDOWN);
            question.setPosition(index);
            question.setCategory(category);
            getHt().save(question);

            PortalUtils.getJt().execute(
                    "insert into df_question_role (dfQuestion, dfRole, canWrite, canRead, canSearch, canShowInResults, canShowInReports) select "
                            + question.getId()
                            + ", r.id, r.defaultWrite, r.defaultRead, r.defaultSearch, r.defaultShowInResults, r.defaultShowInReports from df_role r where r.model="
                            + model.getId());
        }
    }

    @RequestMapping("displayHomeSearches")
    public ModelAndView displayHomeSearches(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_home_searches");

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName("ajaxLoadSavedSearches");
        GridHelper.addGridSupport(request, mv, "advancedSearch_savedSearches",
                options);
        mv.addObject("shareLevelOptions",
                SearchHelper.getShareLevelOptions(PortalUtils.getLocale(request)));

        return mv;
    }

    @RequestMapping("displayHomeMassAssign")
    public ModelAndView displayHomeMassAssign(HttpServletRequest request,
            HttpServletResponse response)
    {
        return new ModelAndView("cc/cc_home_massAssign");
    }

    @RequestMapping("displayHomeAllTimePeriods")
    public ModelAndView displayHomeAllTimePeriods(HttpServletRequest request,
            HttpServletResponse response)
    {
        return new ModelAndView("cc/cc_home_allTimePeriods");
    }

    /**
     * handles ajax calls to update the vrs bar chart based on new parameters
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping("updateVrsBarChart")
    public ModelAndView updateVrsBarChart(HttpServletRequest request,
            HttpServletResponse response)
    {
        JSONObject jb = new JSONObject();

        if (!StringUtils.isEmpty(request.getParameter("prop"))
                && !StringUtils.isEmpty(request.getParameter("opp")))
        {
            jb = CCReportsHelper.getVrsChart(request);
        }

        return jsonObjectResponse(jb);
    }

    /**
     * @param request
     * @param response
     * @return
     * @throws JSONException
     */
    @RequestMapping("getFilterOptions")
    public ModelAndView getFilterOptions(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        Map<String, List> jb = new LinkedHashMap<>();

        List opt = new ArrayList();
        String[] filters = { "major1Code", "yearLevel", "degree1", "gender" };

        for (String f : filters)
        {
            opt = getHt().find("select distinct u." + f
                    + " from UserDetailsImpl u order by u." + f + " asc");
            jb.put(f, opt);
        }

        return jsonObjectResponse(new JSONObject(jb));
    }

    /**
     * Returns a chart object for the activities tab based on the filter type.
     *
     * @param request
     * @param response
     * @return chart json object
     * @throws JSONException
     */
    @RequestMapping("getFilteredActivitiesChart")
    public ModelAndView getFilteredActivitiesChart(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        // dumpRequestParameters(request);
        JSONObject jb = new JSONObject();

        jb = CCReportsHelper.getActivityStats(request, getModule(request));
        return jsonObjectResponse(jb);
    }

    /**
     * Returns a chart object for the activities tab based on the filter type.
     *
     * @param request
     * @param response
     * @return chart json object
     * @throws JSONException
     */
    @RequestMapping("getPositionChart")
    public ModelAndView getPositionChart(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        JSONObject jb = new JSONObject();

        jb = CCReportsHelper.getPositionStats(request, getModule(request));
        return jsonObjectResponse(jb);
    }

    /**
     * Returns a chart with no data. This is to get highcharts to display an
     * initially empty chart.
     *
     * @param request
     * @param response
     * @return
     * @throws JSONException
     */
    @RequestMapping("getDummyChart")
    public ModelAndView getDummyChart(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        // dumpRequestParameters(request);
        JSONObject jb = new JSONObject();
        Map<String, Double> chartData = new LinkedHashMap<>();

        jb = ChartUtils.getBarChart(request.getParameter("title"), "", chartData,
                request.getParameter("yAxisLabel"),
                request.getParameter("xAxisLabel"), true);
        jb.getJSONObject("legend").put("enabled", false);
        return jsonObjectResponse(jb);
    }

    /**
     * @param request
     * @param response
     * @return
     * @throws JSONException
     */
    @RequestMapping("showPositionsGrid")
    public ModelAndView showPositionsGrid(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        ModelAndView mv = new ModelAndView("cc/cc_reports_overviewGrid");

        CCReportsHelper.getGridModel_Positions(mv, request, getModule(request));

        return mv;
    }

    /**
     * @param request
     * @param response
     * @return
     * @throws JSONException
     */
    @RequestMapping("showRecordsGrid")
    public ModelAndView showRecordsGrid(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        ModelAndView mv = new ModelAndView("cc/cc_reports_recordGrid");

        JQGridModel gridModel = CCReportsHelper.getGridModel_studentRecord(mv,
                request, getModule(request));
        JQGridHelper.populateSearchResultsPage(mv, gridModel, request,
                SearchHelper.SEARCH_MODEL_SESSION_KEY);

        return mv;
    }

    @RequestMapping("displayHomeModuleConfig")
    public ModelAndView displayHomeModuleConfig(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        CCModule module = getModule(request);

        if (CCHelper.isAdmin(PortalUtils.getUserLoggedIn(request),
                getModule(request)))
        {
            mv = new ModelAndView("cc/cc_home_moduleConfig");
            mv.addObject("competenciedPositionsCount", CCPositionHelper
                    .getCompetenciedPositionsCount(getModule(request)));
            mv.addObject("competenciesDisabled",
                    module.getCompetencyUsecase() == Competenciable.COMP_DISABLED);
        }
        else
        {
            mv = displayHome(request, response);
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.CCController.AccessDeni3324475388611185")
                            .getTranslation(PortalUtils.getLocale(request)));
        }

        return mv;
    }

    @RequestMapping("displayCompetenciedPositions")
    public ModelAndView displayCompetenciedPositions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_competenciedPositions");
        mv.addObject("positions",
                CCPositionHelper.getCompetenciedPositions(getModule(request)));
        return mv;
    }

    @RequestMapping("displayManageProfileHeaders")
    public ModelAndView displayManageProfileHeaders(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = AcrmHelper.headerManageProfileHeaders(request, response);
        mv.addObject("moduleType", "ccr");
        mv.addObject("backTo", "displayHomeModuleConfig");
        return mv;
    }

    @RequestMapping("updateHeaderOrder")
    public ModelAndView updateHeaderOrder(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        return AcrmHelper.headerUpdateQuestionOrder(request);
    }

    @OrbisRepost
    @RequestMapping("displayManageDirectors")
    public ModelAndView displayManageDirectors(HttpServletRequest request,
            HttpServletResponse response)
    {
        Locale locale = PortalUtils.getLocale(request);
        ModelAndView mv = new ModelAndView("cc/cc_manageDirectors");
        CCModule module = getModule(request);

        List<CCDirectors> directors = getHt()
                .find("from CCDirectors d where d.module=? order by d."
                        + (LocaleUtils.isL1(locale) ? "name" : "l2Name"), module);

        for (CCDirectors d : directors)
        {
            Integer count = (Integer) getHt().find(
                    "select count(d.id) from CCDirector d where d.directors=?", d)
                    .get(0);

            d.setDirectorCount(count);
        }

        mv.addObject("directors", directors);
        if (!StringUtils.isEmpty(request.getParameter("successMessage")))
        {
            mv.addObject("successMessage", request.getParameter("successMessage"));
        }

        return mv;
    }

    @RequestMapping("displayManageMessages")
    public ModelAndView displayManageMessages(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_manageMessages");
        return mv;
    }

    @RequestMapping("saveMessages")
    public ModelAndView saveMessages(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        module.setRequestNotReadyMessage(
                request.getParameter("requestNotReadyMessage"));
        module.setL2RequestNotReadyMessage(StringUtils.getValueForL2Fields(
                request.getParameter("l2RequestNotReadyMessage"),
                request.getParameter("requestNotReadyMessage")));
        module.setStudentHomeFooter(request.getParameter("studentHomeFooter"));
        module.setL2StudentHomeFooter(StringUtils.getValueForL2Fields(
                request.getParameter("l2StudentHomeFooter"),
                request.getParameter("studentHomeFooter")));
        module.setStudentHomeHeader(request.getParameter("studentHomeHeader"));
        module.setL2StudentHomeHeader(StringUtils.getValueForL2Fields(
                request.getParameter("l2StudentHomeHeader"),
                request.getParameter("studentHomeHeader")));
        module.setValidationFooter(request.getParameter("validationFooter"));
        module.setL2ValidationFooter(StringUtils.getValueForL2Fields(
                request.getParameter("l2ValidationFooter"),
                request.getParameter("validationFooter")));
        module.setValidationHeader(request.getParameter("validationHeader"));
        module.setL2ValidationHeader(StringUtils.getValueForL2Fields(
                request.getParameter("l2ValidationHeader"),
                request.getParameter("validationHeader")));
        module.setStaffFooter(request.getParameter("staffFooter"));
        module.setL2StaffFooter(StringUtils.getValueForL2Fields(
                request.getParameter("l2StaffFooter"),
                request.getParameter("staffFooter")));
        module.setStaffHeader(request.getParameter("staffHeader"));
        module.setL2StaffHeader(StringUtils.getValueForL2Fields(
                request.getParameter("l2StaffHeader"),
                request.getParameter("staffHeader")));
        module.setActivityOwnerHeader(request.getParameter("activityOwnerHeader"));
        module.setL2ActivityOwnerHeader(StringUtils.getValueForL2Fields(
                request.getParameter("l2ActivityOwnerHeader"),
                request.getParameter("activityOwnerHeader")));
        module.setActivityOwnerFooter(request.getParameter("activityOwnerFooter"));
        module.setL2ActivityOwnerFooter(StringUtils.getValueForL2Fields(
                request.getParameter("l2ActivityOwnerFooter"),
                request.getParameter("activityOwnerFooter")));
        module.setPositionSearchUnavailableMessage(
                request.getParameter("positionSearchUnavailableMessage"));
        module.setL2PositionSearchUnavailableMessage(
                StringUtils.getValueForL2Fields(
                        request.getParameter("l2PositionSearchUnavailableMessage"),
                        request.getParameter("positionSearchUnavailableMessage")));

        module.setExternalValidatorInstructionMessage(
                request.getParameter("externalValidatorInstructionMessage"));
        module.setL2ExternalValidatorInstructionMessage(
                StringUtils.getValueForL2Fields(
                        request.getParameter(
                                "l2ExternalValidatorInstructionMessage"),
                        request.getParameter(
                                "externalValidatorInstructionMessage")));
        getHt().update(module);

        ModelAndView mv = displayManageMessages(request, response);
        mv.addObject("saved", Boolean.TRUE);
        return mv;
    }

    @RequestMapping("editDepartmentLabel")
    public ModelAndView editDepartmentLabel(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        module.setDepartmentLabel(request.getParameter("name"));
        module.setL2DepartmentLabel(StringUtils.getValueForL2Fields(
                request.getParameter("l2Name"), request.getParameter("name")));
        getHt().update(module);

        return displayManageDirectors(request, response);
    }

    @RequestMapping("editDirectorsLabel")
    public ModelAndView editDirectorsLabel(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        CCDirectors team = CCHelper.getCCDirectorsTeam(request);

        if (team == null)
        {
            mv = displayManageDirectors(request, response);
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.CCController.Baddirecto8620608319351618")
                            .getTranslation(PortalUtils.getLocale(request)));
        }
        else
        {
            String label = request.getParameter("name");
            if (!StringUtils.isEmpty(label))
            {
                List directors = getHt().find(
                        "from CCDirectors d where d.name=? and d.module=? and d.id!=?",
                        new Object[] { label, getModule(request), team });
                if (directors.isEmpty())
                {
                    team.setName(label);
                    team.setL2Name(StringUtils.getValueForL2Fields(
                            request.getParameter("l2Name"),
                            request.getParameter("name")));
                    getHt().update(team);

                    mv = displayManageDirectorsMembers(request, response);
                    mv.addObject("successMessage",
                            new I18nLabel(
                                    "i18n.CCController.Groupnames1996494909795388")
                                    .getTranslation(
                                            PortalUtils.getLocale(request)));
                }
                else
                {
                    mv = displayManageDirectorsMembers(request, response);
                    mv.addObject("errorMessage",
                            new I18nLabel(
                                    "i18n.CCController.Groupnamea6206724549583012")
                                    .getTranslation(
                                            PortalUtils.getLocale(request)));
                }
            }
            else
            {
                mv = displayManageDirectors(request, response);
                mv.addObject("errorMessage",
                        new I18nLabel(
                                "i18n.CCController.Missinggro0083852174882495")
                                .getTranslation(PortalUtils.getLocale(request)));
            }
        }

        return mv;
    }

    @RequestMapping("displayManageDirectorsMembers")
    public ModelAndView displayManageDirectorsMembers(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_directorsMembers");
        CCDirectors directors = CCHelper.getCCDirectorsTeam(request);

        mv.addObject("team", directors);
        mv.addObject("isDeletable",
                CCHelper.getActivityDirectors(directors).size() == 0);
        mv.addObject("members", CCHelper.getCCDirectors(directors));
        mv.addObject("backTo", request.getParameter("backTo"));
        mv.addObject("id", request.getParameter("id"));

        NHelper.populateAcrmCareerSiteElement(mv, request);

        return mv;
    }

    @RequestMapping("addCCDirectors")
    public ModelAndView addCCDirectors(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        if (!StringUtils.isEmpty(request.getParameter("name")))
        {
            List directors = getHt().find(
                    "from CCDirectors d where d.name=? and d.module=?",
                    new Object[] { request.getParameter("name"),
                            getModule(request) });
            if (directors.isEmpty())
            {
                CCDirectors t = new CCDirectors();
                t.setModule(getModule(request));
                t.setName(request.getParameter("name"));
                t.setL2Name(StringUtils.getValueForL2Fields(
                        request.getParameter("l2Name"),
                        request.getParameter("name")));
                getHt().save(t);

                mv = displayManageDirectors(request, response);
                mv.addObject("successMessage",
                        new I18nLabel(
                                "i18n.CCController.Groupsucce4560665261648458")
                                .getTranslation(PortalUtils.getLocale(request)));
            }
            else
            {
                mv = displayManageDirectors(request, response);
                mv.addObject("errorMessage",
                        new I18nLabel(
                                "i18n.CCController.Groupnamea6856373454339919")
                                .getTranslation(PortalUtils.getLocale(request)));
            }
        }
        else
        {
            mv = displayManageDirectors(request, response);
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.CCController.Missinggro8240348125239372")
                            .getTranslation(PortalUtils.getLocale(request)));
        }

        return mv;
    }

    @RequestMapping("addDirectorsMemberById")
    public ModelAndView addDirectorsMemberById(HttpServletRequest request,
            HttpServletResponse response) throws ServletRequestBindingException
    {
        CCDirectors team = Objects
                .requireNonNull(CCHelper.getCCDirectorsTeam(request));
        UserDetailsImpl user = RequestUtils.getRequiredEntity(request, "userId",
                UserDetailsImpl.class);
        if (CCDirectorHelper.isDirector(user, team))
        {
            FlashMessageUtils.warn(request,
                    "i18n.CCController.Thememberi2629210790004153");
        }
        else
        {
            CCDirectorHelper.save(team, user);
            FlashMessageUtils.success(request,
                    "i18n.CCController.Addedmembe5186702567767271");
        }
        return displayManageDirectorsMembers(request, response);
    }

    @RequestMapping("addDirectorsMembers")
    public ModelAndView addDirectorsMembers(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCDirectors team = CCHelper.getCCDirectorsTeam(request);
        if (team != null)
        {
            if (!StringUtils.isEmpty(request.getParameter("users")))
            {
                List<String> members = CCHelper.getCCDirectors(team,
                        "select director.member.username");
                String[] usersFromRequest = request.getParameter("users")
                        .split("\r\n");
                for (String uName : usersFromRequest)
                {
                    String username = uName.trim();
                    if (!members.contains(username))
                    {
                        List<UserDetailsImpl> users = getHt().find(
                                "from UserDetailsImpl u where u.username=?",
                                username);
                        if (!users.isEmpty())
                        {
                            UserDetailsImpl member = users.get(0);
                            CCDirectorHelper.save(team, member);
                        }
                    }
                }
            }
        }

        ModelAndView mv = displayManageDirectorsMembers(request, response);
        mv.addObject("addSuccess", Boolean.TRUE);
        return mv;
    }

    @RequestMapping("deleteCCDirectors")
    public ModelAndView deleteCCDirectors(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        CCDirectors team = CCHelper.getCCDirectorsTeam(request);

        if (team != null)
        {
            PortalUtils.getJt().update(
                    "delete from cc_director where directors = ?",
                    new Object[] { team.getId() });
            getHt().delete(team);
            mv = displayManageDirectors(request, response);
            mv.addObject("successMessage",
                    new I18nLabel("i18n.CCController.Groupsucce8941117672069295")
                            .getTranslation(PortalUtils.getLocale(request)));
        }

        return mv;
    }

    @RequestMapping("deleteTeamMember")
    public ModelAndView deleteTeamMember(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCHelper.delete(CCHelper.getCCDirector(request));

        ModelAndView mv = displayManageDirectorsMembers(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.CCController.Groupmembe7720835880371568")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    @RequestMapping("displayAdminHome")
    public ModelAndView displayAdminHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (!CCHelper.isAdmin(userLoggedIn, module))
        {
            return new ModelAndView("cc/cc_noPermission");
        }

        CCHelper.updateCurrentUserRole(CCHelper.CC_ROLE_ADMIN,
                PortalUtils.getUserLoggedIn(request));

        ModelAndView mv = new ModelAndView("cc/cc_adminHome");

        CCHelper.populatePeriodSelection(request, mv, module);

        return mv;
    }

    @RequestMapping("ajaxLoadDashboardStatsForPeriod")
    public ModelAndView ajaxLoadDashboardStatsForPeriod(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        CCModule module = getModule(request);
        String currentRole = CCHelper.getCurrentUserRole(module,
                PortalUtils.getUserLoggedIn(request));
        if (CCHelper.CC_ROLE_ADMIN.equals(currentRole))
        {
            mv = getShortCircuitView("cc/cc_adminDashboardTable");
        }
        else if (CCHelper.CC_ROLE_ACTIVITY_DIRECTOR.equals(currentRole))
        {
            mv = getShortCircuitView("cc/cc_activityDirectorDashboardTable");
        }

        populateDashboard(module, mv, request);

        return mv;
    }

    private void populateDashboard(CCModule module, ModelAndView mv,
            HttpServletRequest request)
    {
        String activityInClause = CCHelper.getValidActivitiesForUserHQL(
                PortalUtils.getUserLoggedIn(request), module, request);

        Integer periodId = RequestUtils.getIntParameter(request, "periodId", -1);
        request.setAttribute("periodId", periodId);

        Date now = new Date();
        Date tenDaysAgo = DateUtils.subtractDays(now, 10);

        String currentRole = CCHelper.getCurrentUserRole(module,
                PortalUtils.getUserLoggedIn(request));

        String pendingValidationsCount = "select count(rp.id) from CCRecordPosition rp join rp.position as position join rp.position.activity as activity join rp.position.activity.period as period where "
                + CcRecordPositionValidationsHql.getWherePendingValidations(module,
                        request);
        mv.addObject("pendingValidationsCount",
                getHt().find(pendingValidationsCount).get(0));

        int pendingExternalValidationsCount = 0;
        if (module.isEnableExternalValidations())
        {
            pendingExternalValidationsCount = getHt().findInt(
                    "select count(rp.id) from CCRecordPosition rp join rp.position as position join rp.position.activity as activity join rp.position.activity.period as period where "
                            + CcRecordPositionPendingExternalValidationsHql
                                    .getWherePendingExternalValidations(module,
                                            request));
        }
        mv.addObject("pendingExternalValidationsCount",
                pendingExternalValidationsCount);

        if (CCHelper.CC_ROLE_ACTIVITY_DIRECTOR.equals(currentRole))
        {
            String pendingValidations10DaysCount = "select count(rp.id) from CCRecordPosition rp join rp.position join rp.position.activity join rp.position.activity.period where "
                    + CcRecordPositionPendingValidations10DaysHql
                            .getWherePendingValidations10Days(module, request);
            mv.addObject("pendingValidations10DaysCount",
                    getHt().find(pendingValidations10DaysCount).get(0));
        }

        String declinedValidations10DaysCount = "select count(rp.id) from CCRecordPosition rp join rp.position join rp.position.activity join rp.position.activity.period where "
                + CcRecordPositionDeclinedValidations10DaysHql
                        .getWhereDeclinedValidations10Days(module, request);
        mv.addObject("declinedValidations10DaysCount",
                getHt().find(declinedValidations10DaysCount).get(0));

        String approvedValidations10DaysCount = "select count(rp.id) from CCRecordPosition rp join rp.position join rp.position.activity join rp.position.activity.period where "
                + CcRecordPositionApprovedValidations10DaysHql
                        .getWhereApprovedValidations10Days(module, request);
        mv.addObject("approvedValidations10DaysCount",
                getHt().find(approvedValidations10DaysCount).get(0));

        int approvedValidationsZeroCompetenciesCount = 0;

        mv.addObject("approvedValidationsZeroCompetenciesCount",
                approvedValidationsZeroCompetenciesCount);

        int approvedValidationsNoReflectionCount = 0;
        AcrmReflectionConfig conf = module.getReflectionConfigurableSupport()
                .getReflectionConfig(module);
        if (conf != null && conf.isAnyReflectionEnabled())
        {
            approvedValidationsNoReflectionCount = getHt().findInt(
                    "select count(rp.id) from CCRecordPosition rp join rp.position join rp.position.activity join rp.position.activity.period where "
                            + CcRecordPositionApprovedValidationsNoReflectionHql
                                    .getWhereApprovedValidationsZeroReflections(
                                            module, request));
        }
        mv.addObject("approvedValidationsNoReflection",
                approvedValidationsNoReflectionCount);

        int pendingValidationWithTimeTracking = 0;
        if (module.isEnableTimeTracking())
        {
            pendingValidationWithTimeTracking = getHt().findInt(
                    "select count(rp.id) from CCRecordPosition rp join rp.position position join position.activity activity join activity.period period where rp.hours < position.hoursToTrack and rp.status=? and period.id=? and activity.id in "
                            + activityInClause,
                    CCRecordPosition.STATUS_PENDING, periodId);
        }
        mv.addObject("pendingValidationWithTimeTracking",
                pendingValidationWithTimeTracking);

        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);

        Integer pendingRequestsCount = (Integer) getHt().find(
                "select count(distinct p.id) from CCPosition p join p.activity join p.activity.period join p.activity.period.module where "
                        + CCHelper.getPendingRequestsClause(user, module, request))
                .get(0);

        if (CCHelper.CC_ROLE_ACTIVITY_DIRECTOR.equals(currentRole))
        {
            Integer pendingOver10Days = (Integer) getHt().find(
                    "select count(distinct p.id) from CCPosition p join p.activity join p.activity.period join p.activity.period.module where "
                            + CCHelper.getPendingRequestsClause(user, module,
                                    request)
                            + " and p.requestDate not between ? and ? ",
                    new Object[] { tenDaysAgo, now }).get(0);
            mv.addObject("pendingOver10Days", pendingOver10Days);
        }

        Integer pendingActivities = getHt().findInt(
                "select count (a.id) from CCActivity a left join a.period left join a.createdBy left join a.currentOwner where "
                        + CcRecordPositionActivitiesHql
                                .getWherePendingActivities(module, request));

        Integer pendingPositions = PortalUtils.getHt().findInt(
                " select count(p.id) from CCPosition p left join p.activity as activity left join p.program left join p.activity.period as period where "
                        + CcRecordPositionPositionsHql
                                .getWherePendingPositions(module, request));

        Integer studentsWithAtleastOnePosition = PortalUtils.getHt()
                .findInt("select count(u.id) from UserDetailsImpl u where "
                        + CcRecordPositionStudentsWithAtleastOnePositionHql
                                .getWhereStudentsWithAtleastOnePosition(module,
                                        request));

        mv.addObject("pendingRequestsCount", pendingRequestsCount);
        mv.addObject("pendingActivities", pendingActivities);
        mv.addObject("pendingPositions", pendingPositions);

        mv.addObject("studentsWithAtleastOnePosition",
                studentsWithAtleastOnePosition);
    }

    private void populateDashboardStats(ModelAndView mv, CCModule module,
            CCActivity activity, CCPosition position, HttpServletRequest request,
            Set<String> keysToPopulate)
    {
        Map<CCPeriod, Map<String, Object>> statMaster = new LinkedHashMap<>();
        List<CCPeriod> periods = new ArrayList<>();

        String activityInClause = CCHelper.getValidActivitiesForUserHQL(
                PortalUtils.getUserLoggedIn(request), module, request);

        if (activity != null)
        {
            activityInClause = " (" + activity.getId() + ") ";
            periods.add(activity.getPeriod());
        }
        else if (position != null)
        {
            periods.add(position.getActivity().getPeriod());
        }
        else
        {
            periods = getHt().find(
                    "from CCPeriod p where p.module = ? and (p.active = true or p.activeStudent = true or p.activeVal = true or p.activeRequests = true) order by "
                            + (LocaleUtils.isL1(request) ? "p.name" : "p.l2Name")
                            + " desc, p.id desc",
                    module);
        }

        Integer positionId = position == null ? null : position.getId();
        Integer activityId = activity == null ? null : activity.getId();

        for (CCPeriod period : periods)
        {
            statMaster.put(period, CCHelper.getPeriodStatsMap(keysToPopulate,
                    period, activityInClause, positionId));
        }

        mv.addObject("positionId", positionId);
        mv.addObject("activityId", activityId);
        mv.addObject("periodStats", statMaster);
    }

    private void initializePermissions()
    {
        initializePermission(PersonGroupHelper.CC_ADMINISTRATOR,
                "Allows the user to be added as a Co-curricular Administrator");
        initializePermission(PersonGroupHelper.CC_STAFF,
                "Allows the user to act as a Co-curricular Staff person");
        initializePermission(PersonGroupHelper.CC_VALIDATOR,
                "Allows the user to act as a Co-curricular Validator");
        initializePermission(PersonGroupHelper.CC_CONFIGURATION,
                "Allows the user to access the configuration of a Co-curricular module");
        initializePermission(PersonGroupHelper.CC_CAN_ADD_ACTIVITIES,
                "Allows a non-admin user to make additional Co-curricular Activities");
    }

    private void initializePermission(String permission, String description)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        PersonGroup pg = PersonGroupHelper.getForGroupName(permission);
        if (pg == null)
        {
            // logger.info("Adding PermissionGroup: " + permission);

            PersonGroupHelper.create(permission, "Co-Curricular Record", false,
                    false, description, null);
        }
        else
        {
            if (null == pg.getCategory())
            {
                pg.setCategory("Co-Curricular Record");
                ht.update(pg);
            }

            if (null == pg.getDescription())
            {
                pg.setDescription(description);
                ht.update(pg);
            }
        }
    }

    private CCGridInterface getCcGridInterface()
    {
        if (ccGridInterface == null)
        {
            ccGridInterface = new CCGridInterface(this);
        }
        return ccGridInterface;
    }

    private ModelAndView getCCExceptionAjaxResponse(CCException cce)
    {
        JSONObject j = new JSONObject();
        try
        {
            j.put(SUCCESS_KEY, Boolean.FALSE);
            j.put("error_msg", cce.getMessage());
        }
        catch (JSONException e)
        {
            // e.printStackTrace();
        }
        return jsonObjectResponse(j);
    }

    /**
     * a "guard" method
     *
     * @throws CCException
     */
    private void guardAgainstMissingValidatorGroup() throws CCException
    {
        PersonGroup validatorGroup = PersonGroupHelper
                .getForGroupName(PersonGroupHelper.CC_VALIDATOR);
        if (validatorGroup == null)
        {
            throw new CCException(CCException.TYPE.MISSING_VALIDATOR_GROUP);
        }
    }

    /**
     * a "guard" method
     *
     * @throws CCException
     */
    private void guardUserCanAddActivtyOrPosition(CCActivity activity)
            throws CCException
    {
        // taken out on purpose
    }

    /**
     * Populates the ModelAndView with data that's suitable for rendering a page
     * that allows a user to VIEW a position's "selected learning outcomes".
     *
     * @param locale
     *            TODO
     */
    private void populateCCOutcomes_viewMode(ModelAndView mv, CCModule module,
            CCPosition pos, Locale locale)
    {
        if (module.isTrackLearningOutcomes())
        {
            List<CCLearningOutcome> activeLearningOutcomes = CCHelper
                    .getActiveLearningOutcomes(module, locale);

            if (module.isTrackAchievements())
            {
                List<Integer> aArray = CCHelper
                        .getPositionAchievementAdminAchievementIds(pos);

                if (module.isGroupAchievements())
                {
                    Map<CCLearningOutcome, List<CCAchievement>> achievementMap = new LinkedHashMap<>();

                    for (CCLearningOutcome outcome : activeLearningOutcomes)
                    {
                        List<CCAchievement> selectedAchievements = new ArrayList<>();

                        List<CCAchievement> as = CCHelper
                                .getAchievementsByLearningOutcome(outcome, locale);

                        aArray = CCHelper
                                .getPositionAchievementAdminAchievementIds(pos);

                        for (CCAchievement a : as)
                        {
                            if (a.isActive() && aArray.contains(a.getId()))
                            {
                                selectedAchievements.add(a);
                            }
                        }

                        if (!selectedAchievements.isEmpty())
                        {
                            achievementMap.put(outcome, selectedAchievements);
                        }
                    }
                    mv.addObject("achievementMap", achievementMap);
                }
                else
                {
                    List<CCAchievement> achievements = CCHelper
                            .getActiveAchievements(module, locale);

                    List<Integer> oArray = CCHelper.getLearningOutcomeIds(pos);

                    List<CCAchievement> selectedAchievements = new ArrayList<>();

                    for (CCAchievement a : achievements)
                    {
                        if (aArray.contains(a.getId()) && a.isActive()
                                && oArray.contains(a.getLearningOutcome().getId())
                                && a.getLearningOutcome().isActive())
                        {
                            selectedAchievements.add(a);
                        }
                    }

                    mv.addObject("achievements", selectedAchievements);
                }
            }
            else
            {
                List<Integer> oArray = CCHelper.getLearningOutcomeIds(pos);
                List<CCLearningOutcome> selectedLearningOutcomes = new ArrayList<>();

                for (CCLearningOutcome lo : activeLearningOutcomes)
                {
                    if (oArray.contains(lo.getId()) && lo.isActive())
                    {
                        selectedLearningOutcomes.add(lo);
                    }
                }

                mv.addObject("learningOutcomes", selectedLearningOutcomes);
            }
        }
    }

    private void deletedCCRecordPositionOutcomes(CCModule module,
            CCRecordPosition rp)
    {
        if (module.isTrackLearningOutcomes())
        {
            if (module.isTrackAchievements())
            {
                PortalUtils.getJt().update(
                        "delete from cc_pas where recordPosition = ?",
                        new Object[] { rp.getId() });
            }
            PortalUtils.getJt().update(
                    "delete from cc_pls where recordPosition = ?",
                    new Object[] { rp.getId() });

        }

    }

    private CCLearningOutcome getCCLearningOutcome(HttpServletRequest request)
    {
        CCLearningOutcome learningOutcome = new CCLearningOutcome();

        if (request.getParameter("learningOutcomeId") != null
                && !StringUtils.isEmpty(request.getParameter("learningOutcomeId")))
        {
            learningOutcome = (CCLearningOutcome) getHt()
                    .load(CCLearningOutcome.class, Integer.valueOf(Integer
                            .parseInt(request.getParameter("learningOutcomeId"))));
        }
        else if (request.getAttribute("learningOutcomeId") != null)
        {
            learningOutcome = (CCLearningOutcome) getHt().load(
                    CCLearningOutcome.class,
                    (Integer) (request.getAttribute("learningOutcomeId")));
        }

        return learningOutcome;
    }

    @Override
    @RequestMapping("gridCheckToggleAll")
    public ModelAndView gridCheckToggleAll(HttpServletRequest request,
            HttpServletResponse response)
    {
        UserDetailsImpl validator = NHelper.getUser(request);
        ModelAndView mv;
        if (validator != null)
        {
            String[] hql = CCHelper.buildHqlForValidatorsData(validator,
                    CCRecordPosition.STATUS_PENDING, false, true,
                    getModule(request), request);

            List ids = GridHelper.getQueryPrimaryIds(request, hql[1] + hql[2],
                    "p.id");

            mv = GridCheckHelper.gridCheckToggleAll(request, ids);
        }
        else
        {
            mv = super.gridCheckToggleAll(request, response);
        }
        return mv;
    }

    private CCAchievement getCCAchievement(HttpServletRequest request)
    {
        CCAchievement achievement = new CCAchievement();

        if (request.getParameter("achievementId") != null
                && !StringUtils.isEmpty(request.getParameter("achievementId")))
        {
            achievement = (CCAchievement) getHt().load(CCAchievement.class,
                    Integer.valueOf(Integer
                            .parseInt(request.getParameter("achievementId"))));
        }
        else if (request.getAttribute("achievementId") != null)
        {
            achievement = (CCAchievement) getHt().load(CCAchievement.class,
                    (Integer) (request.getAttribute("achievementId")));
        }

        return achievement;
    }

    private CCRecord populateRecord(ModelAndView mv, CCModule module,
            UserDetailsImpl user, boolean populateLearningOutcomes,
            HttpServletRequest request)
    {
        CCRecord r = null;
        UserDetailsImpl whoIsThis = PortalUtils.getUserLoggedIn(request);

        List ccModules = new ArrayList();
        ccModules.add(module);
        CCHelper.populateCCPublicSiteElement(mv, ccModules, request);

        if (user != null)
        {
            r = CCHelper.getOrAddCCRecord(module, user);

            mv.addObject("r", r);
            request.setAttribute("recordId", r.getId());

            String rpHql = "from CCRecordPosition rp where rp.record=? order by rp.position.activity.category, rp.position.startDate, rp.position.endDate";
            List<CCRecordPosition> rps = getHt().find(rpHql, r);

            if (populateLearningOutcomes)
            {
                // Remove order by clause from HQL for use as a subquery
                rpHql = rpHql.substring(0, rpHql.indexOf(" order by"));
                mv.addObject("learningOutcomes",
                        CCHelper.populateCCRLearningOutcomes(r,
                                "select rp.id " + rpHql, module));
                if (module.isTrackAchievements())
                {
                    List<Object[]> statResults = getHt().find(
                            "select pa.recordPosition.id, count(pa.id) from CCPositionAchievementStudent pa where pa.recordPosition.record=? group by pa.recordPosition.id",
                            r);

                    Map<Integer, Integer> achCount = new HashMap<>();

                    for (Object[] res : statResults)
                    {
                        achCount.put((Integer) res[0], (Integer) res[1]);
                    }
                    mv.addObject("achCounts", achCount);
                }
            }

            Select csSelect = new Select();
            csSelect.add("crp.id");
            csSelect.add("ca.competency");
            List<Map<String, Object>> cs = CCHelper
                    .getAchievedRecordPositionCompetencies(csSelect, r.getId());
            Map<Integer, List<Integer>> recordCompetencyIds = new HashMap<>();

            for (CCRecordPosition rp : rps)
            {
                List<Integer> ids = cs.stream()
                        .filter(o -> ((Integer) o.get("id")).intValue() == rp
                                .getId().intValue())
                        .map(o -> (Integer) o.get("competency"))
                        .collect(Collectors.toList());
                recordCompetencyIds.put(rp.getId(), ids);

                CCRecordPositionHelper.populateProgramTrackingEnrollment(rp,
                        whoIsThis);
            }

            // POSITION DESCRIPTION QUESTION
            // - get the df mapping for the position description
            DFModel dfModel = CCPositionQuestionModelHelper
                    .getPositionQuestionModel(module).getDFModel();
            mv.addObject("positionDescriptionOrbisValue", DFHelper
                    .getDFSiteMappingField(dfModel, PortalConfig.DFQSM_DESCRIPTION)
                    .orElse(null));

            mv.addObject("recordCompetencyIds", recordCompetencyIds);
            Select select = new Select();
            select.add("distinct c.id");
            select.add("c.name");
            select.add("c.l2Name");
            select.add("c.description");
            select.add("c.l2Description");

            mv.addObject("distinctRpCompetencies", CCHelper
                    .getAchievedRecordPositionCompetencies(select, r.getId()));
            mv.addObject("rps", rps);

            AcrmReflectionConfig reflectionConfig = module.getReflectionConfigurableSupport()
                    .getReflectionConfig(module);
            if (reflectionConfig != null)
            {
                Map<Integer, AcrmReflectionRecord> recordAssign = new LinkedHashMap<>();
                for (CCRecordPosition rp : rps)
                {
                    AcrmReflectionRecordAssign assign = rp
                            .getReflectionRecordableSupport()
                            .getOrCreateReflectionRecordAssign(rp);
                    recordAssign.put(rp.getId(), assign.getAcrmReflectionRecord());
                }
                mv.addObject("reflectionRecordAssign", recordAssign);

                AcrmReflectionRecordHelper.configReflectionCount(reflectionConfig,
                        mv);
                mv.addObject("config", reflectionConfig);
            }

            boolean anyReflectionsEnabled = reflectionConfig != null
                    && reflectionConfig.isAnyReflectionEnabled();
            mv.addObject("reflectionsEnabled", anyReflectionsEnabled);
            if (anyReflectionsEnabled)
            {
                String rpInClause = DBUtils.buildInClause(rps.stream()
                        .map(ContentItem::getId).collect(Collectors.toList()));
                mv.addObject("rpsWithReflection",
                        PortalUtils.getHt()
                                .find(String.format(
                                        """
                                                select ref.ccRecordPosition.id from AcrmReflectionRecordCCRecordPosition ref where ref.ccRecordPosition.id in %s and
                                                exists(select arr.acrmReflection.id from AcrmReflectionAcrmReflectionRecord arr where ref.acrmReflectionRecord.id=arr.acrmReflectionRecord.id)
                                                """,
                                        rpInClause)));
            }

            mv.addObject("shortlistCount", getHt().find(
                    "select count(*) from CCRecordPositionShortlist s where s.record=?",
                    r).get(0));

            // Stuff for the Fusion chart...
            Integer[] totals = getValidationTotals(rps, true,
                    module.isEnableCompetencySelection());
            mv.addObject("rpsApproved", totals[0]);
            mv.addObject("rpsDeclined", totals[1]);
            mv.addObject("rpsPending", totals[2]);
            if (module.isEnableCompetencySelection())
            {
                mv.addObject("rpsCompetencies", totals[4]);
            }

            // cummulative leadership points. use only when admin signed in
            // and enableLeadershipPoints is true.
            if (module.isEnableLeadershipPoints() && whoIsThis.getAssignedTypes()
                    .containsKey(PersonGroupHelper.CC_ADMINISTRATOR))
            {
                mv.addObject("leadershipPoints", totals[3]);
            }

            if (AcrmHelper.canViewAccount(PortalUtils.getUserLoggedIn(request),
                    user.getPrimaryGroup()))
            {
                NHelper.populateAcrmCareerSiteElement(mv, request);
            }
            if (anyReflectionsEnabled)
            {
                mv.addObject("reflectionCount", PortalUtils.getHt().find(
                        "select count(rp.id) from CCRecordPosition rp where rp.record.id=? and exists(select ref.id from ECReflection ref where ref.record.contentItemClass='com.orbis.web.content.cc.CCRecordPosition' and ref.record.contentItemId=rp.id)",
                        r.getId()).stream().findFirst().orElse(null));
                mv.addObject("noReflectionCount", PortalUtils.getHt().find(
                        "select count(rp.id) from CCRecordPosition rp where rp.record.id=? and not exists(select ref.id from ECReflection ref where ref.record.contentItemClass='com.orbis.web.content.cc.CCRecordPosition' and ref.record.contentItemId=rp.id)",
                        r.getId()).stream().findFirst().orElse(null));
            }

            if (module.isEnableExternalValidations())
            {
                int dfModelId = PortalUtils.getHt().findInt(
                        "select epqm.DFModel.id from CCExternalPositionQuestionModel epqm where epqm.module=?",
                        module);
                Map externalPositionFields = CollectionUtils.mapifyOneToOne(
                        "select dfqsm.siteMapping.orbisKey, dfqsm.dfQuestion.answerField1 from DFQuestionSiteMapping dfqsm "
                                + "join dfqsm.siteMapping join dfqsm.dfQuestion join dfqsm.dfQuestion.category "
                                + "where dfqsm.dfQuestion.category.model.id=? and dfqsm.siteMapping.orbisKey in "
                                + DBUtils.buildInClauseWithQuotes(
                                        Arrays.asList(PortalConfig.DFQSM_TITLE,
                                                PortalConfig.DFQSM_DESCRIPTION,
                                                PortalConfig.DFQSM_ORG_NAME)),
                        dfModelId);
                mv.addObject("externalPositionFields", externalPositionFields);
            }

            mv.addObject("ccTemplateType", CCLetterTemplate.class);
            boolean asposeAvailable = false;
            JSONObject additionalParams = new JSONObject();
            try
            {
                additionalParams.put("moduleId", module.getId());
                asposeAvailable = DocTemplateHelper.isTemplateValid(
                        CCLetterTemplate.class, CCLetterTemplate.TYPE_CC_RECORD,
                        additionalParams, PortalUtils.getLocale(request));
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
            mv.addObject("asposeAvailable", asposeAvailable);
            mv.addObject("competenciesDisabled",
                    module.getCompetencyUsecase() == Competenciable.COMP_DISABLED);
        }
        return r;
    }

    private void populatePositionValidators(ModelAndView mv,
            List<Integer> positionIds)
    {
        List<Object[]> individualPositionValidators = PortalUtils.getHt().find(
                "select position.id, CONCAT(validator.preferredFirstName, ' ', validator.lastName) from CCPositionValidator pv join pv.position as position join pv.validator as validator where position.id in "
                        + DBUtils.buildInClause(positionIds)
                        + " order by validator.lastName, validator.preferredFirstName");
        Map combinedPositionValidators = new HashMap();
        for (Object[] positionValidator : individualPositionValidators)
        {
            combinedPositionValidators.merge(positionValidator[0],
                    positionValidator[1], (a, b) -> a += ", " + b);
        }

        mv.addObject("positionValidators", combinedPositionValidators);
    }

    public static List getRecordPositionLearningOutcomeStats(CCRecordPosition rp,
            CCModule module)
    {
        List locStats = null;
        if (module.isTrackAchievements())
        {
            locStats = PortalUtils.getHt().find(
                    "select pas.achievement.learningOutcome.name, count(pas.achievement.learningOutcome.name) from CCPositionAchievementStudent pas where pas.recordPosition=? group by pas.achievement.learningOutcome.name ",
                    rp);
        }
        else
        {
            locStats = PortalUtils.getHt().find(
                    "select pls.learningOutcome.name, count(pls.learningOutcome.name) from CCPositionLearningOutcomeStudent pls where pls.recordPosition=? group by pls.learningOutcome.name ",
                    rp);
        }

        List locs = new ArrayList();
        for (Iterator iterator = locStats.iterator(); iterator.hasNext();)
        {
            Object[] lo = (Object[]) iterator.next();
            CCLearningOutcomeCount loc = new CCLearningOutcomeCount((String) lo[0],
                    (Integer) lo[1]);
            locs.add(loc);
        }
        Collections.sort(locs);
        return locs;
    }

    private Integer[] getValidationTotals(List CCRecordPositions,
            boolean includeInactivePeriods, boolean competencySelectionEnabled)
    {
        int approved = 0;
        int declined = 0;
        int pending = 0;
        int points = 0;
        int awaitingCompetencies = 0;

        for (Iterator i = CCRecordPositions.iterator(); i.hasNext();)
        {
            CCRecordPosition ccrp = (CCRecordPosition) i.next();
            if (CCRecordPosition.STATUS_PENDING.equalsIgnoreCase(ccrp.getStatus()))
            {
                pending++;
            }

            if (CCRecordPosition.STATUS_DECLINED.equalsIgnoreCase(ccrp.getStatus()))
            {
                if (ccrp.getPosition().getActivity().getPeriod().isActive()
                        || includeInactivePeriods)
                {
                    declined++;
                }
            }

            if (CCRecordPosition.STATUS_APPROVED.equalsIgnoreCase(ccrp.getStatus()))
            {
                if (ccrp.getPosition().getActivity().getPeriod().isActive()
                        || includeInactivePeriods)
                {
                    approved++;
                    points += ccrp.getPosition().getPoints();
                }
            }

            if (competencySelectionEnabled
                    && StringUtils.isEmpty(ccrp.getPersonalCompetencies()))
            {
                if (ccrp.getPosition().getActivity().getPeriod().isActive()
                        || includeInactivePeriods)
                {
                    awaitingCompetencies++;
                }
            }
        }

        Integer totals[] = { Integer.valueOf(approved), Integer.valueOf(declined),
                Integer.valueOf(pending), Integer.valueOf(points),
                Integer.valueOf(awaitingCompetencies) };
        return totals;
    }

    @RequestMapping("ajaxLoadValidatorValidations")
    public ModelAndView ajaxLoadValidatorValidations(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_validatorValidationsAjax");

        UserDetailsImpl validator = NHelper.getUser(request);

        String statusFilter = request.getParameter("statusFilter");

        String selectClause = "select p.id, p.position.activity.period.name, p.position.activity.activity, "
                + "p.position.title, p.position.id, p.record.owner.preferredFirstName, p.record.owner.lastName, "
                + "p.record.owner.username, p.dateAdded, p.confirmationCode, "
                + (!CCRecordPosition.STATUS_PENDING.equals(statusFilter)
                        ? "p.validatedBy.preferredFirstName, p.validatedBy.lastName, p.dateValidated, "
                        : "")
                + "p.position.activity.id, p.position.externalPosition, p.externalValidationDate, p.externalValidatorFullName, p.hours, p.position.hoursToTrack, p.position.enableTimeTracking ";

        String[] hql = CCHelper.buildHqlForValidatorsData(validator, statusFilter,
                false, true, getModule(request), request);

        GridHelper.runQuery(request, mv, hql[1] + hql[2], selectClause, "p.id");

        mv.addObject("statusFilter", statusFilter);
        return mv;
    }

    private boolean isNotMyRecordMode(HttpServletRequest request)
    {
        return (StringUtils.isEmpty(request.getParameter("myRecordMode"))
                || (!StringUtils.isEmpty(request.getParameter("myRecordMode"))
                        && request.getParameter("myRecordMode").equals("false")));
    }

    private void manageValidatorPosition(HttpServletRequest request, String doWhat)
    {
        UserDetailsImpl validator = UserDetailsHelper
                .getUserByUserId(Integer.valueOf(request.getParameter("userId")));

        if (!StringUtils.isEmpty(request.getParameter("positionId")))
        {
            CCPosition position = CCHelper.getCCPosition(request);
            if (doWhat.equalsIgnoreCase("create"))
            {
                handleValidatorPosition(validator, position);
            }
            else if (doWhat.equalsIgnoreCase("delete"))
            {
                handleValidatorPositionDelete(validator, position);
            }
        }
        else if (!StringUtils.isEmpty(request.getParameter("activityId")))
        {
            CCActivity activity = (CCActivity) getHt().load(CCActivity.class,
                    Integer.valueOf(request.getParameter("activityId")));
            List<CCPosition> positions = getHt()
                    .find("from CCPosition p where p.activity = ?", activity);

            for (CCPosition position : positions)
            {
                if (doWhat.equalsIgnoreCase("create"))
                {
                    handleValidatorPosition(validator, position);
                }
                else if (doWhat.equalsIgnoreCase("delete"))
                {
                    handleValidatorPositionDelete(validator, position);
                }
            }
        }
    }

    private void handleValidatorPosition(UserDetailsImpl validator,
            CCPosition position)
    {
        if (((Integer) getHt().find(
                "select count(pv.id) from CCPositionValidator pv where pv.position = ? and validator = ?",
                new Object[] { position, validator }).get(0)).intValue() == 0)
        {
            CCPositionValidator ccv = new CCPositionValidator();
            ccv.setPosition(position);
            ccv.setValidator(validator);
            getHt().save(ccv);
            CCHelper.applyValidatorGroup(validator);
        }
    }

    private void handleValidatorPositionDelete(UserDetailsImpl validator,
            CCPosition position)
    {
        List<CCPositionValidator> results = getHt().find(
                "select pv from CCPositionValidator pv where pv.position = ? and pv.validator = ?",
                new Object[] { position, validator });
        if (!results.isEmpty())
        {
            getHt().delete(results.get(0));
            if (((Integer) getHt().find(
                    "select count(pv.id) from CCPositionValidator pv where pv.validator = ?",
                    validator).get(0)).intValue() == 0)
            {
                UserDetailsHelper.removeUserGroup(validator,
                        PersonGroupHelper.CC_VALIDATOR);
                // getHt().update(validator);
            }
        }
    }

    private List getPositionsForMassAssign(HttpServletRequest request,
            String selectClause)
    {
        List positions = new ArrayList();
        if (request.getParameterValues("positionIds") == null
                || request.getParameterValues("positionIds").length == 0)
        {
            String filterHql = " p.activity.period.id = "
                    + request.getParameter("period");

            if (!StringUtils.isEmpty(request.getParameter("category")))
            {
                filterHql += " and p.activity.category = '"
                        + request.getParameter("category") + "'";
            }
            if (!StringUtils.isEmpty(request.getParameter("organization")))
            {
                filterHql += " and p.activity.organization = '"
                        + request.getParameter("organization") + "'";
            }
            if (!StringUtils.isEmpty(request.getParameter("department")))
            {
                filterHql += " and p.activity.department = '"
                        + request.getParameter("department") + "'";
            }
            if (!StringUtils.isEmpty(request.getParameter("activity")))
            {
                filterHql += " and p.activity.id = "
                        + request.getParameter("activity");
            }

            String hql = (selectClause != null ? selectClause : "")
                    + " from CCPosition p where " + filterHql;
            positions = getHt().find(hql);
        }
        else
        {
            String hql = (selectClause != null ? selectClause : "")
                    + " from CCPosition p where p.id in " + DBUtils.buildInClause(
                            request.getParameterValues("positionIds"));
            positions = getHt().find(hql);
        }

        return positions;
    }

    @Override
    protected ModelAndView handleRequestInternal(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = super.handleRequestInternal(request, response);

        CCModule module = getModule(request);

        // populate MV with "CCR User Roles"....
        Map roles = CCHelper.getUserRoles(PortalUtils.getUserLoggedIn(request),
                module);

        if (mv == null || roles == null)
        {
        }

        if (mv != null)
        {
            mv.addObject("roles", roles);
            mv.addObject("userHasMultipleRoles",
                    CCHelper.getUserRoleListFromMap(roles).size() > 1);

            String currentRole = CCHelper.getCurrentUserRole(module,
                    PortalUtils.getUserLoggedIn(request));
            mv.addObject("currentRole", currentRole);
            mv.addObject("userTypeKey", currentRole);

            // populate MV with "CCModule"....
            mv.addObject("module", module);

            if (CCHelper.isSpiralRobotEnabled(module,
                    PortalUtils.getUserLoggedIn(request)))
            {
                PortalUtils.forceSpiralRobot(request);
            }
        }

        return mv;
    }

    @RequestMapping("displayManageAdministrators")
    public ModelAndView displayManageAdministrators(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_configureAdministrators");
        CCModule module = getModule(request);

        List<Object[]> staffOwners = getHt()
                .find("select mso.user.id, mso.user.preferredFirstName,"
                        + " mso.user.lastName, mso.user.username from "
                        + "CCAdmin mso where mso.module = ? and mso.user.deleted = 0 order by mso.user.lastName,"
                        + " mso.user.firstName", module);

        List<Integer> exemptionIds = new ArrayList<>();

        for (Object[] u : staffOwners)
        {
            exemptionIds.add((Integer) u[0]);
        }

        mv.addObject("staffOwners", staffOwners);
        mv.addObject("nonStaffOwners", getHt().find(
                "select u.id, u.preferredFirstName, u.lastName, u.username from UserDetailsImpl u join u.groups as g where g.name=?"
                        + (StringUtils.isEmpty(DBUtils.buildInClause(exemptionIds))
                                ? ""
                                : (" and u.id not in "
                                        + DBUtils.buildInClause(exemptionIds)))
                        + " and u.deleted=false order by u.lastName, u.preferredFirstName",
                PersonGroupHelper.CC_ADMINISTRATOR));

        NHelper.populateAcrmCareerSiteElement(mv, request);

        return mv;
    }

    @RequestMapping("addAdministrator")
    public ModelAndView addAdministrator(HttpServletRequest request,
            HttpServletResponse response)
    {
        JSONArray members;
        try
        {
            members = new JSONArray(request.getParameter("membersJson"));

            CCModule module = getModule(request);

            PortalUtils.getJt().update("delete from cc_admin where module = ?",
                    new Object[] { module.getId() });

            for (int i = 0; i < members.length(); i++)
            {
                UserDetailsImpl user = (UserDetailsImpl) getHt().load(
                        UserDetailsImpl.class, Integer.valueOf(members.getInt(i)));

                if (user != null
                        && user.getAssignedTypes()
                                .containsKey(PersonGroupHelper.CC_ADMINISTRATOR)
                        && user.isEnabled() && !user.isDeleted()
                        && ((Integer) PortalUtils.getHt().find(
                                "select count(a.id) from CCAdmin a where a.module=? and a.user=?",
                                new Object[] { module, user }).get(0))
                                .intValue() == 0)
                {
                    CCAdmin admin = new CCAdmin();
                    admin.setUser(user);
                    admin.setModule(getModule(request));
                    admin.setDateCreated(new Date());
                    getHt().save(admin);
                }
            }
        }
        catch (JSONException e)
        {
            e.printStackTrace();
        }
        return displayManageAdministrators(request, response);
    }

    @RequestMapping("deleteAdministrator")
    public ModelAndView deleteAdministrator(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCHelper.delete(CCHelper.getCCAdmin(request));
        return displayManageAdministrators(request, response);
    }

    @RequestMapping("updateModuleAdmins")
    public ModelAndView updateModuleAdmins(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        CCModule module = getModule(request);

        JSONArray added = new JSONArray(request.getParameter("added"));
        JSONArray removed = new JSONArray(request.getParameter("removed"));

        Boolean successfulUpdate = true;

        try
        {
            for (int i = 0; i < added.length(); i++)
            {
                Integer userId = added.getInt(i);
                CCAdmin existingAdmin = CCHelper.checkAdminInModule(module, userId);
                if (existingAdmin == null)
                {
                    CCAdmin cca = new CCAdmin();
                    cca.setModule(module);

                    UserDetailsImpl user = (UserDetailsImpl) getHt()
                            .load(UserDetailsImpl.class, userId);

                    cca.setUser(user);
                    getHt().save(cca);
                }
            }

            for (int i = 0; i < removed.length(); i++)
            {
                UserDetailsImpl admin = (UserDetailsImpl) getHt()
                        .load(UserDetailsImpl.class, removed.getInt(i));
                CCHelper.removeAdminFromModule(admin, module);
            }
        }
        catch (Exception e)
        {
            successfulUpdate = false;
        }

        ModelAndView mv = displayManageAdministrators(request, response);
        mv.addObject("successfulUpdate", successfulUpdate);

        return mv;
    }

    /**
     * Look up users with the "Co-Curricular Administrator" permission...
     */
    @RequestMapping("ajaxLookupAdministrators")
    public ModelAndView ajaxLookupAdministrators(HttpServletRequest request,
            HttpServletResponse response)
    {
        List<Object[]> userData = CCHelper.lookupUsers(request.getParameter("term"),
                PersonGroupHelper.CC_ADMINISTRATOR);
        return NHelper.lookupsAJAXResponse(userData, 0, "[2] [3] ([1])");
    }

    @RequestMapping("saveActivityDirectors")
    public ModelAndView saveActivityDirectors(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCActivity activity = CCHelper.getCCActivity(request);
        String directorsIds[] = request.getParameterValues("directorsId");
        CCHelper.saveActivityDirectors(activity, directorsIds);

        String returnTo = request.getParameter("returnTo");
        ModelAndView mv = null;

        if ("displayActivityOverview".equals(returnTo))
        {
            mv = displayActivityOverview(request, response);
        }
        else if ("displayActivityUnitAdmins".equals(returnTo))
        {
            mv = displayActivityUnitAdmins(request, response);
        }

        if (mv != null)
        {
            mv.addObject("successMessage",
                    new I18nLabel("i18n.CCController.UpdateSucc5888678286035961")
                            .getTranslation(PortalUtils.getLocale(request)));
        }

        return mv;
    }

    @RequestMapping("displayAddPosition")
    public ModelAndView displayAddPosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_addPosition");
        Locale locale = PortalUtils.getLocale(request);
        CCActivity activity = CCHelper.getCCActivity(request);
        CCModule module = getModule(request);

        mv.addObject("activity", activity);
        mv.addObject("module", module);
        mv.addObject("backToTimePeriod", request.getParameter("backToTimePeriod"));

        CCHelper.populateForAddingPosition(mv, module, locale,
                PortalUtils.getUserLoggedIn(request));

        return mv;
    }

    @RequestMapping("addPosition")
    public ModelAndView addPosition(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        boolean happy = true;
        CCActivity activity = CCHelper.getCCActivity(request);

        CCPosition position = null;
        try
        {
            position = addNewPosition(activity, request);
        }
        catch (CCException cce)
        {
            return getCCExceptionAjaxResponse(cce);
        }
        catch (Exception e)
        {
            happy = false;
            e.printStackTrace();
        }

        JSONObject ret = new JSONObject();
        ret.put(SUCCESS_KEY, happy);
        ret.put("positionId", position == null ? null : position.getId());
        try
        {
            ret.put("periodId", position == null ? null
                    : position.getActivity().getPeriod().getId());
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return jsonObjectResponse(ret);
    }

    private CCPosition addNewPosition(CCActivity activity,
            HttpServletRequest request) throws CCException
    {
        // GUARD AGAINST THE NON-EXISTANCE OF THE 'VALIDATOR' GROUP
        // PERMISSION...
        guardAgainstMissingValidatorGroup();

        // GUARD AGAINST INSUFFICIENT PERMISSIONS...
        guardUserCanAddActivtyOrPosition(activity);

        String posLabel = request.getParameter("position");
        if (StringUtils.isEmpty(posLabel))
        {
            posLabel = request.getParameter("_title");
        }
        CCModule module = getModule(request);

        // GUARD AGAINST DUPLICATE POSITIONS...
        CCHelper.guardAgainstDuplicatePosition(activity, posLabel);

        // ADD SELECTED DIRECTORS TO ACTIVITY
        if (module.isEnableActivityDirectors()
                && StringUtils.isInteger(request.getParameter("directorsId")))
        {
            CCHelper.addDirectors(activity, CCHelper.getCCDirectorsTeam(request));
        }

        UserDetailsImpl creator = PortalUtils.getUserLoggedIn(request);
        Date now = new Date();

        CCPosition positionCreated = new CCPosition();
        positionCreated.setCreatedDate(now);
        positionCreated.setCreatedBy(creator);
        positionCreated.setUpdatedDate(now);
        positionCreated.setUpdatedBy(creator);
        positionCreated.setActivity(activity);
        positionCreated.setRequestModule(module);
        positionCreated.setRequestDate(now);

        String currentUserRole = CCHelper.getCurrentUserRole(module,
                PortalUtils.getUserLoggedIn(request));
        if (CCHelper.CC_ROLE_ADMIN.equals(currentUserRole))
        {
            positionCreated.setApprovedBy(creator);
            positionCreated.setPendingRequest(false);
            positionCreated.setRequestAccepted(now);
        }
        else
        {
            positionCreated.setPendingRequest(true);
        }
        positionCreated.setEnabled(false);

        DFHelper.bindAnswers(
                CCPositionQuestionModelHelper.getPositionQuestionModel(module),
                positionCreated, request, creator, true);

        getHt().saveOrUpdate(positionCreated);

        try
        {
            CCHelper.deleteCCOutcomes(module, positionCreated);

            CCHelper.saveCCOutcomes(request, module, positionCreated);

            // CREATE THE VALIDATORS FOR THIS POSITION...
            JSONArray jValidators = new JSONArray(
                    request.getParameter("validators"));
            for (int i = 0; i < jValidators.length(); i++)
            {
                List<UserDetailsImpl> users = getHt().find(
                        "from UserDetailsImpl u where u.id=?",
                        jValidators.getInt(i));
                if (!users.isEmpty())
                {
                    CCPositionValidator ccv = new CCPositionValidator();
                    ccv.setPosition(positionCreated);
                    ccv.setValidator(users.get(0));
                    getHt().save(ccv);
                    CCHelper.applyValidatorGroup(users.get(0));
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return positionCreated;
    }

    @RequestMapping("displayManageActivityQuestions")
    public ModelAndView displayManageActivityQuestions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_manageActivityQuestions");
        mv.addObject("model", CCActivityQuestionModelHelper
                .getActivityQuestionModel(getModule(request)));
        return mv;
    }

    @RequestMapping("displayRecordPrograms")
    public ModelAndView displayRecordPrograms(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordPrograms");
        CCRecord record = CCHelper.getCCRecord(request);
        mv.addObject("r", record);

        if (record != null
                && AcrmHelper.canViewAccount(PortalUtils.getUserLoggedIn(request),
                        record.getOwner().getPrimaryGroup()))
        {
            NHelper.populateAcrmCareerSiteElement(mv, request);
        }

        return mv;
    }

    @RequestMapping("savePositionProgram")
    public ModelAndView savePositionProgram(HttpServletRequest request,
            HttpServletResponse response)
    {
        try
        {
            PTProgram program = PTHelper.getProgram(request);
            CCPosition position = CCHelper.getCCPosition(request);
            if (!position.isExternalPosition())
            {
                position.setProgram(program);
                getHt().update(position);
                List<PTProgramEnrollment> enrolees = getHt().find(
                        "from PTProgramEnrollment pe where pe.program = ?",
                        program);
                for (PTProgramEnrollment enrolee : enrolees)
                {
                    CCHelper.addCCRecordPosition(program, enrolee.getStudent(),
                            request);
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        ModelAndView mv = displayPositionProgramTracking(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.CCController.ProgramTra1232472351938057")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    @Override
    @RequestMapping("displayInteractions")
    public ModelAndView displayInteractions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        String typeString = null;

        if (!StringUtils.isEmpty(request.getParameter("type")))
        {
            typeString = request.getParameter("type");
        }
        else if (!StringUtils.isEmpty(request.getParameter("taskType")))
        {
            typeString = request.getParameter("taskType");
        }
        INTERACTION_TYPE type = null;
        if (typeString != null)
        {
            type = INTERACTION_TYPE.valueOf(typeString);
            switch (type)
            {
                case CCACTIVITY:
                    mv = displayActivityInteractions(request, response);
                    break;
                case CCPOSITION:
                    mv = displayPositionInteractions(request, response);
                    break;
                case CCRECORD:
                    mv = displayRecordInteractions(request, response);
                    break;
                default:
                    break;
            }
        }

        return mv;
    }

    @Override
    @RequestMapping("displayInteractionNotes")
    public ModelAndView displayInteractionNotes(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = displayInteractions(request, response);
        if (mv != null)
        {
            mv.addObject("_i_currentTab", "_i_notes");
            mv.addObject("interactionType", "note");
            InteractionHelper.populateInteractionNotesOptions(request, mv);
        }
        return mv;
    }

    @Override
    @RequestMapping("displayInteractionTasks")
    public ModelAndView displayInteractionTasks(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = displayInteractions(request, response);
        if (mv != null)
        {
            mv.addObject("_i_currentTab", "_i_tasks");
            mv.addObject("interactionType", "task");
            InteractionHelper.populateInteractionTasksOptions(request, mv);
        }
        return mv;
    }

    @Override
    @RequestMapping("displayMessages")
    public ModelAndView displayMessages(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = displayInteractions(request, response);
        if (mv != null)
        {
            mv.addObject("_i_currentTab", "_i_messages");
            mv.addObject("interactionType", "message");
            InteractionHelper.populateInteractionMessagesOptions(request, mv);
        }
        return mv;
    }

    @Override
    @RequestMapping("displayInteractionForms")
    public ModelAndView displayInteractionForms(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = displayInteractions(request, response);
        if (mv != null)
        {
            mv.addObject("_i_currentTab", "_i_forms");
            mv.addObject("interactionType", "form");
            InteractionHelper.populateInteractionFormsOptions(request, mv);
        }
        return mv;
    }

    private ModelAndView displayRecordInteractions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordInteractions");

        if (request.getParameter("studentId") == null
                && request.getAttribute("studentId") == null)
        {
            if (request.getParameter("ccRecordId") != null)
            {
                request.setAttribute("studentId", PortalUtils.getJt().queryForInt(
                        "SELECT owner FROM cc_record " + "WHERE id = ?",
                        new Object[] {
                                Integer.valueOf(request.getParameter("ccRecordId")) }));
            }
        }

        populateCCRecordPage(mv, request, response);
        InteractionHelper.populateInteractionSummaryOptions(request, mv);

        INTERACTION_TYPE type = !StringUtils.isEmpty(request.getParameter("option"))
                ? INTERACTION_TYPE.valueOf(request.getParameter("option"))
                : INTERACTION_TYPE.USER;

        mv.addObject("optionType", type);

        return mv;
    }

    private ModelAndView displayPositionInteractions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_positionInteractions");
        CCPosition position = CCHelper.getCCPosition(request);
        mv.addObject("position", position);

        CCHelper.populateQuickStats(position, mv);
        CCHelper.populateAccessFlags(mv, position, request);
        CCHelper.populatePositionCommon(mv, request, position);
        InteractionHelper.populateInteractionSummaryOptions(request, mv);
        return mv;
    }

    @Override
    @RequestMapping("displayInteractionNoteEdit")
    public ModelAndView displayInteractionNoteEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        if (!StringUtils.isEmpty(request.getParameter("type")))
        {
            INTERACTION_TYPE type = INTERACTION_TYPE
                    .valueOf(request.getParameter("type"));
            switch (type)
            {
                case CCACTIVITY:
                    mv = displayActivityInteractionNoteEdit(request, response);
                    break;
                case CCPOSITION:
                    mv = displayPositionInteractionNoteEdit(request, response);
                    break;
                case CCRECORD:
                    mv = displayRecordInteractionNoteEdit(request, response);
                    break;
                default:
                    break;
            }
        }

        return mv;
    }

    private ModelAndView displayRecordInteractionNoteEdit(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordInteractionNoteEdit");
        populateCCRecordPage(mv, request, response);

        InteractionHelper.populateNoteEdit(mv, request, INTERACTION_TYPE.CCRECORD);

        return mv;
    }

    private ModelAndView displayPositionInteractionNoteEdit(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_positionInteractionNoteEdit");
        CCPosition position = CCHelper.getCCPosition(request);
        mv.addObject("position", position);

        InteractionHelper.populateNoteEdit(mv, request,
                INTERACTION_TYPE.CCPOSITION);

        return mv;
    }

    @RequestMapping("displayActivityInteractionNoteEdit")
    public ModelAndView displayActivityInteractionNoteEdit(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityInteractionNoteEdit");
        CCActivity a = CCHelper.getCCActivity(request);
        CCHelper.populateQuickStats(a);
        mv.addObject("activity", a);

        InteractionHelper.populateNoteEdit(mv, request,
                INTERACTION_TYPE.CCACTIVITY);

        return mv;
    }

    @Override
    @RequestMapping("viewInteractionNote")
    public ModelAndView viewInteractionNote(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = displayHome(request, response);

        if (!StringUtils.isEmpty(request.getParameter("type")))
        {
            INTERACTION_TYPE noteType = INTERACTION_TYPE
                    .valueOf(request.getParameter("type"));
            switch (noteType)
            {
                case CCACTIVITY:
                    mv = viewCCActivityInteractionNote(request, response);
                    break;
                case CCPOSITION:
                    mv = viewCCPositionInteractionNote(request, response);
                    break;
                case CCRECORD:
                    mv = viewCCRecordInteractionNote(request, response);
                    break;
                default:
                    break;
            }
        }

        InteractionHelper.initializeNoteViewPage(mv, request);
        return mv;
    }

    private ModelAndView viewCCRecordInteractionNote(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordViewInteractionNote");
        populateCCRecordPage(mv, request, response);
        return mv;
    }

    private ModelAndView viewCCPositionInteractionNote(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_positionViewInteractionNote");
        CCPosition position = CCHelper.getCCPosition(request);
        mv.addObject("position", position);

        CCHelper.populateQuickStats(position, mv);

        return mv;
    }

    private ModelAndView viewCCActivityInteractionNote(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityViewInteractionNote");
        CCActivity a = CCHelper.getCCActivity(request);
        CCHelper.populateQuickStats(a);
        mv.addObject("activity", a);

        return mv;
    }

    @Override
    @RequestMapping("displayMessageEdit")
    public ModelAndView displayMessageEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordInteractionMessageEdit");
        populateCCRecordPage(mv, request, response);

        if (request.getParameter("studentId") != null)
        {
            mv.addObject("acrmUser", PortalUtils.getHt().load(UserDetailsImpl.class,
                    Integer.valueOf(request.getParameter("studentId"))));
            mv.addObject("ccRecordId", request.getAttribute("recordId"));
        }

        InteractionHelper.populateMessageEdit(mv, request);
        return mv;
    }

    @Override
    @RequestMapping("viewMessage")
    public ModelAndView viewMessage(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordViewInteractionMessage");

        request.setAttribute("studentId", request.getParameter("acrmUserId"));

        populateCCRecordPage(mv, request, response);

        InteractionHelper.initializeMessageViewPage(mv, request);
        return mv;
    }

    @Override
    @RequestMapping("displayInteractionTaskEdit")
    public ModelAndView displayInteractionTaskEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        String taskType = !StringUtils.isEmpty(request.getParameter("type"))
                ? request.getParameter("type")
                : request.getParameter("taskType");

        if (!StringUtils.isEmpty(taskType))
        {
            INTERACTION_TYPE type = INTERACTION_TYPE.valueOf(taskType);
            switch (type)
            {
                case CCACTIVITY:
                    mv = displayActivityInteractionTaskEdit(request, response);
                    break;
                case CCPOSITION:
                    mv = displayPositionInteractionTaskEdit(request, response);
                    break;
                case CCRECORD:
                    mv = displayRecordInteractionTaskEdit(request, response);
                    break;
                default:
                    break;
            }
        }

        return mv;
    }

    private ModelAndView displayRecordInteractionTaskEdit(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordInteractionTaskEdit");
        populateCCRecordPage(mv, request, response);

        InteractionHelper.populateTaskEdit(mv, request, INTERACTION_TYPE.CCRECORD);

        return mv;
    }

    private ModelAndView displayPositionInteractionTaskEdit(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_positionInteractionTaskEdit");
        CCPosition position = CCHelper.getCCPosition(request);
        mv.addObject("position", position);

        InteractionHelper.populateTaskEdit(mv, request,
                INTERACTION_TYPE.CCPOSITION);

        return mv;
    }

    private ModelAndView displayActivityInteractionTaskEdit(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityInteractionTaskEdit");
        CCActivity a = CCHelper.getCCActivity(request);
        CCHelper.populateQuickStats(a);
        mv.addObject("activity", a);

        InteractionHelper.populateTaskEdit(mv, request,
                INTERACTION_TYPE.CCACTIVITY);

        return mv;
    }

    @Override
    @RequestMapping("viewInteractionTask")
    public ModelAndView viewInteractionTask(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = displayHome(request, response);

        if (!StringUtils.isEmpty(request.getParameter("type")))
        {
            INTERACTION_TYPE noteType = INTERACTION_TYPE
                    .valueOf(request.getParameter("type"));
            switch (noteType)
            {
                case CCACTIVITY:
                    mv = viewCCActivityInteractionTask(request, response);
                    break;
                case CCPOSITION:
                    mv = viewCCPositionInteractionTask(request, response);
                    break;
                case CCRECORD:
                    mv = viewCCRecordInteractionTask(request, response);
                    break;
                default:
                    break;
            }
        }

        InteractionHelper.initializeTaskViewPage(mv, request);
        return mv;
    }

    private ModelAndView viewCCRecordInteractionTask(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordViewInteractionTask");
        populateCCRecordPage(mv, request, response);

        return mv;
    }

    private ModelAndView viewCCPositionInteractionTask(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_positionViewInteractionTask");
        CCPosition position = CCHelper.getCCPosition(request);
        mv.addObject("position", position);

        CCHelper.populateQuickStats(position, mv);

        return mv;
    }

    private ModelAndView viewCCActivityInteractionTask(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityViewInteractionTask");
        CCActivity a = CCHelper.getCCActivity(request);
        CCHelper.populateQuickStats(a);
        mv.addObject("activity", a);

        return mv;
    }

    @Override
    @RequestMapping("displayInteractionFormEdit")
    public ModelAndView displayInteractionFormEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        String formType = !StringUtils.isEmpty(request.getParameter("type"))
                ? request.getParameter("type")
                : request.getParameter("formType");

        if (!StringUtils.isEmpty(formType))
        {
            INTERACTION_TYPE type = INTERACTION_TYPE.valueOf(formType);
            switch (type)
            {
                case CCACTIVITY:
                    mv = displayActivityInteractionFormEdit(request, response);
                    break;
                case CCPOSITION:
                    mv = displayPositionInteractionFormEdit(request, response);
                    break;
                case CCRECORD:
                    mv = displayRecordInteractionFormEdit(request, response);
                    break;
                default:
                    break;
            }
        }

        return mv;
    }

    private ModelAndView displayRecordInteractionFormEdit(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordInteractionFormEdit");
        populateCCRecordPage(mv, request, response);

        InteractionHelper.populateFormEdit(mv, request, INTERACTION_TYPE.CCRECORD);

        return mv;
    }

    private ModelAndView displayPositionInteractionFormEdit(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_positionInteractionFormEdit");
        CCPosition position = CCHelper.getCCPosition(request);
        mv.addObject("position", position);

        InteractionHelper.populateFormEdit(mv, request,
                INTERACTION_TYPE.CCPOSITION);

        return mv;
    }

    private ModelAndView displayActivityInteractionFormEdit(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityInteractionFormEdit");
        CCActivity a = CCHelper.getCCActivity(request);
        CCHelper.populateQuickStats(a);
        mv.addObject("activity", a);

        InteractionHelper.populateFormEdit(mv, request,
                INTERACTION_TYPE.CCACTIVITY);

        return mv;
    }

    @Override
    @RequestMapping("viewInteractionForm")
    public ModelAndView viewInteractionForm(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = displayHome(request, response);

        if (!StringUtils.isEmpty(request.getParameter("type")))
        {
            INTERACTION_TYPE formType = INTERACTION_TYPE
                    .valueOf(request.getParameter("type"));
            switch (formType)
            {
                case CCACTIVITY:
                    mv = viewCCActivityInteractionForm(request, response);
                    break;
                case CCPOSITION:
                    mv = viewCCPositionInteractionForm(request, response);
                    break;
                case CCRECORD:
                    mv = viewCCRecordInteractionForm(request, response);
                    break;
                default:
                    break;
            }
        }

        InteractionHelper.initializeFormViewPage(mv, request);
        return mv;
    }

    private ModelAndView viewCCRecordInteractionForm(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordViewInteractionForm");
        populateCCRecordPage(mv, request, response);

        return mv;
    }

    private ModelAndView viewCCPositionInteractionForm(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_positionViewInteractionForm");
        CCPosition position = CCHelper.getCCPosition(request);
        mv.addObject("position", position);

        CCHelper.populateQuickStats(position, mv);
        CCHelper.populateAccessFlags(mv, position, request);

        return mv;
    }

    private ModelAndView viewCCActivityInteractionForm(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityViewInteractionForm");
        CCActivity a = CCHelper.getCCActivity(request);
        CCHelper.populateQuickStats(a);
        mv.addObject("activity", a);

        return mv;
    }

    @RequestMapping("displayChangePositionActivity")
    public ModelAndView displayChangePositionActivity(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        Locale locale = PortalUtils.getLocale(request);
        CCPosition p = CCHelper.getCCPosition(request);

        if (p != null)
        {
            CCModule module = p.getActivity().getPeriod().getModule();

            mv = new ModelAndView("cc/cc_changePositionActivity");
            mv.addObject("position", p);

            mv.addObject("ccModule", module);

            mv.addObject("activePeriods", getHt().find(
                    "from CCPeriod p where p.module=? and p.active = true order by p."
                            + (LocaleUtils.isL1(locale) ? "name" : "l2Name"),
                    module));
        }
        else
        {
            mv = displayHome(request, response);
            mv.addObject("errorMessage", PortalUtils.getMessageSource().getMessage(
                    "i18n.ccController.PositionNotFound", null, locale));
        }

        return mv;
    }

    @RequestMapping("movePosition")
    public ModelAndView movePosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        try
        {
            UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
            CCPosition p = CCHelper.getCCPosition(request);
            CCActivity currentActivity = p.getActivity();
            CCModule module = p.getActivity().getPeriod().getModule();
            CCActivity targetActivity = CCHelper.getActivityFromSelector(module,
                    request);

            if (currentActivity.getId().equals(targetActivity.getId()))
            {
                mv = displayChangePositionActivity(request, response);
            }
            else if (CCHelper.isAdmin(userLoggedIn, module) || (CCHelper
                    .isActivityDirectors(currentActivity, userLoggedIn)
                    && CCHelper.isActivityDirectors(targetActivity, userLoggedIn)))
            {
                // GUARD AGAINST DUPLICATE POSITIONS...
                // guardAgainstDuplicatePosition(a, posLabel);

                // SAVE ACTIVITY ANSWERS (IF ANY)
                CCHelper.saveActivityAnswers(targetActivity, module, userLoggedIn,
                        request);

                p.setActivity(targetActivity);
                p.setUpdatedDate(new Date());
                p.setUpdatedBy(userLoggedIn);
                getHt().update(p);

                mv = displayChangePositionActivity(request, response);
                mv.addObject("successMessage",
                        new I18nLabel(
                                "i18n.CCController.PositionMo8020879430213336")
                                .getTranslation(PortalUtils.getLocale(request)));
            }
            else
            {
                mv = displayChangePositionActivity(request, response);
                mv.addObject("errorMessage",
                        new I18nLabel(
                                "i18n.CCController.Permission8867276953326326")
                                .getTranslation(PortalUtils.getLocale(request)));
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();

            mv = displayChangePositionActivity(request, response);
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.CCController.ErrorFailu0232727565455828")
                            .getTranslation(PortalUtils.getLocale(request)));
        }

        return mv;
    }

    @RequestMapping("updateActivityOwner")
    public ModelAndView updateActivityOwner(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        try
        {
            UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

            CCModule module = getModule(request);
            CCActivity activity = CCHelper.getCCActivity(request);
            UserDetailsImpl owner = (UserDetailsImpl) getHt().load(
                    UserDetailsImpl.class,
                    Integer.valueOf(request.getParameter("ownerId")));

            if (null != activity.getCurrentOwner()
                    && activity.getCurrentOwner().getId().equals(owner.getId()))
            {
                mv = displayActivityOverview(request, response);
            }
            else if (CCHelper.isAdmin(userLoggedIn, module)
                    || (CCHelper.isActivityDirectors(activity, userLoggedIn)))
            {
                activity.setCurrentOwner(owner);
                activity.setUpdatedOn(new Date());
                activity.setUpdatedBy(userLoggedIn);
                getHt().update(activity);

                mv = displayActivityOverview(request, response);
                mv.addObject("successMessage",
                        new I18nLabel(
                                "i18n.CCController.ActivityOw6319027912447200")
                                .getTranslation(PortalUtils.getLocale(request)));
            }
            else
            {
                mv = displayActivityOverview(request, response);
                mv.addObject("errorMessage",
                        new I18nLabel(
                                "i18n.CCController.Permission9915681247105678")
                                .getTranslation(PortalUtils.getLocale(request)));
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            mv = displayActivityOverview(request, response);
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.CCController.ErrorFailu0564216236592172")
                            .getTranslation(PortalUtils.getLocale(request)));
        }

        return mv;
    }

    @RequestMapping("ajaxLoadActivityQuestions")
    public ModelAndView ajaxLoadActivityQuestions(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);

        ModelAndView mv = getShortCircuitView(
                "cc/cc_ajaxActivityQuestionsForPositionSelector");

        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        CCActivity activity = null;

        boolean canWrite = false;

        if (StringUtils.isInteger(request.getParameter("activityId")))
        {
            activity = CCHelper.getCCActivity(request);

            String currentRole = CCHelper.getCurrentUserRole(module,
                    PortalUtils.getUserLoggedIn(request));

            if (!StringUtils.isEmpty(currentRole))
            {
                if (CCHelper.CC_ROLE_ADMIN.equals(currentRole)
                        && CCHelper.isAdmin(userLoggedIn, module))
                {
                    canWrite = true;
                }
                else if (CCHelper.CC_ROLE_ACTIVITY_DIRECTOR.equals(currentRole)
                        && CCHelper.isActivityDirectors(activity, userLoggedIn))
                {
                    canWrite = true;
                }
                else if (CCHelper.CC_ROLE_STAFF.equals(currentRole)
                        && CCHelper.isStaff(userLoggedIn, module)
                        && activity.getCreatedBy() != null
                        && activity.getCreatedBy().getId()
                                .equals(userLoggedIn.getId()))
                {
                    canWrite = true;
                }
            }

            mv.addObject("activity", activity);
        }
        else
        {
            canWrite = true;
        }

        CCHelper.populateActivityQuestions(mv, module, activity, userLoggedIn,
                canWrite);

        return mv;
    }

    @RequestMapping("ajaxEnableActivityDirectors")
    public ModelAndView ajaxEnableActivityDirectors(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);

        module.setEnableActivityDirectors("true"
                .equalsIgnoreCase(request.getParameter("enableActivityDirectors")));
        getHt().update(module);

        SiteElement se = module.getSiteElement();

        Map<String, String> params = new HashMap<>();
        params.put("action", "displayManageDirectors");
        params.put("successMessage", "Activity Directors feature is now "
                + (module.isEnableActivityDirectors() ? "ON" : "OFF"));

        return redirect(se, params, PortalUtils.getUserLoggedIn(request));

    }

    @RequestMapping("displayEmailSettings")
    public ModelAndView displayEmailSettings(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_emailSettings");
        return mv;
    }

    @RequestMapping("saveEmailSettings")
    public ModelAndView saveEmailSettings(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);

        module.setDeclineEmailForRequest(
                request.getParameter("declineEmailForRequest"));
        module.setL2DeclineEmailForRequest(StringUtils.getValueForL2Fields(
                request.getParameter("l2DeclineEmailForRequest"),
                request.getParameter("declineEmailForRequest")));
        module.setApproveEmailForRequest(
                request.getParameter("approveEmailForRequest"));
        module.setL2ApproveEmailForRequest(StringUtils.getValueForL2Fields(
                request.getParameter("l2ApproveEmailForRequest"),
                request.getParameter("approveEmailForRequest")));
        module.setDeclineSubjectForRequest(
                request.getParameter("declineSubjectForRequest"));
        module.setL2DeclineSubjectForRequest(StringUtils.getValueForL2Fields(
                request.getParameter("l2DeclineSubjectForRequest"),
                request.getParameter("declineSubjectForRequest")));
        module.setApproveSubjectForRequest(
                request.getParameter("approveSubjectForRequest"));
        module.setL2ApproveSubjectForRequest(StringUtils.getValueForL2Fields(
                request.getParameter("l2ApproveSubjectForRequest"),
                request.getParameter("approveSubjectForRequest")));
        module.setFromEmailForRequest(request.getParameter("fromEmailForRequest"));

        module.setDeclineEmail(request.getParameter("declineEmail"));
        module.setL2DeclineEmail(StringUtils.getValueForL2Fields(
                request.getParameter("l2DeclineEmail"),
                request.getParameter("declineEmail")));
        module.setApproveEmail(request.getParameter("approveEmail"));
        module.setL2ApproveEmail(StringUtils.getValueForL2Fields(
                request.getParameter("l2ApproveEmail"),
                request.getParameter("approveEmail")));
        module.setDeclineSubject(request.getParameter("declineSubject"));
        module.setL2DeclineSubject(StringUtils.getValueForL2Fields(
                request.getParameter("l2DeclineSubject"),
                request.getParameter("declineSubject")));
        module.setApproveSubject(request.getParameter("approveSubject"));
        module.setL2ApproveSubject(StringUtils.getValueForL2Fields(
                request.getParameter("l2ApproveSubject"),
                request.getParameter("approveSubject")));
        module.setFromEmail(request.getParameter("fromEmail"));

        module.setEmailValidator(request.getParameter("emailValidator") != null
                && request.getParameter("emailValidator").equals("on"));
        module.setFromEmailForValidators(
                request.getParameter("fromEmailForValidators"));
        module.setValidatorBody(request.getParameter("validatorBody"));
        module.setL2ValidatorBody(StringUtils.getValueForL2Fields(
                request.getParameter("l2ValidatorBody"),
                request.getParameter("validatorBody")));
        module.setValidatorSubject(request.getParameter("validatorSubject"));
        module.setL2ValidatorSubject(StringUtils.getValueForL2Fields(
                request.getParameter("l2ValidatorSubject"),
                request.getParameter("validatorSubject")));
        module.setIncludeOpportunityLink(
                request.getParameter("includeOpportunityLink") != null && request
                        .getParameter("includeOpportunityLink").equals("on"));

        module.setFromEmailForParticipants(
                request.getParameter("fromEmailForParticipants"));
        module.setParticipantsBody(request.getParameter("participantsBody"));
        module.setL2ParticipantsBody(StringUtils.getValueForL2Fields(
                request.getParameter("l2ParticipantsBody"),
                request.getParameter("participantsBody")));
        module.setParticipantsSubject(request.getParameter("participantsSubject"));
        module.setL2ParticipantsSubject(StringUtils.getValueForL2Fields(
                request.getParameter("l2ParticipantsSubject"),
                request.getParameter("participantsSubject")));

        module.setFromEmailForPublicUpdateEmail(
                request.getParameter("fromEmailForPublicUpdateEmail"));
        module.setPublicUpdateEmailBody(
                request.getParameter("publicUpdateEmailBody"));
        module.setL2PublicUpdateEmailBody(StringUtils.getValueForL2Fields(
                request.getParameter("l2PublicUpdateEmailBody"),
                request.getParameter("publicUpdateEmailBody")));
        module.setPublicUpdateEmailSubject(
                request.getParameter("publicUpdateEmailSubject"));
        module.setL2PublicUpdateEmailSubject(StringUtils.getValueForL2Fields(
                request.getParameter("l2PublicUpdateEmailSubject"),
                request.getParameter("publicUpdateEmailSubject")));

        // external position
        module.setExternalPositionEmailSubject(RequestUtils.getStringParameter(
                request, "externalPositionEmailSubject",
                module.getExternalPositionEmailSubject()));
        module.setL2ExternalPositionEmailSubject(RequestUtils.getStringParameter(
                request, "l2ExternalPositionEmailSubject",
                module.getL2ExternalPositionEmailSubject()));
        module.setExternalPositionEmailBody(RequestUtils.getStringParameter(request,
                "externalPositionEmailBody",
                module.getExternalPositionEmailBody()));
        module.setL2ExternalPositionEmailBody(RequestUtils.getStringParameter(
                request, "l2ExternalPositionEmailBody",
                module.getL2ExternalPositionEmailBody()));

        // Activity Graph Email
        module.setFromEmailForActivityGraph(RequestUtils.getStringParameter(request,
                "fromEmailForActivityGraph",
                module.getFromEmailForActivityGraph()));
        module.setActivityGraphEmailSubject(RequestUtils.getStringParameter(request,
                "activityGraphEmailSubject",
                module.getActivityGraphEmailSubject()));
        module.setL2ActivityGraphEmailSubject(RequestUtils.getStringParameter(
                request, "l2ActivityGraphEmailSubject",
                module.getL2ActivityGraphEmailSubject()));
        module.setActivityGraphEmailBody(RequestUtils.getStringParameter(request,
                "activityGraphEmailBody", module.getActivityGraphEmailBody()));
        module.setL2ActivityGraphEmailBody(RequestUtils.getStringParameter(request,
                "l2ActivityGraphEmailBody", module.getL2ActivityGraphEmailBody()));

        getHt().update(module);

        ModelAndView mv = displayEmailSettings(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.CCController.EmailSetti2856935909017722")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    @RequestMapping(method = { RequestMethod.GET,
            RequestMethod.POST }, path = "displayReports")
    public ModelAndView displayReports(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);

        ReportSubController<CCController> reportSubController = ReportSubController
                .getInstance("ccModule_" + module.getId(), this, RequestUtils
                        .getBooleanParameter(request, "resetReports", false));

        if (!reportSubController.isConfigured()
                || request.getParameter("resetReports") != null)
        {
            reportSubController.setMainJsp("cc/cc_reports");
            reportSubController.addPlugin(new CCSummaryReport(module.getId()));
            reportSubController.addPlugin(new CCPeriodReport(module.getId()));
            reportSubController.addPlugin(new CCValidatorReport(module.getId()));
            reportSubController.addPlugin(new CCRecordReport(module.getId()));
            reportSubController
                    .addPlugin(new CCRecordPositionReport(module.getId()));
            reportSubController.addPlugin(new CCPositionReport(module.getId()));
            reportSubController.addPlugin(new CCActivityReport(module.getId()));
            reportSubController
                    .addPlugin(new CCActivityPositionReport(module.getId()));
            if (module.getCompetencyUsecase() != Competenciable.COMP_DISABLED)
            {
                reportSubController
                        .addPlugin(new CCCompetencyReport(module.getId()));
            }
            // reportSubController
            // .addPlugin(new CCLearningOutcomesReport(module.getId()));
        }

        return reportSubController.processRequest(request, response);
    }

    @RequestMapping("deleteStudentRecord")
    public ModelAndView deleteStudentRecord(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = displayHome(request, response);
        List records = getHt().find("from CCRecord r where r.id="
                + Integer.valueOf(request.getParameter("ccrId")));

        if (null != records && !records.isEmpty())
        {
            CCRecord record = (CCRecord) records.get(0);
            PortalUtils.getJt().update(
                    "delete from interaction_note_cc_record_assigned where record = ?",
                    new Object[] { record.getId() });

            PortalUtils.getJt().update(
                    "delete from interaction_task_cc_record_assigned where record = ?",
                    new Object[] { record.getId() });

            Boolean hasRecordPositions = ((Integer) getHt().find(
                    "select count(rp.id) from CCRecordPosition rp where rp.record=?",
                    record).get(0)).intValue() > 0;

            if (hasRecordPositions)
            {
                PortalUtils.getJt().update(
                        "delete from cc_record_position where record = ?",
                        new Object[] { record.getId() });
            }

            PortalUtils.getJt().update("delete from cc_record where id = ?",
                    new Object[] { record.getId() });

            mv.addObject("deleteSuccess", Boolean.TRUE);
        }
        else
        {
            mv.addObject("deleteSuccess", Boolean.FALSE);
        }
        return mv;
    }

    @Override
    @RequestMapping("displayImportData")
    public ModelAndView displayImportData(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        CCModule module = getModule(request);

        if (CCHelper.isAdmin(PortalUtils.getUserLoggedIn(request), module))
        {
            mv = new ModelAndView("cc/cc_importData");
            mv.addObject("importUsecase", new CCImportUsecase(module));
            mv.addObject("module", module);
        }
        else
        {
            mv = displayHomeModuleConfig(request, response);
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.CCController.AccessDeni3894953377600263")
                            .getTranslation(PortalUtils.getLocale(request)));
        }

        return mv;
    }

    @RequestMapping("displayRecordPositionImportData")
    public ModelAndView displayRecordPositionImportData(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        CCModule module = getModule(request);

        if (CCHelper.isAdmin(PortalUtils.getUserLoggedIn(request), module))
        {
            mv = new ModelAndView("cc/cc_importData");
            mv.addObject("importUsecase",
                    new CCRecordPositionImportUsecase(module));
            mv.addObject("module", module);
        }
        else
        {
            mv = displayHomeModuleConfig(request, response);
            mv.addObject("errorMessage",
                    new I18nLabel("i18n.CCController.AccessDeni3894953377600263")
                            .getTranslation(PortalUtils.getLocale(request)));
        }

        return mv;
    }

    @Override
    @RequestMapping("importData_templateBuilder")
    public ModelAndView importData_templateBuilder(HttpServletRequest request,
            HttpServletResponse response)
    {
        return null;
    }

    @Override
    public ImportUsecaseFactory getImportUsecaseFactory()
    {
        return new ImportUsecaseFactory()
        {
            @Override
            public ImportUsecase getImportUsecase(HttpServletRequest request)
            {
                return AcrmDataImportHelper.getImportUsecase(request);
            }
        };
    }

    @RequestMapping("importData_uploadSpreadsheet")
    public ModelAndView importData_uploadSpreadsheet(HttpServletRequest request,
            HttpServletResponse response)
    {
        String importUsecase = request.getParameter("importUsecase");
        ModelAndView mv = new ModelAndView("cc/cc_importDataStaging");
        mv.addObject("importUsecase", importUsecase);
        mv.addObject("module", getModule(request));

        String importDisplayMethod = "";
        if (importUsecase.startsWith(CCImportUsecase.USECASE_KEY))
        {
            importDisplayMethod = "displayImportData";
        }
        else if (importUsecase
                .startsWith(CCRecordPositionImportUsecase.USECASE_KEY))
        {
            importDisplayMethod = "displayRecordPositionImportData";
        }
        mv.addObject("importDisplayMethod", importDisplayMethod);

        AcrmDataImportHelper.handleDataImportFileUpload(
                request.getParameter("importSpreadsheet"),
                PortalUtils.getUserLoggedIn(request), mv,
                PortalUtils.getLocale(request));

        return mv;
    }

    @Override
    @RequestMapping("importData_startImport")
    public ModelAndView importData_startImport(HttpServletRequest request,
            HttpServletResponse response)
    {
        AcrmDataImportHelper.startImport(request);
        ModelAndView mv = displayImportData(request, response);
        mv.addObject("importUsecase",
                AcrmDataImportHelper.getImportUsecase(request));
        mv.addObject("successMessage",
                new I18nLabel("i18n.CCController.Importingn8812186383253484")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    @Override
    @RequestMapping("importData_checkImportProgress")
    public ModelAndView importData_checkImportProgress(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        return DFHelper.ajaxLoadDataImportProgress(request);
    }

    @RequestMapping("displayShortlist")
    public ModelAndView displayShortlist(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_shortlist");

        GridOptions options = new GridOptions();
        options.setGridLoadAjaxMethodName("ajaxLoadShortlistTable");
        options.setGridLoadOnDocumentReady(true);

        GridHelper.addGridSupport(request, mv, "cc_shortlist", options);

        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        CCModule module = getModule(request);
        populateRecord(mv, module, userLoggedIn, false, request);

        List ccModules = new ArrayList();
        ccModules.add(module);
        CCHelper.populateCCPublicSiteElement(mv, ccModules, request);

        return mv;
    }

    @RequestMapping("ajaxLoadShortlistTable")
    public ModelAndView ajaxLoadShortlistTable(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_shortlist_table");

        Integer record = CCHelper.getCCRecordID(getModule(request),
                PortalUtils.getUserLoggedIn(request));

        GridQuery query = new GridQuery();
        query.setGridID("cc_shortlist");
        query.setGridInstanceId(request.getParameter("gridInstanceId"));
        query.setPrimaryId("rpc.id");
        query.setFromAndWhereClause(
                " from CCRecordPositionShortlist rpc where rpc.record.id=?");
        query.setRequest(request);
        query.setMv(mv);
        query.setQueryParams(new Object[] { record });
        GridHelper.runQuery(query);

        return mv;
    }

    @RequestMapping("updateRecordPositionShortlist")
    public ModelAndView updateRecordPositionShortlist(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule ccModule = getModule(request);

        return CCHelper.updateRecordPositionShortlist(request, ccModule);
    }

    @RequestMapping("displayPositionCompetenciesConfig")
    public ModelAndView displayPositionCompetenciesConfig(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_positionCompetenciesConfig");

        CCPosition position = (CCPosition) PortalUtils.getHt().load(
                CCPosition.class,
                Integer.parseInt(request.getParameter("positionId")));

        mv.addAllObjects(CompetencyAnticipatedHelper.populateCompetenciableEditForm(
                CCPositionCompetency.class, position,
                CCPositionHelper.getCompetencyConfigs(position)));

        mv.addObject("position", position);

        return mv;
    }

    @RequestMapping("savePositionCompetencyConfig")
    public ModelAndView savePositionCompetencyConfig(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCPosition position = (CCPosition) PortalUtils.getHt().load(
                CCPosition.class,
                Integer.parseInt(request.getParameter("positionId")));

        CompetencyAnticipatedHelper.processCompetenciableEditForm(request,
                CCPositionCompetency.class, position);
        FlashMessageUtils.success(request,
                "i18n.competency.common.competenciesSaved");
        return displayPositionCompetenciesConfig(request, response);
    }

    @RequestMapping("saveAchievedCompetencies")
    public ModelAndView saveAchievedCompetencies(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCRecordPositionHelper.saveRecordPositionAchievedCompetencies(request);
        FlashMessageUtils.success(request,
                "i18n.competency.common.competenciesSaved");
        boolean fromCards = RequestUtils.getBooleanParameter(request,
                "fromCards", false);
        if (fromCards)
        {
            return displayHome(request, response);
        }
        return displayRecordPositionEdit(request, response);
    }

    @RequestMapping("ajaxLoadPositionCompetenciesTable")
    public ModelAndView ajaxLoadPositionCompetenciesTable(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_positionCompetenciesGridAjax");

        return CCHelper.ajaxLoadPositionCompetencies(request, mv);
    }

    @RequestMapping("getCompetencyChangedAjaxResponse")
    public ModelAndView getCompetencyChangedAjaxResponse(int rpId)
    {
        JSONObject ret = new JSONObject();
        List<Integer> competencies = PortalUtils.getHt().find(
                "select c.competency.id from AcrmCompetencyCCR c where c.recordPosition.id=?",
                rpId);
        try
        {
            ret.put("updatedCompetencyCount", competencies.size());

            String updatedCompetencyIds = "^";
            for (Integer c : competencies)
            {
                updatedCompetencyIds += c + "^";
            }
            if (competencies.size() == 0)
            {
                updatedCompetencyIds += "^";
            }

            ret.put("updatedCompetencyIds", updatedCompetencyIds);
        }
        catch (JSONException e)
        {
            e.printStackTrace();
        }
        return jsonObjectResponse(ret);
    }

    @RequestMapping("displayOldCompetencyConfig")
    public ModelAndView displayOldCompetencyConfig(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_oldCompetencyConfig");

        DFModel model = CCPositionQuestionModelHelper
                .getPositionQuestionModel(getModule(request)).getDFModel();
        List<Object[]> ccQuestions = getHt().find(
                "select q.id, q.questionText from DFQuestion q where q.category.model=? and q.type=?",
                new Object[] { model, DFQuestion.TYPE_MULTI_CHOICE });

        mv.addObject("ccQuestions", ccQuestions);

        return mv;
    }

    @RequestMapping("saveOldCompetencyConfig")
    public ModelAndView saveOldCompetencyConfig(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        String minLOAchievements = request.getParameter("minLOAchievements");
        String maxLOAchievements = request.getParameter("maxLOAchievements");
        String minCompetencies = request.getParameter("minCompetencies_old");
        String maxCompetencies = request.getParameter("maxCompetencies_old");
        boolean trackLOMode = "trackLearningOutcomes"
                .equals(request.getParameter("selectedCompetencyMode"));
        boolean oldCompetencyMode = "enableCompetencySelection"
                .equals(request.getParameter("selectedCompetencyMode"));

        module.setStudentsChangeCompetenciesAfterApproval(!StringUtils.isEmpty(
                request.getParameter("studentsChangeCompetenciesAfterApproval")));
        module.setTrackLearningOutcomes(trackLOMode);
        module.setTrackAchievements(trackLOMode
                && !StringUtils.isEmpty(request.getParameter("trackAchievements")));
        module.setGroupAchievements(trackLOMode
                && !StringUtils.isEmpty(request.getParameter("groupAchievements")));
        module.setMinLOAchievements(!StringUtils.isInteger(minLOAchievements) ? 1
                : Integer.valueOf(minLOAchievements));
        module.setMaxLOAchievements(!StringUtils.isInteger(maxLOAchievements) ? 1
                : Integer.valueOf(maxLOAchievements));
        module.setStudentSelectLOA(trackLOMode
                && !StringUtils.isEmpty(request.getParameter("studentSelectLOA")));
        module.setStudentSelectLOASubset(trackLOMode && !StringUtils
                .isEmpty(request.getParameter("studentSelectLOASubset")));
        module.setShowTooltips(trackLOMode
                && !StringUtils.isEmpty(request.getParameter("showTooltips")));
        module.setEnableCompetencySelection(oldCompetencyMode);
        module.setStudentsReceivePositionCompetencies(!StringUtils.isEmpty(
                request.getParameter("studentsReceivePositionCompetencies")));
        module.setMinCompetencies(!StringUtils.isInteger(minCompetencies) ? 0
                : Integer.valueOf(minCompetencies));
        module.setMaxCompetencies(!StringUtils.isInteger(maxCompetencies) ? 0
                : Integer.valueOf(maxCompetencies));

        if (!StringUtils.isEmpty(request.getParameter("competencyQuestion")))
        {
            module.setCompetencyQuestionId(
                    Integer.valueOf(request.getParameter("competencyQuestion")));
        }

        getHt().update(module);

        return displayOldCompetencyConfig(request, response);
    }

    @RequestMapping("displayModuleCompetenciesConfig")
    public ModelAndView displayModuleCompetenciesConfig(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_moduleCompetenciesConfig");

        mv.addAllObjects(
                CompetencyAnticipatedHelper.populateCompetenciableModuleEdit(
                        CCModuleCompetency.class, getModule(request)));

        return mv;
    }

    @RequestMapping("saveModuleCompetencyConfig")
    public ModelAndView saveModuleCompetencyConfig(HttpServletRequest request,
            HttpServletResponse response)
    {
        CompetencyAnticipatedHelper.processCompetenciableModuleEdit(request,
                CCModuleCompetency.class, getModule(request));

        FlashMessageUtils.success(request,
                "i18n.competency.common.competenciesSaved");
        return displayModuleCompetenciesConfig(request, response);
    }

    @Override
    @RequestMapping("displayRecordReflectionEdit")
    public ModelAndView displayRecordReflectionEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordReflectionEdit"
                + (request.getParameter("ajax") != null ? "Ajax" : ""));

        if (request.getParameter("shortCircuit") != null)
        {
            mv.addObject(SiteController.SHORT_CIRCUIT_ATTRIBUTE_NAME, Boolean.TRUE);
        }

        CCRecordPosition rp = CCHelper.getCCRecordPosition(request);
        ECReflectionHelper.populateReflectionEditForExperientialRecord(request, mv,
                rp);

        mv.addObject("rp", rp);

        return mv;
    }

    @Override
    @RequestMapping("saveRecordReflection")
    public ModelAndView saveRecordReflection(HttpServletRequest request,
            HttpServletResponse response)
    {
        ECReflectionHelper.saveReflection(request);

        ModelAndView mv = displayRecordPositionEdit(request, response);

        if (StringUtils.isNotEmpty(request.getParameter("ajax")))
        {
            mv = jsonBooleanResponse(SUCCESS_KEY, true);
        }

        return mv;
    }

    /**
     * This method is used to ajax load each card in the reports tab.
     *
     * @param request
     * @param response
     * @return the mv returned by calling the subAction in the report with the
     *         specified reportKey.
     */
    @RequestMapping("loadReport")
    public ModelAndView loadReport(HttpServletRequest request,
            HttpServletResponse response)
    {
        ReportSubController reportSubController = ReportSubController
                .getInstance("ccModule_" + getModule(request).getId(), this);

        return reportSubController.processRequest(request, response);
    }

    @RequestMapping("displayMassUpdatePositionDetails")
    public ModelAndView displayMassUpdatePositionDetails(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_massUpdatePositionDetails");
        CCPositionQuestionModel modelInstance = CCPositionQuestionModelHelper
                .getPositionQuestionModel(getModule(request));

        CCHelper.populatePositionQuestions(mv, getModule(request), null,
                PortalUtils.getUserLoggedIn(request), false);
        DFHelper.populateMassUpdatePage(mv, request, modelInstance);

        mv.addObject("ccPeriods",
                getHt().find("from CCPeriod p where p.module=? and p.active=true",
                        getModule(request)));

        return mv;
    }

    @RequestMapping("massUpdatePositionDetails")
    public ModelAndView massUpdatePositionDetails(HttpServletRequest request,
            HttpServletResponse response)
    {
        new MassUpdateCCPositionThread(request, getModule(request)).start();
        ModelAndView mv = displayMassUpdatePositionDetails(request, response);

        mv.addObject("massUpdateInitiated", true);

        return mv;
    }

    @RequestMapping("massUpdatePositionValidator")
    public ModelAndView massUpdatePositionValidator(HttpServletRequest request,
            HttpServletResponse response)
    {
        boolean massUpdateInitiated = true;

        try
        {
            new MassUpdateCCPositionValidatorThread(
                    JSONUtils.toList(
                            new JSONArray(request.getParameter("selectedIds"))),
                    request).start();
        }
        catch (JSONException e)
        {
            massUpdateInitiated = false;
        }

        ModelAndView mv = displayMassUpdatePositionValidator(request, response);

        mv.addObject("massUpdateInitiated", massUpdateInitiated);

        return mv;
    }

    @RequestMapping("massUpdatePositionVisiblity")
    public ModelAndView massUpdatePositionVisiblity(HttpServletRequest request,
            HttpServletResponse response)
    {
        boolean massUpdateInitiated = true;

        try
        {
            massUpdateInitiated = CCHelper.massUpdatePositionVisibility(
                    JSONUtils.toList(
                            new JSONArray(request.getParameter("selectedIds"))),
                    "1".equals(request.getParameter("enable")), request);
        }
        catch (JSONException e)
        {
            massUpdateInitiated = false;
        }

        ModelAndView mv = displayMassUpdatePositionVisibility(request, response);

        mv.addObject("massUpdateInitiated", massUpdateInitiated);

        return mv;
    }

    @RequestMapping("displayMassUpdateActivityDetails")
    public ModelAndView displayMassUpdateActivityDetails(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_massUpdateActivityDetails");
        CCActivityQuestionModel modelInstance = CCActivityQuestionModelHelper
                .getActivityQuestionModel(getModule(request));

        CCHelper.populateActivityQuestions(mv, getModule(request), null,
                PortalUtils.getUserLoggedIn(request), false);
        DFHelper.populateMassUpdatePage(mv, request, modelInstance);

        mv.addObject("ccPeriods",
                getHt().find("from CCPeriod p where p.module=? and p.active=true",
                        getModule(request)));

        return mv;
    }

    @RequestMapping("massUpdateActivityDetails")
    public ModelAndView massUpdateActivityDetails(HttpServletRequest request,
            HttpServletResponse response)
    {
        new MassUpdateCCActivityThread(request, getModule(request)).start();
        ModelAndView mv = displayMassUpdateActivityDetails(request, response);

        mv.addObject("massUpdateInitiated", true);

        return mv;
    }

    @Override
    @RequestMapping("displayMassAssignGlobalTags")
    public ModelAndView displayMassAssignGlobalTags(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = super.displayMassAssignGlobalTags(request, response);

        mv.addObject("ccPeriods",
                getHt().find("from CCPeriod p where p.module=? and p.active=true",
                        getModule(request)));

        return mv;
    }

    @RequestMapping("displayStudentProfileQuestionsConfig")
    public ModelAndView displayStudentProfileQuestionsConfig(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "cc/cc_studentProfileQuestionVisibilityConfig");

        AcrmRegQuestionVisibilityHelper
                .populateQuestionConfig(mv, getModule(request),
                        PortalUtils.getLocale(request), AcrmRegistrationController
                                .getRegistrationModule(PersonGroupHelper.STUDENT),
                        null);

        return mv;
    }

    @RequestMapping("toggleProfileQuestionVis")
    public ModelAndView toggleProfileQuestionVis(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        String property = request.getParameter("property");
        Integer questionId = Integer.valueOf(request.getParameter("questionId"));

        boolean checked = AcrmRegQuestionVisibilityHelper.toggleQuestionVisibility(
                property, questionId, getModule(request),
                AcrmRegistrationController
                        .getRegistrationModule(PersonGroupHelper.STUDENT),
                new CCModuleQuestionVisibility(), null, false);

        return jsonBooleanResponse("val", checked);
    }

    @RequestMapping("ajaxLoadTimePeriodStatsWidget")
    public ModelAndView ajaxLoadTimePeriodStatsWidget(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        CCPeriod currentPeriod = CCHelper.getCCPeriod(request);
        ModelAndView mv = getShortCircuitView("cc/cc_timePeriodStatsWidget");
        int periodId = RequestUtils.getIntParameter(request, "periodId", -1);

        if (periodId > 0)
        {
            Locale locale = PortalUtils.getLocale(request);
            mv.addObject("periodId", periodId);

            Set<String> keysToPopulate = new HashSet<>();
            keysToPopulate.add("approvedAndEnabledPositions");
            keysToPopulate.add("approvedAndDisabledPositions");
            keysToPopulate.add("declinedPositions");
            keysToPopulate.add("pendingPositions");
            keysToPopulate.add("activeActivities");
            keysToPopulate.add("disabledActivities");
            keysToPopulate.add("approvedValidations");
            keysToPopulate.add("declinedValidations");
            keysToPopulate.add("pendingValidations");
            keysToPopulate.add("validatorsWithApproved");
            keysToPopulate.add("validatorsWithDeclined");
            keysToPopulate.add("validatorsWithPending");
            Map periodData = CCHelper.getPeriodStatsMap(keysToPopulate,
                    currentPeriod,
                    CCHelper.getValidActivitiesForUserHQL(
                            PortalUtils.getUserLoggedIn(request), module, request),
                    null);

            Map<String, Double> positionData = new LinkedHashMap<>();
            Map<String, Double> activityData = new LinkedHashMap<>();
            Map<String, Double> validationData = new LinkedHashMap<>();
            Map<String, Double> validatorData = new LinkedHashMap<>();

            activityData.put(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Active7008416726367108", locale),
                    new Double(periodData.get("activeActivities").hashCode()));
            activityData.put(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Inactive9703838618940935", locale),
                    new Double(periodData.get("disabledActivities").hashCode()));

            positionData.put(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Approvedan3055502842922154", locale),
                    new Double(periodData.get("approvedAndEnabledPositions")
                            .hashCode()));
            positionData.put(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Approvedan8597021916765215", locale),
                    new Double(periodData.get("approvedAndDisabledPositions")
                            .hashCode()));
            positionData.put(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Declined2912725360851155", locale),
                    new Double(periodData.get("declinedPositions").hashCode()));
            positionData.put(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Pending2520804463190741", locale),
                    new Double(periodData.get("pendingPositions").hashCode()));

            validationData.put(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Approved1581019601499230", locale),
                    new Double(periodData.get("approvedValidations").hashCode()));
            validationData.put(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Declined3480776656735950", locale),
                    new Double(periodData.get("declinedValidations").hashCode()));
            validationData.put(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Pending5061577344922317", locale),
                    new Double(periodData.get("pendingValidations").hashCode()));

            validatorData.put(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Validators3454949712612157", locale),
                    new Double(
                            periodData.get("validatorsWithApproved").hashCode()));
            validatorData.put(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Validators6488789580534317", locale),
                    new Double(
                            periodData.get("validatorsWithDeclined").hashCode()));
            validatorData.put(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Validators3398954983086276", locale),
                    new Double(periodData.get("validatorsWithPending").hashCode()));

            JSONObject activitiesChart = ChartUtils.getPieChart(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Activity8647993348425534", locale),
                    "",
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Series19833897631777114", locale),
                    activityData, false, false);
            mv.addObject("activities", activitiesChart);

            JSONObject positionsChart = ChartUtils.getPieChart(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Positions1224899417842891", locale),
                    "",
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Series14621000735385855", locale),
                    positionData, false, false);
            mv.addObject("positions", positionsChart);

            JSONObject validationChart = ChartUtils.getPieChart(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Validation5677369981021815", locale),
                    "",
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Series12246783994217313", locale),
                    validationData, false, false);
            mv.addObject(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.validation1369681728501188", locale),
                    validationChart);

            JSONObject validatorChart = ChartUtils.getPieChart(
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Validators7052623729197782", locale),
                    "",
                    PortalUtils.getI18nMessage(
                            "i18n.CCController.Series19083667737606275", locale),
                    validatorData, false, false);
            mv.addObject("validators", validatorChart);
        }

        return mv;
    }

    @RequestMapping("ajaxLoadRecordPositionEditSidebar")
    public ModelAndView ajaxLoadRecordPositionEditSidebar(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv;

        CCModule module = getModule(request);
        CCRecordPosition rp = CCHelper.getCCRecordPosition(request);
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);

        mv = getShortCircuitView("cc/cc_recordPositionEditSidebar");
        if (CCHelper.isRecordPositionValidForUser(rp, rp.getOwner(), user))
        {
            CCRecordPositionHelper.populateMVForRecordPositionEdit(mv, request,
                    module, rp);
            mv.addObject("profilePicture", rp.getOwner().getProfileImageUrl());
        }

        return mv;
    }

    @RequestMapping("ajaxLoadTimeLinePeriodStatsWidget")
    public ModelAndView ajaxLoadTimeLinePeriodStatsWidget(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_timeLineStatsWidget");

        Locale locale = PortalUtils.getLocale(request);
        Integer periodId = RequestUtils.getInteger(request, "periodId");
        if (periodId != null)
        {
            List<OrbisHqlResultSet> periodDates = PortalUtils.getHt().f(
                    "select p.startDate, p.endDate from CCPeriod p where p.id=?",
                    periodId);
            Date periodStartDate = (Date) periodDates.get(0).get("startDate");
            Date periodEndDate = (Date) periodDates.get(0).get("endDate");

            List<Object[]> rpDateBoundaries = PortalUtils.getHt().find(
                    "select min(rp.dateAdded), max(rp.dateAdded), min(rp.dateValidated), max(rp.dateValidated) from CCRecordPosition rp join rp.position.activity.period as period where period.id=?",
                    periodId);
            Date earliestRecordPositionAdded = (Date) rpDateBoundaries.get(0)[0];
            Date lastRecordPositionAdded = (Date) rpDateBoundaries.get(0)[1];
            Date earliestRecordPositionValidated = (Date) rpDateBoundaries
                    .get(0)[2];
            Date lastRecordPositionValidated = (Date) rpDateBoundaries.get(0)[3];

            Date startDate = earliestRecordPositionValidated;
            if (startDate == null
                    || DateUtils.isBefore(earliestRecordPositionAdded, startDate))
            {
                startDate = earliestRecordPositionAdded;
            }
            if (startDate == null || DateUtils.isBefore(periodStartDate, startDate))
            {
                startDate = periodStartDate;
            }

            Date endDate = lastRecordPositionValidated;
            if (endDate == null
                    || DateUtils.isAfter(lastRecordPositionAdded, endDate))
            {
                endDate = lastRecordPositionAdded;
            }
            if (endDate == null || DateUtils.isAfter(periodEndDate, endDate))
            {
                endDate = periodEndDate;
            }

            if (startDate != null && endDate != null
                    && DateUtils.isAfter(DateUtils.now(), startDate))
            {
                List<Date> months = new ArrayList<>();
                List<String> monthNames = new ArrayList<>();
                Date segment = DateUtils.getMonthEnd(startDate);
                Date endPoint = DateUtils.getMonthEnd(DateUtils
                        .addMonth((DateUtils.isBefore(DateUtils.now(), endDate)
                                ? DateUtils.now()
                                : endDate), 1));
                while (DateUtils.isBefore(segment, endPoint))
                {
                    months.add(segment);
                    monthNames.add(DateUtils
                            .getMonthName(DateUtils.getMonth(segment))
                            + (DateUtils.getYear(startDate) != DateUtils
                                    .getYear(endDate)
                                            ? " " + DateUtils.getYear(segment)
                                            : ""));
                    segment = DateUtils.getMonthEnd(DateUtils.addMonth(segment, 1));
                }

                String sql = "SELECT z.range as 'Month', COUNT(*) as 'Count' FROM(SELECT CASE ";
                for (Date month : months)
                {
                    sql += "WHEN property BETWEEN '"
                            + DateUtils.formatDate(DateUtils.getMonthStart(month),
                                    DateUtils.DF_DATABASE_DATETIME_COMPATIBLE)
                            + "' AND '"
                            + DateUtils.formatDate(month,
                                    DateUtils.DF_DATABASE_DATETIME_COMPATIBLE)
                            + "' THEN '"
                            + DateUtils.getMonthName(DateUtils.getMonth(month))
                            + (DateUtils.getYear(startDate) != DateUtils.getYear(
                                    endDate) ? " " + DateUtils.getYear(month) : "")
                            + "' ";
                }
                String myStartDate = DateUtils.formatDate(
                        DateUtils.getMonthStart(startDate),
                        DateUtils.DF_DATABASE_DATETIME_COMPATIBLE);
                String myEndDate = DateUtils.formatDate(
                        DateUtils.getMonthEnd(endDate),
                        DateUtils.DF_DATABASE_DATETIME_COMPATIBLE);

                sql += "END AS range FROM table joins" + " WHERE property BETWEEN '"
                        + myStartDate + "' AND '" + myEndDate + "'"
                        + " periodFilter) z GROUP BY z.range";

                List<Map<String, Object>> recordsCreatedPerMonth = PortalUtils
                        .getJt()
                        .queryForList(sql.replace("table", "cc_record")
                                .replaceAll("property", "dateCreated")
                                .replace("joins", "").replace("periodFilter", ""));
                List<Map<String, Object>> recordPositionsCreatedPerMonth = PortalUtils
                        .getJt()
                        .queryForList(sql.replace("table", "cc_record_position rp")
                                .replaceAll("property", "dateAdded")
                                .replace("joins",
                                        "INNER JOIN cc_position position ON position.id = rp.position INNER JOIN cc_activity activity ON activity.id = position.activity")
                                .replace("periodFilter",
                                        "AND activity.period=" + periodId));
                List<Map<String, Object>> validationsPerMonth = PortalUtils.getJt()
                        .queryForList(sql.replace("table", "cc_record_position rp")
                                .replaceAll("property", "dateValidated")
                                .replace("joins",
                                        "INNER JOIN cc_position position ON position.id = rp.position INNER JOIN cc_activity activity ON activity.id = position.activity")
                                .replace("periodFilter",
                                        "AND activity.period=" + periodId));
                Double[] recordSeries = new Double[monthNames.size()];
                Double[] recordPositionSeries = new Double[monthNames.size()];
                Double[] validationSeries = new Double[monthNames.size()];
                int i = 0;
                int recordTotal = 0, recordPositionTotal = 0, validationTotal = 0;
                for (String monthName : monthNames)
                {
                    int recordMonthCount = 0, recordPositionMonthCount = 0,
                            validationMonthCount = 0;
                    for (Map countResult : recordsCreatedPerMonth)
                    {
                        if (monthName.equals(countResult.get("Month")))
                        {
                            recordMonthCount = (int) countResult.get("Count");
                        }
                    }
                    for (Map countResult : recordPositionsCreatedPerMonth)
                    {
                        if (monthName.equals(countResult.get("Month")))
                        {
                            recordPositionMonthCount = (int) countResult
                                    .get("Count");
                        }
                    }
                    for (Map countResult : validationsPerMonth)
                    {
                        if (monthName.equals(countResult.get("Month")))
                        {
                            validationMonthCount = (int) countResult.get("Count");
                        }
                    }

                    recordTotal += recordMonthCount;
                    recordSeries[i] = Integer.valueOf(recordTotal).doubleValue();
                    recordPositionTotal += recordPositionMonthCount;
                    recordPositionSeries[i] = Integer.valueOf(recordPositionTotal)
                            .doubleValue();
                    validationTotal += validationMonthCount;
                    validationSeries[i] = Integer.valueOf(validationTotal)
                            .doubleValue();
                    i++;
                }
                Map<String, Double[]> lineSeries = new HashMap<>();
                lineSeries.put(PortalUtils.getI18nMessage(
                        "i18n.CCController.RecordsCre1593248886459949", locale),
                        recordSeries);
                lineSeries.put(PortalUtils.getI18nMessage(
                        "i18n.CCController.PositionsA2752358143266888", locale),
                        recordPositionSeries);
                lineSeries.put(PortalUtils.getI18nMessage(
                        "i18n.CCController.Validation7422372625878792", locale),
                        validationSeries);

                JSONObject chart = ChartUtils.getLineChart("",
                        new JSONArray(monthNames), lineSeries, "", "");
                try
                {
                    chart.getJSONObject("yAxis").put("min", 0);
                    chart.getJSONObject("yAxis").put("allowDecimals", false);
                }
                catch (JSONException e)
                {
                    e.printStackTrace();
                }
                mv.addObject("totals", chart);
            }
            else
            {
                mv.addObject("timeLineError", PortalUtils.getI18nMessage(
                        "i18n.CCController.Timelineca3639841287355121", locale));
            }
        }
        else
        {
            mv.addObject("timeLineError", PortalUtils.getI18nMessage(
                    "i18n.CCController.Periodnotf7699872255790111", locale));
        }

        return mv;
    }

    @RequestMapping("addValidatorToActivity")
    public ModelAndView addValidatorToActivity(HttpServletRequest request,
            HttpServletResponse response)
    {
        manageValidatorPosition(request, "create");

        return displayActivityValidators(request, response);
    }

    @RequestMapping("addValidatorToPosition")
    public ModelAndView addValidatorToPosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        manageValidatorPosition(request, "create");

        return displayPositionValidators(request, response);
    }

    @RequestMapping("saveActivityStatus")
    public ModelAndView saveActivityStatus(HttpServletRequest request,
            HttpServletResponse response)
    {
        boolean success = CCHelper.saveActivityStatus(
                CCHelper.getCCActivity(request),
                Integer.valueOf(request.getParameter("status")), request);

        ModelAndView mv = displayActivityOverview(request, response);

        mv.addObject(SUCCESS_KEY, success);

        return mv;
    }

    @RequestMapping("ajaxLoadValidPosSelectOptions")
    public ModelAndView ajaxLoadValidPosSelectOptions(HttpServletRequest request,
            HttpServletResponse response)
    {
        String activityId = request.getParameter("activityId");
        boolean isL1 = LocaleUtils.isL1(request);
        String sql = "select id, " + (isL1 ? "title" : "l2Title")
                + " as title from cc_position" //
                + " where activity=? and status=?" //
                + " order by " + (isL1 ? "title" : "l2Title");
        List<Map<String, Object>> positions = getJt().queryForList(sql,
                new Object[] { activityId, CCPosition.APPROVAL_STATUS_APPROVED });
        JSONArray jsonArray = new JSONArray(positions);

        return jsonArrayResponse(jsonArray);
    }

    @RequestMapping("ajaxLoadActivityTagAssign")
    public ModelAndView ajaxLoadActivityTagAssign(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("cc/cc_positionSelect_tagsAssign");
        String periodId = request.getParameter("periodId");
        String category = request.getParameter("category");
        String organization = request.getParameter("organization");
        String department = request.getParameter("department");
        String activity = request.getParameter("activity");
        String activityId = request.getParameter("activityId");
        CCActivity a = CCHelper.getSelectedActivity(getModule(request), periodId,
                category, organization, department, activity, activityId);

        JSONObject additionalParams = JSONUtils.newJSONObject("activityId",
                a.getId() != null ? a.getId() : -1);

        try
        {
            AcrmHelper.processTagAssign(request, mv, additionalParams, a, true);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return mv;
    }

    @Override
    @RequestMapping("displayManageEngagementActivities")
    public ModelAndView displayManageEngagementActivities(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView(
                "cc/cc_positionManageEngagementActivities");

        CCPosition position = CCHelper.getCCPosition(request);
        mv.addObject("position", position);

        CCHelper.populateQuickStats(position, mv);
        CCHelper.populateAccessFlags(mv, position, request);
        CCHelper.populatePositionCommon(mv, request, position);

        InteractionEngagementHelper.populateManageEngagementActivities(mv,
                InteractionEngagementActivityCCPosition.class, position.getId(),
                CollectionUtils.newHashMap("posId", position.getId()));

        return mv;
    }

    @Override
    @RequestMapping("displayEngagementActivityEdit")
    public ModelAndView displayEngagementActivityEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_positionEngagementActivityEdit");
        InteractionEngagementHelper.populateActivityEdit(request, mv,
                CCHelper.getCCPosition(request),
                InteractionEngagementActivityCCPosition.class,
                EnumSet.of(TYPE.CCVALIDATED),
                CollectionUtils.newHashMap("posId", request.getParameter("posId")));
        return mv;
    }

    @RequestMapping("displayPositionAudit")
    public ModelAndView displayPositionAudit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;
        CCPosition position = CCHelper.getCCPosition(request);
        if (position != null)
        {
            mv = new ModelAndView("cc/cc_positionAudit");
            CCHelper.populateQuickStats(position, mv);
            CCHelper.populateAccessFlags(mv, position, request);
            CCHelper.populatePositionCommon(mv, request, position);
            boolean isSpiralRobot = CCHelper.isSpiralRobotEnabled(
                    getModule(request), PortalUtils.getUserLoggedIn(request));

            PortalUtils.populatePortalLogGrid(request, mv, "ccPosition",
                    position.getId(), isSpiralRobot);
        }
        else
        {
            mv = displayHome(request, response);
            mv.addObject("errorMessage",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.ccController.PositionNotFound", null,
                            PortalUtils.getLocale(request)));
        }

        return mv;
    }

    @RequestMapping("displayActivityAudit")
    public ModelAndView displayActivityAudit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_activityAudit");
        CCActivity a = CCHelper.getCCActivity(request);
        CCHelper.populateActivityCommon(a, getModule(request), request, mv);

        boolean isSpiralRobot = CCHelper.isSpiralRobotEnabled(getModule(request),
                PortalUtils.getUserLoggedIn(request));

        PortalUtils.populatePortalLogGrid(request, mv, "ccActivity", a.getId(),
                isSpiralRobot);

        return mv;
    }

    @RequestMapping("displayRecordPositionAudit")
    public ModelAndView displayRecordPositionAudit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordPositionAudit");
        CCRecordPosition rp = CCHelper.getCCRecordPosition(request);

        mv.addObject("rp", rp);

        boolean isSpiralRobot = CCHelper.isSpiralRobotEnabled(getModule(request),
                PortalUtils.getUserLoggedIn(request));

        PortalUtils.populatePortalLogGrid(request, mv, "ccRecordPosition",
                rp.getId(), isSpiralRobot);

        return mv;
    }

    @Override
    public EnumSet<TYPE> getAvailableEngagementActivityTypes()
    {
        return EnumSet.of(TYPE.CCVALIDATED);
    }

    @RequestMapping("setDfQuestionShowInHeader")
    public ModelAndView setDfQuestionShowInHeader(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        Integer questionId = Integer.valueOf(request.getParameter("questionId"));
        boolean show = RequestUtils.getBooleanParameter(request, "showInHeader",
                false);
        CCModule module = getModule(request);

        return AcrmHelper.setRegQuestionShowInHeader(questionId, show, module);
    }

    @RequestMapping("ajaxLoadActivityTagClouds")
    public ModelAndView ajaxLoadActivityTagClouds(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = null;
        String cloudKey = request.getParameter("cloudKey");
        String tagCloudPanelId = request.getParameter("tagCloudPanelId");
        Locale locale = PortalUtils.getLocale(request);
        Integer activityId = RequestUtils.getRequiredIntParameter(request,
                "activityId");

        JSONObject additionalParams = JSONUtils.newJSONObject("activityId",
                activityId);
        boolean unassignableFromLookup = RequestUtils.getBooleanParameter(request,
                "unassignableFromLookup", false);
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);

        if ("academicCloud".equals(cloudKey))
        {
            mv = AcrmHelper.populateAjaxTagCloud(CCActivityAcademicTag.class,
                    additionalParams, "academicCloud", locale,
                    unassignableFromLookup, user.getId(), tagCloudPanelId);
        }
        else if ("campusCloud".equals(cloudKey))
        {
            mv = AcrmHelper.populateAjaxTagCloud(CCActivityCampusTag.class,
                    additionalParams, "campusCloud", locale, unassignableFromLookup,
                    user.getId(), tagCloudPanelId);
        }
        else if ("complianceCloud".equals(cloudKey))
        {
            mv = AcrmHelper.populateAjaxTagCloud(CCActivityComplianceTag.class,
                    additionalParams, "complianceCloud", locale,
                    unassignableFromLookup, user.getId(), tagCloudPanelId);
        }
        else if ("demographicCloud".equals(cloudKey))
        {
            mv = AcrmHelper.populateAjaxTagCloud(CCActivityDemographicTag.class,
                    additionalParams, "demographicCloud", locale,
                    unassignableFromLookup, user.getId(), tagCloudPanelId);
        }
        else if ("industryCloud".equals(cloudKey))
        {
            mv = AcrmHelper.populateAjaxTagCloud(CCActivityIndustryTag.class,
                    additionalParams, "industryCloud", locale,
                    unassignableFromLookup, user.getId(), tagCloudPanelId);
        }

        return mv;
    }

    @RequestMapping("displayManageLetters")
    public ModelAndView displayManageLetters(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = new ModelAndView("cc/cc_letters");

        JSONObject additionalParams = new JSONObject();
        additionalParams.put("moduleId", getModule(request).getId());

        DocTemplateHelper.prepTemplatesConfigPage(mv, CCLetterTemplate.class,
                "displayManageLetters", additionalParams, request);

        return mv;
    }


    @RequestMapping("ajaxPopulateAnticipatedCompetenciesWidget")
    public ModelAndView ajaxPopulateAnticipatedCompetenciesWidget(
            HttpServletRequest request, HttpServletResponse response)
    {
        CCPosition position = CCHelper.getPosition(request);
        JSONArray jsonArray = CompetencyAnticipatedHelper
                .populateAnticipatedCompetenciesWidget(request, position);
        return jsonArrayResponse(jsonArray);
    }

    @RequestMapping("ajaxLoadAvailablePositionCompetencies")
    public ModelAndView ajaxLoadAvailablePositionCompetencies(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView(
                "cc/cc_positionCompetenciesWizardStep");

        List<ConfigLevel> configLevels = new ArrayList<>();

        configLevels
                .add(new ConfigLevel(CCModuleCompetency.class, getModule(request)));

        CompetencyAnticipatedHelper.populateCompetenciableInheritedEditForm(
                CCPositionCompetency.class, null, configLevels, mv);

        return mv;
    }

    @RequestMapping("checkRecordPositionRequirements")
    public ModelAndView checkRecordPositionRequirements(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        JSONObject ret = new JSONObject();
        CCRecordPosition rp = CCHelper.getCCRecordPosition(request);
        ret.put("errors", CCHelper.validateRequestToPublish(rp,
                PortalUtils.getLocale(request)));
        return jsonObjectResponse(ret);
    }

    @RequestMapping("displayReflectionConfig")
    public ModelAndView displayReflectionConfig(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_manageReflections");
        Locale locale = PortalUtils.getLocale(request);
        CCModule module = getModule(request);
        AcrmReflectionConfigurableHelper.populateConfigEdit(mv, module, locale);
        return mv;
    }

    @RequestMapping("saveReflectionConfig")
    public ModelAndView saveReflectionConfig(HttpServletRequest request,
            HttpServletResponse response)
    {
        CCModule module = getModule(request);
        PortalUtils.getHt().update(module);
        AcrmReflectionConfigurableHelper.saveConfigEdit(request, module);
        ModelAndView mv = displayReflectionConfig(request, response);
        mv.addObject("configSaved", Boolean.TRUE);
        return mv;
    }

    @Override
    @RequestMapping("displayReflectionRecord_version2")
    public ModelAndView displayReflectionRecord_version2(HttpServletRequest request,
            HttpServletResponse response)
    {
        AcrmReflectionRecordAssign<?, ?> orCreateReflectionRecordAssign = AcrmReflectionRecordAssignHelper
                .getOrCreateReflectionRecordAssign(request);

        ParamsMap pageReloadParams = new ParamsMap(
                "displayReflectionRecord_version2");
        pageReloadParams.put("recordPositionId",
                orCreateReflectionRecordAssign.getRecordable().getId().toString());

        String backToAction = RequestUtils.getStringParameter(request,
                "backToAction", "displayRecordPositionEdit");
        boolean fromCards = RequestUtils.getBooleanParameter(request,
                "fromCards", false);
        if (fromCards)
        {
            backToAction = "displayHome";
        }

        ParamsMap backToParams = new ParamsMap(backToAction);
        String backToParamsJson = request.getParameter("backToParamsJson");
        if (!StringUtils.isEmpty(backToParamsJson))
        {
            try
            {
                JSONObject jsonObject = JSONUtils.toJSONObject(backToParamsJson)
                        .orElse(new JSONObject());
                JSONUtils.toMap(jsonObject).forEach(
                        (key, value) -> backToParams.put(key, (String) value));
            }
            catch (JSONException e)
            {
                logger.error("Error while parsing backToParamsJson");
            }
        }
        else
        {
            backToParams.put("recordPosition", orCreateReflectionRecordAssign
                    .getRecordable().getId().toString());
        }
        return AcrmReflectionRecordHelper.displayReflectionRecord_version2(
                orCreateReflectionRecordAssign, pageReloadParams, backToParams,
                request);
    }

    @Override
    @RequestMapping("ajaxSaveReflection_version2")
    public ModelAndView ajaxSaveReflection_version2(HttpServletRequest request,
            HttpServletResponse response)
    {
        AcrmReflectable reflectable = AcrmReflectionHelper
                .requireReflectable(request);
        JSONUtils.JSONBuilder builder = JSONUtils.builder();

        List<String> pageErrors = new ArrayList<>();

        CCRecordPosition rp = PortalUtils.getHt()
                .get(CCRecordPosition.class,
                        RequestUtils.isNotEmpty(request, "rpId") ?
                                Integer.valueOf(request.getParameter("rpId")) :
                                null);

        // The seatbelt to prevent students from adding a reflection
        // when the record is already approved by an admin in same time and
        // "Allow students to add/edit reflection after record-position is approved"
        // is disabled in the configuration.
        if (rp != null && rp.getStatus().equals("Approved")
                && PortalUtils.getUserLoggedIn(request).isStudent()
                && !rp.getRecord().getModule()
                .isStudentCanChangeReflectionAfterRecordApproval())
        {
            MessageSource messageSource = PortalUtils.getMessageSource();
            pageErrors.add(messageSource.getMessage(
                    "i18n.CCController.Reflection2093587952977158",
                    null, PortalUtils.getLocale(request)));
        }
        else
        {
            ValidationData validationData = AcrmReflectionRecordControllerHelper
                    .saveReflection(reflectable, request);

            pageErrors = validationData.getPageErrors().stream()
                    .map(I18nLabel::getTranslation)
                    .collect(Collectors.toList());
            builder.put("success", !validationData.hasErrors());

            try
            {
                builder.put("formErrorsJson",
                        validationData.getFormErrorsJson());
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
        builder.put("pageErrors", pageErrors);

        return NHelper.AJAXResponse(builder.buildToString());
    }

}
