package com.orbis.web.content.cc;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

import javax.sql.DataSource;

import org.apache.commons.beanutils.BeanUtils;
import org.hibernate.HibernateException;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.context.MessageSource;
import org.springframework.web.servlet.ModelAndView;

import com.google.common.collect.Lists;
import com.orbis.acegi.providers.dao.hibernate.PersonGroupHelper;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsPreferences;
import com.orbis.acegi.providers.dao.hibernate.UserPreferencesHelper;
import com.orbis.df.DFHelper;
import com.orbis.df.DFModel;
import com.orbis.df.DFQuestion;
import com.orbis.jqgrid.JQGridColumn;
import com.orbis.jqgrid.JQGridModel;
import com.orbis.jqgrid.JQGridSearch;
import com.orbis.portal.CommandQueryTemplate;
import com.orbis.portal.OrbisHqlResultSet;
import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.portal.PortalConstants;
import com.orbis.portal.PortalLog;
import com.orbis.portal.PortalUtils;
import com.orbis.portal.QueryCallback;
import com.orbis.question2.Question2Helper;
import com.orbis.question2.QuestionAnswers;
import com.orbis.search.SearchHelper;
import com.orbis.search.SearchMassAssignButton;
import com.orbis.search.SearchModel;
import com.orbis.search.criteria.CriteriaGroup;
import com.orbis.search.criteria.CriteriaModel;
import com.orbis.search.criteria.CriteriaQuestion;
import com.orbis.search.criteria.CriteriaQuestionHQLBuilder;
import com.orbis.search.entity.EmailingEmailer;
import com.orbis.search.entity.EmailingModel;
import com.orbis.search.entity.Entity;
import com.orbis.search.entity.Relationship;
import com.orbis.utils.CollectionUtils;
import com.orbis.utils.DBUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.EmailMessage;
import com.orbis.utils.EmailUtils;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.JSONUtils;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.LookupUtils;
import com.orbis.utils.PropertyUtils;
import com.orbis.utils.QueryBuilder;
import com.orbis.utils.RequestModuleUser;
import com.orbis.utils.RequestUtils;
import com.orbis.utils.StringUtils;
import com.orbis.utils.query.Select;
import com.orbis.utils.query.Where;
import com.orbis.web.OrbisController;
import com.orbis.web.OrbisModule;
import com.orbis.web.content.ContentItem;
import com.orbis.web.content.acrm.AcrmHelper;
import com.orbis.web.content.acrm.AcrmRegQuestionVisibilityHelper;
import com.orbis.web.content.acrm.AcrmRegistrationController;
import com.orbis.web.content.acrm.AcrmRegistrationModule;
import com.orbis.web.content.acrm.AcrmUserTag;
import com.orbis.web.content.acrm.Tag;
import com.orbis.web.content.acrm.TagAssign;
import com.orbis.web.content.acrm.TagAssignHelper;
import com.orbis.web.content.acrm.TagAssignable;
import com.orbis.web.content.acrm.TagGrouping;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.acrm.competency.Competenciable;
import com.orbis.web.content.acrm.competency.CompetencyAchievedHelper;
import com.orbis.web.content.acrm.competency.ConfigLevel;
import com.orbis.web.content.acrm.competency.ProficiencyItemHelper;
import com.orbis.web.content.acrm.reflection.AcrmReflectionHelper;
import com.orbis.web.content.acrm.reflection.config.AcrmReflectionConfig;
import com.orbis.web.content.cc.criteriaPlugins.CCActivityCriteriaPlugin;
import com.orbis.web.content.cc.criteriaPlugins.CCPositionCriteriaPlugin;
import com.orbis.web.content.ec.publish.PublishHelper;
import com.orbis.web.content.ev.domain.GlobalEvent;
import com.orbis.web.content.ev.domain.GlobalEventRegistration;
import com.orbis.web.content.ev.web.globalevents.GlobalEventHelper;
import com.orbis.web.content.grid.DataViewerDataBuilder;
import com.orbis.web.content.grid.DataViewerState;
import com.orbis.web.content.grid.GridFilter;
import com.orbis.web.content.grid.GridHelper;
import com.orbis.web.content.grid.GridQuery;
import com.orbis.web.content.grid.GridQueryFactory;
import com.orbis.web.content.grid.NameValuePair;
import com.orbis.web.content.interaction.InteractionEngagementActivity.TYPE;
import com.orbis.web.content.interaction.InteractionEngagementHelper;
import com.orbis.web.content.interaction.InteractionHelper;
import com.orbis.web.content.interaction.InteractionHelper.INTERACTION_TYPE;
import com.orbis.web.content.interaction.InteractionNoteCCActivityAssigned;
import com.orbis.web.content.interaction.InteractionNoteCCPositionAssigned;
import com.orbis.web.content.interaction.InteractionNoteCCRecordAssigned;
import com.orbis.web.content.interaction.InteractionTaskCCActivityAssigned;
import com.orbis.web.content.interaction.InteractionTaskCCPositionAssigned;
import com.orbis.web.content.interaction.InteractionTaskCCRecordAssigned;
import com.orbis.web.content.np.NHelper;
import com.orbis.web.content.portal.DeletionNodeHelper;
import com.orbis.web.content.portal.PortalToolsHelper;
import com.orbis.web.content.pt.PTHelper;
import com.orbis.web.content.pt.PTProgram;
import com.orbis.web.content.pt.PTProgramEnrollment;
import com.orbis.web.report.JasperController;
import com.orbis.web.site.SiteController;
import com.orbis.web.site.SiteElement;

import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public final class CCHelper
{
    public static final String GRID_STATS_VALIDATORS_SEARCH = "validatorsSearch";

    public static final String GRID_STATS_TOTAL_VALIDATORS = "totalValidators";

    public static final String GRID_STATS_VALIDATORS_WITH_DECLINED = "validatorsWithDeclined";

    public static final String GRID_STATS_VALIDATORS_WITH_APPROVED = "validatorsWithApproved";

    public static final String GRID_STATS_VALIDATORS_WITH_PENDING = "validatorsWithPending";

    public static final String GRID_STATS_TOTAL_VALIDATIONS = "totalValidations";

    public static final String GRID_STATS_ACCEPTED_VALIDATIONS = "acceptedValidations";

    public static final String GRID_STATS_DECLINED_VALIDATIONS = "declinedValidations";

    public static final String GRID_STATS_PENDING_VALIDATIONS = "pendingValidations";

    public static final String GRID_STATS_COMPLETED_VALIDATIONS = "completedValidations";

    public static final String GRID_STATS_DISABLED_POSITIONS = "disabledPositions";

    public static final String GRID_STATS_TOTAL_POSITIONS = "totalPositions";

    public static final String GRID_STATS_DECLINED_POSITIONS = "declinedPositions";

    public static final String GRID_STATS_APPROVED_POSITIONS = "approvedPositions";

    public static final String GRID_STATS_APPROVED_AND_ENABLED_POSITIONS = "approvedAndEnabledPositions";

    public static final String GRID_STATS_APPROVED_AND_DISABLED_POSITIONS = "approvedAndDisabledPositions";

    public static final String GRID_STATS_ACTIVE_POSITIONS = "activePositions";

    public static final String GRID_STATS_PENDING_POSITIONS = "pendingPositions";

    public static final String GRID_STATS_TOTAL_RECORDS_AT_LEAST_ONE = "totalRecordsAtLeastOne";

    public static final String GRID_STATS_TOTAL_RECORDS = "totalRecords";

    public static final String GRID_STATS_RECORDS = "records";

    public static final String CC_ROLE_ADMIN = "admin";

    public static final String CC_ROLE_STAFF = "staff";

    public static final String CC_ROLE_VALIDATOR = "validator";

    public static final String CC_ROLE_STUDENT = "student";

    public static final String CC_ROLE_ALUMNI = "alumni";

    public static final String CC_ROLE_ACTIVITY_DIRECTOR = "activityDirector";

    public static final String CC_ROLE_ACTIVITY_OWNER = "activityOwner";

    public static final String CC_ROLE_ANONYMOUS = "anonymous";

    public static class QuestionType
    {
        public static final int TEXT = 1;

        public static final int DATE = 2;

        public static final int NUMBER = 3;

        public static final int CHOICE_SINGLE = 4;

        public static final int CHOICE_MULTI = 5;

        public static final int MATRIX_SINGLE = 6;

        public static final int MATRIX_MULTI = 7;

        public static final int FILE_UPLOAD = 8;

        public static final int DESCRIPTIVE = 9;

        public static final int TREE = 10;

        public static final int WEEKLY_TIME_COMMITMENT = 11;
    }

    public static class DisplayFormat
    {
        public static final int GENERAL = 0;

        public static final int VERTICAL = 1;

        public static final int HORIZONTAL = 2;

        public static final int COMBO = 3;

        public static final int DATE = 4;

        public static final int TIME = 5;

        public static final int DATETIME = 6;

        public static final int MULTILINE = 7;
    }

    public static class DisplayFormatMultiLine
    {
        public static final int TEXTAREA = 0;

        public static final int FCK_SIMPLE = 1;

        public static final int FCK_RICH = 2;

        public static final int SINGLE_PATH = 3;
    }

    public static class QuestionEditControl
    {
        // Only name and order are editable
        public static final int LEVEL_1 = 1;

        // Level 1 plus Visibility edits
        public static final int LEVEL_2 = 2;

        public static final int FULL_CONTROL = 100;
    }

    private CCHelper()
    {
    }

    public static CCModule getCCModule(Integer ccModuleId)
    {
        CCModule ret = null;
        List<SiteElement> ccSes = PortalUtils.getSiteManager()
                .getElementsByType("ccController");

        for (SiteElement se : ccSes)
        {
            CCModule ccModule = (CCModule) se.getContentItem();
            if (ccModule.getId().equals(ccModuleId))
            {
                ret = ccModule;
                break;
            }
        }

        return ret;
    }

    public static void clonePositionLearningOutcomes(CCPosition pos,
            CCPosition newPosition)
    {
        List<CCPositionLearningOutcome> los = PortalUtils.getHt()
                .find("from CCPositionLearningOutcome lo where lo.position=?", pos);

        for (CCPositionLearningOutcome lo : los)
        {
            CCPositionLearningOutcome newLO = new CCPositionLearningOutcome();
            newLO.setPosition(newPosition);
            newLO.setLearningOutcome(lo.getLearningOutcome());
            PortalUtils.getHt().save(newLO);
        }
    }

    public static void clonePositionAchivementAdmins(CCPosition pos,
            CCPosition newPosition)
    {
        List<CCPositionAchievementAdmin> paas = PortalUtils.getHt().find(
                "from CCPositionAchievementAdmin paa where paa.position=? ", pos);

        for (CCPositionAchievementAdmin paa : paas)
        {
            CCPositionAchievementAdmin newPaa = new CCPositionAchievementAdmin();
            newPaa.setPosition(newPosition);
            newPaa.setAchievement(paa.getAchievement());
            PortalUtils.getHt().save(newPaa);
        }
    }

    public static void clonePositionValidators(CCPosition pos,
            CCPosition newPosition)
    {
        List<CCPositionValidator> pvs = PortalUtils.getHt()
                .find("from CCPositionValidator pv where pv.position=? ", pos);

        for (CCPositionValidator pv : pvs)
        {
            CCPositionValidator newPv = new CCPositionValidator();
            newPv.setPosition(newPosition);
            newPv.setValidator(pv.getValidator());
            PortalUtils.getHt().save(newPv);
        }
    }

    public static List<CCModule> getCCModules()
    {
        return getCCModules(true);
    }

    public static List<CCModule> getCCModules(boolean includeArchived)
    {
        return PortalToolsHelper.getPortalModules("ccController", includeArchived);
    }

    public static CCPublicModule getCCPublicModule(CCModule module)
    {
        CCPublicModule ret = null;
        List<SiteElement> ccpSes = PortalUtils.getSiteManager()
                .getElementsByType("ccPublicController");

        for (SiteElement se : ccpSes)
        {
            CCPublicModule ccPublicModule = (CCPublicModule) se.getContentItem();
            if (ccPublicModule.getCcModule() != null
                    && ccPublicModule.getCcModule().getId().equals(module.getId()) && !se.isInTrash())
            {
                ret = ccPublicModule;
                break;
            }
        }

        return ret;
    }

    public static void populateCCPublicSiteElement(ModelAndView mv, List ccModules,
            HttpServletRequest request)
    {
        List ccPublicControllers = new ArrayList();
        for (Iterator iterator = ccModules.iterator(); iterator.hasNext();)
        {
            CCModule ccModule = (CCModule) iterator.next();
            List<SiteElement> ccpSes = PortalUtils.getSiteManager()
                    .getElementsByType("ccPublicController");

            for (SiteElement ccpSe : ccpSes)
            {
                CCPublicModule ccPublicModule = (CCPublicModule) ccpSe
                        .getContentItem();
                if (ccPublicModule != null && ccPublicModule.getCcModule() != null
                        && ccPublicModule.getCcModule().getId()
                                .equals(ccModule.getId()))
                {
                    List<SiteElement> ses = PortalUtils.getSiteManager()
                            .getElementsByContentItem(ccPublicModule);
                    for (SiteElement se : ses)
                    {
                        if (PortalUtils.canAccessSiteElement(se,
                                PortalUtils.getUserLoggedIn(request),
                                new HashSet()))
                        {
                            ccPublicControllers.add(se);
                        }

                    }
                }
            }

        }
        mv.addObject("ccPublicControllers", ccPublicControllers);
    }

    public static JSONObject nodeSelectorJsonPosition(List shortlist, Object[] d)
            throws JSONException
    {
        JSONObject p = new JSONObject();
        p.put("positionId", d[0]);
        p.put("position", d[1]);
        p.put("enabled",
                Boolean.parseBoolean(d[2].toString()) ? "Active" : "Inactive");
        p.put("positionEnabled", d[2]);
        p.put("periodId", d[3]);
        p.put("period", d[4]);
        p.put("activityId", d[5]);
        p.put("activity", d[6]);
        p.put("status", d[7] != null ? "Approved"
                : d[7] == null && d[8] != null ? "Declined" : "Pending");
        p.put("activityStatus", ((Integer) d[9]).intValue() == 0 ? "Pending"
                : (((Integer) d[9]).intValue() == 1 ? "Active"
                        : (((Integer) d[9]).intValue() == 2 ? "Disabled" : "N?A")));
        p.put("shortlisted", shortlist.contains(d[0]));
        return p;
    }

    public static String getNodeSelectorSelect(String positionAlias)
    {
        return StringUtils.fillInPlaceholders(
                "select [0].id, [0].title, [0].enabled, [0].activity.period.id, [0].activity.period.name, [0].activity.id, [0].activity.activity, [0].approvedBy.id, [0].deniedBy.id, [0].activity.status",
                new Object[] { positionAlias });
    }

    public static JSONObject getNextCCNode(HttpServletRequest request,
            CCModule module, boolean limitByCurrentRole)
    {
        Locale locale = PortalUtils.getLocale(request);
        JSONObject ret = new JSONObject();

        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);

        String selectedPeriodId = request.getParameter("periodId");
        String selectedCategory = request.getParameter("category");
        String selectedOrganization = request.getParameter("organization");
        String selectedDepartment = request.getParameter("department");
        String selectedActivity = request.getParameter("activity");
        String onlyApproved = request.getParameter("onlyApproved");
        String selectedStatuses = request.getParameter("selectedStatuses");
        String selectedActivityStatuses = request
                .getParameter("selectedActivityStatuses");

        return buildQueryAndGetNodes(module, limitByCurrentRole, locale, user,
                selectedPeriodId, selectedCategory, selectedOrganization,
                selectedDepartment, selectedActivity, onlyApproved,
                selectedStatuses, selectedActivityStatuses);
    }

    public static JSONObject buildQueryAndGetNodes(CCModule module,
            boolean limitByCurrentRole, Locale locale, UserDetailsImpl user,
            String selectedPeriodId, String selectedCategory,
            String selectedOrganization, String selectedDepartment,
            String selectedActivity, String onlyApproved, String selectedStatuses,
            String selectedActivityStatuses)
    {
        JSONObject ret = new JSONObject();

        try
        {
            if (StringUtils.isEmpty(selectedPeriodId))
            {
                List data = PortalUtils.getHt().find("select ccp.id, ccp."
                        + (LocaleUtils.isL1(locale) ? "name" : "l2Name")
                        + " from CCPeriod ccp where ccp.active=true and ccp.module=?",
                        module);

                ret.put("nodes", data);
            }
            else if (!StringUtils.isInteger(selectedPeriodId))
            {
                ret.put("nodes", new ArrayList());
            }
            else
            {

                // 'targetLevel' will be the name of the activity-node that
                // we'll be
                // searching for...
                String targetLevel = null;

                // First, determine the "highest activity-level" that the module
                // is
                // configured to support...
                if (module.isShowLevelCategory())
                {
                    targetLevel = "category";
                }
                else if (module.isShowLevelOrganization())
                {
                    targetLevel = "organization";
                }
                else if (module.isShowLevelDepartment())
                {
                    targetLevel = "department";
                }
                else
                {
                    targetLevel = "activity";
                }

                // Next, calculate the "next targetLevel" to search on, based on
                // the
                // "selected level"...
                if ("category".equals(targetLevel))
                {
                    if (!StringUtils.isEmpty(selectedCategory))
                    {
                        if (module.isShowLevelOrganization())
                        {
                            targetLevel = "organization";
                        }
                        else if (module.isShowLevelDepartment())
                        {
                            targetLevel = "department";
                        }
                        else
                        {
                            targetLevel = "activity";
                        }
                    }

                    if (!StringUtils.isEmpty(selectedOrganization))
                    {
                        if (module.isShowLevelDepartment())
                        {
                            targetLevel = "department";
                        }
                        else
                        {
                            targetLevel = "activity";
                        }
                    }

                    if (!StringUtils.isEmpty(selectedDepartment))
                    {
                        targetLevel = "activity";
                    }

                    if (!StringUtils.isEmpty(selectedActivity))
                    {
                        targetLevel = "position";
                    }
                }
                else if ("organization".equals(targetLevel))
                {
                    if (!StringUtils.isEmpty(selectedOrganization))
                    {
                        if (module.isShowLevelDepartment())
                        {
                            targetLevel = "department";
                        }
                        else
                        {
                            targetLevel = "activity";
                        }
                    }

                    if (!StringUtils.isEmpty(selectedDepartment))
                    {
                        targetLevel = "activity";
                    }

                    if (!StringUtils.isEmpty(selectedActivity))
                    {
                        targetLevel = "position";
                    }
                }
                else if ("department".equals(targetLevel))
                {
                    if (!StringUtils.isEmpty(selectedDepartment))
                    {
                        targetLevel = "activity";
                    }

                    if (!StringUtils.isEmpty(selectedActivity))
                    {
                        targetLevel = "position";
                    }
                }
                else if ("activity".equals(targetLevel))
                {
                    if (!StringUtils.isEmpty(selectedActivity))
                    {
                        targetLevel = "position";
                    }
                }

                // Next, construct the final query...
                StringBuilder hql = new StringBuilder();

                if ("position".equals(targetLevel))
                {
                    hql.append(getNodeSelectorSelect("ccp")
                            + " from CCPosition ccp join ccp.activity join ccp.activity.period where ccp.activity.period.id=")
                            .append(selectedPeriodId)
                            .append(" and ccp.activity.period.module.id=")
                            .append(module.getId()).append(" and ccp.activity.id=")
                            .append(selectedActivity);

                    if (onlyApproved != null
                            && onlyApproved.toString().equalsIgnoreCase("true"))
                    {
                        hql.append(" and ccp.status=1 ");
                    }

                    if (StringUtils.isInteger(selectedStatuses))
                    {
                        String statuses = selectedStatuses;
                        List<Integer> statusList = new ArrayList();
                        for (int i = 0; i < statuses.length(); i++)
                        {
                            statusList.add(Integer
                                    .valueOf(String.valueOf(statuses.charAt(i))));
                        }
                        hql.append(" and ccp.status in "
                                + DBUtils.buildInClause(statusList));
                    }

                    String role = CCHelper.getCurrentUserRole(module, user);

                    if (limitByCurrentRole && !StringUtils.isEmpty(role))
                    {
                        if (CCHelper.CC_ROLE_ACTIVITY_DIRECTOR.equals(role))
                        {
                            hql.append(
                                    " and ccp.activity.id in (select ccad.activity.id from CCActivityDirectors ccad where ccad.directors.id in (select ccd.directors.id from CCDirector ccd where ccd.member.id=")
                                    .append(user.getId()).append("))");
                        }
                        else if (CCHelper.CC_ROLE_STAFF.equals(role))
                        {
                            hql.append(" and (ccp.activity.createdBy.id=")
                                    .append(user.getId())
                                    .append(" or ccp.activity.currentOwner.id=")
                                    .append(user.getId())
                                    .append(" or ccp.createdBy.id=")
                                    .append(user.getId()).append(")");
                        }
                        else if (CCHelper.CC_ROLE_ACTIVITY_OWNER.equals(role))
                        {
                            hql.append(" and (ccp.activity.currentOwner.id=")
                                    .append(user.getId()).append(")");
                        }
                        else if (CCHelper.CC_ROLE_VALIDATOR.equals(role))
                        {
                            hql.append(
                                    " and ccp.id in (select ccpv.position.id from CCPositionValidator ccpv where ccpv.validator.id=")
                                    .append(user.getId()).append(")");
                        }
                        else if ((CCHelper.CC_ROLE_STUDENT.equals(role)
                                || CCHelper.CC_ROLE_ALUMNI.equals(role)
                                || CCHelper.CC_ROLE_ANONYMOUS.equals(role)))
                        {
                            hql.append(
                                    " and ccp.enabled=true and ccp.status=1 and ccp.activity.status = ")
                                    .append(CCActivity.STATUS_ACTIVE);
                        }
                    }
                    else if (user == null
                            || !(isAdmin(user, module) || isValidator(user)
                                    || isActivityDirector(user, module))
                            || !(CC_ROLE_ADMIN
                                    .equals(getCurrentUserRole(module, user))
                                    || CC_ROLE_VALIDATOR.equals(
                                            getCurrentUserRole(module, user))
                                    || CC_ROLE_ACTIVITY_DIRECTOR.equals(
                                            getCurrentUserRole(module, user))))
                    {
                        hql.append(
                                " and ccp.enabled=true and ccp.status=1 and ccp.activity.status = ")
                                .append(CCActivity.STATUS_ACTIVE);
                    }

                    List<Object[]> data = PortalUtils.getHt().find(hql.toString());
                    List shortlist = PortalUtils.getHt().find(
                            "select rpc.position.id from CCRecordPositionShortlist rpc where rpc.record=?",
                            CCHelper.getCCRecord(module, user));
                    JSONArray positions = new JSONArray();
                    for (Object[] d : data)
                    {
                        JSONObject p = nodeSelectorJsonPosition(shortlist, d);
                        if (p.get("enabled").equals("Active")
                                || (user != null && !user.isStudent()))
                        {
                            positions.put(p);
                        }
                    }

                    ret.put("positions", positions);
                }
                else
                {
                    if ("activity".equals(targetLevel))
                    {
                        hql.append("select cca.id, cca.activity, cca.status");
                    }
                    else
                    {
                        hql.append("select distinct cca.").append(targetLevel);
                    }

                    hql.append(" from CCActivity cca where cca.period.id=")
                            .append(selectedPeriodId)
                            .append(" and cca.period.module.id=")
                            .append(module.getId());

                    if (StringUtils.isInteger(selectedActivityStatuses))
                    {
                        String statuses = selectedActivityStatuses;
                        List<Integer> statusList = new ArrayList();
                        for (int i = 0; i < statuses.length(); i++)
                        {
                            statusList.add(Integer
                                    .valueOf(String.valueOf(statuses.charAt(i))));
                        }
                        hql.append(" and cca.status in "
                                + DBUtils.buildInClause(statusList));
                    }

                    String role = CCHelper.getCurrentUserRole(module, user);

                    if (limitByCurrentRole && !StringUtils.isEmpty(role))
                    {
                        if (CCHelper.CC_ROLE_ACTIVITY_DIRECTOR.equals(role))
                        {
                            hql.append(
                                    " and cca.id in (select ccad.activity.id from CCActivityDirectors ccad where ccad.directors.id in (select ccd.directors.id from CCDirector ccd where ccd.member.id=")
                                    .append(user.getId()).append("))");
                        }
                        else if (CCHelper.CC_ROLE_STAFF.equals(role))
                        {
                            hql.append(" and (cca.createdBy.id=")
                                    .append(user.getId())
                                    .append(" or cca.currentOwner.id=")
                                    .append(user.getId())
                                    .append(" or cca.id in (select ccp.activity.id from CCPosition ccp where ccp.createdBy.id=")
                                    .append(user.getId()).append("))");
                        }
                        else if (CCHelper.CC_ROLE_ACTIVITY_OWNER.equals(role))
                        {
                            hql.append(" and (cca.currentOwner.id=")
                                    .append(user.getId()).append("))");
                        }
                        else if (CCHelper.CC_ROLE_VALIDATOR.equals(role))
                        {
                            hql.append(
                                    " and cca.id in (select ccpv.position.activity.id from CCPositionValidator ccpv where ccpv.validator.id=")
                                    .append(user.getId()).append(")");
                        }
                        else if ((CCHelper.CC_ROLE_STUDENT.equals(role)
                                || CCHelper.CC_ROLE_ALUMNI.equals(role)
                                || CCHelper.CC_ROLE_ANONYMOUS.equals(role)))
                        {
                            hql.append(" and cca.status = ")
                                    .append(CCActivity.STATUS_ACTIVE);
                        }
                    }
                    else if (user == null
                            || !(isAdmin(user, module) || isValidator(user)
                                    || isActivityDirector(user, module))
                            || !(CC_ROLE_ADMIN
                                    .equals(getCurrentUserRole(module, user))
                                    || CC_ROLE_VALIDATOR.equals(
                                            getCurrentUserRole(module, user))
                                    || CC_ROLE_ACTIVITY_DIRECTOR.equals(
                                            getCurrentUserRole(module, user))))
                    {
                        hql.append(" and cca.status = " + CCActivity.STATUS_ACTIVE);
                    }

                    if (!StringUtils.isEmpty(selectedCategory))
                    {
                        selectedCategory = selectedCategory.replaceAll("'", "''");
                        String selectedCategory2 = selectedCategory.replace("&amp;",
                                "&");
                        hql.append(" and (cca.category='").append(selectedCategory)
                                .append("' or cca.category='" + selectedCategory2
                                        + "')");
                    }

                    if (!StringUtils.isEmpty(selectedOrganization))
                    {
                        selectedOrganization = selectedOrganization.replaceAll("'",
                                "''");
                        String selectedOrganization2 = selectedOrganization
                                .replace("&amp;", "&");
                        hql.append(" and (cca.organization='")
                                .append(selectedOrganization)
                                .append("' or cca.organization='"
                                        + selectedOrganization2 + "')");
                    }

                    if (!StringUtils.isEmpty(selectedDepartment))
                    {
                        selectedDepartment = selectedDepartment.replaceAll("'",
                                "''");
                        String selectedDepartment2 = selectedDepartment
                                .replace("&amp;", "&");
                        hql.append(" and (cca.department='")
                                .append(selectedDepartment)
                                .append("' or cca.department='"
                                        + selectedDepartment2 + "')");
                    }

                    hql.append(" order by cca.").append(targetLevel).append(" asc");

                    List data = PortalUtils.getHt().find(hql.toString());

                    if ("activity".equals(targetLevel))
                    {
                        JSONArray aData = new JSONArray();
                        for (Iterator i = data.iterator(); i.hasNext();)
                        {
                            Object[] d = (Object[]) i.next();
                            JSONObject o = new JSONObject();
                            o.put("activityId", d[0]);
                            o.put("activity", d[1]);
                            o.put("status", d[2]);
                            aData.put(o);
                        }
                        ret.put("nodes", aData);
                    }
                    else
                    {
                        ret.put("nodes", data);
                    }
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return ret;
    }

    /**
     * Returns a 20 character random String
     */
    public static String generateLookupCode()
    {

        String rnd = "";
        do
        {
            rnd += String.valueOf(Math.random()).substring(2);
        }
        while (rnd.length() <= 20);
        return rnd.substring(0, 20);
    }

    public static CCPositionLearningOutcome getPositionLearningOutcome(CCPosition p,
            CCLearningOutcome lo)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        List<CCPositionLearningOutcome> pla = ht.find(
                "from CCPositionLearningOutcome c where c.position=? and c.learningOutcome=? order by c.learningOutcome.name",
                new Object[] { p, lo });

        CCPositionLearningOutcome pl = null;

        if (!pla.isEmpty())
        {
            pl = pla.get(0);
        }
        return pl;
    }

    public static CCPositionLearningOutcomeStudent getPositionLearningOutcomeStudent(
            CCRecordPosition rp, CCLearningOutcome cla)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        List<CCPositionLearningOutcomeStudent> pls = ht.find(
                "from CCPositionLearningOutcomeStudent pls where pls.recordPosition=? and pls.learningOutcome=? order by pls.learningOutcome.name",
                new Object[] { rp, cla });

        CCPositionLearningOutcomeStudent pl = null;

        if (!pls.isEmpty())
        {
            pl = pls.get(0);
        }
        return pl;
    }

    public static CCPosition getPosition(HttpServletRequest request)
    {
        CCPosition position = null;

        if (request.getAttribute("positionId") != null)
        {
            position = (CCPosition) PortalUtils.getHt().load(CCPosition.class,
                    (Integer) request.getAttribute("positionId"));
        }
        else if (StringUtils.isInteger(request.getParameter("posId")))
        {
            position = (CCPosition) PortalUtils.getHt().load(CCPosition.class,
                    Integer.valueOf(request.getParameter("posId")));
        }
        else if (StringUtils.isInteger(request.getParameter("positionId")))
        {
            position = (CCPosition) PortalUtils.getHt().load(CCPosition.class,
                    Integer.valueOf(request.getParameter("positionId")));
        }

        return position;
    }

    static CCActivity getActivityFromSelector(CCModule module,
            HttpServletRequest request)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        CCActivity ret = null;

        String finalActivityId = request.getParameter("finalActivityId");

        if (StringUtils.isInteger(finalActivityId))
        {
            ret = (CCActivity) ht.load(CCActivity.class,
                    Integer.valueOf(finalActivityId));
        }

        if (ret == null)
        {
            Integer periodId = Integer.valueOf(request.getParameter("period"));

            String category = "category";
            String organization = "organization";
            String department = "department";
            String activity = request.getParameter("activity");

            String finalCategory = "category";
            String finalOrganization = "organization";
            String finalDepartment = "department";
            String finalActivity = request.getParameter("finalActivity");

            boolean newCategory = true;
            boolean newOrganization = true;
            boolean newDepartment = true;
            boolean newActivity = true;

            if (module.isShowLevelCategory())
            {
                category = request.getParameter("category");
                finalCategory = request.getParameter("finalCategory");

                if (StringUtils.isEmpty(finalCategory))
                {
                    finalCategory = category;
                }

                newCategory = StringUtils.isEmpty(category)
                        && !finalCategory.equals(category);
            }

            if (module.isShowLevelOrganization())
            {
                organization = request.getParameter("organization");
                finalOrganization = request.getParameter("finalOrganization");

                if (StringUtils.isEmpty(finalOrganization))
                {
                    finalOrganization = organization;
                }

                newOrganization = StringUtils.isEmpty(organization)
                        && !finalOrganization.equals(organization);
            }

            if (module.isShowLevelDepartment())
            {
                department = request.getParameter("department");
                finalDepartment = request.getParameter("finalDepartment");

                if (StringUtils.isEmpty(finalDepartment))
                {
                    finalDepartment = department;
                }

                newDepartment = StringUtils.isEmpty(department)
                        && !finalDepartment.equals(department);
            }

            if (StringUtils.isEmpty(finalActivity))
            {
                finalActivity = activity;
            }
            newActivity = StringUtils.isEmpty(activity)
                    || !finalActivity.equals(activity);

            if (newCategory || newOrganization || newDepartment || newActivity)
            {
                ret = new CCActivity();
                ret.setCategory(finalCategory);
                ret.setOrganization(finalOrganization);
                ret.setDepartment(finalDepartment);
                ret.setActivity(finalActivity);
                ret.setCreatedOn(new Date());
                ret.setCreatedBy(PortalUtils.getUserLoggedIn(request));
                ret.setUpdatedBy(PortalUtils.getUserLoggedIn(request));
                ret.setUpdatedOn(new Date());
                ret.setCurrentOwner(PortalUtils.getUserLoggedIn(request));

                if (periodId.intValue() != -1)
                {
                    ret.setPeriod((CCPeriod) ht.load(CCPeriod.class, periodId));
                    List<CCActivity> foundActivities = ht.find(
                            "from CCActivity a where a.category = ? and a.organization = ? and a.department = ? and a.activity = ? and a.period.id = ?",
                            new Object[] { finalCategory, finalOrganization,
                                    finalDepartment, finalActivity, periodId });

                    if (!foundActivities.isEmpty())
                    {
                        ret = foundActivities.get(0);
                    }
                    else
                    {
                        ht.save(ret);
                    }
                }
            }
            else
            {
                List<CCActivity> activities = ht.find(
                        "from CCActivity a where a.category = ? and a.organization = ? and a.department = ? and a.activity = ? and a.period.id = ?",
                        new Object[] { finalCategory, finalOrganization,
                                finalDepartment, finalActivity, periodId });

                if (!activities.isEmpty())
                {
                    ret = activities.get(0);
                }

            }
        }

        return ret;
    }

    static CCActivity findOrCreatePeriodActivity(CCPeriod period, String category,
            String organization, String department, String activity,
            UserDetailsImpl creator)
    {
        CCActivity a = null;

        try
        {
            if (period != null)
            {
                List<CCActivity> l = PortalUtils.getHt()
                        .find("from CCActivity a where a.period=? "
                                + " and a.category=? and a.organization=? "
                                + " and a.department=? and a.activity=?",
                                new Object[] { period, category, organization,
                                        department, activity });

                if (l.size() == 1)
                {
                    a = l.get(0);
                }
                else
                {
                    a = new CCActivity();
                    a.setPeriod(period);
                    a.setCategory(category);
                    a.setOrganization(organization);
                    a.setDepartment(department);
                    a.setActivity(activity);
                    a.setCreatedOn(new Date());
                    a.setCreatedBy(creator);
                    a.setCurrentOwner(creator);
                    PortalUtils.getHt().save(a);
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return a;
    }

    static CCDirectors getCCDirectorsTeam(HttpServletRequest request)
    {
        CCDirectors directors = null;

        try
        {
            Integer id = (Integer) (!StringUtils
                    .isEmpty(request.getParameter("directorsId"))
                            ? Integer.parseInt(request.getParameter("directorsId"))
                            : request.getAttribute("directorsId"));

            directors = (CCDirectors) PortalUtils.getHt().load(CCDirectors.class,
                    id);
        }
        catch (Exception e)
        {
            // e.printStackTrace();
        }

        return directors;
    }

    static List getCCDirectors(CCDirectors directors, String selectClause)
    {
        List members = PortalUtils.getHt().find(selectClause
                + " from CCDirector director where director.directors=? order by director.member.lastName, director.member.preferredFirstName, director.member.id ",
                directors);
        return members;
    }

    static List getCCDirectors(CCDirectors directors)
    {
        return getCCDirectors(directors, "");
    }

    static void saveCCOutcomes(HttpServletRequest request, CCModule module,
            CCPosition p)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        if (module.isTrackLearningOutcomes())
        {
            if (module.isTrackAchievements()
                    && request.getParameterValues("paa") != null)
            {
                // CREATE THE ANTICIPATED ACHIEVEMENTS FOR THIS
                // POSITION...
                String[] jpaa = request.getParameterValues("paa");
                for (int i = 0; i < jpaa.length; i++)
                {
                    CCAchievement cca = (CCAchievement) ht.load(CCAchievement.class,
                            Integer.valueOf(jpaa[i]));

                    if (cca.getLearningOutcome() != null)
                    {
                        CCPositionLearningOutcome cpla = CCHelper
                                .getPositionLearningOutcome(p,
                                        cca.getLearningOutcome());

                        if (cpla == null)
                        {
                            cpla = new CCPositionLearningOutcome();
                            cpla.setPosition(p);
                            cpla.setLearningOutcome(cca.getLearningOutcome());
                            ht.save(cpla);
                        }
                    }

                    CCPositionAchievementAdmin paa = new CCPositionAchievementAdmin();
                    paa.setPosition(p);
                    paa.setAchievement(cca);
                    ht.save(paa);
                }
            }
            else if (!module.isTrackAchievements()
                    && request.getParameterValues("pla") != null)
            {
                String[] jpla = request.getParameterValues("pla");
                if (jpla != null)
                {
                    for (int i = 0; i < jpla.length; i++)
                    {
                        CCLearningOutcome cclo = (CCLearningOutcome) ht.load(
                                CCLearningOutcome.class, Integer.valueOf(jpla[i]));
                        if (cclo != null)
                        {
                            CCPositionLearningOutcome cpla = CCHelper
                                    .getPositionLearningOutcome(p, cclo);
                            if (cpla == null)
                            {
                                cpla = new CCPositionLearningOutcome();
                                cpla.setPosition(p);
                                cpla.setLearningOutcome(cclo);
                                ht.save(cpla);
                            }
                        }
                    }
                }
            }
        }

    }

    static UserDetailsImpl getValidator(HttpServletRequest request)
    {
        UserDetailsImpl validator = null;

        if (request.getAttribute("auId") == null)
        {
            try
            {
                validator = NHelper.getUser(request);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
        else
        {
            UserDetailsImpl au = (UserDetailsImpl) PortalUtils.getHt().load(
                    UserDetailsImpl.class, (Integer) request.getAttribute("auId"));
            if (au != null)
            {
                validator = au;
            }
        }
        return validator;
    }

    static List<Object[]> lookupUsers(String searchTerm, String primaryGroup)
    {
        searchTerm = searchTerm.trim();
        List<Object> params = new ArrayList<>();

        return searchTerm.length() > 0 ? PortalUtils.getHt().find(
                LookupUtils.getUserLookupHQL(searchTerm, primaryGroup, "", params),
                params.toArray()) : new ArrayList<>();
    }

    static ModelAndView lookupPositions(HttpServletRequest request, CCModule module)
            throws Exception
    {
        String positionSearchTerm = request.getParameter("term");
        boolean onlyEnabled = request.getParameter("onlyEnabled") != null
                && request.getParameter("onlyEnabled").equalsIgnoreCase("true");
        boolean onlyApproved = request.getParameter("onlyApproved") != null
                && request.getParameter("onlyApproved").equalsIgnoreCase("true");

        List<Object[]> positionDatas = getPositionDatas(positionSearchTerm,
                onlyEnabled, onlyApproved, module,
                PortalUtils.getUserLoggedIn(request));

        JSONArray ret = new JSONArray();

        JSONObject result = null;
        String label = null;
        for (Object[] user : positionDatas)
        {
            result = new JSONObject();
            // userId
            result.put("id", user[0]);
            // userLogin
            result.put("activityId", user[6]);
            label = user[3] + ": " + user[2] + " - " + user[1] + "";
            result.put("label", label);
            result.put("value", label);
            ret.put(result);
        }

        return NHelper.AJAXResponse(ret.toString());
    }

    static List<CCPeriod> getActivePeriods(CCModule module, Locale locale)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        return ht.find("from CCPeriod p where p.module=? and p.active=true order by p."
                + (LocaleUtils.isL1(locale) ? "name" : "l2Name"), module);
    }

    static List<Object[]> getPositionDatas(String searchTerm, boolean onlyEnabled,
            boolean onlyApproved, CCModule module, UserDetailsImpl userLoggedIn)
    {
        QueryBuilder q = new QueryBuilder();
        q.append(
                "select p.id, p.title, p.activity.activity, p.activity.period.name, p.activity.organization, p.activity.department, p.activity.id, p.activity.category ");
        getPositionDatasQuery(searchTerm, onlyEnabled, onlyApproved, module,
                userLoggedIn, q, false);
        q.append(" order by p.title, p.activity.activity");

        return searchTerm.length() > 0 ? CollectionUtils.distinctObjectArrayList(
                PortalUtils.getHt().find(q), 0) : new ArrayList<Object[]>();
    }

    static JSONObject getPositionDatasForDataViewer(boolean onlyEnabled,
            boolean onlyApproved, boolean shortlistOnly, CCModule module,
            UserDetailsImpl userLoggedIn, DataViewerState dvState) throws Exception
    {
        JSONObject resp = new JSONObject();
        CCRecord record = CCHelper.getCCRecord(module, userLoggedIn);
        QueryBuilder countQ = new QueryBuilder();
        countQ.append("select count(p.id) ");
        getPositionDatasQuery(dvState.getKeyword(), onlyEnabled, onlyApproved,
                module, userLoggedIn, countQ, true);

        if (shortlistOnly)
        {
            appendShortlistClause(record, countQ);
        }

        handlePositionDatasDataViewerFilters(countQ, dvState, resp);

        List<Integer> count = PortalUtils.getHt().find(countQ);

        if (count.size() > 0 && count.get(0).intValue() > 0)
        {
            QueryBuilder q = new QueryBuilder();

            q.append(
                    "select p.id, p.title, p.activity.activity, p.activity.period.name, p.activity.organization, p.activity.department, p.activity.id, p.activity.category ");
            getPositionDatasQuery(dvState.getKeyword(), onlyEnabled, onlyApproved,
                    module, userLoggedIn, q, true);

            Map<String, String> dataViewerKeyReplacements = CollectionUtils
                    .<String, String> mapBuilder().put("position", "p.title")
                    .put("activity", "p.activity.activity")
                    .put("department", "p.activity.department")
                    .put("category", "p.activity.category")
                    .put("organization", "p.activity.organization")
                    .put("period", "p.activity.period.name").build();

            if (shortlistOnly)
            {
                appendShortlistClause(record, q);
            }

            handlePositionDatasDataViewerFilters(q, dvState, resp);

            String orderByClause = dvState.getSortForQuery(
                    dataViewerKeyReplacements.keySet().toArray(new String[] {}),
                    dataViewerKeyReplacements);

            q.append(orderByClause);

            List<Object[]> positions = PortalUtils.getHt().find(q,
                    (dvState.getPage() - 1) * dvState.getItemsPerPage(),
                    dvState.getItemsPerPage());

            List<Integer> positionIds = positions.stream()
                    .map(row -> (Integer) row[0]).collect(Collectors.toList());

            String positionIdsInClause = DBUtils.buildInClause(positionIds);

            List<Integer> shortlist = shortlistOnly ? new ArrayList<Integer>()
                    : PortalUtils.getHt().find(
                            "select rpc.position.id from CCRecordPositionShortlist rpc where rpc.record=? and rpc.position.id in "
                                    + positionIdsInClause,
                            record);

            List<Integer> addedPositions = PortalUtils.getHt().find(
                    "select rp.position.id from CCRecordPosition rp where rp.record = ? and rp.position in "
                            + positionIdsInClause,
                    record);

            JSONArray results = new JSONArray();

            for (Object[] position : positions)
            {
                JSONObject data = new DataViewerDataBuilder()//
                        .id(position[0])//
                        .entry("positionId", position[0])//
                        .entry("activityId", position[6])//
                        .entry("position", position[1])//
                        .entry("activity", position[2])//
                        .entry("department", position[5])//
                        .entry("category", position[7])//
                        .entry("organization", position[4])//
                        .entry("period", position[3])//
                        .entry("shortlisted",
                                shortlistOnly ? true
                                        : shortlist.contains(position[0]))//
                        .entry("hasPosition", addedPositions.contains(position[0]))//
                        .build();

                results.put(data);
            }

            resp.put("data", results);
            resp.put("page", dvState.getPage());
            resp.put("totalResults", count.get(0));
        }
        else
        {
            resp.put("data", new JSONArray());
            resp.put("page", 1);
            resp.put("totalResults", 0);
        }

        return resp;
    }

    private static void appendShortlistClause(CCRecord record, QueryBuilder countQ)
    {
        countQ.append(
                " and p.id in (select rpc.position.id from CCRecordPositionShortlist rpc where rpc.record=?) ",
                record);
    }

    private static void handlePositionDatasDataViewerFilters(QueryBuilder q,
            DataViewerState dvState, JSONObject resp)
    {
        JSONObject filters = dvState.getJsonFilters();
        if (filters != null)
        {
            boolean corrected = correctFilters(filters);

            try
            {
                if (corrected)
                {
                    resp.put("filters", filters);
                }

                if (filters.has("period"))
                {
                    q.append(" and p.activity.period.id = ?",
                            filters.getJSONObject("period").getJSONArray("value")
                                    .getString(0));
                }

                if (filters.has("category"))
                {
                    q.append(" and p.activity.category = ?",
                            filters.getJSONObject("category").getJSONArray("value")
                                    .getString(0));
                }

                if (filters.has("organization"))
                {
                    q.append(" and p.activity.organization = ?",
                            filters.getJSONObject("organization")
                                    .getJSONArray("value")
                                    .getString(0));
                }

                if (filters.has("department"))
                {
                    q.append(" and p.activity.department = ?",
                            filters.getJSONObject("department")
                                    .getJSONArray("value")
                                    .getString(0));
                }

                if (filters.has("activity"))
                {
                    q.append(" and p.activity.id = ?",
                            filters.getJSONObject("activity")
                                    .getJSONArray("value").getString(0));
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
    }

    /**
     * This goes through the filters that came from the client and checks to see
     * if it properly follows the hiearchy that CC Programs come from
     * 
     * @param filters
     *            filters from dataviewer. This will be modified if it's not
     *            correct
     * @return boolean that says if the filters object was modified
     */
    private static boolean correctFilters(JSONObject filters)
    {
        JSONObject ret = null;
        // The hierarchy rules that need to be followed. If one comes in empty,
        // the rest should also be empty
        String[] fillOrder = new String[] { "period", "category", "organization",
                "department", "activity" };
        ;
        boolean hasBeenCorrected = false;
        boolean shouldHaveEndedByNow = false;

        for (int idx = 0; idx < fillOrder.length; idx++)
        {

            if (!(filters.has(fillOrder[idx]) || shouldHaveEndedByNow))
            {
                // Once there's a missing filter, we no longer want the rest of
                // the potential filters coming in
                shouldHaveEndedByNow = true;
            }
            else if (filters.has(fillOrder[idx]) && shouldHaveEndedByNow)
            {
                // filters have been found after an empty filter, the filter now
                // needs to be corrected
                hasBeenCorrected = true;
                filters.remove(fillOrder[idx]);
            }
        }

        return hasBeenCorrected;
    }

    private static void getPositionDatasQuery(String searchTerm,
            boolean onlyEnabled, boolean onlyApproved, CCModule module,
            UserDetailsImpl userLoggedIn, QueryBuilder q, boolean fromDataViewer)
    {
        String userTypeHQL = "";

        if (isAdmin(userLoggedIn, module)
                && "admin".equals(getCurrentUserRole(module, userLoggedIn)))
        {
            userTypeHQL = " and p.activity.period.active=true ";
        }
        else if (isValidator(userLoggedIn)
                && "validator".equals(getCurrentUserRole(module, userLoggedIn)))
        {
            userTypeHQL = " and p.activity.period.activeVal=true ";
        }
        else if ((userLoggedIn.isStudent() || isAlumni(userLoggedIn))
                && (CC_ROLE_STUDENT.equals(getCurrentUserRole(module, userLoggedIn))
                        || CC_ROLE_ALUMNI
                                .equals(getCurrentUserRole(module, userLoggedIn))))
        {
            userTypeHQL = " and p.activity.period.activeStudent=true and p.status=1 and p.enabled=true ";
        }

        q.append(
                " from CCPosition p join p.activity join p.activity.period where p.activity.status=1 and p.activity.period.module=? ",
                module);
        List params = new ArrayList();
        if (fromDataViewer && !StringUtils.isEmpty(searchTerm) || !fromDataViewer)
        {
            q.append(
                    " and " + LookupUtils.getCCPositionSearchTermWhereClause(
                            searchTerm, "p", null, params, false),
                    params.toArray());
        }

        q.append(userTypeHQL);
        q.appendIf(onlyEnabled, " and p.enabled=true");
        q.appendIf(onlyApproved, " and p.status=?",
                CCPosition.APPROVAL_STATUS_APPROVED);
    }

    /**
     * @param node
     *            Required. Permitted values: category, organization,
     *            department, activity
     * @param nodeValue
     *            Optional.
     * @param periodId
     *            Required.
     * @param ccModule
     *            Required.
     * @param ht
     *            Required.
     * @return If <nodeValue> == NULL, then a List of all <node>'s of
     *         <periodId>. If <nodeValue> != NULL, then a List of all 'sub
     *         nodes' of <nodeValue> of <periodId>.
     */
    static List<String> getActivityNodesByPeriodId(String node, String nodeValue,
            Integer periodId, CCModule ccModule)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        List nodes = new ArrayList();
        if (!StringUtils.isEmpty(node) && !node.equals("activity"))
        {
            if (nodeValue == null)
            {
                nodes = CollectionUtils.distinctList(ht.find("select a." + node
                        + " from CCActivity a where a.period.id = ? "
                        + " and a.period.module = ? " + " order by a." + node,
                        new Object[] { periodId, ccModule }));
            }
            else
            {
                String subNode = null;
                if (node.equals("category"))
                {
                    subNode = "organization";
                }
                else if (node.equals("organization"))
                {
                    subNode = "department";
                }
                else if (node.equals("department"))
                {
                    subNode = "activity";
                }

                nodeValue = nodeValue.replaceAll("'", "''");

                nodes = CollectionUtils.distinctList(ht.find(
                        "select a." + subNode + " from CCActivity a where a." + node
                                + "='" + nodeValue
                                + "' and a.period.id = ? and a.period.module = ?"
                                + " order by a." + subNode,
                        new Object[] { periodId, ccModule }));
            }
        }
        return nodes;
    }

    /**
     * @param node
     *            Required. Permitted values: category, organization,
     *            department, activity
     * @param nodeValue
     *            Optional.
     * @param ccModule
     *            Required.
     * @param ht
     *            Required.
     * @return - If <nodeValue> == NULL, then a List of all <node>'s. If
     *         <nodeValue> != NULL, then a List of all 'sub nodes' of
     *         <nodeValue>.
     */
    static List<String> getActivityNodesForAllActivePeriods(String node,
            String nodeValue, CCModule ccModule)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        List nodes = new ArrayList();
        if (!StringUtils.isEmpty(node) && !node.equals("activity"))
        {
            if (nodeValue == null)
            {
                nodes = CollectionUtils.distinctList(ht.find("select a." + node
                        + " from CCActivity a where a.period.active = true "
                        + " and a.period.module = ? " + " order by a." + node,
                        ccModule));
            }
            else
            {
                String subNode = null;
                if (node.equals("category"))
                {
                    subNode = "organization";
                }
                else if (node.equals("organization"))
                {
                    subNode = "department";
                }
                else if (node.equals("department"))
                {
                    subNode = "activity";
                }

                nodeValue = nodeValue.replaceAll("'", "''");

                nodes = CollectionUtils.distinctList(ht.find("select a." + subNode
                        + " from CCActivity a where a." + node + "='" + nodeValue
                        + "' and a.period.active = true and a.period.module = ?"
                        + " order by a." + subNode, ccModule));
            }
        }
        return nodes;
    }

    static void loadPositions(ModelAndView mv, HttpServletRequest request,
            CCModule module)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        String periodId = request.getParameter("periodId");
        String category = request.getParameter("category");
        String organization = request.getParameter("organization");
        String department = request.getParameter("department");
        String activity = request.getParameter("activity");
        String activityId = request.getParameter("activityId");

        StringBuilder hql = new StringBuilder();

        if (StringUtils.isInteger(activityId))
        {
            hql.append("from CCPosition p where p.activity.id=").append(activityId);
        }
        else
        {
            hql.append(
                    "from CCPosition p where p.activity.period.active=true and p.activity.period.module.id=")
                    .append(module.getId());

            if (StringUtils.isInteger(periodId))
            {
                CCPeriod period = (CCPeriod) ht.load(CCPeriod.class,
                        Integer.valueOf(periodId));

                if (period != null)
                {
                    mv.addObject("period", period);
                    hql.append(" and p.activity.period.id=").append(periodId);
                }
            }

            if (module.isShowLevelCategory() && !StringUtils.isEmpty(category))
            {
                hql.append(" and p.activity.category='")
                        .append(category.replaceAll("'", "''")).append("'");
            }

            if (module.isShowLevelOrganization()
                    && !StringUtils.isEmpty(organization))
            {
                hql.append(" and p.activity.organization='")
                        .append(organization.replaceAll("'", "''")).append("'");
            }

            if (module.isShowLevelDepartment() && !StringUtils.isEmpty(department))
            {
                hql.append(" and p.activity.department='")
                        .append(department.replaceAll("'", "''")).append("'");
            }

            hql.append(" and p.activity.activity='")
                    .append(activity.replaceAll("'", "''")).append("'");
        }

        List positions = ht.find(hql.toString());

        mv.addObject("category", category);
        mv.addObject("organization", organization);
        mv.addObject("department", department);
        mv.addObject("activity", activity);
        mv.addObject("positions", positions);
    }

    static ModelAndView lookupActivities(HttpServletRequest request,
            HttpServletResponse response, CCModule module)
    {
        ModelAndView mv = new ModelAndView("cc/cc_ajaxActivities");
        mv.addObject("activitySearchTerm",
                request.getParameter("activitySearchTerm"));
        mv.addObject("activityDatas", getActivityDatas(
                request.getParameter("activitySearchTerm"), request, module));
        mv.addObject(SiteController.SHORT_CIRCUIT_ATTRIBUTE_NAME, Boolean.TRUE);
        return mv;
    }

    static ModelAndView lookupStudents(HttpServletRequest request,
            HttpServletResponse response, CCModule module)
    {
        ModelAndView mv = new ModelAndView("cc/cc_ajaxStudents");
        mv.addObject("studentSearchTerm",
                request.getParameter("studentSearchTerm"));
        mv.addObject("studentDatas", getStudentDatas(
                request.getParameter("studentSearchTerm"), request, module));
        mv.addObject(SiteController.SHORT_CIRCUIT_ATTRIBUTE_NAME, Boolean.TRUE);
        return mv;
    }

    static ModelAndView lookupCombined(HttpServletRequest request,
            HttpServletResponse response, CCModule module) throws Exception
    {
        return runCombinedSearch(request, module);
    }

    static ModelAndView lookupNewStudents(HttpServletRequest request,
            HttpServletResponse response, CCModule module) throws Exception
    {
        return runStudentSearch(request, module);
    }

    static ModelAndView lookupNewValidators(HttpServletRequest request,
            HttpServletResponse response)
    {
        return runValidatorsSearch(request);
    }

    static ModelAndView lookupNewActivities(HttpServletRequest request,
            HttpServletResponse response, CCModule module)
    {
        return runActivitySearch(request, module);
    }

    static ModelAndView lookupNewPositions(HttpServletRequest request,
            HttpServletResponse response, CCModule module)
    {
        boolean onlyEnabled = request.getParameter("onlyEnabled") != null
                && request.getParameter("onlyEnabled").equalsIgnoreCase("true");

        return runPositionSearch(request, module, onlyEnabled);
    }

    static ModelAndView lookupNewUsers(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        return AcrmHelper.lookupUsers(request.getParameter("term"), null, 20,
                false);
    }

    static ModelAndView lookupNewUsers(HttpServletRequest request,
            HttpServletResponse response, int idIndex) throws Exception
    {
        return AcrmHelper.lookupUsers(request.getParameter("term"), null, 20,
                idIndex, false, null);
    }

    static ModelAndView lookupValidators(HttpServletRequest request,
            HttpServletResponse response, CCModule module)
    {
        ModelAndView mv = new ModelAndView("cc/cc_ajaxValidators");
        mv.addObject("validatorSearchTerm",
                request.getParameter("validatorSearchTerm"));
        mv.addObject("validatorDatas", getValidatorDatas(
                request.getParameter("validatorSearchTerm"), request, module));
        mv.addObject(SiteController.SHORT_CIRCUIT_ATTRIBUTE_NAME, Boolean.TRUE);
        return mv;
    }

    private static List getActivityDatas(String searchTerm,
            HttpServletRequest request, CCModule module)
    {
        List params = Lists.newArrayList(module);
        return searchTerm.length() > 0 ? PortalUtils.getHt().find(
                "select a.id, a.activity, a.period.name from CCActivity a join a.period where a.period.active=true and a.period.module=? and "
                        + LookupUtils.getCCActivitySearchTermWhereClause(searchTerm,
                                "a", params)
                        + " order by a.activity, a.department ",
                params.toArray()) : new ArrayList();
    }

    private static List getStudentDatas(String searchTerm,
            HttpServletRequest request, CCModule module)
    {

        CommandQueryTemplate ht = PortalUtils.getHt();
        return searchTerm.length() > 0 ? CollectionUtils.distinctObjectArrayList(ht
                .find("select ccr.owner.id, ccr.owner.preferredFirstName, ccr.owner.lastName, ccr.owner.username from CCRecord ccr join ccr.owner where "
                        + LookupUtils.getUserSearchTermHqlWhereClause(searchTerm,
                                "ccr.owner", null)
                        + " order by ccr.owner.lastName, ccr.owner.preferredFirstName "),
                0) : new ArrayList();
    }

    private static List getValidatorDatas(String searchTerm,
            HttpServletRequest request, CCModule module)
    {

        CommandQueryTemplate ht = PortalUtils.getHt();
        return searchTerm.length() > 0 ? CollectionUtils.distinctObjectArrayList(ht
                .find("select u.id, u.preferredFirstName, u.lastName, u.username from UserDetailsImpl u join u.groups as pgroups where pgroups.name='"
                        + PersonGroupHelper.CC_VALIDATOR + "' and "
                        + LookupUtils.getUserSearchTermHqlWhereClause(searchTerm,
                                "u", null)
                        + " order by u.lastName, u.preferredFirstName "),
                0) : new ArrayList();
    }

    /**
     * Returns the number of CCPositions with the specified title within the
     * specified CCActivity.
     */
    static int getPositionCount(CCActivity ccActivity, String positionTitle)
    {
        return (Integer) PortalUtils.getHt().find(
                "select count(p.id) from CCPosition p where p.activity=? and p.title=?",
                new Object[] { ccActivity, positionTitle }).get(0);
    }

    static CCPosition getCCPosition(HttpServletRequest request)
    {
        return getCCPosition(request, false);
    }

    static CCPosition getCCPosition(HttpServletRequest request,
            boolean overrideSecurity)
    {
        Integer positionId = null;

        if (StringUtils.isInteger(request.getParameter("positionId")))
        {
            positionId = Integer.valueOf(request.getParameter("positionId"));
        }
        else if (StringUtils.isInteger(request.getParameter("posId")))
        {
            positionId = Integer.valueOf(request.getParameter("posId"));
        }
        else if (request.getAttribute("positionId") != null)
        {
            positionId = ((Integer) request.getAttribute("positionId"));
        }

        CCPosition position = null;
        if (positionId != null)
        {
            position = (CCPosition) PortalUtils.getHt().load(CCPosition.class,
                    positionId);
        }
        if (!overrideSecurity && !isPositionValidForUser(position,
                PortalUtils.getUserLoggedIn(request), request))
        {
            position = null;
        }
        return position;
    }

    static int getRecordCount(CCActivity a)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        int c = ((Integer) ht.find(
                "select count(rp) from CCRecordPosition rp where rp.position.activity=? and rp.status='Approved'",
                new Object[] { a }).get(0)).intValue();
        return c;
    }

    // Unused Method
    static List getAchievements(CCModule ccModule, Locale locale)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        return ht.find(
                "from CCAchievement a where a.learningOutcome.module=? order by a."
                        + (LocaleUtils.isL1(locale) ? "achievement"
                                : "l2Achievement"),
                ccModule);
    }

    static List<CCAchievement> getActiveAchievements(CCModule ccModule,
            Locale locale)
    {
        return PortalUtils.getHt().find(
                "from CCAchievement a where a.learningOutcome.module=? AND a.active=true order by a."
                        + (LocaleUtils.isL1(locale) ? "achievement"
                                : "l2Achievement"),
                ccModule);
    }

    static List<CCAchievement> getAchievementsByLearningOutcome(
            CCLearningOutcome lo, Locale locale)
    {
        return PortalUtils.getHt()
                .find("from CCAchievement a where a.learningOutcome=? order by a."
                        + (LocaleUtils.isL1(locale) ? "achievement"
                                : "l2Achievement"),
                        lo);
    }

    static CCActivity getCCActivity(HttpServletRequest request)
    {
        return (CCActivity) getEntity(CCActivity.class, "activityId", request);
    }

    static CCPeriod getCCPeriod(HttpServletRequest request)
    {
        return (CCPeriod) getEntity(CCPeriod.class, "periodId", request);
    }

    /**
     * Used for combination search on CC home for Activity Director & Admins
     *
     * @param request
     *            should contain term and boolean status for each search type
     * @param module
     * @return ajax response containing 2d array; in order, each contained array
     *         will represent search results for students, validators,
     *         activities, positions
     * @throws Exception
     */
    private static ModelAndView runCombinedSearch(HttpServletRequest request,
            CCModule module) throws Exception
    {
        // If search type is enabled, do search individually and store data,
        // else store fake array
        // JSP will parse this as a real array
        List content = new ArrayList();

        if (Boolean.parseBoolean(request.getParameter("searchStudents")))
        {
            content.add(runStudentSearch(request, module).getModel().get("ajaxData")
                    .toString());
        }
        else
        {
            content.add("[]");
        }

        if (Boolean.parseBoolean(request.getParameter("searchValidators")))
        {
            content.add(runValidatorsSearch(request).getModel().get("ajaxData")
                    .toString());
        }
        else
        {
            content.add("[]");
        }

        if (Boolean.parseBoolean(request.getParameter("searchActivities")))
        {
            content.add(runActivitySearch(request, module).getModel()
                    .get("ajaxData").toString());
        }
        else
        {
            content.add("[]");
        }

        if (Boolean.parseBoolean(request.getParameter("searchPositions")))
        {
            boolean onlyEnabled = request.getParameter("onlyEnabled") != null
                    && request.getParameter("onlyEnabled").equalsIgnoreCase("true");
            content.add(runPositionSearch(request, module, onlyEnabled).getModel()
                    .get("ajaxData").toString());
        }
        else
        {
            content.add("[]");
        }

        ModelAndView mv = new ModelAndView("ajax");
        mv.addObject(SiteController.SHORT_CIRCUIT_ATTRIBUTE_NAME, Boolean.TRUE);
        mv.addObject("ajaxData", content);

        return mv;
    }

    private static ModelAndView runStudentSearch(HttpServletRequest request,
            CCModule module) throws Exception
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        String searchTerm = request.getParameter("term");
        int limit = DBUtils.getLimit(request, 100);

        String selectClause = "select ccr.owner.preferredFirstName, ccr.owner.lastName, ccr.owner.username, ccr.owner.id, g.name from CCRecord ccr join ccr.owner join ccr.owner.groups as g ";
        String whereClause = " where g.primaryGroup=true and ccr.owner.deleted=false and ccr.module.id = "
                + module.getId().toString() + " and ";
        String orderByClause = " order by ccr.owner.lastName, ccr.owner.preferredFirstName ";
        List srchResults = new ArrayList();

        // whereClause += LookupUtils.getUserSearchTermWhereClause(searchTerm,
        // "ccr.owner", null);

        whereClause += "(ccr.owner.preferredFirstName like ? "
                + "or ccr.owner.lastName like ? "
                + "or ccr.owner.firstAndLastName like ? "
                + "or ccr.owner.emailAddress like ? "
                + "or ccr.owner.username like ? " + "or ccr.owner.company like ?)";

        srchResults = ht.find(selectClause + whereClause + orderByClause,
                searchTerm, searchTerm, searchTerm, searchTerm,
                searchTerm, searchTerm);

        // srchResults = NHelper.distinctList(srchResults);

        // trim search results to the specified limit
        srchResults = limitList(srchResults, limit);

        return NHelper.lookupsAJAXResponse(srchResults, 3, "[0] [1] ([4])");
    }

    private static ModelAndView runValidatorsSearch(HttpServletRequest request)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        String searchTerm = request.getParameter("term");
        int limit = DBUtils.getLimit(request, 100);

        String selectClause = "select u.preferredFirstName, u.lastName, u.username, u.id from UserDetailsImpl u join u.groups as pgroups  ";
        String whereClause = " where u.enabled=true and u.deleted=false and pgroups.name='"
                + PersonGroupHelper.CC_VALIDATOR + "' and ";
        String orderByClause = " order by u.lastName, u.preferredFirstName ";

        whereClause += "(u.preferredFirstName like ? " + "or u.lastName like ? "
                + "or u.firstAndLastName like ? " + "or u.emailAddress like ? "
                + "or u.username like ? " + "or u.company like ?) ";

        List srchResults = ht.find(selectClause + whereClause + orderByClause,
                new Object[] { searchTerm, searchTerm, searchTerm, searchTerm,
                        searchTerm, searchTerm });

        // trim search results to the specified limit
        srchResults = limitList(srchResults, limit);

        return NHelper.lookupsAJAXResponse(srchResults, 3, "[0] [1] ([2])");
    }

    private static ModelAndView runActivitySearch(HttpServletRequest request,
            CCModule module)
    {
        final CommandQueryTemplate ht = PortalUtils.getHt();
        String searchTerm = request.getParameter("term").replaceAll("'", "''");

        String validActivities = getValidActivitiesForUserHQL(
                PortalUtils.getUserLoggedIn(request), module, request);

        final String hql = "select a.id, a.activity, a.period.name from CCActivity a join a.period join a.period.module where a.period.active=true and a.period.module.id= "
                + module.getId() + " and (a.activity like '%" + searchTerm
                + "%' or a.department like '%" + searchTerm
                + "%' or a.organization like '%" + searchTerm
                + "%' or a.category like '%" + searchTerm + "%') and a.id in "
                + validActivities + " order by a.activity, a.department ";

        List<Object[]> results = ht.<List<Object[]>>executeFind(new QueryCallback()
        {
            @Override
            public Object doInHibernate(EntityManager session)
                    throws HibernateException {

                Query query = session.createQuery(hql);
                query.setMaxResults(15);
                return query.getResultList();
            }
        });

        return NHelper.lookupsAJAXResponse(results, 0, "[2] - <b>[1]</b>");
    }

    private static ModelAndView runPositionSearch(HttpServletRequest request,
            CCModule module, boolean onlyEnabled)
    {
        final CommandQueryTemplate ht = PortalUtils.getHt();
        String searchTerm = request.getParameter("term").replaceAll("'", "''");

        String validActivities = getValidActivitiesForUserHQL(
                PortalUtils.getUserLoggedIn(request), module, request);

        final String hql = "select p.id, p.title, p.activity.activity, p.activity.period.name, "
                + "p.activity.organization, p.activity.department "
                + "from CCPosition p where p.activity.period.module.id="
                + module.getId() + " and p.activity.period.active=true "
                + "and (p.title like '%" + searchTerm
                + "%' or p.activity.activity like '%" + searchTerm
                + "%' or p.activity.department like '%" + searchTerm + "%' "
                + "or p.activity.organization like '%" + searchTerm
                + "%' or p.activity.category like '%" + searchTerm + "%') "
                + (onlyEnabled ? "and p.enabled=true " : " ")
                + " and p.activity.id in " + validActivities
                + " order by p.title, p.activity.activity ";

        List<Object[]> results = ht.<List<Object[]>>executeFind(new QueryCallback()
        {
            @Override
            public Object doInHibernate(EntityManager session)
                    throws HibernateException
            {
                Query query = ht.createQuery(session, hql);
                query.setFirstResult(0);
                query.setMaxResults(15);
                return query.getResultList();
            }
        });

        List orgs = CollectionUtils.distinctObjectArrayList(results, 0);

        return NHelper.lookupsAJAXResponse(orgs, 0, "[3] - [2] - <b>[1]</b>");
    }

    private static List limitList(List originalList, int numberOfRecords)
    {
        List ret = originalList;
        if (originalList.size() > numberOfRecords)
        {
            ret = ret.subList(0, numberOfRecords);
        }
        return ret;
    }

    static ModelAndView generateCCReportPDF(HttpServletRequest request, String auId,
            DataSource dataSource)
    {
        ModelAndView mv = null;
        Map<String, String> parameters = new HashMap<>();
        parameters.put("ReportTitle", "Official Co-Curricular Record");
        parameters.put("absoluteWebAppPath",
                PortalUtils.getRealPath("/") + "site\\");

        parameters.put("absoluteWebRootPath", PortalUtils.getRealPath("/"));

        String contextReportPath = PortalConstants.SITE_REPORT_PATH;
        parameters.put("contextReportPath", contextReportPath);
        parameters.put("id", auId);

        String reportXml = "cc\\" + request.getParameter("reportXml");

        String type = "pdf";

        ByteArrayOutputStream output = JasperController.runJasperReport(reportXml,
                type, parameters, dataSource);

        mv = JasperController.getReportDownloadView("ccr_" + auId + ".pdf", output);

        return mv;
    }

    public static void logPrintActivity(UserDetailsImpl userLoggedIn, String auId,
            CCModule module)
    {
        CCRecordPrintLog log = new CCRecordPrintLog();
        log.setUser(userLoggedIn);
        log.setDatePrinted(new Date());
        log.setCcRecord(getCCRecord(module, (UserDetailsImpl) PortalUtils.getHt()
                .load(UserDetailsImpl.class, Integer.valueOf(auId))));

        PortalUtils.getHt().save(log);
    }

    /**
     * Gets active learning outcomes from the database
     *
     * @param locale
     *            TODO
     */
    static List<CCLearningOutcome> getActiveLearningOutcomes(CCModule module,
            Locale locale)
    {
        return PortalUtils.getHt().find(
                "from CCLearningOutcome lo where lo.module=? AND lo.active=true order by "
                        + (LocaleUtils.isL1(locale) ? "lo.name" : "lo.l2Name"),
                module);
    }

    /**
     * Gets all learning outcomes in the database, both active and inactive ones
     *
     * @param locale
     *            TODO
     */
    static List<CCLearningOutcome> getLearningOutcomes(CCModule module,
            Locale locale)
    {
        return PortalUtils.getHt()
                .find("from CCLearningOutcome lo where lo.module=? order by "
                        + (LocaleUtils.isL1(locale) ? "lo.name" : "lo.l2Name"),
                        module);
    }

    // Unused Method
    /**
     * Gets all position learning outcomes in the database that have the given
     * position
     */
    static List<Integer> getPositionLearningOutcomeAdminIds(CCPosition p)
    {
        return PortalUtils.getHt().find(
                "select pla.id from CCPositionLearningOutcome pla where pla.position=?",
                p);
    }

    static List<Integer> getLearningOutcomeIds(CCPosition p)
    {
        return PortalUtils.getHt().find(
                "select pla.learningOutcome.id from CCPositionLearningOutcome pla where pla.position=?",
                p);
    }

    /**
     * Gets all position achievement admin in the database that have the given
     * position
     *
     * @param ht
     *            hibernate template
     * @param ccPosition
     *            current position
     * @return a list of type CCPositionAchievementAdmin
     */
    static List<Integer> getPositionAchievementAdminAchievementIds(CCPosition p)
    {
        return PortalUtils.getHt().find(
                "select paa.achievement.id from CCPositionAchievementAdmin paa where paa.position=? order by paa.achievement.achievement",
                p);
    }

    static List<Integer> getPositionAchievementStudentIds(CCModule m,
            CCRecordPosition rp)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        List ids = new ArrayList();
        if (rp != null && rp.getId() != null)
        {
            if (m.isTrackAchievements())
            {
                ids = ht.find(
                        "select pas.achievement.id from CCPositionAchievementStudent pas where pas.recordPosition=? order by pas.achievement.achievement",
                        rp);
            }
            else
            {
                ids = ht.find(
                        "select pls.learningOutcome.id from CCPositionLearningOutcomeStudent pls where pls.recordPosition=? order by pls.learningOutcome.name",
                        rp);
            }
        }
        else
        {
            ids = new ArrayList();
        }
        return ids;
    }

    static List<Integer> getPositionLearningOutcomeStudentIds(CCModule m,
            CCRecordPosition rp)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        List ids = new ArrayList();
        if (rp != null && rp.getId() != null)
        {
            ids = ht.find(
                    "select pls.learningOutcome.id from CCPositionLearningOutcomeStudent pls where pls.recordPosition=? order by pls.learningOutcome.name",
                    rp);
        }
        return ids;
    }

    /**
     * returns our search model with all parameters set
     *
     * @param module
     * @param request
     *            request coming from admin home when search button is clicked
     * @param ht
     *            Hibernate template
     *
     * @return
     */
    static SearchModel getSearchModel_Records(CCModule module, SiteElement se,
            HttpServletRequest request)
    {
        Locale locale = PortalUtils.getLocale(request);

        SearchModel searchModel = new SearchModel();
        searchModel.setOwnerModuleClassName(se.getContentItemClass().getName());
        searchModel.setOwnerModuleId(se.getContentItemId().toString());
        searchModel.setState(SearchModel.STATE_CONFIG_MASTER);
        searchModel.setCanEmail(true);
        searchModel.setCanEmailCampaign(true);
        searchModel.setCanViewDetails(true);
        searchModel.setCanViewCriteria(true);
        searchModel.setCanExport(true);
        searchModel.setCanSave(true);
        searchModel.setShowQuestionOrder(false);
        searchModel.setAttribute("ccModuleId", module.getId().toString());
        searchModel.setAttribute("positionHqlAlias", "rp.position");

        InteractionHelper.initializeMassAssignButton(searchModel,
                INTERACTION_TYPE.CCRECORD, CCRecord.class, locale,
                PortalUtils.getUserLoggedIn(request));

        CriteriaModel criteriaModel = new CriteriaModel();
        populateCriteriaModel_CCRecord(criteriaModel, module, "r", request);

        Entity master = new Entity("i18n.CCHelper.Studentswi6709774906538507",
                CCRecord.class, "r", "id", criteriaModel, "r.owner.lastName", "asc",
                true);

        master.setStaticWhereHql(" and r.module.id=" + module.getId().toString());

        List<String> emailPaths = new ArrayList<>();
        emailPaths.add("r.owner.emailAddress");
        emailPaths.add("r.owner.alternateEmail");
        emailPaths.add("r.owner.username");
        emailPaths.add("r.owner.userLocale");

        EmailingEmailer ee = new EmailingEmailer(emailPaths, "r.owner.firstName",
                "r.owner.lastName", "r.owner.noCampaignEmails = 0",
                "r.owner.userLocale");

        EmailingModel em = new EmailingModel();
        em.getEmailers().add(ee);
        master.setEmailingModel(em);

        master.setRelationships(getRelationships_CCRecord(module, request));
        searchModel.setMasterEntity(master);

        return searchModel;
    }

    static SearchModel getSearchModel_Validators(CCModule module,
            HttpServletRequest request, String additionalRpHql,
            String additionalPvHql, boolean includeRpHql, boolean includePvHql)
    {
        if (additionalRpHql == null)
        {
            additionalRpHql = "";
        }

        if (additionalPvHql == null)
        {
            additionalPvHql = "";
        }

        SearchModel model = AcrmHelper.getAcrmUserSearchModel(null, request);
        Entity master = model.getMasterEntity();

        CriteriaModel criteriaModel = new CriteriaModel();
        populateCriteriaModel_Validator(criteriaModel, master.getEntityHqlAlias(),
                PortalUtils.getLocale(request));
        master.setCriteriaModel(criteriaModel);

        String staticWhereHql = StringUtils.isEmpty(master.getStaticWhereHql()) ? ""
                : master.getStaticWhereHql();
        String validatedBySubQuery = "select rp.id"
                + " from CCRecordPosition rp join rp.record "
                + " where rp.validatedBy=au and rp.record.module.id="
                + module.getId() + " " + additionalRpHql;

        String validatorSubQuery = "select pv.id"
                + " from CCPositionValidator pv join pv.position.activity.period "
                + " where pv.validator=au"
                + " and pv.position.activity.period.module.id=" + module.getId()
                + " " + additionalPvHql;

        String additionalStaticHql = "";

        if (includeRpHql)
        {
            additionalStaticHql += " exists(" + validatedBySubQuery + ")";
        }

        if (includePvHql)
        {
            additionalStaticHql += (includeRpHql ? " or " : " ") + " exists("
                    + validatorSubQuery + ")";
        }

        master.setStaticWhereHql(
                staticWhereHql + " and (" + additionalStaticHql + ")");
        master.setRelationships(getRelationships_Validator(module, request));

        model.setAttribute("ccModuleId", module.getId().toString());
        model.setAttribute("positionHqlAlias", "pv.position");
        model.setAttribute("activityClause", getValidActivitiesForUserHQL(
                PortalUtils.getUserLoggedIn(request), module, request));
        model.setAttribute("positionId", request.getParameter("positionId"));
        model.setAttribute("periodId", request.getParameter("periodId"));

        return model;
    }

    private static void populateCriteriaModel_Validator(CriteriaModel criteriaModel,
            String hqlPrefix, Locale locale)
    {
        CriteriaGroup group = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.questionText.basicCriteria");
        LinkedList<CriteriaQuestion> questions = new LinkedList<>();

        CriteriaQuestion q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.userStatus");
        q.setQuestionKey(hqlPrefix + ".userStatus");
        q.setQuestionOrder(1);
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        PortalConfig enableNewProspectManagement = PortalConfigHelper
                .getPortalConfig(PortalConfig.ENABLE_NEW_PROSPECT_MANAGEMENT);
        q.setOptionChoices(enableNewProspectManagement != null
                && enableNewProspectManagement.getOrbisValueBoolean()
                        ? AcrmHelper.getUserStatusMapNewProspect(locale)
                        : AcrmHelper.getUserStatusMap(locale));
        q.setOptionIncludeOtherFlag(false);
        q.setCriteriaHeight(100);
        q.setColWidth(100);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setTooltip("i18n.gridSearch.criteriaModel.questionText.User.Name");
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.User.Name");
        q.setQuestionKey(hqlPrefix + ".username");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setVisibleResults(true);
        q.setColWidth(140);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setTooltip("i18n.gridSearch.criteriaModel.questionText.firstName");
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.firstName");
        q.setQuestionKey(hqlPrefix + ".firstName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setVisibleResults(true);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setTooltip("i18n.gridSearch.criteriaModel.questionText.preferredName");
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.preferredName");
        q.setQuestionKey(hqlPrefix + ".commonName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setTooltip("i18n.gridSearch.criteriaModel.questionText.lastName");
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.lastName");
        q.setQuestionKey(hqlPrefix + ".lastName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setVisibleResults(true);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setTooltip("i18n.gridSearch.criteriaModel.questionText.emailAddress");
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.emailAddress");
        q.setQuestionKey(hqlPrefix + ".emailAddress");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setVisibleResults(true);
        questions.add(q);

        group.setQuestions(questions);
        criteriaModel.addCriteriaGroup(group);
    }

    private static List<Relationship> getRelationships_Validator(CCModule module,
            HttpServletRequest request)
    {
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
        List<Relationship> relationships = new ArrayList<>();
        Locale locale = request.getLocale();

        JSONObject additionalParameters = new JSONObject();
        try
        {
            additionalParameters.put("moduleId", module.getId());
            additionalParameters.put("activityClause",
                    getValidActivitiesForUserHQL(user, module, request));
            additionalParameters.put("periodId", request.getParameter("periodId"));
            additionalParameters.put("positionId",
                    request.getParameter("positionId"));
        }
        catch (JSONException e)
        {
            e.printStackTrace();
        }

        Relationship counts = new Relationship(
                "i18n.gridSearch.criteriaModel.criteriaGroup.Counts",
                Relationship.TYPE.COUNT);
        counts.setForiegnKeyHql("c.id");
        counts.setRelatedEntity(getCountsEntity(locale));
        counts.setCountRelationshipInterface(CCValidationsCountHqlBuilder.class);
        counts.setAdditionalParameters(additionalParameters.toString());
        relationships.add(counts);

        Relationship positions = new Relationship(
                "i18n.gridSearch.criteriaModel.entities.ValidatedCoCurricularPositions",
                Relationship.TYPE.MANY_TO_ONE);
        CriteriaModel criteriaModel = new CriteriaModel();
        populateCriteriaModel_CCPosition(criteriaModel, module, "pv.position", true,
                null, PortalUtils.getLocale(request), user);
        Entity rpEntity = new Entity(
                "i18n.gridSearch.criteriaModel.entities.ValidatedCoCurricularPositions",
                CCPositionValidator.class, "pv", "id", criteriaModel,
                "pv.position.title", "asc", false);
        rpEntity.setStaticFromHql(" join pv.position.activity ");
        positions.setRelatedEntity(rpEntity);
        positions.setForiegnKeyHql("pv.validator.id");
        relationships.add(positions);

        return relationships;
    }

    private static Entity getCountsEntity(Locale locale)
    {
        CriteriaModel countsModel = new CriteriaModel();
        CriteriaGroup cg = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.Counts");
        LinkedList<CriteriaQuestion> questions = new LinkedList<>();

        CriteriaQuestion q = new CriteriaQuestion();
        q.setQuestionText(PortalUtils.getMessageSource().getMessage(
                "i18n.CCHelper.PendingVal7724856933681715", null, locale));
        q.setQuestionKey(CCValidationsCountHqlBuilder.PENDING_COUNT_QUESTION_KEY);
        q.setType(CriteriaQuestion.TYPE_NUMBER);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(PortalUtils.getMessageSource().getMessage(
                "i18n.CCHelper.ApprovedVa6314807167914951", null, locale));
        q.setQuestionKey(CCValidationsCountHqlBuilder.APPROVED_COUNT_QUESTION_KEY);
        q.setType(CriteriaQuestion.TYPE_NUMBER);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(PortalUtils.getMessageSource().getMessage(
                "i18n.CCHelper.DeclinedVa9138073855790014", null, locale));
        q.setQuestionKey(CCValidationsCountHqlBuilder.DECLINED_COUNT_QUESTION_KEY);
        q.setType(CriteriaQuestion.TYPE_NUMBER);
        q.setColWidth(150);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(PortalUtils.getMessageSource().getMessage(
                "i18n.CCHelper.ofPosition3844741596258750", null, locale));
        q.setQuestionKey(CCValidationsCountHqlBuilder.TOTAL_COUNT_QUESTION_KEY);
        q.setType(CriteriaQuestion.TYPE_NUMBER);
        q.setColWidth(150);
        questions.add(q);

        cg.setQuestions(questions);
        countsModel.addCriteriaGroup(cg);

        Entity countsEntity = new Entity(
                "i18n.gridSearch.criteriaModel.criteriaGroup.Counts",
                UserDetailsImpl.class, "c", "id", countsModel, "", "asc", false);

        countsEntity.setHideFromExport(true);
        countsEntity.setGridBuilderClass(CCValidationsCountGridBuilder.class);

        return countsEntity;
    }

    static SearchModel getSearchModel_Positions(CCModule module, SiteElement se,
            Integer statusQueryType, HttpServletRequest request,
            boolean showInteractions)
    {
        Locale locale = PortalUtils.getLocale(request);

        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        boolean isStudentOrAlumni = CC_ROLE_STUDENT
                .equals(getCurrentUserRole(module, userLoggedIn))
                || CC_ROLE_ALUMNI.equals(getCurrentUserRole(module, userLoggedIn));

        SearchModel searchModel = new SearchModel();
        searchModel.setOwnerModuleClassName(se.getContentItemClass().getName());
        searchModel.setOwnerModuleId(se.getContentItemId().toString());
        searchModel.setState(SearchModel.STATE_CONFIG_MASTER);
        searchModel.setCanViewDetails(true);
        searchModel.setCanViewCriteria(true);
        searchModel.setCanExport(!isStudentOrAlumni);
        searchModel.setCanSave(!isStudentOrAlumni);
        searchModel.setShowQuestionOrder(false);
        searchModel.setAttribute("ccModuleId", module.getId().toString());
        searchModel.setAttribute("positionHqlAlias", "p");

        if (showInteractions)
        {
            InteractionHelper.initializeMassAssignButton(searchModel,
                    INTERACTION_TYPE.CCPOSITION, CCPosition.class, locale,
                    userLoggedIn);
        }

        // Mass Update Position Questions
        SearchMassAssignButton button = new SearchMassAssignButton();
        button.setLabel("Mass Update Position Details");
        button.setAction("displayMassUpdatePosition");
        button.addAdditionalParam("currentUserName", userLoggedIn.getUsername());
        searchModel.addMassAssignButton("massUpdate", button);

        // Mass Update Position Validators
        button = new SearchMassAssignButton();
        button.setLabel("Mass Update Position Validators");
        button.setAction("displayMassUpdatePositionValidator");
        button.addAdditionalParam("currentUserName", userLoggedIn.getUsername());
        searchModel.addMassAssignButton("massUpdateValidators", button);

        // Mass Update Position Visibility
        button = new SearchMassAssignButton();
        button.setLabel("Mass Update Position Visibility");
        button.setAction("displayMassUpdatePositionVisibility");
        button.addAdditionalParam("currentUserName", userLoggedIn.getUsername());
        searchModel.addMassAssignButton("massUpdateVisibility", button);

        CriteriaModel criteriaModel = new CriteriaModel();
        populateCriteriaModel_CCPosition(criteriaModel, module, "p", true,
                statusQueryType, locale, userLoggedIn);

        Entity master = new Entity(
                "i18n.gridSearch.criteriaModel.entities.CoCurricularPositions",
                CCPosition.class, "p", "id", criteriaModel, "p.title", "asc", true);

        String activityInClause = "";
        if (isStudentOrAlumni)
        {
            activityInClause = " and p.activity.status=" + CCActivity.STATUS_ACTIVE
                    + " ";
        }
        else if (CC_ROLE_ACTIVITY_DIRECTOR
                .equals(getCurrentUserRole(module, userLoggedIn)))
        {
            activityInClause = " and p.activity.id in "
                    + getValidActivitiesForUserHQL(userLoggedIn, module, request);
        }

        String masterHql = " and p.activity.period.module.id = "
                + module.getId().toString() + activityInClause;
        if (isStudentOrAlumni)
        {
            masterHql += " and p.enabled = true and p.approvedBy is not null and p.disabledBy is null and p.deniedBy is null and "
                    + "((p.program is not null and p.program.id in (select pt.program.id from PTProgramEnrollment pt where pt.student.id= "
                    + userLoggedIn.getId() + ")) or p.program is null)";
        }

        master.setStaticWhereHql(masterHql);

        master.setRelationships(
                getRelationships_CCPosition(module, isStudentOrAlumni, request));

        searchModel.setMasterEntity(master);

        return searchModel;
    }

    private static List<Relationship> getRelationships_CCPosition(CCModule module,
            boolean isStudent, HttpServletRequest request)
    {

        Locale locale = PortalUtils.getLocale(request);

        List<Relationship> relationships = new ArrayList<>();

        if (!isStudent)
        {
            Relationship recordPositions = new Relationship(
                    "i18n.gridSearch.criteriaModel.questionText.Participants",
                    Relationship.TYPE.MANY_TO_ONE);
            recordPositions.setRelatedEntity(getEntity_CCRecordPosition(module,
                    true, false, false, "Participants", request));
            recordPositions.setForiegnKeyHql("rp.position.id");
            relationships.add(recordPositions);

            if (module.getCompetencyUsecase() != Competenciable.COMP_DISABLED)
            {
                CCPositionHelper.addPositionCompetencyRelationship(module,
                        relationships);
            }
        }
        String departmentLabelBilingual = LocaleUtils.isL1(locale)
                ? module.getDepartmentLabel()
                : module.getL2DepartmentLabel();
        if (module.isEnableActivityDirectors())
        {
            Relationship activityDirectors = new Relationship(
                    departmentLabelBilingual != null ? departmentLabelBilingual
                            : "Department",
                    Relationship.TYPE.MANY_TO_ONE);
            activityDirectors.setRelatedEntity(
                    getEntity_CCActivityDirectors_byPosition(module, locale));
            activityDirectors.setForiegnKeyHql("ad.activity.id");
            activityDirectors.setMasterJoinHql("p.activity.id");
            relationships.add(activityDirectors);
        }

        if (module.isConnectToEvents())
        {
            Relationship events = new Relationship(
                    "i18n.gridSearch.criteriaModel.questionText.Events",
                    Relationship.TYPE.MANY_TO_ONE);
            events.setRelatedEntity(getEntity_GlobalEvent(locale));
            events.setForiegnKeyHql("e.cCPosition.id");
            relationships.add(events);
        }

        Relationship positionValidators = new Relationship(
                "i18n.gridSearch.criteriaModel.entities.PositionValidator",
                Relationship.TYPE.MANY_TO_ONE);
        positionValidators.setRelatedEntity(getEntity_CCValidators(module, locale));
        positionValidators.setForiegnKeyHql("pv.position.id");
        relationships.add(positionValidators);

        int userLoggedInId = PortalUtils.getUserLoggedIn(request).getId();
        relationships.addAll(TagAssignHelper.getTagRelationships(CCPosition.class,
                null, null, null, locale, null, "", "", userLoggedInId));

        if (InteractionHelper.isNotesEnabled())
        {
            relationships.add(getPositionRelationship_Notes(request));
        }
        if (InteractionHelper.isTasksEnabled())
        {
            relationships.add(getPositionRelationship_Tasks(request));
        }

        return relationships;
    }

    private static Entity getEntity_GlobalEvent(Locale locale)
    {
        CriteriaModel criteriaModel = new CriteriaModel();
        CriteriaGroup group = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.eventCriteria");
        LinkedList<CriteriaQuestion> questions = new LinkedList<>();

        CriteriaQuestion q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.EventModule");
        q.setQuestionKey("e.module.name");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(250);
        questions.add(q);

        if (LocaleUtils.isL1(locale))
        {
            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.GlobalEventHelper.gridSearch.criteriaModel.questionText.title");
            q.setQuestionKey("e.title");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColWidth(300);
            questions.add(q);
        }
        else
        {
            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.GlobalEventHelper.gridSearch.criteriaModel.questionText.title");
            q.setQuestionKey("e.l2Title");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColWidth(300);
            questions.add(q);
        }

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Registrations");
        q.setQuestionKey("e.registrationCount");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setVisibleCriteria(false);
        q.setSortable(false);
        q.setColAlignment("right");
        q.setColWidth(100);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Category");
        q.setQuestionKey("e.category.id");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(100);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.SubCategory");
        q.setQuestionKey("e.subCategory.id");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(100);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.location");
        q.setQuestionKey("e.location");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(100);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.calendarName");
        q.setQuestionKey("e.calendarName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(100);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Status");
        q.setQuestionKey("e.status");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(GlobalEventHelper.getSearchChoices_EventStates());
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.live");
        q.setQuestionKey("e.live");
        q.setType(CriteriaQuestion.TYPE_BOOLEAN);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.liveDate");
        q.setQuestionKey("e.liveDate");
        q.setType(CriteriaQuestion.TYPE_DATE);
        q.setDateFormatForDisplay(DateUtils.DF_MEDIUM_DATE);
        q.setColWidth(100);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.StartDate");
        q.setQuestionKey("e.startDate");
        q.setType(CriteriaQuestion.TYPE_DATE);
        q.setDateFormatForDisplay(DateUtils.DF_MEDIUM_DATE);
        q.setColWidth(100);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.StartTime");
        q.setQuestionKey("e.startTime");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(100);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.endDate");
        q.setQuestionKey("e.endDate");
        q.setType(CriteriaQuestion.TYPE_DATE);
        q.setDateFormatForDisplay(DateUtils.DF_MEDIUM_DATE);
        q.setColWidth(100);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.RegistrationDeadline");
        q.setQuestionKey("e.registrationDeadline");
        q.setType(CriteriaQuestion.TYPE_DATE);
        q.setDateFormatForDisplay(DateUtils.DF_MEDIUM_DATE);
        q.setColWidth(100);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.maxRegistrations");
        q.setQuestionKey("e.maxRegistrations");
        q.setType(CriteriaQuestion.TYPE_NUMBER);
        questions.add(q);

        group.setQuestions(questions);
        criteriaModel.addCriteriaGroup(group);

        Entity entity = new Entity("i18n.gridSearch.criteriaModel.entities.Events",
                GlobalEvent.class, "e", "id", criteriaModel, "e.title", "desc",
                false);

        return entity;
    }

    static SearchModel getSearchModel_RecordPositions(CCModule module,
            SiteElement se, HttpServletRequest request)
    {
        SearchModel searchModel = new SearchModel();
        searchModel.setOwnerModuleClassName(se.getContentItemClass().getName());
        searchModel.setOwnerModuleId(se.getContentItemId().toString());
        searchModel.setState(SearchModel.STATE_CONFIG_MASTER);
        searchModel.setCanViewDetails(true);
        searchModel.setCanViewCriteria(true);
        searchModel.setCanExport(true);
        searchModel.setCanSave(true);
        searchModel.setShowQuestionOrder(false);
        searchModel.setAttribute("ccModuleId", module.getId().toString());
        searchModel.setAttribute("positionHqlAlias", "rp.position");

        CriteriaModel criteriaModel = new CriteriaModel();
        populateCriteriaModel_CCRecordPosition(criteriaModel, module, true, true,
                true, "rp", request);

        Entity master = new Entity("i18n.CCHelper.StudentPar2386429414478148",
                CCRecordPosition.class, "rp", "id", criteriaModel,
                "rp.record.owner.lastName", "asc", true);

        String activityInClause = getValidActivitiesForUserHQL(
                PortalUtils.getUserLoggedIn(request), module, request);
        master.setStaticWhereHql(
                " and rp.record.module.id = " + module.getId().toString()
                        + " and rp.position.activity.id in " + activityInClause);

        searchModel.setMasterEntity(master);

        populateRecordPositionRelationships(module, searchModel, request);

        return searchModel;
    }

    static SearchModel getSearchModel_Validations(CCModule module, SiteElement se,
            HttpServletRequest request)
    {
        SearchModel searchModel = new SearchModel();
        searchModel.setOwnerModuleClassName(se.getContentItemClass().getName());
        searchModel.setOwnerModuleId(se.getContentItemId().toString());
        searchModel.setState(SearchModel.STATE_CONFIG_MASTER);
        searchModel.setCanViewDetails(true);
        searchModel.setCanViewCriteria(true);
        searchModel.setCanExport(true);
        searchModel.setCanSave(true);
        searchModel.setShowQuestionOrder(false);
        searchModel.setAttribute("ccModuleId", module.getId().toString());
        searchModel.setAttribute("positionHqlAlias", "rp.position");

        CriteriaModel criteriaModel = new CriteriaModel();
        populateCriteriaModel_Validations(criteriaModel, module, true, "rp",
                PortalUtils.getLocale(request),
                PortalUtils.getUserLoggedIn(request));

        Entity master = new Entity(
                "i18n.gridSearch.criteriaModel.entities.CoCurricularRecordPositions",
                CCRecordPosition.class, "rp", "id", criteriaModel,
                "rp.record.owner.lastName", "asc", true);

        String activityInClause = getValidActivitiesForUserHQL(
                PortalUtils.getUserLoggedIn(request), module, request);
        master.setStaticWhereHql(
                " and rp.record.module.id = " + module.getId().toString()
                        + " and rp.position.activity.id in " + activityInClause);

        searchModel.setMasterEntity(master);

        return searchModel;
    }

    private static void populateCriteriaModel_Validations(
            CriteriaModel criteriaModel, CCModule module,
            boolean includeCCPositionCriteriaPlugin, String rpHql, Locale locale,
            UserDetailsImpl userLoggedIn)
    {
        CriteriaGroup group = null;
        LinkedList<CriteriaQuestion> questions = null;
        CriteriaQuestion q = null;

        if (includeCCPositionCriteriaPlugin)
        {
            // CO-CURRICULAR DATA FILTERS...
            group = new CriteriaGroup(
                    "i18n.gridSearch.criteriaModel.criteriaGroup.CocurricularFilters");
            group.setCriteriaPluginClass(CCPositionCriteriaPlugin.class);
            criteriaModel.addCriteriaGroup(group);
        }

        // PERIOD, ACTIVITY, POSITION COLUMNS....
        group = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.CocurricularElements");
        questions = new LinkedList<>();

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Period");
        q.setQuestionKey(rpHql + ".position.activity.period.name");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(100);
        q.setVisibleCriteria(false);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.studentID");
        q.setQuestionKey(rpHql + ".record.owner.username");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(140);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.StudentFirstName");
        q.setQuestionKey(rpHql + ".record.owner.firstName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(140);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.StudentLastName");
        q.setQuestionKey(rpHql + ".record.owner.lastName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(140);
        questions.add(q);

        if (module.isShowLevelCategory())
        {
            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.Category");
            q.setQuestionKey(rpHql + ".position.activity.category");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColWidth(250);
            q.setVisibleCriteria(false);
            questions.add(q);
        }

        if (module.isShowLevelOrganization())
        {
            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.organization");
            q.setQuestionKey(rpHql + ".position.activity.organization");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColWidth(250);
            q.setVisibleCriteria(false);
            questions.add(q);
        }

        if (module.isShowLevelDepartment())
        {
            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.Department");
            q.setQuestionKey(rpHql + ".position.activity.department");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColWidth(250);
            q.setVisibleCriteria(false);
            questions.add(q);
        }

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Activity");
        q.setQuestionKey(rpHql + ".position.activity.activity");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(250);
        q.setVisibleCriteria(false);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Position");
        q.setQuestionKey(rpHql + ".position.title");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(250);
        q.setVisibleCriteria(false);
        questions.add(q);

        group.setQuestions(questions);
        criteriaModel.addCriteriaGroup(group);

        group = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.ParticipantDetails");
        questions = new LinkedList<>();

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Status");
        q.setQuestionKey(rpHql + ".status");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(getRecordPositionStatusChoices());
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Hours");
        q.setQuestionKey(rpHql + ".hours");
        q.setType(CriteriaQuestion.TYPE_NUMBER);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.DateValidationRequested");
        q.setQuestionKey(rpHql + ".dateValidated");
        q.setType(CriteriaQuestion.TYPE_DATE);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Participant.ValidatorFirstName");
        q.setQuestionKey(rpHql + ".validatedBy.firstName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Participant.ValidatorLastName");
        q.setQuestionKey(rpHql + ".validatedBy.lastName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        questions.add(q);

        group.setQuestions(questions);
        criteriaModel.addCriteriaGroup(group);

        group = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.StudentCriteria");
        AcrmRegistrationModule regModule = AcrmRegistrationController
                .getRegistrationModule(PersonGroupHelper.STUDENT);
        questions = AcrmRegQuestionVisibilityHelper.createCriteriaQuestions(module,
                locale, userLoggedIn, rpHql + ".record.owner.", regModule, null);

        group.setQuestions(questions);
        criteriaModel.addCriteriaGroup(group);
    }

    public static void populateCriteriaModel_CCRecordPosition(
            CriteriaModel criteriaModel, CCModule module,
            boolean includeRecordStuff, boolean includeCCPositionCriteriaPlugin,
            boolean includePositionStuff, String rpHql, HttpServletRequest request)
    {
        Locale locale = PortalUtils.getLocale(request);

        CriteriaGroup group = null;
        LinkedList<CriteriaQuestion> questions = null;
        CriteriaQuestion q = null;

        if (includeCCPositionCriteriaPlugin)
        {
            // CO-CURRICULAR DATA FILTERS...
            group = new CriteriaGroup(
                    "i18n.gridSearch.criteriaModel.criteriaGroup.CocurricularFilters");
            group.setCriteriaPluginClass(CCPositionCriteriaPlugin.class);
            criteriaModel.addCriteriaGroup(group);
        }

        // PERIOD, ACTIVITY, POSITION COLUMNS....
        group = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.CocurricularElements");
        questions = new LinkedList<>();

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Period");
        q.setQuestionKey(rpHql + ".position.activity.period.name");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(100);
        q.setVisibleCriteria(false);
        questions.add(q);

        if (module.isShowLevelCategory())
        {
            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.Category");
            q.setQuestionKey(rpHql + ".position.activity.category");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColWidth(250);
            q.setVisibleCriteria(false);
            questions.add(q);
        }

        if (module.isShowLevelOrganization())
        {
            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.organization");
            q.setQuestionKey(rpHql + ".position.activity.organization");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColWidth(250);
            q.setVisibleCriteria(false);
            questions.add(q);
        }

        if (module.isShowLevelDepartment())
        {
            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.Department");
            q.setQuestionKey(rpHql + ".position.activity.department");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColWidth(250);
            q.setVisibleCriteria(false);
            questions.add(q);
        }

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Activity");
        q.setQuestionKey(rpHql + ".position.activity.activity");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(250);
        q.setVisibleCriteria(false);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Position");
        q.setQuestionKey(rpHql + ".position.title");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(250);
        q.setVisibleCriteria(false);
        questions.add(q);

        group.setQuestions(questions);
        criteriaModel.addCriteriaGroup(group);

        group = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.ParticipantDetails");
        questions = new LinkedList<>();

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Participant.Created");
        q.setQuestionKey(rpHql + ".dateAdded");
        q.setType(CriteriaQuestion.TYPE_DATE);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.AddedByFirstName");
        q.setQuestionKey(rpHql + ".addedBy.firstName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.CCHelper.AddedByPre5702413530704103");
        q.setQuestionKey(rpHql + ".addedBy.commonName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.AddedByLastName");
        q.setQuestionKey(rpHql + ".addedBy.lastName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Participant.Status");
        q.setQuestionKey(rpHql + ".status");
        q.setType(CriteriaQuestion.TYPE_CHOICE_SINGLE);
        q.setOptionChoices(getRecordPositionStatusChoices());
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Participant.Validated");
        q.setQuestionKey(rpHql + ".dateValidated");
        q.setType(CriteriaQuestion.TYPE_DATE);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Participant.ValidatorFirstName");
        q.setQuestionKey(rpHql + ".validatedBy.firstName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.CCHelper.ValidatorP1704724460388789");
        q.setQuestionKey(rpHql + ".validatedBy.commonName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Participant.ValidatorLastName");
        q.setQuestionKey(rpHql + ".validatedBy.lastName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Participant.Validated");
        q.setQuestionKey(rpHql + ".dateValidated");
        q.setType(CriteriaQuestion.TYPE_DATE);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Participant.Hours");
        q.setQuestionKey(rpHql + ".hours");
        q.setType(CriteriaQuestion.TYPE_NUMBER);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Participant.ShowOnCertificate");
        q.setQuestionKey(rpHql + ".showOnPdf");
        q.setType(CriteriaQuestion.TYPE_BOOLEAN);
        questions.add(q);

        group.setQuestions(questions);
        criteriaModel.addCriteriaGroup(group);

        if (includeRecordStuff)
        {
            populateCriteriaModel_CCRecord(criteriaModel, module, rpHql + ".record",
                    request);
        }

        if (includePositionStuff)
        {
            populateCriteriaModel_CCPosition(criteriaModel, module,
                    rpHql + ".position", false, null, locale,
                    PortalUtils.getUserLoggedIn(request));
        }
    }

    private static void populateRecordPositionRelationships(CCModule module,
            SearchModel searchModel, HttpServletRequest request)
    {
        List<Relationship> relationships = new LinkedList<>();
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        Locale locale = PortalUtils.getLocale(request);
        relationships.add(AcrmReflectionHelper.prepareReflectionRelationship(
                userLoggedIn, locale, module, CCRecordPosition.class, "rp"));
        try
        {
            List<Class<? extends TagAssign<? extends Tag, ? extends TagAssignable>>> tagAssignTypes = new ArrayList();
            tagAssignTypes.add(AcrmUserTag.class);
            relationships.addAll(TagAssignHelper.getTagRelationships(
                    userLoggedIn.getClass(),
                    AcrmRegistrationController.getRegistrationModule(
                            PersonGroupHelper.STUDENT_GROUP), null,
                    "rp.record.owner.id", locale, tagAssignTypes, null, null,
                    userLoggedIn.getId()));

            relationships.addAll(TagAssignHelper.getTagRelationships(
                    CCActivity.class, null, null, "rp.position.activity.id", locale, null,
                    null, null, userLoggedIn.getId()));
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        if (module.getCompetencyUsecase() != Competenciable.COMP_DISABLED)
        {
            CCRecordPositionHelper.addRecordPositionCompetencyRelationship(module,
                    relationships);
        }

        searchModel.getMasterEntity().setRelationships(relationships);
    }

    private static Map<String, String> getRecordPositionStatusChoices()
    {
        Map<String, String> map = new LinkedHashMap<>();

        map.put(CCRecordPosition.STATUS_PENDING, CCRecordPosition.STATUS_PENDING);
        map.put(CCRecordPosition.STATUS_APPROVED, CCRecordPosition.STATUS_APPROVED);
        map.put(CCRecordPosition.STATUS_DECLINED, CCRecordPosition.STATUS_DECLINED);

        return map;
    }

    public static List<Relationship> getRelationships_CCRecord(CCModule module,
            HttpServletRequest request)
    {
        Locale locale = PortalUtils.getLocale(request);

        List<Relationship> relationships = new ArrayList<>();

        Relationship positions = new Relationship(
                "i18n.gridSearch.criteriaModel.entities.RecordPositions",
                Relationship.TYPE.MANY_TO_ONE);
        positions.setRelatedEntity(getEntity_CCRecordPosition(module, false, true,
                true, "Record Positions", request));
        positions.setForiegnKeyHql("rp.record.id");
        relationships.add(positions);

        Relationship hourEntry = new Relationship(
                "i18n.gridSearch.criteriaModel.questionText.TimeTrackingEntries",
                Relationship.TYPE.MANY_TO_ONE);
        hourEntry.setRelatedEntity(getEntity_CCRecordPositionEntry(module, locale,
                "i18n.gridSearch.criteriaModel.questionText.TimeTrackingEntries"));
        hourEntry.setForiegnKeyHql("rpe.recordPosition.record.id");
        relationships.add(hourEntry);

        if (module.getCompetencyUsecase() != Competenciable.COMP_DISABLED)
        {
            CCRecordHelper.addRecordCompetencyRelationship(module, relationships);
        }
        if (InteractionHelper.isNotesEnabled())
        {
            relationships.add(getRecordRelationship_Notes(request));
        }
        if (InteractionHelper.isTasksEnabled())
        {
            relationships.add(getRecordRelationship_Tasks(request));
        }

        return relationships;
    }

    private static Entity getEntity_CCRecordPositionEntry(CCModule module,
            Locale locale, String entityLabel)
    {
        CriteriaModel criteriaModel = new CriteriaModel();

        CriteriaGroup group = new CriteriaGroup(
                PortalUtils.getMessageSource().getMessage(
                        "i18n.CCHelper.RecordPosi0116773703471970", null, locale));
        LinkedList<CriteriaQuestion> questions = new LinkedList<>();
        CriteriaQuestion q = null;

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Date");
        q.setQuestionKey("rpe.fromDate");
        q.setType(CriteriaQuestion.TYPE_DATE);
        q.setColWidth(100);
        q.setVisibleCriteria(true);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Hours");
        q.setQuestionKey("rpe.hours");
        q.setType(CriteriaQuestion.TYPE_NUMBER);
        q.setColWidth(100);
        q.setVisibleCriteria(true);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Notes");
        q.setQuestionKey("rpe.entry");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(200);
        q.setVisibleCriteria(true);
        questions.add(q);

        group.setQuestions(questions);
        criteriaModel.addCriteriaGroup(group);

        Entity hours = new Entity(entityLabel, CCRecordPositionEntry.class, "rpe",
                "id", criteriaModel, "rpe.fromDate", "desc", false);

        return hours;
    }

    private static Entity getEntity_CCRecordPosition(CCModule module,
            boolean includeRecordStuff, boolean includeCCPositionCriteriaPlugin,
            boolean includePositionStuff, String entityLabel,
            HttpServletRequest request)
    {
        CriteriaModel criteriaModel = new CriteriaModel();

        populateCriteriaModel_CCRecordPosition(criteriaModel, module,
                includeRecordStuff, includeCCPositionCriteriaPlugin,
                includePositionStuff, "rp", request);

        Entity position = new Entity(entityLabel, CCRecordPosition.class, "rp",
                "id", criteriaModel, "rp.dateAdded", "desc", false);
        position.setStaticFromHql(" join rp.position.activity ");

        return position;
    }

    public static void populateCriteriaModel_CCRecord(CriteriaModel criteriaModel,
            CCModule module, String recordHql, HttpServletRequest request)
    {
        CriteriaGroup group;
        LinkedList<CriteriaQuestion> questions;

        group = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.StudentCriteria");
        AcrmRegistrationModule regModule = AcrmRegistrationController
                .getRegistrationModule(PersonGroupHelper.STUDENT);
        questions = AcrmRegQuestionVisibilityHelper.createCriteriaQuestions(module,
                PortalUtils.getLocale(request),
                PortalUtils.getUserLoggedIn(request), recordHql + ".owner.",
                regModule, null);

        group.setQuestions(questions);
        criteriaModel.addCriteriaGroup(group);

        group = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.RecordCriteria");
        questions = new LinkedList<>();

        CriteriaQuestion q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Record.Created");
        q.setQuestionKey(recordHql + ".dateCreated");
        q.setType(CriteriaQuestion.TYPE_DATE);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Record.LastUpdated");
        q.setQuestionKey(recordHql + ".dateUpdated");
        q.setType(CriteriaQuestion.TYPE_DATE);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.Record.LookupCode");
        q.setQuestionKey(recordHql + ".lookupCode");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        questions.add(q);

        group.setQuestions(questions);
        criteriaModel.addCriteriaGroup(group);
    }

    public static void populateCriteriaModel_CCActivity(CriteriaModel criteriaModel,
            CCModule module, boolean includeActivityQuestions, String activityHql,
            Locale locale, UserDetailsImpl userLoggedIn)
    {
        CriteriaQuestion q = null;
        CriteriaGroup group = null;
        LinkedList<CriteriaQuestion> questions = null;

        // CO-CURRICULAR DATA FILTERS...
        group = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.CocurricularFilters");
        if (!"timePeriod".equalsIgnoreCase(activityHql))
        {
            group.setCriteriaPluginClass(CCActivityCriteriaPlugin.class);
        }

        // PERIOD, ACTIVITY, POSITION COLUMNS....
        questions = new LinkedList<>();

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Period");
        q.setQuestionKey("a.period.name");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(100);
        q.setVisibleCriteria(false);
        questions.add(q);

        if (module.isShowLevelCategory())
        {
            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.Category");
            q.setQuestionKey("a.category");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColWidth(250);
            q.setVisibleCriteria(false);
            questions.add(q);
        }

        if (module.isShowLevelOrganization())
        {
            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.organization");
            q.setQuestionKey("a.organization");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColWidth(250);
            q.setVisibleCriteria(false);
            questions.add(q);
        }

        if (module.isShowLevelDepartment())
        {
            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.Department");
            q.setQuestionKey("a.department");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColWidth(250);
            q.setVisibleCriteria(false);
            questions.add(q);
        }

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Activity");
        q.setQuestionKey("a.activity");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(250);
        q.setVisibleCriteria(false);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.createdBy");
        q.setQuestionKey("a.createdBy.firstAndLastName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setColWidth(180);
        q.setVisibleCriteria(false);
        q.setSortable(false);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.CreatedOn");
        q.setQuestionKey("a.createdOn");
        q.setType(CriteriaQuestion.TYPE_DATE);
        q.setColWidth(75);
        q.setVisibleCriteria(false);
        questions.add(q);

        group.setQuestions(questions);
        criteriaModel.addCriteriaGroup(group);

        // ACTIVITY OWNERS...
        if (module.isEnableActivityOwnerRole() || module.isEnableStaffRole())
        {
            group = new CriteriaGroup(
                    "i18n.gridSearch.criteriaModel.criteriaGroup.ActivityOwners");
            questions = new LinkedList<>();

            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.ActivityOwners");
            q.setQuestionKey("a.currentOwner.id");
            q.setType(CriteriaQuestion.TYPE_CHOICE);
            q.setOptionChoices(getOptionChoices_ActivityOwners(module));
            q.setColWidth(180);
            q.setVisibleCriteria(true);
            q.setVisibleResults(false);
            questions.add(q);

            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.CurrentOwner");
            q.setQuestionKey("a.currentOwner.firstAndLastName");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColWidth(180);
            q.setVisibleCriteria(false);
            q.setVisibleResults(true);
            q.setSortable(false);
            questions.add(q);

            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.CurrentOwnerEmail");
            q.setQuestionKey("a.currentOwner.emailAddress");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColWidth(180);
            q.setVisibleCriteria(false);
            q.setVisibleResults(true);
            questions.add(q);

            if (getCCPublicModule(module) != null)
            {
                q = new CriteriaQuestion();
                q.setQuestionText("i18n.CCHelper.UpdateRequ8086612936972749");
                q.setQuestionKey("a.ownerUpdateRequested");
                q.setType(CriteriaQuestion.TYPE_BOOLEAN);
                questions.add(q);
            }

            group.setQuestions(questions);
            criteriaModel.addCriteriaGroup(group);
        }

        // DYNAMIC ACTIVITY QUESTIONS...
        if (includeActivityQuestions)
        {
            group = new CriteriaGroup(
                    "i18n.gridSearch.criteriaModel.criteriaGroup.ActivityStatus");
            questions = new LinkedList<>();

            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.ActivityStatus");
            q.setQuestionKey("a.status");
            q.setType(CriteriaQuestion.TYPE_CHOICE);
            Map<String, String> optionChoices = new LinkedHashMap<>();
            optionChoices.put(String.valueOf(CCActivity.STATUS_PENDING_APPROVAL),
                    new I18nLabel("i18n.common.pending_approval", locale)
                            .getTranslation());
            optionChoices.put(String.valueOf(CCActivity.STATUS_ACTIVE),
                    new I18nLabel("i18n.common.active", locale).getTranslation());
            optionChoices.put(String.valueOf(CCActivity.STATUS_DISABLED),
                    new I18nLabel("i18n.common.inactive", locale).getTranslation());
            q.setOptionChoices(optionChoices);
            q.setColWidth(180);

            questions.add(q);
            group.setQuestions(questions);
            criteriaModel.addCriteriaGroup(group);

            DFHelper.populateCriteriaGroupsForDFModel(
                    CCActivityQuestionModelHelper.getActivityQuestionModel(module)
                            .getDFModel(),
                    "a", criteriaModel, locale.toLanguageTag(), userLoggedIn);

        }
    }

    private static Map<String, String> getOptionChoices_ActivityOwners(
            CCModule module)
    {
        Map<String, String> map = new LinkedHashMap<>();

        List<Object[]> owners = PortalUtils.getHt().find(
                "select u.id, u.preferredFirstName, u.lastName, u.username from UserDetailsImpl u where u.id in (select distinct a.currentOwner.id from CCActivity a where a.period.module=?) order by u.firstName",
                module);

        Integer userId = null;
        String firstName = null;
        String lastName = null;
        String username = null;
        for (Object[] owner : owners)
        {
            userId = (Integer) owner[0];
            firstName = (String) owner[1];
            lastName = (String) owner[2];
            username = (String) owner[3];
            map.put(userId.toString(),
                    firstName + " " + lastName + " (" + username + ")");
        }

        return map;
    }

    public static void populateCriteriaModel_CCPosition(CriteriaModel criteriaModel,
            CCModule module, final String positionHql, boolean includePositionStuff,
            Integer statusQueryType, Locale locale, UserDetailsImpl userLoggedIn)
    {
        CriteriaGroup cg;
        LinkedList<CriteriaQuestion> questions;
        CriteriaQuestion cq = null;

        if (statusQueryType != null)
        {
            cg = new CriteriaGroup(
                    "i18n.gridSearch.criteriaModel.criteriaGroup.DateFilter");
            questions = new LinkedList<>();

            cq = new CriteriaQuestion();

            String questionText = "";
            String questionKey = "";

            if (statusQueryType.intValue() == CCPosition.APPROVAL_STATUS_APPROVED)
            {
                questionText = "i18n.gridSearch.criteriaModel.questionText.DateApproved";
                questionKey = "requestAccepted";
            }
            else if (statusQueryType
                    .intValue() == CCPosition.APPROVAL_STATUS_DECLINED)
            {
                questionText = "i18n.gridSearch.criteriaModel.questionText.DateDeclined";
                questionKey = "requestDenied";
            }
            else if (statusQueryType
                    .intValue() == CCPosition.APPROVAL_STATUS_PENDING)
            {
                questionText = "i18n.gridSearch.criteriaModel.questionText.DateRequested";
                questionKey = "requestDate";
            }

            cq.setQuestionText(questionText);
            cq.setQuestionKey(positionHql + "." + questionKey);
            cq.setType(CriteriaQuestion.TYPE_DATE);
            cq.setDateFormatForDisplay(DateUtils.DF_MEDIUM_DATE_SHORT_TIME);
            cq.setColWidth(120);
            questions.add(cq);

            cg.setQuestions(questions);
            criteriaModel.addCriteriaGroup(cg);
        }

        if (includePositionStuff)
        {
            // CO-CURRICULAR DATA FILTERS...
            cg = new CriteriaGroup(
                    "i18n.gridSearch.criteriaModel.criteriaGroup.CocurricularFilters");
            cg.setCriteriaPluginClass(CCPositionCriteriaPlugin.class);
            criteriaModel.addCriteriaGroup(cg);

            // PERIOD, ACTIVITY, POSITION COLUMNS....
            cg = new CriteriaGroup(
                    "i18n.gridSearch.criteriaModel.criteriaGroup.CocurricularElements");
            questions = new LinkedList<>();

            cq = new CriteriaQuestion();
            cq.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Period");
            cq.setQuestionKey(positionHql + ".activity.period.name");
            cq.setType(CriteriaQuestion.TYPE_TEXT);
            cq.setColWidth(100);
            cq.setVisibleCriteria(false);
            questions.add(cq);

            if (module.isShowLevelCategory())
            {
                cq = new CriteriaQuestion();
                cq.setQuestionText(
                        "i18n.gridSearch.criteriaModel.questionText.Category");
                cq.setQuestionKey(positionHql + ".activity.category");
                cq.setType(CriteriaQuestion.TYPE_TEXT);
                cq.setColWidth(250);
                cq.setVisibleCriteria(false);
                questions.add(cq);
            }

            if (module.isShowLevelOrganization())
            {
                cq = new CriteriaQuestion();
                cq.setQuestionText(
                        "i18n.gridSearch.criteriaModel.questionText.organization");
                cq.setQuestionKey(positionHql + ".activity.organization");
                cq.setType(CriteriaQuestion.TYPE_TEXT);
                cq.setColWidth(250);
                cq.setVisibleCriteria(false);
                questions.add(cq);
            }

            if (module.isShowLevelDepartment())
            {
                cq = new CriteriaQuestion();
                cq.setQuestionText(
                        "i18n.gridSearch.criteriaModel.questionText.Department");
                cq.setQuestionKey(positionHql + ".activity.department");
                cq.setType(CriteriaQuestion.TYPE_TEXT);
                cq.setColWidth(250);
                cq.setVisibleCriteria(false);
                questions.add(cq);
            }

            cq = new CriteriaQuestion();
            cq.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.Activity");
            cq.setQuestionKey(positionHql + ".activity.activity");
            cq.setType(CriteriaQuestion.TYPE_TEXT);
            cq.setColWidth(250);
            cq.setVisibleCriteria(false);
            questions.add(cq);

            cq = new CriteriaQuestion();
            cq.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.Position");
            cq.setQuestionKey(positionHql + ".title");
            cq.setType(CriteriaQuestion.TYPE_TEXT);
            cq.setColWidth(250);
            questions.add(cq);

            cq = new CriteriaQuestion();
            cq.setQuestionText("i18n.gridSearch.criteriaModel.questionText.Status");
            cq.setQuestionKey(positionHql + ".status");
            cq.setType(CriteriaQuestion.TYPE_CHOICE);
            Map statuses = new HashMap();
            statuses.put("0", "Pending Approval");
            statuses.put("1", "Approved");
            statuses.put("2", "Declined");
            cq.setOptionChoices(statuses);
            cq.setColWidth(180);
            cq.setVisibleCriteria(true);
            cq.setVisibleResults(true);
            questions.add(cq);

            cq = new CriteriaQuestion();
            cq.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.Visible");
            cq.setQuestionKey(positionHql + ".enabled");
            cq.setType(CriteriaQuestion.TYPE_BOOLEAN);
            questions.add(cq);

            cq = new CriteriaQuestion();
            cq.setQuestionText("i18n.CCHelper.HasApprove3160662646326110");
            cq.setQuestionKey(positionHql + ".hasApprovedValidations");
            cq.setType(CriteriaQuestion.TYPE_BOOLEAN);
            cq.setCustomHQLBuilder(new CriteriaQuestionHQLBuilder()
            {
                @Override
                public String getHQL(CriteriaQuestion question, Entity entity)
                {
                    StringBuilder hql = new StringBuilder();

                    if (question != null && question.getAnswer() != null)
                    {
                        hql.append(question.getAnswer().getBooleanValue()
                                ? " and exists"
                                : " and not exists");
                        hql.append(" (select ccrp.id from CCRecordPosition ccrp");
                        hql.append(" where ccrp.position=" + positionHql);
                        hql.append(" and ccrp.status='"
                                + CCRecordPosition.STATUS_APPROVED + "'");
                        hql.append(")");
                    }

                    return hql.toString();
                }

                @Override
                public String getDescription(CriteriaQuestion question,
                        Entity entity)
                {
                    String hql = "";
                    return hql;
                }
            });
            cq.setVisibleResults(false);
            questions.add(cq);

            if (getCCPublicModule(module) != null)
            {
                cq = new CriteriaQuestion();
                cq.setQuestionText("i18n.CCHelper.UpdateRequ1932850358260185");
                cq.setQuestionKey(positionHql + ".ownerUpdateRequested");
                cq.setType(CriteriaQuestion.TYPE_BOOLEAN);
                questions.add(cq);
            }

            cg.setQuestions(questions);
            criteriaModel.addCriteriaGroup(cg);
        }

        // POSITION QUESTIONS...
        DFHelper.populateCriteriaGroupsForDFModel(
                CCPositionQuestionModelHelper.getPositionQuestionModel(module)
                        .getDFModel(),
                positionHql, criteriaModel, locale.getLanguage(), userLoggedIn);

        if (module.isConnectToPT())
        {
            // PROGRAM TRACKING QUESTION....
            cq = new CriteriaQuestion();
            cq.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.program");
            cq.setQuestionKey(positionHql + ".program");
            cq.setType(CriteriaQuestion.TYPE_IS_EMPTY);
            cq.setColWidth(180);
            questions = new LinkedList<>();
            questions.add(cq);
            cg = new CriteriaGroup(
                    "i18n.gridSearch.criteriaModel.criteriaGroup.ProgramTracking");
            cg.setQuestions(questions);
            criteriaModel.addCriteriaGroup(cg);
        }
    }

    public static Map<String, String> getOptionChoices_Period(CCModule module,
            Locale locale)
    {
        Map<String, String> map = new LinkedHashMap<>();

        String periodFieldName = LocaleUtils.isL1(locale) ? "name" : "l2Name";
        List<Object[]> periods = PortalUtils.getHt()
                .find("select p." + periodFieldName
                        + ", p.active from CCPeriod p where p.module=? order by p."
                        + periodFieldName, module);

        for (Object[] data : periods)
        {
            String name = (String) data[0];
            Boolean active = (Boolean) data[1];
            map.put(name, (active ? name : (name + " (inactive)")));
        }

        return map;
    }

    public static Map<String, String> getOptionChoices_Activity(CCModule module)
    {
        Map<String, String> map = new LinkedHashMap<>();
        List<String> activities = PortalUtils.getHt().find(
                "select a.activity from CCActivity a where a.period.module=? order by a.activity",
                module);

        for (String a : activities)
        {
            map.put(a, a);
        }

        return map;
    }

    public static Map<String, String> getOptionChoices_Category(CCModule module)
    {
        Map<String, String> map = new LinkedHashMap<>();
        List<String> categories = PortalUtils.getHt().find(
                "select distinct a.category from CCActivity a where a.period.module=? order by a.category",
                module);

        for (String c : categories)
        {
            map.put(c, c);
        }

        return map;
    }

    public static Map<String, String> getOptionChoices_Organization(CCModule module)
    {
        Map<String, String> map = new LinkedHashMap<>();
        List<String> orgs = PortalUtils.getHt().find(
                "select distinct a.organization from CCActivity a where a.period.module=? order by a.organization",
                module);

        for (String o : orgs)
        {
            map.put(o, o);
        }

        return map;
    }

    public static Map<String, String> getOptionChoices_Department(CCModule module)
    {
        Map<String, String> map = new LinkedHashMap<>();
        List<String> dept = PortalUtils.getHt().find(
                "select distinct a.department from CCActivity a where a.period.module=? order by a.department",
                module);

        for (String d : dept)
        {
            map.put(d, d);
        }

        return map;
    }

    static void populateReturnForm(SearchModel searchModel, SiteElement siteElement,
            HttpServletRequest request)
    {
        SearchHelper.populateReturnForm(searchModel, siteElement, "displayReports",
                PortalUtils.getUserLoggedIn(request),
                "<input type='hidden' name='reportType' value='term'>",
                "<input type='hidden' name='reportFilter' value='"
                        + request.getParameter("reportFilter") + "'>");
    }

    // CHANGE THIS TO CCVALIDATORS
    static JQGridModel getGridModel_CCValidators(CCPeriod r, Locale locale)
    {
        JQGridModel gridModel = new JQGridModel();

        JQGridSearch validators = getValidatorGridSearch(r, locale);
        JQGridSearch positions = getPositionsDetail_CCValidators(r, locale);

        gridModel.setMaster(validators);
        gridModel.addDetail("positions", positions);

        return gridModel;
    }

    static JQGridModel getGridModel_CCCategories(CCPeriod r, Locale locale)
    {
        JQGridModel gridModel = new JQGridModel();

        JQGridSearch categories = getCategoriesGridSearch(r, locale);

        gridModel.setMaster(categories);

        return gridModel;
    }

    static JQGridModel getGridModel_CCOrganizations(CCPeriod r, Locale locale)
    {
        JQGridModel gridModel = new JQGridModel();

        JQGridSearch organizations = getOrganizationsGridSearch(r, locale);

        gridModel.setMaster(organizations);

        return gridModel;
    }

    static JQGridModel getGridModel_CCDepartments(CCPeriod r, Locale locale)
    {
        JQGridModel gridModel = new JQGridModel();

        JQGridSearch departments = getDepartmentsGridSearch(r, locale);

        gridModel.setMaster(departments);

        return gridModel;
    }

    private static JQGridSearch getValidatorGridSearch(CCPeriod r, Locale locale)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        List<Integer> auIds = ht.find(
                "select distinct cpv.validator.id from CCPositionValidator cpv where cpv.position.activity.period.id="
                        + r.getId().toString());

        String hql = "from UserDetailsImpl u where u.id in "
                + DBUtils.buildInClause(auIds);

        LinkedList<JQGridColumn> columns = getGridColumns_CCValidators();
        JQGridSearch validators = new JQGridSearch("UserDetailsImpl", "u", "id",
                hql, columns, locale);

        validators.setSortIndex("u.lastName");
        validators.setSortOrder("asc");
        validators.setSearchTypeLabel("Co-curricular Validators");
        validators.setMaster(true);

        List<String> emailPaths = new ArrayList<>();
        emailPaths.add("u.emailAddress");
        emailPaths.add("u.alternateEmail");
        emailPaths.add("u.username");
        emailPaths.add("u.userLocale");
        EmailingEmailer ee = new EmailingEmailer(emailPaths, "u.firstName",
                "u.lastName", "u.noCampaignEmails = 0", "u.userLocale");
        EmailingModel em = new EmailingModel();
        em.getEmailers().add(ee);
        Entity masterEntity = new Entity();
        masterEntity.setEmailingModel(em);
        validators.setEntity(masterEntity);

        return validators;
    }

    private static JQGridSearch getCategoriesGridSearch(CCPeriod r, Locale locale)
    {
        String hql =
                "  from CCActivity a where a.category is not null and a.period.id = "
                        + r.getId()
                        + " group by a.category, a.period.id, a.period.name";

        LinkedList<JQGridColumn> columns = getGridColumns_CCCategories();
        JQGridSearch categories = new JQGridSearch("CCActivity", "a", "period.id",
                hql, columns, locale);

        categories.setSortIndex("a.category");
        categories.setSortOrder("asc");
        categories.setSearchTypeLabel("Categories");
        categories.setMaster(true);

        return categories;
    }

    private static JQGridSearch getOrganizationsGridSearch(CCPeriod r,
            Locale locale)
    {
        String hql =
                "  from CCActivity a where a.organization is not null and a.period.id = "
                        + r.getId()
                        + " group by a.organization, a.period.id, a.period.name";

        LinkedList<JQGridColumn> columns = getGridColumns_CCOrganizations();
        JQGridSearch orgs = new JQGridSearch("CCActivity", "a", "period.id",
                hql, columns, locale);

        orgs.setSortIndex("a.organization");
        orgs.setSortOrder("asc");
        orgs.setSearchTypeLabel("Organizations");
        orgs.setMaster(true);

        return orgs;
    }

    private static JQGridSearch getDepartmentsGridSearch(CCPeriod r, Locale locale)
    {
        String hql =
                "  from CCActivity a where a.department is not null and a.period.id = "
                        + r.getId()
                        + " group by a.department, a.period.id, a.period.name";

        LinkedList<JQGridColumn> columns = getGridColumns_CCDepartments();
        JQGridSearch departments = new JQGridSearch("CCActivity", "a", "period.id",
                hql, columns, locale);

        departments.setSortIndex("a.department");
        departments.setSortOrder("asc");
        departments.setSearchTypeLabel("Organizations");
        departments.setMaster(true);

        return departments;
    }

    private static LinkedList<JQGridColumn> getGridColumns_CCValidators()
    {
        LinkedList<JQGridColumn> columns = new LinkedList<>();

        JQGridColumn c;

        // Validator First Name
        c = new JQGridColumn();
        c.setLabel("First Name");
        c.setIndex("u.firstName");
        c.setHidden(false);
        c.setSortable(true);
        c.setWidth(100);
        columns.add(c);

        // Validator Last Name
        c = new JQGridColumn();
        c.setLabel("Last Name");
        c.setIndex("u.lastName");
        c.setHidden(false);
        c.setSortable(true);
        c.setWidth(100);
        columns.add(c);

        return columns;
    }

    private static LinkedList<JQGridColumn> getGridColumns_CCCategories()
    {
        LinkedList<JQGridColumn> columns = new LinkedList<>();

        JQGridColumn c;

        c = new JQGridColumn();
        c.setLabel("Category");
        c.setIndex("a.category");
        c.setHidden(false);
        c.setSortable(true);
        c.setWidth(100);
        columns.add(c);

        c = new JQGridColumn();
        c.setLabel("Period");
        c.setIndex("a.period.name");
        c.setHidden(false);
        c.setSortable(true);
        c.setWidth(100);
        columns.add(c);

        return columns;
    }

    private static LinkedList<JQGridColumn> getGridColumns_CCOrganizations()
    {
        LinkedList<JQGridColumn> columns = new LinkedList<>();

        JQGridColumn c;

        c = new JQGridColumn();
        c.setLabel("Organization");
        c.setIndex("a.organization");
        c.setHidden(false);
        c.setSortable(true);
        c.setWidth(100);
        columns.add(c);

        c = new JQGridColumn();
        c.setLabel("Period");
        c.setIndex("a.period.name");
        c.setHidden(false);
        c.setSortable(true);
        c.setWidth(100);
        columns.add(c);

        return columns;
    }

    private static LinkedList<JQGridColumn> getGridColumns_CCDepartments()
    {
        LinkedList<JQGridColumn> columns = new LinkedList<>();

        JQGridColumn c;

        c = new JQGridColumn();
        c.setLabel("Department");
        c.setIndex("a.department");
        c.setHidden(false);
        c.setSortable(true);
        c.setWidth(100);
        columns.add(c);

        c = new JQGridColumn();
        c.setLabel("Period");
        c.setIndex("a.period.name");
        c.setHidden(false);
        c.setSortable(true);
        c.setWidth(100);
        columns.add(c);

        return columns;
    }

    private static JQGridSearch getPositionsDetail_CCValidators(CCPeriod r,
            Locale locale)
    {
        String hql = "from CCPositionValidator cpv where cpv.position.activity.period.id="
                + r.getId().toString();

        LinkedList<JQGridColumn> columns = getGridColumns_CCValidatorPositions(
                r.getModule());
        JQGridSearch positions = new JQGridSearch("CCPositionValidator", "cpv",
                "id", hql, columns, locale);

        positions.setSortIndex("cpv.position.activity.category");
        positions.setSortOrder("asc");
        positions.setSearchTypeLabel("Validator Positions");
        positions.setMasterHqlFkName("cpv.validator.id");
        positions.setMaster(false);
        return positions;
    }

    private static LinkedList<JQGridColumn> getGridColumns_CCValidatorPositions(
            CCModule module)
    {
        LinkedList<JQGridColumn> columns = new LinkedList<>();

        JQGridColumn c;

        if (module.isShowLevelCategory())
        {
            // Category
            c = new JQGridColumn();
            c.setLabel("Category");
            c.setIndex("cpv.position.activity.category");
            c.setHidden(false);
            c.setSortable(true);
            c.setWidth(150);
            columns.add(c);
        }

        if (module.isShowLevelOrganization())
        {
            // Organization
            c = new JQGridColumn();
            c.setLabel("Organization");
            c.setIndex("cpv.position.activity.organization");
            c.setHidden(false);
            c.setSortable(true);
            c.setWidth(150);
            columns.add(c);
        }

        if (module.isShowLevelDepartment())
        {
            // Department
            c = new JQGridColumn();
            c.setLabel("Deptartment");
            c.setIndex("cpv.position.activity.department");
            c.setHidden(false);
            c.setSortable(true);
            c.setWidth(150);
            columns.add(c);
        }

        // Acitivity
        c = new JQGridColumn();
        c.setLabel("Activity");
        c.setIndex("cpv.position.activity.activity");
        c.setHidden(false);
        c.setSortable(true);
        c.setWidth(150);
        columns.add(c);

        // Position
        c = new JQGridColumn();
        c.setLabel("Position");
        c.setIndex("cpv.position.title");
        c.setHidden(false);
        c.setSortable(true);
        c.setWidth(150);
        columns.add(c);

        return columns;
    }

    static Map<Integer, List<String>> populateCCRLearningOutcomes(CCRecord r,
            String recordPositionIdSubQuery, CCModule module)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        List<Object[]> statResults = null;
        if (module.isTrackAchievements())
        {
            statResults = ht.find(
                    "select distinct pa.recordPosition.id, pa.achievement.learningOutcome.name from CCPositionAchievementStudent pa where pa.recordPosition.id in "
                            + DBUtils
                                    .buildInClauseSubQuery(recordPositionIdSubQuery)
                            + " order by pa.achievement.learningOutcome.name",
                    r);
        }
        else
        {
            statResults = ht.find(
                    "select plo.recordPosition.id, plo.learningOutcome.name from CCPositionLearningOutcomeStudent plo where plo.recordPosition.id in "
                            + DBUtils
                                    .buildInClauseSubQuery(recordPositionIdSubQuery)
                            + " order by plo.learningOutcome.name",
                    r);
        }

        Map<Integer, List<String>> learningOutcomes = new HashMap<>();

        for (Object[] res : statResults)
        {
            if (!learningOutcomes.containsKey(res[0]))
            {
                learningOutcomes.put((Integer) res[0], new LinkedList<String>());
            }

            learningOutcomes.get(res[0]).add((String) res[1]);
        }

        return learningOutcomes;
    }

    static void populatePositionLOStats(CCPosition position, ModelAndView mv,
            CCModule module, Locale locale)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        Map<Integer, List<Map<String, Object>>> positionLearningOutcomeStatsMap = new HashMap<>();
        Map<Integer, List<Map<String, Object>>> topPositionLearningOutcomeStatsMap = new HashMap<>();
        Map<String, Object> details = null;
        String nameField = LocaleUtils.isL1(locale) ? "name" : "l2Name";
        List<Object[]> results = ht
                .find("select plo.position.id, plo.learningOutcome." + nameField
                        + ", plo.learningOutcome.id from CCPositionLearningOutcome plo where plo.position=?"
                        + " order by plo.learningOutcome." + nameField, position);

        List<Object[]> achievementCounts = ht.find(
                "select a.learningOutcome.id, count(a.id) from CCAchievement a where a.learningOutcome.id in (select plo.learningOutcome.id from CCPositionLearningOutcome plo where plo.position=?"
                        + ") group by a.learningOutcome",
                position);
        List<Object[]> topLearningOutcomes = null;

        if (module.isTrackAchievements())
        {
            topLearningOutcomes = ht.find(
                    "select pas.recordPosition.position.id, pas.achievement.learningOutcome."
                            + nameField
                            + ", count(pas.id) from CCPositionAchievementStudent pas where pas.recordPosition.position=?"
                            + " group by pas.achievement.learningOutcome."
                            + nameField
                            + ", pas.recordPosition.position order by count(pas.id), pas.achievement.learningOutcome."
                            + nameField,
                    position);
        }
        else
        {
            String hql = "select plos.recordPosition.position.id, plos.learningOutcome."
                    + nameField
                    + ", count(plos.id) from CCPositionLearningOutcomeStudent plos where plos.recordPosition.position=?"
                    + " group by plos.recordPosition.position.id, plos.learningOutcome."
                    + nameField + " order by count(plos.id), plos.learningOutcome."
                    + nameField;

            topLearningOutcomes = ht.find(hql, position);
        }

        Map<Integer, Integer> achievementCountMap = new HashMap<>();
        for (Object[] ac : achievementCounts)
        {
            achievementCountMap.put((Integer) ac[0], (Integer) ac[1]);
        }

        for (Object[] tlo : topLearningOutcomes)
        {
            if (!topPositionLearningOutcomeStatsMap.containsKey(tlo[0]))
            {
                topPositionLearningOutcomeStatsMap.put((Integer) tlo[0],
                        new LinkedList<Map<String, Object>>());
            }
            details = new HashMap<>();
            details.put("learningOutcome", tlo[1]);
            details.put("achievementCount", tlo[2]);
            topPositionLearningOutcomeStatsMap.get(tlo[0]).add(details);
        }

        for (Object[] res : results)
        {
            if (!positionLearningOutcomeStatsMap.containsKey(res[0]))
            {
                positionLearningOutcomeStatsMap.put((Integer) res[0],
                        new LinkedList<Map<String, Object>>());
            }
            details = new HashMap<>();
            details.put("learningOutcome", res[1]);
            details.put("achievementCount",
                    achievementCountMap.containsKey(res[2])
                            ? achievementCountMap.get(res[2])
                            : 0);
            positionLearningOutcomeStatsMap.get(res[0]).add(details);
        }

        mv.addObject("positionLearningOutcomeStatsMap",
                positionLearningOutcomeStatsMap);
        mv.addObject("topPositionLearningOutcomeStatsMap",
                topPositionLearningOutcomeStatsMap);
    }

    static void populatePositionsLOStats(CCActivity activity,
            String positionIdsSubQuery, ModelAndView mv, CCModule module,
            Locale locale)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        Map<Integer, List<Map<String, Object>>> positionLearningOutcomeStatsMap = new HashMap<>();
        Map<Integer, List<Map<String, Object>>> topPositionLearningOutcomeStatsMap = new HashMap<>();
        Map<String, Object> details = null;
        String nameField = LocaleUtils.isL1(locale) ? "name" : "l2Name";
        List<Object[]> results = ht
                .find("select plo.position.id, plo.learningOutcome." + nameField
                        + ", plo.learningOutcome.id from CCPositionLearningOutcome plo where plo.position.id in "
                        + DBUtils.buildInClauseSubQuery(positionIdsSubQuery)
                        + " order by plo.learningOutcome." + nameField, activity);

        List<Object[]> achievementCounts = ht.find(
                "select a.learningOutcome.id, count(a.id) from CCAchievement a where a.learningOutcome.id in (select plo.learningOutcome.id from CCPositionLearningOutcome plo where plo.position.id in "
                        + DBUtils.buildInClauseSubQuery(positionIdsSubQuery)
                        + ") group by a.learningOutcome",
                activity);
        List<Object[]> topLearningOutcomes = null;

        if (module.isTrackAchievements())
        {
            topLearningOutcomes = ht.find(
                    "select pas.recordPosition.position.id, pas.achievement.learningOutcome."
                            + nameField
                            + ", count(pas.id) from CCPositionAchievementStudent pas where pas.recordPosition.position.id in "
                            + DBUtils.buildInClauseSubQuery(positionIdsSubQuery)
                            + " group by pas.achievement.learningOutcome."
                            + nameField
                            + ", pas.recordPosition.position order by count(pas.id), pas.achievement.learningOutcome."
                            + nameField,
                    activity);
        }
        else
        {
            String hql = "select plos.recordPosition.position.id, plos.learningOutcome."
                    + nameField
                    + ", count(plos.id) from CCPositionLearningOutcomeStudent plos where plos.recordPosition.position.id in "
                    + DBUtils.buildInClauseSubQuery(positionIdsSubQuery)
                    + " group by plos.recordPosition.position.id, plos.learningOutcome."
                    + nameField + " order by count(plos.id), plos.learningOutcome."
                    + nameField;

            topLearningOutcomes = ht.find(hql, activity);
        }

        Map<Integer, Integer> achievementCountMap = new HashMap<>();
        for (Object[] ac : achievementCounts)
        {
            achievementCountMap.put((Integer) ac[0], (Integer) ac[1]);
        }

        for (Object[] tlo : topLearningOutcomes)
        {
            if (!topPositionLearningOutcomeStatsMap.containsKey(tlo[0]))
            {
                topPositionLearningOutcomeStatsMap.put((Integer) tlo[0],
                        new LinkedList<Map<String, Object>>());
            }
            details = new HashMap<>();
            details.put("learningOutcome", tlo[1]);
            details.put("achievementCount", tlo[2]);
            topPositionLearningOutcomeStatsMap.get(tlo[0]).add(details);
        }

        for (Object[] res : results)
        {
            if (!positionLearningOutcomeStatsMap.containsKey(res[0]))
            {
                positionLearningOutcomeStatsMap.put((Integer) res[0],
                        new LinkedList<Map<String, Object>>());
            }
            details = new HashMap<>();
            details.put("learningOutcome", res[1]);
            details.put("achievementCount",
                    achievementCountMap.containsKey(res[2])
                            ? achievementCountMap.get(res[2])
                            : 0);
            positionLearningOutcomeStatsMap.get(res[0]).add(details);
        }

        mv.addObject("positionLearningOutcomeStatsMap",
                positionLearningOutcomeStatsMap);
        mv.addObject("topPositionLearningOutcomeStatsMap",
                topPositionLearningOutcomeStatsMap);
    }

    static void deleteActivityDirectors(CCActivity activity)
    {
        PortalUtils.getJt().update(
                "delete from cc_activity_directors where activity = ?",
                new Object[] { activity.getId() });
    }

    static CCAdmin getCCAdmin(HttpServletRequest request)
    {
        return (CCAdmin) getEntity(CCAdmin.class, "adminId", request);
    }

    static CCDirector getCCDirector(HttpServletRequest request)
    {
        return (CCDirector) getEntity(CCDirector.class, "directorId", request);
    }

    static void delete(CCDirector director)
    {
        try
        {
            PortalUtils.getHt().delete(director);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    /**
     * Admin users have full access of the CCR module. An Admin is either a) the
     * "root user" or b) a user who has the "Co-Curricular Administrator"
     * permission and is registered with the CCR module as a CCAdmin
     */
    public static boolean isAdmin(UserDetailsImpl user, CCModule module)
    {
        boolean ret = false;

        if (user != null && module != null)
        {
            if ("root".equals(user.getUsername()))
            {
                ret = true;
            }
            else if (user.getAssignedTypes()
                    .containsKey(PersonGroupHelper.CC_ADMINISTRATOR)
                    && user.isEnabled() && !user.isDeleted())
            {
                ret = ((Integer) PortalUtils.getHt().find(
                        "select count(a.id) from CCAdmin a where a.module=? and a.user=?",
                        new Object[] { module, user }).get(0)).intValue() != 0;
            }

        }

        return ret;
    }

    /**
     * A validator is someone who can validate a student's CCPostion. Any user
     * with the "Co-Curricular Validator" permission can be a validator.
     */
    static boolean isValidator(UserDetailsImpl user)
    {
        return user.getAssignedTypes().containsKey(PersonGroupHelper.CC_VALIDATOR);
    }

    /**
     * A student is someone who owns a CCRecord. Any user with the "Alumni"
     * primary-group is an alumni.
     */
    static boolean isAlumni(UserDetailsImpl user)
    {
        return user.isAlumni() || user.isAlumniYr1();
    }

    /**
     * An anonymous is someone who does not fit into any of the other
     * categories.
     */
    static boolean isAnonymous(UserDetailsImpl user, CCModule module)
    {
        return !isAdmin(user, module) && !isStaff(user, module)
                && !isActivityDirector(user, module) && !isValidator(user)
                && !user.isStudent() && !isAlumni(user)
                && !isActivityOwner(user, module);
    }

    /**
     * An Activity Director is someone who is a member (CCDirector) of a
     * "directors group" (CCDirectors) of this CCR module. CCDirectors are
     * associated with a CCActivity, giving any group-members access control
     * over that particular activity.
     */
    public static boolean isActivityDirector(UserDetailsImpl user, CCModule module)
    {
        return module.isEnableActivityDirectors() && ((Integer) PortalUtils.getHt()
                .find("select count(d.id) from CCDirector d where d.member=? and d.directors.module=?",
                        new Object[] { user, module })
                .get(0)).intValue() != 0;
    }

    /**
     * A "CCR Staff" user is kind of like an admin, but with limited access. Any
     * user with the "Co-Curricular Staff" permission is considered a "CCR
     * Staff" user.
     *
     * @param module
     */
    static boolean isStaff(UserDetailsImpl user, CCModule module)
    {
        return module.isEnableStaffRole()
                && user.getAssignedTypes().containsKey(PersonGroupHelper.CC_STAFF);
    }

    static void delete(CCAdmin ccAdmin)
    {
        try
        {
            PortalUtils.getHt().delete(ccAdmin);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    static Map<String, Boolean> getUserRoles(UserDetailsImpl user, CCModule module)
    {
        Map<String, Boolean> roles = new LinkedHashMap<>();
        roles.put(CC_ROLE_ADMIN, false);
        roles.put(CC_ROLE_VALIDATOR, false);
        roles.put(CC_ROLE_STUDENT, false);
        roles.put(CC_ROLE_ALUMNI, false);
        roles.put(CC_ROLE_ANONYMOUS, false);

        if (module.isEnableActivityDirectors())
        {
            roles.put(CC_ROLE_ACTIVITY_DIRECTOR, false);
        }

        if (module.isEnableStaffRole())
        {
            roles.put(CC_ROLE_STAFF, false);
        }

        if (module.isEnableActivityOwnerRole())
        {
            roles.put(CC_ROLE_ACTIVITY_OWNER, false);
        }

        if (user != null)
        {
            roles.put(CC_ROLE_ADMIN, isAdmin(user, module));
            roles.put(CC_ROLE_VALIDATOR, isValidator(user));
            roles.put(CC_ROLE_STUDENT, user.isStudent());
            roles.put(CC_ROLE_ALUMNI, isAlumni(user));
            roles.put(CC_ROLE_ANONYMOUS, isAnonymous(user, module));

            if (module.isEnableActivityDirectors())
            {
                roles.put(CC_ROLE_ACTIVITY_DIRECTOR,
                        isActivityDirector(user, module));
            }

            if (module.isEnableStaffRole())
            {
                roles.put(CC_ROLE_STAFF, isStaff(user, module));
            }

            if (module.isEnableActivityOwnerRole())
            {
                roles.put(CC_ROLE_ACTIVITY_OWNER, isActivityOwner(user, module));
            }
        }
        else
        {
            roles.put(CC_ROLE_ANONYMOUS, true);
        }

        return roles;
    }

    public static boolean isActivityOwner(UserDetailsImpl user, CCModule module)
    {
        return module.isEnableActivityOwnerRole() && ((Integer) PortalUtils.getHt()
                .find("select count(a.id) from CCActivity a where a.currentOwner=? and a.period.module=?",
                        new Object[] { user, module })
                .get(0)).intValue() != 0;
    }

    static List<String> getUserRoleListFromMap(Map<String, Boolean> map)
    {
        List<String> roles = new ArrayList<>();
        for (Entry<String, Boolean> r : map.entrySet())
        {
            if (r.getValue())
            {
                roles.add(r.getKey());
            }
        }

        return roles;
    }

    public static boolean isActivityDirectors(CCActivity activity,
            CCDirectors directors)
    {
        return ((Integer) PortalUtils.getHt().find(
                "select count(ad.id) from CCActivityDirectors ad where ad.activity=? and ad.directors=?",
                new Object[] { activity, directors }).get(0)).intValue() != 0;
    }

    public static boolean isActivityDirectors(CCActivity activity,
            UserDetailsImpl user)
    {
        return ((Integer) PortalUtils.getHt().find(
                "select count(ad.id) from CCActivityDirectors ad, CCDirector d where ad.activity=? and ad.directors=d.directors and d.member=?",
                new Object[] { activity, user }).get(0)).intValue() != 0;
    }

    public static boolean isPositionValidator(CCPosition position,
            UserDetailsImpl user)
    {
        return ((Integer) PortalUtils.getHt().find(
                "select count(pv.id) from CCPositionValidator pv where pv.position=? and pv.validator=?",
                new Object[] { position, user }).get(0)).intValue() != 0;
    }

    static void addDirectors(CCActivity activity, CCDirectors directors)
    {
        if (null != activity && null != directors
                && !isActivityDirectors(activity, directors))
        {
            CCActivityDirectors ad = new CCActivityDirectors();
            ad.setActivity(activity);
            ad.setDirectors(directors);
            PortalUtils.getHt().save(ad);
        }
    }

    public static boolean isPositionStaff(CCPosition position, UserDetailsImpl user)
    {
        boolean ret = false;
        if (isStaff(user, position.getActivity().getPeriod().getModule())
                && ((position.getCreatedBy() != null
                        && position.getCreatedBy().getId().equals(user.getId()))
                        || isActivityStaff(position.getActivity(), user)))
        {
            ret = true;
        }
        return ret;
    }

    public static boolean isActivityStaff(CCActivity activity, UserDetailsImpl user)
    {
        boolean ret = false;
        if (isStaff(user, activity.getPeriod().getModule())
                && ((activity.getCreatedBy() != null
                        && activity.getCreatedBy().getId().equals(user.getId()))
                        || (activity.getCurrentOwner() != null && activity
                                .getCurrentOwner().getId().equals(user.getId()))))
        {
            ret = true;
        }
        return ret;
    }

    public static boolean isActivityOwner(CCActivity activity, UserDetailsImpl user)
    {
        boolean ret = false;
        if (activity.getCurrentOwner() != null
                && activity.getCurrentOwner().getId().equals(user.getId()))
        {
            ret = true;
        }
        return ret;
    }

    static void saveActivityDirectors(CCActivity activity, String directorsIds[])
    {
        deleteActivityDirectors(activity);

        if (directorsIds != null)
        {
            for (int i = 0; i < directorsIds.length; i++)
            {
                if (StringUtils.isInteger(directorsIds[i]))
                {
                    CCDirectors directors = (CCDirectors) PortalUtils.getHt()
                            .load(CCDirectors.class, Integer.valueOf(directorsIds[i]));

                    if (null != directors && directors.getModule().getId()
                            .equals(activity.getPeriod().getModule().getId()))
                    {
                        CCActivityDirectors ad = new CCActivityDirectors();
                        ad.setActivity(activity);
                        ad.setDirectors(directors);
                        PortalUtils.getHt().save(ad);
                    }
                }
            }
        }
    }

    static void populateQuickStats(CCPosition position, ModelAndView mv)
    {
        if (position != null)
        {
            updatePositionOnRecordCount(position);

            position.setParticipantCount(((Integer) PortalUtils.getHt().find(
                    "select count(distinct rp.id) from CCRecordPosition rp where rp.position=?",
                    position).get(0)).intValue());

            position.setValidatorCount(((Integer) PortalUtils.getHt().find(
                    "select count(distinct v.validator.id) from CCPositionValidator v where v.position=?",
                    position).get(0)).intValue());

            position.setDirectorCount(((Integer) PortalUtils.getHt().find(
                    "select count(distinct d.member.id) from CCDirector d, CCActivityDirectors ad where d.directors.id=ad.directors.id and ad.activity=?",
                    position.getActivity()).get(0)).intValue());

            mv.addObject("eventCount", ((Integer) PortalUtils.getHt()
                    .find("select count(*) from GlobalEvent e where e.cCPosition=?",
                            position)
                    .get(0)).intValue());
        }
    }

    static void updateActivityOnRecordCount(CCActivity activity)
    {
        int onRecordCount = ((Integer) PortalUtils.getHt().find(
                "select count(rp) from CCRecordPosition rp where rp.position.activity=? ",
                new Object[] { activity }).get(0)).intValue();
        activity.setOnRecordCount(onRecordCount);
        PortalUtils.getHt().update(activity);

    }

    static void populateQuickStats(CCActivity activity)
    {
        if (activity != null)
        {
            updateActivityOnRecordCount(activity);

            activity.setValidatorCount(((Integer) PortalUtils.getHt().find(
                    "select count(distinct v.validator.id) from CCPositionValidator v where v.position.activity=?",
                    activity).get(0)).intValue());

            activity.setPositionCount(((Integer) PortalUtils.getHt().find(
                    "select count(distinct p.id) from CCPosition p where p.activity=?",
                    activity).get(0)).intValue());

            activity.setDirectorCount(((Integer) PortalUtils.getHt().find(
                    "select count(distinct d.member.id) from CCDirector d, CCActivityDirectors ad where d.directors.id=ad.directors.id and ad.activity=?",
                    activity).get(0)).intValue());

            activity.setClubMemberCount(PortalUtils.getHt().findInt(
                    "select count(rp) from CCRecordPosition rp where rp.position.activity=? and rp.status=?",
                    activity, CCRecordPosition.STATUS_APPROVED));

            float percentageCompleted = 0f;

            int completedActivities = ((Integer) PortalUtils.getHt().find(
                    "select count(rp) from CCRecordPosition rp where rp.position.activity=? and rp.status=?",
                    new Object[] { activity, CCRecordPosition.STATUS_APPROVED })
                    .get(0)).intValue();

            int incompletedActivities = ((Integer) PortalUtils.getHt().find(
                    "select count(rp) from CCRecordPosition rp where rp.position.activity=? and rp.status!=?",
                    new Object[] { activity, CCRecordPosition.STATUS_DECLINED })
                    .get(0)).intValue();

            if (incompletedActivities > 0)
            {
                percentageCompleted = (float) completedActivities
                        / (float) incompletedActivities * 100f;
            }

            activity.setValidationProgressPercentComplete(
                    Math.round(percentageCompleted));
        }
    }

    static void updateCurrentUserRole(String role, UserDetailsImpl userLoggedIn)
    {
        QuestionAnswers answers = UserPreferencesHelper
                .getQuestionAnswers(userLoggedIn);

        Question2Helper.bindValue(answers, "s1", role);
        PortalUtils.getHt().update(answers);
    }

    static String getCurrentUserRole(CCModule module, UserDetailsImpl userLoggedIn)
    {
        String currentUserRole = "";

        List<String> answerList = null;

        if (userLoggedIn != null)
        {
            answerList = PortalUtils.getHt().find(
                    "select up.settings.s1 from UserDetailsPreferences up where up.user=?",
                    new Object[] { userLoggedIn });
        }
        else
        {
            answerList = PortalUtils.getHt().find(
                    "select up.settings.s1 from UserDetailsPreferences up where up.user is null");
        }

        if (answerList.size() > 0)
        {
            currentUserRole = answerList.get(0);
        }
        else
        {
            QuestionAnswers settings = null;
            try
            {
                QuestionAnswers defaultSettings = UserPreferencesHelper
                        .getDefaultSettings();
                settings = (QuestionAnswers) BeanUtils.cloneBean(defaultSettings);
                PortalUtils.getHt().save(settings);
            }
            catch (Exception e)
            {
                // e.printStackTrace();
            }

            UserDetailsPreferences udp = new UserDetailsPreferences();
            udp.setUser(userLoggedIn);
            udp.setSettings(settings);
            PortalUtils.getHt().save(udp);
            if (settings != null)
            {
                currentUserRole = settings.getS1();
            }
        }

        Map<String, Boolean> roles = CCHelper.getUserRoles(userLoggedIn, module);

        if (StringUtils.isEmpty(currentUserRole)
                || !roles.containsKey(currentUserRole)
                || !roles.get(currentUserRole))
        {
            currentUserRole = null;

            for (String role : roles.keySet())
            {
                if (roles.get(role))
                {
                    currentUserRole = role;
                    updateCurrentUserRole(currentUserRole, userLoggedIn);
                    break;
                }
            }
        }

        return currentUserRole;
    }

    // Unused Method
    static List getRecordPositions(CCModule module, String selectClause,
            String whereClause)
    {
        whereClause = !StringUtils.isEmpty(whereClause) ? (" and " + whereClause)
                : "";

        return PortalUtils.getHt().find(selectClause
                + " from CCRecordPosition rp where rp.position.activity.period.module=? and rp.position.activity.period.active=true "
                + whereClause, module);
    }

    public static String getValidActivitiesForUserHQL(UserDetailsImpl user,
            CCModule module, HttpServletRequest request)
    {
        String currentRole = getCurrentUserRole(module,
                PortalUtils.getUserLoggedIn(request));
        String hql = "";

        if ((isAdmin(user, module) && CC_ROLE_ADMIN.equals(currentRole))
                || (isStaff(user, module) && CC_ROLE_STAFF.equals(currentRole)))
        {
            hql = " (select a.id from CCActivity a where a.period.module.id="
                    + module.getId() + ") ";
        }
        else if (CC_ROLE_ACTIVITY_DIRECTOR.equals(currentRole))
        {
            hql = " (select cad.activity.id from CCActivityDirectors cad, CCDirector d where cad.directors = d.directors and d.member.id="
                    + user.getId() + ") ";
        }
        else
        {
            hql = " (select a.id from CCActivity a where a.period.module.id="
                    + module.getId() + " and a.status=" + CCActivity.STATUS_ACTIVE
                    + ") ";
        }
        return hql;
    }

    static String getPendingRequestsClause(UserDetailsImpl user, CCModule module,
            HttpServletRequest request)
    {
        String periodId = !StringUtils.isEmpty(request.getParameter("periodId"))
                ? " and p.activity.period.id=" + request.getParameter("periodId")
                : "";

        return "p.activity.period.module.id=" + module.getId().toString() + periodId
                + " and p.pendingRequest = true and p.deniedBy is null and p.activity.id in "
                + getValidActivitiesForUserHQL(user, module, request);
    }

    static List getCCPositions(CCModule module, String selectClause,
            String whereClause)
    {
        whereClause = !StringUtils.isEmpty(whereClause) ? (" and " + whereClause)
                : "";

        return PortalUtils.getHt().find(selectClause
                + " from CCPosition p where p.activity.period.module=? and p.activity.period.active=true "
                + whereClause, module);
    }

    static List<DFQuestion> getActivityQuestions(CCModule module)
    {
        return DFHelper.getDFQuestions(CCActivityQuestionModelHelper
                .getActivityQuestionModel(module).getDFModel());
    }

    static SearchModel getSearchModel_Activities(CCModule module, SiteElement se,
            HttpServletRequest request)
    {
        Locale locale = PortalUtils.getLocale(request);

        boolean isStudentOrAlumni = CC_ROLE_STUDENT.equals(
                getCurrentUserRole(module, PortalUtils.getUserLoggedIn(request)))
                || CC_ROLE_ALUMNI.equals(getCurrentUserRole(module,
                        PortalUtils.getUserLoggedIn(request)));
        boolean skipConfigState = RequestUtils.getBooleanParameter(request,
                "skipConfigState", false);

        SearchModel searchModel = new SearchModel();
        searchModel.setOwnerModuleClassName(se.getContentItemClass().getName());
        searchModel.setOwnerModuleId(se.getContentItemId().toString());
        searchModel.setState(skipConfigState ? SearchModel.STATE_SHOW_RESULTS
                : SearchModel.STATE_CONFIG_MASTER);
        searchModel.setCanEmail(!isStudentOrAlumni);
        searchModel.setCanEmailCampaign(!isStudentOrAlumni);
        searchModel.setCanViewDetails(true);
        searchModel.setCanViewCriteria(true);
        searchModel.setCanExport(!isStudentOrAlumni);
        searchModel.setCanSave(!isStudentOrAlumni);

        searchModel.setShowQuestionOrder(false);
        searchModel.setAttribute("ccModuleId", module.getId().toString());
        searchModel.setAttribute("activityHqlAlias", "a");

        InteractionHelper.initializeMassAssignButton(searchModel,
                INTERACTION_TYPE.CCACTIVITY, CCActivity.class, locale,
                PortalUtils.getUserLoggedIn(request));

        // Mass Update Activity Questions
        SearchMassAssignButton button = new SearchMassAssignButton();
        button.setLabel("Mass Update Activity Details");
        button.setAction("displayMassUpdateActivity");
        button.addAdditionalParam("currentUserName",
                PortalUtils.getUserLoggedIn(request).getUsername());
        searchModel.addMassAssignButton("massUpdateDetails", button);

        AcrmHelper.initializeMassAssignTagsButton(searchModel,
                PortalUtils.getUserLoggedIn(request), locale, CCActivity.class,
                "activityIds");

        CriteriaModel criteriaModel = new CriteriaModel();
        populateCriteriaModel_CCActivity(criteriaModel, module, true, "a", locale,
                PortalUtils.getUserLoggedIn(request));

        Entity master = new Entity(
                "i18n.gridSearch.criteriaModel.entities.CoCurricularActivities",
                CCActivity.class, "a", "id", criteriaModel, "a.activity", "asc",
                true);

        String activityInClause = "";
        if (isStudentOrAlumni)
        {
            activityInClause = " and a.status=" + CCActivity.STATUS_ACTIVE + " ";
        }
        else if (CC_ROLE_ACTIVITY_DIRECTOR.equals(
                getCurrentUserRole(module, PortalUtils.getUserLoggedIn(request))))
        {
            activityInClause = " and a.id in " + getValidActivitiesForUserHQL(
                    PortalUtils.getUserLoggedIn(request), module, request);
        }

        String staticWhereHql = " and a.period.module.id="
                + module.getId().toString() + activityInClause;

        String periodId = request.getParameter("periodId");
        if (StringUtils.isInteger(periodId))
        {
            staticWhereHql += " and a.period.id=" + periodId;
        }

        String status = request.getParameter("activityStatus");
        if (StringUtils.isInteger(status))
        {
            staticWhereHql += " and a.status=" + status;
        }

        master.setStaticWhereHql(staticWhereHql);

        master.setRelationships(
                getRelationships_CCActivity(module, isStudentOrAlumni, request));

        if (module.isEnableActivityOwnerRole() || module.isEnableStaffRole())
        {
            List<String> emailPaths = new ArrayList<>();
            emailPaths.add("a.currentOwner.emailAddress");
            emailPaths.add("a.currentOwner.alternateEmail");
            emailPaths.add("a.currentOwner.username");
            emailPaths.add("a.currentOwner.userLocale");

            EmailingEmailer ee = new EmailingEmailer(emailPaths,
                    "a.currentOwner.firstName", "a.currentOwner.lastName",
                    "a.currentOwner.noCampaignEmails = 0",
                    "a.currentOwner.userLocale");

            EmailingModel em = new EmailingModel();
            em.getEmailers().add(ee);
            master.setEmailingModel(em);
        }

        searchModel.setMasterEntity(master);

        return searchModel;
    }

    private static List<Relationship> getRelationships_CCActivity(CCModule module,
            boolean isStudent, HttpServletRequest request)
    {
        Locale locale = PortalUtils.getLocale(request);

        List<Relationship> relationships = new ArrayList<>();

        Relationship positions = new Relationship(
                "i18n.gridSearch.criteriaModel.questionText.Positions",
                Relationship.TYPE.MANY_TO_ONE);
        positions
                .setRelatedEntity(getEntity_CCPosition(module, isStudent, request));
        positions.setAlwaysPresent(isStudent);
        positions.setForiegnKeyHql("p.activity.id");
        relationships.add(positions);

        if (!isStudent)
        {
            Relationship recordPositions = new Relationship(
                    "i18n.gridSearch.criteriaModel.questionText.Participants",
                    Relationship.TYPE.MANY_TO_ONE);
            recordPositions.setRelatedEntity(getEntity_CCRecordPosition(module,
                    true, false, true, "Participants", request));
            recordPositions.setForiegnKeyHql("rp.position.activity.id");
            relationships.add(recordPositions);
        }

        String departmentLabelBilingual = LocaleUtils.isL1(locale)
                ? module.getDepartmentLabel()
                : module.getL2DepartmentLabel();
        if (departmentLabelBilingual != null && module.isEnableActivityDirectors())
        {
            Relationship activityDirectors = new Relationship(
                    departmentLabelBilingual, Relationship.TYPE.MANY_TO_ONE);
            activityDirectors.setRelatedEntity(
                    getEntity_CCActivityDirectors(module, locale));
            activityDirectors.setForiegnKeyHql("ad.activity.id");
            relationships.add(activityDirectors);
        }

        Relationship positionValidators = new Relationship(
                "i18n.gridSearch.criteriaModel.entities.PositionValidator",
                Relationship.TYPE.MANY_TO_ONE);
        positionValidators.setRelatedEntity(getEntity_CCValidators(module, locale));
        positionValidators.setForiegnKeyHql("pv.position.activity.id");
        relationships.add(positionValidators);

        int userLoggedInId = PortalUtils.getUserLoggedIn(request).getId();
        relationships.addAll(TagAssignHelper.getTagRelationships(CCActivity.class,
                null, null, null, locale, null, "", "", userLoggedInId));

        if (InteractionHelper.isNotesEnabled())
        {
            relationships.add(getActivityRelationship_Notes(request));
        }
        if (InteractionHelper.isTasksEnabled())
        {
            relationships.add(getActivityRelationship_Tasks(request));
        }

        return relationships;
    }

    private static Entity getEntity_CCValidators(CCModule module, Locale locale)
    {
        CriteriaModel criteriaModel = new CriteriaModel();
        CriteriaGroup group = new CriteriaGroup(
                "i18n.gridSearch.criteriaModel.criteriaGroup.PositionValidatorCriteria");
        LinkedList<CriteriaQuestion> questions = new LinkedList<>();

        CriteriaQuestion q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.ValidatorName");
        q.setQuestionKey("pv.validator.id");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(getOptionChoices_PositionValidators(module));
        q.setVisibleResults(true);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.CCHelper.ValidatorE7597800476651348");
        q.setQuestionKey("pv.validator.emailAddress");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setVisibleResults(true);
        questions.add(q);

        group.setQuestions(questions);
        criteriaModel.addCriteriaGroup(group);

        Entity entity = new Entity(
                "i18n.gridSearch.criteriaModel.entities.PositionValidator",
                CCPositionValidator.class, "pv", "id", criteriaModel,
                "pv.validator.firstName", "desc", false);

        return entity;
    }

    private static Map<String, String> getOptionChoices_PositionValidators(
            CCModule module)
    {
        Map<String, String> map = new LinkedHashMap<>();

        List<Object[]> validators = PortalUtils.getHt().find(
                "select pv.validator.id, pv.validator.preferredFirstName, pv.validator.lastName, pv.validator.username"
                        + " from CCPositionValidator pv where pv.position.activity.period.module=?"
                        + " order by pv.validator.preferredFirstName, pv.validator.lastName",
                module);

        Integer userId = null;
        String firstName = null;
        String lastName = null;
        String username = null;
        for (Object[] v : validators)
        {
            userId = (Integer) v[0];
            firstName = (String) v[1];
            lastName = (String) v[2];
            username = (String) v[3];
            map.put(userId.toString(),
                    firstName + " " + lastName + " (" + username + ")");
        }

        return map;
    }

    private static Entity getEntity_CCActivityDirectors(CCModule module,
            Locale locale)
    {
        String departmentLabelBilingual = LocaleUtils.isL1(locale)
                ? module.getDepartmentLabel()
                : module.getL2DepartmentLabel();
        CriteriaModel criteriaModel = new CriteriaModel();
        CriteriaGroup group = new CriteriaGroup(PortalUtils.getMessageSource()
                .getMessage("i18n.CCHelper.Criteria0874133541459973",
                        new Object[] { departmentLabelBilingual }, locale));
        LinkedList<CriteriaQuestion> questions = new LinkedList<>();

        CriteriaQuestion q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.GroupName");
        q.setQuestionKey("ad.directors.name");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setVisibleCriteria(false);
        q.setColWidth(250);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.GroupName");
        q.setQuestionKey("ad.directors.id");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(getOptionChoices_ActivityDirectors(module, locale));
        q.setVisibleResults(false);
        questions.add(q);

        group.setQuestions(questions);
        criteriaModel.addCriteriaGroup(group);

        Entity entity = new Entity(departmentLabelBilingual,
                CCActivityDirectors.class, "ad", "id", criteriaModel,
                "ad.directors.name", "desc", false);

        return entity;
    }

    private static Entity getEntity_CCActivityDirectors_byPosition(CCModule module,
            Locale locale)
    {
        String departmentLabelBilingual = LocaleUtils.isL1(locale)
                ? module.getDepartmentLabel()
                : module.getL2DepartmentLabel();
        CriteriaModel criteriaModel = new CriteriaModel();
        CriteriaGroup group = new CriteriaGroup(PortalUtils.getMessageSource()
                .getMessage("i18n.CCHelper.Criteria0874133541459973",
                        new Object[] { departmentLabelBilingual }, locale));
        LinkedList<CriteriaQuestion> questions = new LinkedList<>();

        CriteriaQuestion q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.GroupName");
        q.setQuestionKey("ad.directors.name");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setVisibleCriteria(false);
        q.setColWidth(250);
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.GroupName");
        q.setQuestionKey("ad.directors.id");
        q.setType(CriteriaQuestion.TYPE_CHOICE);
        q.setOptionChoices(getOptionChoices_ActivityDirectors(module, locale));
        q.setColWidth(250);
        q.setVisibleResults(false);
        questions.add(q);

        group.setQuestions(questions);
        criteriaModel.addCriteriaGroup(group);

        Entity entity = new Entity(departmentLabelBilingual,
                CCActivityDirectors.class, "ad", "id", criteriaModel,
                "ad.directors.name", "desc", false);

        return entity;
    }

    private static Map<String, String> getOptionChoices_ActivityDirectors(
            CCModule module, Locale locale)
    {
        Map<String, String> map = new LinkedHashMap<>();

        boolean isLocaleL1 = LocaleUtils.isL1(locale);

        List<CCDirectors> directors = PortalUtils
                .getHt().find(
                        "from CCDirectors d where d.module=? order by d."
                                + (isLocaleL1 ? "name" : "l2Name") + " desc",
                        module);

        for (CCDirectors d : directors)
        {
            map.put(d.getId().toString(), isLocaleL1 ? d.getName() : d.getL2Name());
        }

        return map;
    }

    private static Entity getEntity_CCPosition(CCModule module, boolean isStudent,
            HttpServletRequest request)
    {
        Locale locale = PortalUtils.getLocale(request);

        CriteriaModel criteriaModel = new CriteriaModel();
        populateCriteriaModel_CCPosition(criteriaModel, module, "p", false, null,
                locale, PortalUtils.getUserLoggedIn(request));

        Entity position = new Entity("i18n.common.position", CCPosition.class, "p",
                "id", criteriaModel, "p.title", "desc", false);
        if (isStudent)
        {
            position.setStaticWhereHql(
                    " and p.enabled = true and p.approvedBy is not null and p.disabledBy is null and p.deniedBy is null and "
                            + "((p.program is not null and p.program.id in (select pt.program.id from PTProgramEnrollment pt where pt.student.id= "
                            + PortalUtils.getUserLoggedIn(request).getId()
                            + ")) or p.program is null)");
        }

        return position;
    }

    static void saveActivityAnswers(CCActivity activity, CCModule module,
            UserDetailsImpl userLoggedIn, HttpServletRequest request)
    {
        try
        {
            DFHelper.bindAnswers(
                    CCActivityQuestionModelHelper.getActivityQuestionModel(module),
                    activity, request, userLoggedIn, true);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    static void populateActivityQuestions(ModelAndView mv, CCModule module,
            CCActivity activity, UserDetailsImpl userLoggedIn, boolean canWrite)
    {
        DFModel dfModel = CCActivityQuestionModelHelper
                .getActivityQuestionModel(module).getDFModel();
        DFHelper.populateModel(dfModel, canWrite, activity, userLoggedIn);
        mv.addObject("dfModel", dfModel);
    }

    static boolean delete(CCPosition position)
    {
        boolean deleteSuccess = false;

        try
        {
            PortalUtils.getJt().update(
                    "delete from cc_position_validator where position = ?",
                    new Object[] { position.getId() });
            PortalUtils.getJt().update("delete from cc_pla where position = ?",
                    new Object[] { position.getId() });
            PortalUtils.getJt().update("delete from cc_paa where position = ?",
                    new Object[] { position.getId() });
            PortalUtils.getJt().update(
                    "delete compAsses from acrm_competency_record_assessment compAsses inner join acrm_competency_record comp on comp.id = compAsses.record where comp.position = ?",
                    new Object[] { position.getId() });
            PortalUtils.getJt().update(
                    "delete from acrm_competency_record where position = ?",
                    new Object[] { position.getId() });
            PortalUtils.getJt().update(
                    "delete from cc_record_position where position = ?",
                    new Object[] { position.getId() });
            PortalUtils.getJt().update(
                    "delete from cc_record_position_shortlist where position = ?",
                    new Object[] { position.getId() });
            PortalUtils.getJt().update(
                    "delete from portal_log where ccPosition = ?",
                    new Object[] { position.getId() });
            PortalUtils.getJt().update(
                    "delete from competency_anticipated where position = ?",
                    new Object[] { position.getId() });
            DFHelper.deleteFileUploadsForDeletedEntity(position);

            PortalUtils.getHt().delete(position);

            deleteSuccess = true;
        }
        catch (Exception e)
        {
            e.printStackTrace();
            deleteSuccess = false;
        }

        return deleteSuccess;
    }

    public static CCRecordPositionEntry getCCRecordPositionEntry(
            HttpServletRequest request)
    {
        CCRecordPositionEntry rpe = null;
        if (request.getParameter("rpeId") != null
                && !StringUtils.isEmpty(request.getParameter("rpeId")))
        {
            rpe = (CCRecordPositionEntry) PortalUtils.getHt().load(
                    CCRecordPositionEntry.class,
                    Integer.valueOf(Integer.parseInt(request.getParameter("rpeId"))));
        }
        else if (request.getAttribute("rpeId") != null)
        {
            rpe = (CCRecordPositionEntry) PortalUtils.getHt().load(
                    CCRecordPositionEntry.class,
                    (Integer) (request.getAttribute("rpeId")));
        }
        if (rpe == null)
        {
            rpe = new CCRecordPositionEntry();
            CCRecordPosition rp = getCCRecordPosition(request);
            rpe.setRecordPosition(rp);
        }
        if (rpe.getRecordPosition() == null)
        {
            rpe = null;
        }

        return rpe;
    }

    public static CCRecord getCCRecord(HttpServletRequest request)
    {
        CCRecord ret = (CCRecord) getEntity(CCRecord.class, "recordId", request);
        if (!isRecordValidForUser(ret, PortalUtils.getUserLoggedIn(request),
                request))
        {
            ret = null;
        }
        return ret;
    }

    public static CCRecordPosition getCCRecordPosition(HttpServletRequest request)
    {
        return getCCRecordPosition(request, false);
    }

    public static CCRecordPosition getCCRecordPosition(HttpServletRequest request,
            boolean bypassSecurity)
    {
        CCRecordPosition rp = null;

        if (request.getAttribute("recordPosition") != null)
        {
            rp = (CCRecordPosition) request.getAttribute("recordPosition");
        }
        else
        {
            if (request.getParameter("recordPosition") != null)
            {
                rp = (CCRecordPosition) getEntity(CCRecordPosition.class,
                        "recordPosition", request);
            }
            else
            {
                rp = (CCRecordPosition) getEntity(CCRecordPosition.class, "rpId",
                        request);
            }
        }
        final UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        if (!StringUtils.isEmpty(request.getParameter("confirmationCode"))
                && rp != null)
        {
            if (RequestUtils.getStringParameter(request, "confirmationCode", "")
                    .equals(rp.getConfirmationCode()))
            {
                bypassSecurity = true;
            }
        }
        if (rp != null && !bypassSecurity
                && !isRecordPositionValidForUser(rp, userLoggedIn, userLoggedIn))
        {
            rp = null;
        }

        return rp;
    }

    private static Object getEntity(Class c, String key, HttpServletRequest request)
    {
        return NHelper.getEntity(c, key, request);
    }

    public static boolean isRecordPositionValidForUser(CCRecordPosition rp,
            UserDetailsImpl user, UserDetailsImpl userLoggedIn)
    {
        boolean ret = false;
        String currentRole = CCHelper.getCurrentUserRole(rp.getRecord().getModule(),
                userLoggedIn);
        Map<String, Boolean> roles = CCHelper.getUserRoles(userLoggedIn,
                rp.getRecord().getModule());

        if (!StringUtils.isEmpty(currentRole)
                || CCHelper.getUserRoleListFromMap(roles).size() == 1)
        {
            String role = !StringUtils.isEmpty(currentRole) ? currentRole
                    : CCHelper.getUserRoleListFromMap(roles).get(0);

            if (CCHelper.CC_ROLE_ADMIN.equals(role)
                    || CCHelper.CC_ROLE_ACTIVITY_DIRECTOR.equals(role))
            {
                ret = true;
            }
            else if (CCHelper.CC_ROLE_STAFF.equals(role)
                    || CCHelper.CC_ROLE_ACTIVITY_OWNER.equals(role))
            {
                ret = false;
            }
            else if (CCHelper.CC_ROLE_VALIDATOR.equals(role)
                    && isPositionValidator(rp.getPosition(), user))
            {
                ret = true;
            }
            else if ((CCHelper.CC_ROLE_STUDENT.equals(role)
                    || CCHelper.CC_ROLE_ALUMNI.equals(role)) && user != null
                    && user.getId().equals(rp.getRecord().getOwner().getId()))
            {
                ret = true;
            }
            else
            {
                ret = false;
            }
        }
        return ret;
    }

    public static boolean isRecordValidForUser(CCRecord r, UserDetailsImpl user,
            HttpServletRequest request)
    {
        boolean ret = false;
        String currentRole = CCHelper.getCurrentUserRole(r.getModule(),
                PortalUtils.getUserLoggedIn(request));
        Map<String, Boolean> roles = CCHelper
                .getUserRoles(PortalUtils.getUserLoggedIn(request), r.getModule());

        if (!StringUtils.isEmpty(currentRole)
                || CCHelper.getUserRoleListFromMap(roles).size() == 1)
        {
            String role = !StringUtils.isEmpty(currentRole) ? currentRole
                    : CCHelper.getUserRoleListFromMap(roles).get(0);

            if (CCHelper.CC_ROLE_ADMIN.equals(role)
                    || CCHelper.CC_ROLE_ACTIVITY_DIRECTOR.equals(role))
            {
                ret = true;
            }
            else if (CCHelper.CC_ROLE_STAFF.equals(role)
                    || CCHelper.CC_ROLE_ACTIVITY_OWNER.equals(role)
                    || CCHelper.CC_ROLE_VALIDATOR.equals(role))
            {
                ret = false;
            }
            else if ((CCHelper.CC_ROLE_STUDENT.equals(role)
                    || CCHelper.CC_ROLE_ALUMNI.equals(role))
                    && user.getId().equals(r.getOwner().getId()))
            {
                ret = true;
            }
            else
            {
                ret = false;
            }
        }
        return ret;
    }

    public static boolean isPositionValidForUser(CCPosition position,
            UserDetailsImpl user, HttpServletRequest request)
    {
        boolean ret = false;
        String currentRole = CCHelper.getCurrentUserRole(
                position.getActivity().getPeriod().getModule(),
                PortalUtils.getUserLoggedIn(request));
        Map<String, Boolean> roles = CCHelper.getUserRoles(
                PortalUtils.getUserLoggedIn(request),
                position.getActivity().getPeriod().getModule());

        if (!StringUtils.isEmpty(currentRole)
                || CCHelper.getUserRoleListFromMap(roles).size() == 1)
        {
            String role = !StringUtils.isEmpty(currentRole) ? currentRole
                    : CCHelper.getUserRoleListFromMap(roles).get(0);

            if (CCHelper.CC_ROLE_ADMIN.equals(role)
                    || CCHelper.CC_ROLE_ACTIVITY_DIRECTOR.equals(role))
            {
                ret = true;
            }
            else if (CCHelper.CC_ROLE_STAFF.equals(role)
                    && isPositionStaff(position, user))
            {
                ret = true;
            }
            else if (CCHelper.CC_ROLE_ACTIVITY_OWNER.equals(role)
                    && isActivityOwner(position.getActivity(), user))
            {
                ret = true;
            }
            else if (CCHelper.CC_ROLE_VALIDATOR.equals(role)
                    && isPositionValidator(position, user))
            {
                ret = true;
            }
            else if ((CCHelper.CC_ROLE_STUDENT.equals(role)
                    || CCHelper.CC_ROLE_ALUMNI.equals(role)
                    || CCHelper.CC_ROLE_ANONYMOUS.equals(role))
                    && position.getStatus() == CCPosition.APPROVAL_STATUS_APPROVED)
            {
                ret = true;
            }
            else
            {
                ret = false;
            }
        }
        return ret;
    }

    static boolean canAddToRecord(CCPosition position, CCRecord record)
    {
        boolean ret = true;

        if (record == null)
        {
            ret = true;
        }
        else if (position == null)
        {
            ret = false;
        }
        else if (recordHasPosition(record, position))
        {
            ret = false;
        }
        else if (null != position.getProgram())
        {
            // If the CCPosition is associated with a PTProgram, then the
            // student must be enrolled in that PTProgram before they can add
            // the CCPosition to their CCRecord...

            PTProgramEnrollment enrollment = PTHelper
                    .getEnrollment(position.getProgram(), record.getOwner());

            ret = null != enrollment && (enrollment
                    .getStatus() == PTProgramEnrollment.STATUS_APPROVED
                    || enrollment
                            .getStatus() == PTProgramEnrollment.STATUS_COMPLETED);
        }

        return ret;
    }

    static boolean recordHasPosition(CCRecord record, CCPosition position)
    {
        boolean ret = false;

        if (null != record && null != position)
        {
            List n = PortalUtils.getHt().find(
                    "select rp.id from CCRecordPosition rp where rp.record = ? and rp.position = ?",
                    new Object[] { record, position });

            if (n.size() > 0)
            {
                ret = true;
            }
        }

        return ret;
    }

    public static CCRecord getCCRecord(CCModule module, UserDetailsImpl user)
    {
        CCRecord r = null;

        List records = PortalUtils.getHt().find(
                "from CCRecord r where r.module=? and r.owner=?",
                new Object[] { module, user });

        if (!records.isEmpty())
        {
            r = (CCRecord) records.get(0);
        }

        return r;
    }

    public static Integer getCCRecordID(CCModule module, UserDetailsImpl user)
    {
        Integer r = null;

        List records = PortalUtils.getHt().find(
                "select r.id from CCRecord r where r.module=? and r.owner=?",
                new Object[] { module, user });

        if (!records.isEmpty())
        {
            r = (Integer) records.get(0);
        }

        return r;
    }

    public static CCRecordPosition getCCRecordPosition(CCRecord record,
            CCPosition position)
    {
        CCRecordPosition rp = null;

        if (record != null && position != null)
        {
            List<CCRecordPosition> rps = PortalUtils.getHt().find(
                    "from CCRecordPosition rp where rp.record=? and rp.position=?",
                    new Object[] { record, position });

            if (!rps.isEmpty())
            {
                rp = rps.get(0);
            }
        }

        return rp;
    }

    public static CCRequestPositionModule getCCRequestPositionModule(
            CCModule ccModule)
    {
        CCRequestPositionModule ret = null;
        List<SiteElement> rpSes = PortalUtils.getSiteManager()
                .getElementsByType("ccRequestPositionController");

        for (SiteElement se : rpSes)
        {
            CCRequestPositionModule requestPositionModule = (CCRequestPositionModule) se
                    .getContentItem();
            if (requestPositionModule.getCcModule() != null && requestPositionModule
                    .getCcModule().getId().equals(ccModule.getId()))
            {
                ret = requestPositionModule;
                break;
            }
        }

        return ret;
    }

    public static void updateActivityPositionStatuses(CCActivity activity)
    {
        int status = activity.getStatus() == 1 ? 1 : 0;
        PortalUtils.getJt().execute("update cc_position set enabled=" + status
                + " where activity=" + activity.getId());
    }

    public static void updateActivityStatus(CCActivity activity,
            UserDetailsImpl userLoggedIn)
    {
        int activePositions = ((Integer) PortalUtils.getHt().find(
                "select count(p) from CCPosition p where p.enabled=true and p.activity=?",
                activity).get(0)).intValue();
        if (activePositions > 0
                && activity.getStatus() == CCActivity.STATUS_PENDING_APPROVAL)
        {
            activity.setStatus(CCActivity.STATUS_ACTIVE);
            PortalUtils.getHt().update(activity);

            PortalUtils.createPortalLog(
                    PortalLog.PL_CO_CURRICULAR_ACTIVITY_STATUS_CHANGE,
                    StringUtils.fillInPlaceholders(
                            "The status of activity '[0]' is being changed to '[1]' on account of one of its positions being enabled",
                            new Object[] { activity.getActivity(),
                                    PortalUtils.getI18nMessage("i18n.common.active",
                                            Locale.ENGLISH) }),
                    activity, userLoggedIn);
        }
    }

    static void updatePositionApprovalStatus(CCPosition position,
            int approvalStatus, UserDetailsImpl user)
    {
        if (position.getStatus() != approvalStatus)
        {
            int statusBeforeUpdate = position.getStatus();
            position.setStatus(approvalStatus);
            if (CCPosition.APPROVAL_STATUS_PENDING == approvalStatus)
            {
                position.setPendingRequest(true);
                position.setApprovedBy(null);
                position.setRequestAccepted(null);
                position.setDisabledBy(user);
                position.setDateDisabled(new Date());
                position.setDeniedBy(null);
                position.setRequestDenied(null);
                PortalUtils.getHt().update(position);
            }
            else if (CCPosition.APPROVAL_STATUS_APPROVED == approvalStatus)
            {
                position.setPendingRequest(false);
                position.setApprovedBy(user);
                position.setRequestAccepted(new Date());
                position.setDisabledBy(null);
                position.setDateDisabled(null);
                position.setDeniedBy(null);
                position.setRequestDenied(null);
                PortalUtils.getHt().update(position);

                enableValidators(position);
            }
            else if (CCPosition.APPROVAL_STATUS_DECLINED == approvalStatus)
            {
                position.setPendingRequest(false);
                position.setApprovedBy(null);
                position.setRequestAccepted(null);
                position.setDisabledBy(null);
                position.setDateDisabled(null);
                position.setDeniedBy(user);
                position.setRequestDenied(new Date());
                PortalUtils.getHt().update(position);
            }

            Map<Integer, String> i18nStatus = new HashMap<>();
            i18nStatus.put(CCPosition.APPROVAL_STATUS_PENDING,
                    "i18n.common.pending_approval");
            i18nStatus.put(CCPosition.APPROVAL_STATUS_APPROVED,
                    "i18n.common.approved");
            i18nStatus.put(CCPosition.APPROVAL_STATUS_DECLINED,
                    "i18n.common.declinedStatus");

            PortalUtils.createPortalLog(
                    PortalLog.PL_CO_CURRICULAR_POSITION_STATUS_CHANGE,
                    StringUtils.fillInPlaceholders(
                            "The status of position '[0]' has been changed from '[1]' to '[2]'",
                            new Object[] { position.getTitle(),
                                    PortalUtils.getI18nMessage(
                                            i18nStatus.get(statusBeforeUpdate),
                                            Locale.ENGLISH),
                                    PortalUtils.getI18nMessage(
                                            i18nStatus.get(approvalStatus),
                                            Locale.ENGLISH) }),
                    position, user);
        }
    }

    static void enableValidators(CCPosition position)
    {
        try
        {
            // Find all validators for this position...
            List<CCPositionValidator> vs = PortalUtils.getHt().find(
                    "from CCPositionValidator pv where pv.position=?", position);

            // For each validator...
            for (CCPositionValidator v : vs)
            {
                // Enable the validator-user by granting him the
                // "Co-Curricular Validator" permission (if they don't have it
                // already)
                applyValidatorGroup(v.getValidator());
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    static void applyValidatorGroup(UserDetailsImpl user)
    {
        try
        {
            if (!user.getAssignedTypes()
                    .containsKey(PersonGroupHelper.CC_VALIDATOR))
            {
                UserDetailsHelper.addUserGroup(user,
                        PersonGroupHelper.CC_VALIDATOR);
                PortalUtils.getHt().update(user);
            }
        }
        catch (Exception e)
        {
        }
    }

    public static ContentItem getUserCCRecord(RequestModuleUser rmu)
    {
        return getUserCCRecord(rmu.request, (CCModule) rmu.module, rmu.username);
    }

    public static CCRecord getUserCCRecord(HttpServletRequest request,
            CCModule module, String id)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        String userId = id;
        UserDetailsImpl user = UserDetailsHelper.getUserByUsername(userId);
        CCRecord r = getOrAddCCRecord(module, user);
        CCPosition position = getCCPosition(request);
        CCRecordPosition rp = getOrAddCCRecordPosition(r, position, request, false);
        UserDetailsImpl userLoggedin = PortalUtils.getUserLoggedIn(request);

        String status = request.getParameter("status");

        if (CCRecordPosition.STATUS_APPROVED.equalsIgnoreCase(status))
        {
            rp.setValidatedBy(userLoggedin);
            rp.setDateValidated(new Date());
            CCRecordPositionHelper.changeRecordPositionStatus(rp,
                    CCRecordPosition.STATUS_APPROVED, userLoggedin);
        }
        else if (CCRecordPosition.STATUS_DECLINED.equalsIgnoreCase(status))
        {
            rp.setValidatedBy(userLoggedin);
            rp.setDateValidated(new Date());
            CCRecordPositionHelper.changeRecordPositionStatus(rp,
                    CCRecordPosition.STATUS_DECLINED, userLoggedin);
        }
        else
        {
            CCRecordPositionHelper.changeRecordPositionStatus(rp,
                    CCRecordPosition.STATUS_PENDING, userLoggedin);
        }

        ht.saveOrUpdate(rp);

        if (rp != null && null == rp.getPosition().getProgram() && StringUtils
                .getBooleanValue(request.getParameter("sendEmailToParticipant")))
        {
            sendParticipantEmail(rp, module, PortalUtils.getUserLoggedIn(request));
        }

        if (!((module.isTrackAchievements() || module.isTrackLearningOutcomes())
                && module.isStudentSelectLOA()))
        {
            updateCCLearningOutcomeAchievement(module, rp, request);
        }

        return r;
    }

    public static void sendParticipantEmail(CCRecordPosition rp, CCModule module,
            UserDetailsImpl userLoggedIn)
    {
        boolean isL1Locale = LocaleUtils
                .isL1(rp.getRecord().getOwner().getUserLocale());
        Locale userLocale = isL1Locale ? LocaleUtils.getDefaultLocale()
                : LocaleUtils.getSecondaryLocale();

        String subject = isL1Locale ? module.getParticipantsSubject()
                : module.getL2ParticipantsSubject();
        String htmlMsg = isL1Locale ? module.getParticipantsBody()
                : module.getL2ParticipantsBody();

        htmlMsg += "<br>"
                + PortalUtils.getI18nMessage(
                        "i18n.CCController.Position1867307066134016", userLocale)
                + " |ACTIVITY| - |POSITION| ";

        subject = CCHelper.processEmailTokens(subject,
                rp.getRecord().getOwner().getPreferredFirstName(),
                rp.getRecord().getOwner().getLastName(),
                isL1Locale ? rp.getPosition().getActivity().getActivity()
                        : rp.getPosition().getActivity().getL2Activity(),
                isL1Locale ? rp.getPosition().getTitle()
                        : rp.getPosition().getL2Title(),
                rp.getStatus(), null, null);

        htmlMsg = CCHelper.processEmailTokens(htmlMsg,
                rp.getRecord().getOwner().getPreferredFirstName(),
                rp.getRecord().getOwner().getLastName(),
                isL1Locale ? rp.getPosition().getActivity().getActivity()
                        : rp.getPosition().getActivity().getL2Activity(),
                isL1Locale ? rp.getPosition().getTitle()
                        : rp.getPosition().getL2Title(),
                rp.getStatus(), null, null);

        EmailUtils.sendAndLog1Email(
                new EmailMessage(module.getFromEmailForParticipants(), userLoggedIn,
                        rp.getRecord().getOwner().getEmail(),
                        rp.getRecord().getOwner().getEmail(), subject, htmlMsg,
                        htmlMsg, null, false, false));
    }

    public static void saveCCRecordPositionOutcomes(HttpServletRequest request,
            CCModule module, CCRecordPosition rp)
    {
        if (module.isTrackLearningOutcomes())
        {
            if (module.isTrackAchievements()
                    && request.getParameterValues("pas") != null)
            {
                // CREATE THE ANTICIPATED ACHIEVEMENTS FOR THIS
                // POSITION...
                String[] jpaa = request.getParameterValues("pas");
                for (int i = 0; i < jpaa.length; i++)
                {
                    CCAchievement cca = (CCAchievement) PortalUtils.getHt()
                            .load(CCAchievement.class, Integer.valueOf(jpaa[i]));

                    if (cca.getLearningOutcome() != null)
                    {
                        CCPositionLearningOutcomeStudent cpls = CCHelper
                                .getPositionLearningOutcomeStudent(rp,
                                        cca.getLearningOutcome());

                        if (cpls == null)
                        {
                            cpls = new CCPositionLearningOutcomeStudent();
                            cpls.setRecordPosition(rp);
                            cpls.setLearningOutcome(cca.getLearningOutcome());
                            PortalUtils.getHt().save(cpls);
                        }
                    }

                    CCPositionAchievementStudent pas = new CCPositionAchievementStudent();
                    pas.setRecordPosition(rp);
                    pas.setAchievement(cca);
                    PortalUtils.getHt().save(pas);
                }
            }
            else if (!module.isTrackAchievements()
                    && request.getParameterValues("pls") != null)
            {
                String[] jpaa = request.getParameterValues("pls");
                for (int i = 0; i < jpaa.length; i++)
                {
                    CCLearningOutcome cclo = (CCLearningOutcome) PortalUtils.getHt()
                            .load(CCLearningOutcome.class, Integer.valueOf(jpaa[i]));
                    if (cclo != null)
                    {
                        CCPositionLearningOutcomeStudent cpls = CCHelper
                                .getPositionLearningOutcomeStudent(rp, cclo);
                        if (cpls == null)
                        {
                            cpls = new CCPositionLearningOutcomeStudent();
                            cpls.setRecordPosition(rp);
                            cpls.setLearningOutcome(cclo);
                            PortalUtils.getHt().save(cpls);
                        }
                    }
                }
            }
        }

    }

    public static void updateCCLearningOutcomeAchievement(CCModule module,
            CCRecordPosition rp, HttpServletRequest request)
    {
        if (!module.isEnableCompetencySelection() && !module.isStudentSelectLOA())
        {
            if (module.isTrackAchievements())
            {
                List<CCAchievement> achievements = PortalUtils.getHt().find(
                        "select a.achievement from CCPositionAchievementAdmin a where a.position = ?",
                        rp.getPosition());

                for (CCAchievement achievement : achievements)
                {
                    CCPositionAchievementStudent join = new CCPositionAchievementStudent();
                    join.setRecordPosition(rp);
                    join.setAchievement(achievement);
                    PortalUtils.getHt().save(join);
                }

            }
            if (module.isTrackLearningOutcomes())
            {
                List<CCLearningOutcome> learningOutcomes = PortalUtils.getHt().find(
                        "select lo.learningOutcome from CCPositionLearningOutcome lo where lo.position = ?",
                        rp.getPosition());

                for (CCLearningOutcome learningOutcome : learningOutcomes)
                {
                    CCPositionLearningOutcomeStudent join = new CCPositionLearningOutcomeStudent();
                    join.setRecordPosition(rp);
                    join.setLearningOutcome(learningOutcome);
                    PortalUtils.getHt().save(join);
                }
            }
        }
        else if (module.isEnableCompetencySelection()
                && module.isStudentsReceivePositionCompetencies()
                && module.getCompetencyQuestion() != null)
        {
            try
            {
                rp.setPersonalCompetencies(
                        (String) PropertyUtils.getProperty(rp.getPosition(),
                                module.getCompetencyQuestion().getAnswerField1()));
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
            PortalUtils.getHt().saveOrUpdate(rp);
        }
        else
        {
            saveCCRecordPositionOutcomes(request, module, rp);
        }
        if (CCRecordPosition.STATUS_NOT_SUBMITTED.equals(rp.getStatus()))
        {
            CCRecordPositionHelper.changeRecordPositionStatus(rp,
                    CCRecordPosition.STATUS_PENDING,
                    PortalUtils.getUserLoggedIn(request));
        }
    }

    public static void addCCRecordPosition(PTProgram program,
            UserDetailsImpl student, HttpServletRequest request)
    {

        List<CCPosition> positions = PortalUtils.getHt()
                .find("from CCPosition ccp where ccp.program=?", program);

        if (!positions.isEmpty())
        {
            CCPosition position = positions.get(0);
            CCModule module = position.getActivity().getPeriod().getModule();
            CCRecord record = getOrAddCCRecord(module, student);
            getOrAddCCRecordPosition(record, position, request, false);
        }
    }

    public synchronized static CCRecord getOrAddCCRecord(CCModule module,
            UserDetailsImpl student)
    {
        CCRecord record = null;

        try
        {
            record = getCCRecord(module, student);

            if (record == null)
            {
                record = new CCRecord();
                record.setDateCreated(new Date());
                record.setModule(module);
                record.setOwner(student);
                record.setLookupCode(generateLookupCode());
                PortalUtils.getHt().save(record);
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return record;
    }

    public static CCRecordPosition getOrAddCCRecordPosition(CCRecord record,
            CCPosition position, HttpServletRequest request,
            boolean addAsNotSubmittedStatus)
    {
        CCRecordPosition rp = null;

        if (null != record && null != position)
        {
            List<CCRecordPosition> rps = PortalUtils.getHt().find(
                    "from CCRecordPosition rp where rp.record=? and rp.position=?",
                    new Object[] { record, position });

            if (rps.isEmpty())
            {
                rp = CCRecordPositionHelper.newInstance(request);
                rp.setDateAdded(new Date());
                rp.setRecord(record);
                rp = CCRecordPositionHelper.updateWithExpSnapshot(rp,
                        rp.getRecord().getOwner().getMajor1Code(),
                        rp.getRecord().getOwner().getYearLevel(), new Date());
                rp.setPosition(position);
                rp.setAddedBy(PortalUtils.getUserLoggedIn(request));
                if (addAsNotSubmittedStatus)
                {
                    rp.setStatus(CCRecordPosition.STATUS_NOT_SUBMITTED);
                }
                PortalUtils.getHt().save(rp);
                record.setDateUpdated(new Date());
                PortalUtils.getHt().saveOrUpdate(record);

                PortalUtils.createPortalLog(
                        PortalLog.PL_CO_CURRICULAR_RECORD_POSITION_CREATED,
                        StringUtils.fillInPlaceholders(
                                "Record-position created for student '[0]' for position '[1]'",
                                new Object[] {
                                        record.getOwner().getFullNameWithUsername(),
                                        position.getTitle() }),
                        rp, PortalUtils.getUserLoggedIn(request));

                updatePositionOnRecordCount(rp.getPosition());
                assignCompetencies(rp);
            }
            else
            {
                rp = rps.get(0);
            }
        }

        return rp;
    }

    private static void assignCompetencies(CCRecordPosition rp)
    {
        CompetencyAchievedHelper.assignCompetencies(CCRecordPositionAchieved.class,
                rp, CCRecordPositionHelper.getCompetencyConfigs(rp));
    }

    static void updatePositionOnRecordCount(CCPosition pos)
    {
        int onRecordCount = ((Integer) PortalUtils.getHt().find(
                "select count(rp) from CCRecordPosition rp where rp.position=? ",
                new Object[] { pos }).get(0)).intValue();
        pos.setOnRecordCount(onRecordCount);
        PortalUtils.getHt().update(pos);
    }

    public synchronized static void addCCRecordPosition(GlobalEventRegistration reg,
            HttpServletRequest request)
    {
        CCPosition ccPosition = reg.getGlobalEvent().getcCPosition();

        if (ccPosition != null)
        {
            CCRecord ccRecord = getOrAddCCRecord(
                    ccPosition.getActivity().getPeriod().getModule(),
                    reg.getUser());

            getOrAddCCRecordPosition(ccRecord, ccPosition, request, false);
        }
    }

    public static void deleteCCActivity(CCActivity activity)
    {
        DFHelper.deleteFileUploadsForDeletedEntity(activity);
        DeletionNodeHelper.deleteContentItem(activity);
    }

    public static List getActivityDirectors(CCDirectors directors)
    {
        return PortalUtils.getHt().find(
                "select ad from CCActivityDirectors ad where ad.directors=?",
                directors);
    }

    public static boolean canView(CCPosition p, HttpServletRequest request)
    {
        boolean canView = false;

        try
        {
            UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
            String currentRole = getCurrentUserRole(
                    p.getActivity().getPeriod().getModule(),
                    PortalUtils.getUserLoggedIn(request));

            if (CC_ROLE_ADMIN.equals(currentRole)
                    || CC_ROLE_STUDENT.equals(currentRole)
                    || CC_ROLE_ALUMNI.equals(currentRole)
                    || CC_ROLE_ANONYMOUS.equals(currentRole)
                    || CC_ROLE_VALIDATOR.equals(currentRole))
            {
                canView = true;
            }
            else if (CC_ROLE_STAFF.equals(currentRole))
            {
                canView = (null != p.getActivity().getCreatedBy() && user.getId()
                        .equals(p.getActivity().getCreatedBy().getId()))
                        || (null != p.getActivity().getCurrentOwner()
                                && user.getId().equals(
                                        p.getActivity().getCurrentOwner().getId()))
                        || (null != p.getCreatedBy()
                                && user.getId().equals(p.getCreatedBy().getId()));
            }
            else if (CC_ROLE_ACTIVITY_OWNER.equals(currentRole))
            {
                canView = (null != p.getActivity().getCurrentOwner() && user.getId()
                        .equals(p.getActivity().getCurrentOwner().getId()));
            }
            else if (CC_ROLE_ACTIVITY_DIRECTOR.equals(currentRole))
            {
                canView = ((Integer) PortalUtils.getHt().find(
                        "select count(cad.id) from CCActivityDirectors cad, CCDirector d where cad.activity=? and cad.directors = d.directors and d.member=?",
                        new Object[] { p.getActivity(), user }).get(0))
                                .intValue() != 0;
            }
        }
        catch (Exception e)
        {
            canView = false;
        }

        return canView;
    }

    public static boolean canEdit(CCPosition p, HttpServletRequest request)
    {
        boolean canEdit = false;

        try
        {
            UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
            String currentRole = getCurrentUserRole(
                    p.getActivity().getPeriod().getModule(),
                    PortalUtils.getUserLoggedIn(request));

            if (CC_ROLE_STUDENT.equals(currentRole)
                    || CC_ROLE_ALUMNI.equals(currentRole)
                    || CC_ROLE_ANONYMOUS.equals(currentRole)
                    || CC_ROLE_VALIDATOR.equals(currentRole))
            {
                canEdit = false;
            }
            else if (CC_ROLE_STAFF.equals(currentRole))
            {
                canEdit = null != p.getActivity().getCurrentOwner() && user.getId()
                        .equals(p.getActivity().getCurrentOwner().getId());
            }
            else if (CC_ROLE_ACTIVITY_OWNER.equals(currentRole))
            {
                canEdit = null != p.getActivity().getCurrentOwner()
                        && user.getId()
                                .equals(p.getActivity().getCurrentOwner().getId())
                        && p.getActivity().getPeriod().getModule()
                                .isActivityOwnersCanEdit();
            }
            else if (CC_ROLE_ACTIVITY_DIRECTOR.equals(currentRole))
            {
                canEdit = canView(p, request);
            }
            else if (CC_ROLE_ADMIN.equals(currentRole))
            {
                canEdit = true;
            }
        }
        catch (Exception e)
        {
            canEdit = false;
        }

        return canEdit;
    }

    public static boolean canView(CCActivity a, HttpServletRequest request)
    {
        boolean canView = false;

        try
        {
            UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
            String currentRole = getCurrentUserRole(a.getPeriod().getModule(),
                    PortalUtils.getUserLoggedIn(request));

            if (CC_ROLE_ADMIN.equals(currentRole)
                    || CC_ROLE_STUDENT.equals(currentRole)
                    || CC_ROLE_ALUMNI.equals(currentRole)
                    || CC_ROLE_ANONYMOUS.equals(currentRole)
                    || CC_ROLE_VALIDATOR.equals(currentRole))
            {
                canView = true;
            }
            else if (CC_ROLE_STAFF.equals(currentRole))
            {
                canView = (null != a.getCreatedBy()
                        && user.getId().equals(a.getCreatedBy().getId()))
                        || (null != a.getCurrentOwner() && user.getId()
                                .equals(a.getCurrentOwner().getId()));
            }
            else if (CC_ROLE_ACTIVITY_OWNER.equals(currentRole))
            {
                canView = null != a.getCurrentOwner()
                        && user.getId().equals(a.getCurrentOwner().getId());
            }
            else if (CC_ROLE_ACTIVITY_DIRECTOR.equals(currentRole))
            {
                canView = ((Integer) PortalUtils.getHt().find(
                        "select count(cad.id) from CCActivityDirectors cad, CCDirector d where cad.activity=? and cad.directors = d.directors and d.member=?",
                        new Object[] { a, user }).get(0)).intValue() != 0;
            }
        }
        catch (Exception e)
        {
            canView = false;
        }

        return canView;
    }

    public static boolean canEdit(CCActivity a, HttpServletRequest request)
    {
        boolean canEdit = false;

        try
        {
            UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
            String currentRole = getCurrentUserRole(a.getPeriod().getModule(),
                    PortalUtils.getUserLoggedIn(request));

            if (CC_ROLE_STUDENT.equals(currentRole)
                    || CC_ROLE_ALUMNI.equals(currentRole)
                    || CC_ROLE_ANONYMOUS.equals(currentRole)
                    || CC_ROLE_VALIDATOR.equals(currentRole))
            {
                canEdit = false;
            }
            else if (CC_ROLE_STAFF.equals(currentRole))
            {
                canEdit = (a.getCurrentOwner() != null
                        && user.getId().equals(a.getCurrentOwner().getId()))
                        || (a.getCreatedBy() != null
                                && user.getId().equals(a.getCreatedBy().getId()));
            }
            else if (CC_ROLE_ACTIVITY_OWNER.equals(currentRole))
            {
                canEdit = null != a.getCurrentOwner()
                        && user.getId().equals(a.getCurrentOwner().getId())
                        && a.getPeriod().getModule().isActivityOwnersCanEdit();
            }
            else if (CC_ROLE_ACTIVITY_DIRECTOR.equals(currentRole))
            {
                canEdit = canView(a, request);
            }
            else if (CC_ROLE_ADMIN.equals(currentRole))
            {
                canEdit = true;
            }
        }
        catch (Exception e)
        {
            canEdit = false;
        }

        return canEdit;
    }

    public static void populateAccessFlags(ModelAndView mv, CCActivity a,
            HttpServletRequest request)
    {
        mv.addObject("canViewActivity", canView(a, request));
        mv.addObject("canEditActivity", canEdit(a, request));
    }

    public static void populateAccessFlags(ModelAndView mv, CCPosition p,
            HttpServletRequest request)
    {
        mv.addObject("canViewPosition", canView(p, request));
        mv.addObject("canEditPosition", canEdit(p, request));
    }

    public static SearchModel getSearchModel_TotalRecordsPerPeriod(CCModule module,
            SiteElement siteElement, HttpServletRequest request)
    {
        SearchModel model = new SearchModel();
        model.setOwnerModuleClassName(siteElement.getContentItemClass().getName());
        model.setOwnerModuleId(siteElement.getContentItemId().toString());
        model.setState(SearchModel.STATE_CONFIG_MASTER);
        model.setCanEmail(true);
        model.setCanEmailCampaign(true);
        model.setCanViewDetails(true);
        model.setCanViewCriteria(true);
        model.setCanExport(true);
        model.setCanSave(true);
        model.setShowQuestionOrder(false);
        model.setAttribute("ccModuleId", module.getId().toString());
        model.setAttribute("positionHqlAlias", "rp.position");

        CriteriaModel criteriaModel = new CriteriaModel();
        CCHelper.populateCriteriaModel_CCRecord(criteriaModel, module, "r",
                request);

        Entity master = new Entity(
                "i18n.gridSearch.criteriaModel.entities.CoCurricularRecords",
                CCRecord.class, "r", "id", criteriaModel, "r.owner.lastName", "asc",
                true);

        model.setMasterEntity(master);
        return model;
    }

    public static ModelAndView ajaxCocurricularTable(HttpServletRequest request)
    {
        ModelAndView mv = new ModelAndView("ccrm/ccrm_studentCcrTable").addObject(
                SiteController.SHORT_CIRCUIT_ATTRIBUTE_NAME, Boolean.TRUE);
        GridQuery query = GridQueryFactory.newInstance("ccrm_studentCCR", request,
                mv);

        GridHelper.runQuery(query);

        mv.addObject("myRecordMode", "false");
        mv.addObject("ccModuleSEMap", getCCModules().stream().map(
                m -> CollectionUtils.newMapEntry(m.getId(), m.getSiteElementPath()))
                .collect(Collectors.toMap(Entry::getKey, Entry::getValue)));

        return mv;
    }

    public static ModelAndView exportCcrGrid(HttpServletRequest request)
    {
        ModelAndView mv = null;

        try
        {
            List<Object[]> finalResults = new LinkedList<>();

            if (!StringUtils.isEmpty(request.getParameter("acrmUserId")))
            {
                UserDetailsImpl au = AcrmRegistrationController.getUser(request);

                String selectClause = "select rp.position.activity.category, rp.position.activity.period.name, rp.position.activity.activity, "
                        + " rp.position.title, rp.dateAdded, rp.status, user.preferredFirstName, user.lastName ";
                String fromWhereClause = " from CCRecordPosition rp left join rp.validatedBy as user where rp.record.owner.id="
                        + au.getId();

                List<Object[]> results = GridHelper.runQuery(request, mv,
                        fromWhereClause, selectClause, "rp.id");

                for (Object[] r : results)
                {
                    r[4] = r[4] != null
                            ? DateUtils.formatDate(
                                    DateUtils.parseDate(
                                            r[4].toString(),
                                            DBUtils.DB_DATE_TIME_FORMAT, null),
                                    DateUtils.getOrbisDateTimeFormat(
                                            DateUtils.DF_LONG_DATE)
                                            + " @ "
                                            + DateUtils.getOrbisDateTimeFormat(
                                                    DateUtils.DF_SHORT_TIME_2),
                                    null)
                            : "N/A";
                    r[6] = (r[6] != null ? r[6] : "N/A") + " "
                            + (r[6] != null ? r[7] : "");
                    r[7] = "";
                    finalResults.add(r);
                }

                mv = GridHelper.exportAndLogGrid(finalResults,
                        "Cocurricular Activities for " + au.getFullName(),
                        new String[] { "Category", "Period", "Activity", "Position",
                                "Date Created", "Status", "Validated By" },
                        "", request);
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        return mv;
    }

    public static SearchModel getSearchModel_ShowGridStats(
            HttpServletRequest request, SearchModel model, SiteElement siteElement,
            CCModule module)
    {
        String gridParam = request.getParameter("grid");
        String extraHiddenFields = String.format("{'gridSearch':'%s'", gridParam);
        String grid = gridParam;
        String activityId = request.getParameter("activityId");
        String positionId = request.getParameter("positionId");
        int periodId = 0;
        if (!StringUtils.isEmpty(request.getParameter("periodId")))
        {
            periodId = (Integer.valueOf(request.getParameter("periodId")).intValue());
        }

        String activityInClause = CCHelper.getValidActivitiesForUserHQL(
                PortalUtils.getUserLoggedIn(request), module, request);

        if (!StringUtils.isEmpty(activityId))
        {
            activityInClause = " (" + activityId + ") ";
        }

        Locale locale = PortalUtils.getLocale(request);
        if (GRID_STATS_RECORDS.equals(grid))
        {
            model = CCHelper.getSearchModel_Records(module, siteElement, request);

            model.getMasterEntity()
                    .setStaticFromHql(model.getMasterEntity().getStaticFromHql()
                            + ", CCRecordPosition rp");
            model.getMasterEntity().setStaticWhereHql((!StringUtils
                    .isEmpty(model.getMasterEntity().getStaticWhereHql())
                            ? model.getMasterEntity().getStaticWhereHql()
                            : "")
                    + " and rp.record = r and rp.position.activity.period.id = "
                    + periodId
                    + (!StringUtils.isEmpty(activityInClause)
                            ? " and rp.position.activity.id in " + activityInClause
                            : "")
                    + (!StringUtils.isEmpty(positionId)
                            ? " and rp.position.id = " + positionId
                            : "")
                    + " and rp.position.activity.id in "
                    + CCHelper.getValidActivitiesForUserHQL(
                            PortalUtils.getUserLoggedIn(request), module, request));
        }
        else if (GRID_STATS_TOTAL_RECORDS.equals(grid))
        {
            model = CCHelper.getSearchModel_TotalRecordsPerPeriod(module,
                    siteElement, request);

            model.getMasterEntity().setStaticWhereHql((!StringUtils
                    .isEmpty(model.getMasterEntity().getStaticWhereHql())
                            ? model.getMasterEntity().getStaticWhereHql()
                            : "")
                    + " and r.module.id=" + module.getId().toString()
                    + " and r.id in (select rp.record.id from CCRecordPosition rp where rp.position.activity.period.id = "
                    + periodId
                    + (!StringUtils.isEmpty(activityInClause)
                            ? " and rp.position.activity.id in " + activityInClause
                            : "")
                    + (!StringUtils.isEmpty(positionId)
                            ? " and rp.position.id = " + positionId
                            : "")
                    + ")");
        }
        else if (GRID_STATS_TOTAL_RECORDS_AT_LEAST_ONE.equals(grid))
        {
            model = CCHelper.getSearchModel_RecordPositions(module, siteElement,
                    request);

            model.getMasterEntity()
                    .setStaticWhereHql((!StringUtils
                            .isEmpty(model.getMasterEntity().getStaticWhereHql())
                                    ? model.getMasterEntity().getStaticWhereHql()
                                    : "")
                            + " and rp.position.activity.period.id = " + periodId
                            + (!StringUtils.isEmpty(activityInClause)
                                    ? " and rp.position.activity.id in "
                                            + activityInClause
                                    : "")
                            + (!StringUtils.isEmpty(positionId)
                                    ? " and rp.position.id = " + positionId
                                    : ""));
        }
        else if (GRID_STATS_PENDING_POSITIONS.equals(grid))
        {
            model = CCHelper.getSearchModel_Positions(module, siteElement,
                    CCPosition.APPROVAL_STATUS_PENDING, request,
                    StringUtils.isEmpty(activityId));

            model.setCanMassUpdate(true);

            // Mass Approve Positions Button
            SearchMassAssignButton mapBtn = new SearchMassAssignButton();
            mapBtn.setLabel("Mass Approve Positions");
            mapBtn.setAction("massApprovePositions");
            mapBtn.addAdditionalParam("moduleId", module.getId().toString());
            model.addMassAssignButton("massApprovePositions", mapBtn);

            model.getMasterEntity()
                    .setStaticWhereHql((!StringUtils
                            .isEmpty(model.getMasterEntity().getStaticWhereHql())
                                    ? model.getMasterEntity().getStaticWhereHql()
                                    : "")
                            + " and p.status=0 "
                            + (!StringUtils.isEmpty(activityInClause)
                                    ? " and p.activity.id in " + activityInClause
                                    : "")
                            + (!StringUtils.isEmpty(positionId)
                                    ? " and p.id = " + positionId
                                    : "")
                            + (periodId != 0
                                    ? " and p.activity.period.id=" + periodId
                                    : ""));
        }
        else if (GRID_STATS_ACTIVE_POSITIONS.equals(grid))
        {
            model = CCHelper.getSearchModel_Positions(module, siteElement, null,
                    request, true);

            model.getMasterEntity()
                    .setStaticWhereHql((!StringUtils
                            .isEmpty(model.getMasterEntity().getStaticWhereHql())
                                    ? model.getMasterEntity().getStaticWhereHql()
                                    : "")
                            + " and p.enabled = true and p.activity.period.id = "
                            + periodId
                            + (!StringUtils.isEmpty(activityInClause)
                                    ? " and p.activity.id in " + activityInClause
                                    : "")
                            + (!StringUtils.isEmpty(positionId)
                                    ? " and p.id = " + positionId
                                    : ""));
        }
        else if (GRID_STATS_APPROVED_POSITIONS.equals(grid))
        {
            model = CCHelper.getSearchModel_Positions(module, siteElement,
                    CCPosition.APPROVAL_STATUS_APPROVED, request,
                    StringUtils.isEmpty(activityId));

            model.getMasterEntity()
                    .setStaticWhereHql((!StringUtils
                            .isEmpty(model.getMasterEntity().getStaticWhereHql())
                                    ? model.getMasterEntity().getStaticWhereHql()
                                    : "")
                            + " and p.status=1 and p.activity.period.id = "
                            + periodId
                            + (!StringUtils.isEmpty(activityInClause)
                                    ? " and p.activity.id in " + activityInClause
                                    : "")
                            + (!StringUtils.isEmpty(positionId)
                                    ? " and p.id = " + positionId
                                    : ""));
        }
        else if (GRID_STATS_APPROVED_AND_ENABLED_POSITIONS.equals(grid))
        {
            model = CCHelper.getSearchModel_Positions(module, siteElement,
                    CCPosition.APPROVAL_STATUS_APPROVED, request, true);

            model.getMasterEntity().setStaticWhereHql((!StringUtils
                    .isEmpty(model.getMasterEntity().getStaticWhereHql())
                            ? model.getMasterEntity().getStaticWhereHql()
                            : "")
                    + " and p.status=1 and p.enabled=true and p.activity.period.id = "
                    + periodId
                    + (!StringUtils.isEmpty(activityInClause)
                            ? " and p.activity.id in " + activityInClause
                            : "")
                    + (!StringUtils.isEmpty(positionId)
                            ? " and p.id = " + positionId
                            : ""));
        }
        else if (GRID_STATS_APPROVED_AND_DISABLED_POSITIONS.equals(grid))
        {
            model = CCHelper.getSearchModel_Positions(module, siteElement,
                    CCPosition.APPROVAL_STATUS_APPROVED, request, true);

            model.getMasterEntity().setStaticWhereHql((!StringUtils
                    .isEmpty(model.getMasterEntity().getStaticWhereHql())
                            ? model.getMasterEntity().getStaticWhereHql()
                            : "")
                    + " and p.status=1 and p.enabled=false and p.activity.period.id = "
                    + periodId
                    + (!StringUtils.isEmpty(activityInClause)
                            ? " and p.activity.id in " + activityInClause
                            : "")
                    + (!StringUtils.isEmpty(positionId)
                            ? " and p.id = " + positionId
                            : ""));
        }
        else if (GRID_STATS_DECLINED_POSITIONS.equals(grid))
        {
            model = CCHelper.getSearchModel_Positions(module, siteElement,
                    CCPosition.APPROVAL_STATUS_DECLINED, request,
                    StringUtils.isEmpty(activityId));

            model.getMasterEntity()
                    .setStaticWhereHql((!StringUtils
                            .isEmpty(model.getMasterEntity().getStaticWhereHql())
                                    ? model.getMasterEntity().getStaticWhereHql()
                                    : "")
                            + " and p.status=2 and p.activity.period.id = "
                            + periodId
                            + (!StringUtils.isEmpty(activityInClause)
                                    ? " and p.activity.id in " + activityInClause
                                    : "")
                            + (!StringUtils.isEmpty(positionId)
                                    ? " and p.id = " + positionId
                                    : ""));
        }
        else if (GRID_STATS_TOTAL_POSITIONS.equals(grid))
        {
            model = CCHelper.getSearchModel_Positions(module, siteElement, null,
                    request, true);

            model.getMasterEntity()
                    .setStaticWhereHql((!StringUtils
                            .isEmpty(model.getMasterEntity().getStaticWhereHql())
                                    ? model.getMasterEntity().getStaticWhereHql()
                                    : "")
                            + " and p.activity.period.id = " + periodId
                            + (!StringUtils.isEmpty(activityInClause)
                                    ? " and p.activity.id in " + activityInClause
                                    : "")
                            + (!StringUtils.isEmpty(positionId)
                                    ? " and p.id = " + positionId
                                    : ""));
        }
        else if (GRID_STATS_DISABLED_POSITIONS.equals(grid))
        {
            model = CCHelper.getSearchModel_Positions(module, siteElement, null,
                    request, true);

            model.getMasterEntity()
                    .setStaticWhereHql((!StringUtils
                            .isEmpty(model.getMasterEntity().getStaticWhereHql())
                                    ? model.getMasterEntity().getStaticWhereHql()
                                    : "")
                            + " and p.enabled = 0 and p.activity.period.id = "
                            + periodId
                            + (!StringUtils.isEmpty(activityInClause)
                                    ? " and p.activity.id in " + activityInClause
                                    : "")
                            + (!StringUtils.isEmpty(positionId)
                                    ? " and p.id = " + positionId
                                    : ""));
        }
        else if (GRID_STATS_PENDING_VALIDATIONS.equals(grid))
        {
            model = CCHelper.getSearchModel_Validations(module, siteElement,
                    request);
            model.getMasterEntity().setEntityLabel(PortalUtils.getI18nMessage(
                    "i18n.CCHelper.PendingVal3997058255712360", locale)
                    + (!StringUtils.isEmpty(request.getParameter("periodName"))
                            ? request.getParameter("periodName")
                            : ""));
            model.getMasterEntity()
                    .setStaticWhereHql((!StringUtils
                            .isEmpty(model.getMasterEntity().getStaticWhereHql())
                                    ? model.getMasterEntity().getStaticWhereHql()
                                    : "")
                            + " and rp.status = '" + CCRecordPosition.STATUS_PENDING
                            + "' and rp.position.activity.period.id = " + periodId
                            + (!StringUtils.isEmpty(activityInClause)
                                    ? " and rp.position.activity.id in "
                                            + activityInClause
                                    : "")
                            + (!StringUtils.isEmpty(positionId)
                                    ? " and rp.position.id = " + positionId
                                    : ""));

            extraHiddenFields += ", 'limitedEdit':'true', 'myRecordMode':'false'";
        }
        else if (GRID_STATS_DECLINED_VALIDATIONS.equals(grid))
        {
            model = CCHelper.getSearchModel_Validations(module, siteElement,
                    request);
            model.getMasterEntity().setEntityLabel(PortalUtils.getI18nMessage(
                    "i18n.CCHelper.DeclinedVa8989279942802557", locale)
                    + (!StringUtils.isEmpty(request.getParameter("periodName"))
                            ? request.getParameter("periodName")
                            : ""));
            model.getMasterEntity()
                    .setStaticWhereHql((!StringUtils
                            .isEmpty(model.getMasterEntity().getStaticWhereHql())
                                    ? model.getMasterEntity().getStaticWhereHql()
                                    : "")
                            + " and rp.status = '"
                            + CCRecordPosition.STATUS_DECLINED
                            + "' and rp.position.activity.period.id = " + periodId
                            + (!StringUtils.isEmpty(activityInClause)
                                    ? " and rp.position.activity.id in "
                                            + activityInClause
                                    : "")
                            + (!StringUtils.isEmpty(positionId)
                                    ? " and rp.position.id = " + positionId
                                    : ""));
            extraHiddenFields += ", 'limitedEdit':'true', 'myRecordMode':'false'";
        }
        else if (GRID_STATS_ACCEPTED_VALIDATIONS.equals(grid))
        {
            model = CCHelper.getSearchModel_Validations(module, siteElement,
                    request);
            model.getMasterEntity().setEntityLabel(PortalUtils.getI18nMessage(
                    "i18n.CCHelper.ApprovedVa6280365913589828", locale)
                    + (!StringUtils.isEmpty(request.getParameter("periodName"))
                            ? request.getParameter("periodName")
                            : ""));
            model.getMasterEntity()
                    .setStaticWhereHql((!StringUtils
                            .isEmpty(model.getMasterEntity().getStaticWhereHql())
                                    ? model.getMasterEntity().getStaticWhereHql()
                                    : "")
                            + " and rp.status = '"
                            + CCRecordPosition.STATUS_APPROVED
                            + "' and rp.position.activity.period.id = " + periodId
                            + (!StringUtils.isEmpty(activityInClause)
                                    ? " and rp.position.activity.id in "
                                            + activityInClause
                                    : "")
                            + (!StringUtils.isEmpty(positionId)
                                    ? " and rp.position.id = " + positionId
                                    : ""));
            extraHiddenFields += ", 'limitedEdit':'true', 'myRecordMode':'false'";
        }
        else if (GRID_STATS_COMPLETED_VALIDATIONS.equals(grid))
        {
            model = CCHelper.getSearchModel_Validations(module, siteElement,
                    request);
            model.getMasterEntity().setEntityLabel(PortalUtils.getI18nMessage(
                    "i18n.CCHelper.CompletedV4667381842849927", locale)
                    + (!StringUtils.isEmpty(request.getParameter("periodName"))
                            ? request.getParameter("periodName")
                            : ""));
            model.getMasterEntity()
                    .setStaticWhereHql((!StringUtils
                            .isEmpty(model.getMasterEntity().getStaticWhereHql())
                                    ? model.getMasterEntity().getStaticWhereHql()
                                    : "")
                            + " and rp.status != '"
                            + CCRecordPosition.STATUS_PENDING
                            + "' and rp.position.activity.period.id = " + periodId
                            + (!StringUtils.isEmpty(activityInClause)
                                    ? " and rp.position.activity.id in "
                                            + activityInClause
                                    : "")
                            + (!StringUtils.isEmpty(positionId)
                                    ? " and rp.position.id = " + positionId
                                    : ""));

            extraHiddenFields += ", 'limitedEdit':'true', 'myRecordMode':'false'";
        }
        else if (GRID_STATS_TOTAL_VALIDATIONS.equals(grid))
        {
            model = CCHelper.getSearchModel_Validations(module, siteElement,
                    request);

            model.getMasterEntity()
                    .setStaticWhereHql((!StringUtils
                            .isEmpty(model.getMasterEntity().getStaticWhereHql())
                                    ? model.getMasterEntity().getStaticWhereHql()
                                    : "")
                            + " and (rp.status = '"
                            + CCRecordPosition.STATUS_PENDING + "' or rp.status = '"
                            + CCRecordPosition.STATUS_APPROVED
                            + "' or rp.status = '"
                            + CCRecordPosition.STATUS_DECLINED
                            + "') and rp.position.activity.period.id = " + periodId
                            + (!StringUtils.isEmpty(activityInClause)
                                    ? " and rp.position.activity.id in "
                                            + activityInClause
                                    : "")
                            + (!StringUtils.isEmpty(positionId)
                                    ? " and rp.position.id = " + positionId
                                    : ""));
            extraHiddenFields += ", 'limitedEdit':'true', 'myRecordMode':'false'";
        }
        else if (GRID_STATS_VALIDATORS_WITH_PENDING.equals(grid))
        {
            String additionalPvHql = String.format(
                    " and pv.position.id in (select rp.position.id from CCRecordPosition rp where rp.status = '%s') and pv.position.activity.period.id = %d",
                    CCRecordPosition.STATUS_PENDING, periodId);
            if (!StringUtils.isEmpty(activityInClause))
            {
                additionalPvHql += " and pv.position.activity.id in "
                        + activityInClause;
            }

            if (!StringUtils.isEmpty(positionId))
            {
                additionalPvHql += " and pv.position.id = " + positionId;
            }

            model = CCHelper.getSearchModel_Validators(module, request, null,
                    additionalPvHql, false, true);
        }
        else if (GRID_STATS_VALIDATORS_WITH_APPROVED.equals(grid))
        {

            String additionalRpHql = String.format(
                    " and rp.status = '%s' and rp.position.activity.period.id = %d ",
                    CCRecordPosition.STATUS_APPROVED, periodId);
            if (!StringUtils.isEmpty(activityInClause))
            {
                additionalRpHql += " and rp.position.activity.id in "
                        + activityInClause;
            }

            if (!StringUtils.isEmpty(positionId))
            {
                additionalRpHql += " and rp.position.id = " + positionId;
            }

            model = CCHelper.getSearchModel_Validators(module, request,
                    additionalRpHql, null, true, false);

        }
        else if (GRID_STATS_VALIDATORS_WITH_DECLINED.equals(grid))
        {
            String additionalRpHql = String.format(
                    " and rp.status = '%s' and rp.position.activity.period.id = %d ",
                    CCRecordPosition.STATUS_DECLINED, periodId);
            if (!StringUtils.isEmpty(activityInClause))
            {
                additionalRpHql += " and rp.position.activity.id in "
                        + activityInClause;
            }

            if (!StringUtils.isEmpty(positionId))
            {
                additionalRpHql += " and rp.position.id = " + positionId;
            }

            model = CCHelper.getSearchModel_Validators(module, request,
                    additionalRpHql, null, true, false);
        }
        else if (GRID_STATS_TOTAL_VALIDATORS.equals(grid))
        {
            String additionalPvHql = " and pv.position.activity.period = "
                    + +periodId;
            String additionalRpHql = " and rp.validatedBy is not null and rp.position.activity.period.id = "
                    + +periodId;

            if (!StringUtils.isEmpty(activityInClause))
            {
                additionalPvHql += " and pv.position.activity.id in "
                        + activityInClause;
                additionalRpHql += " and rp.position.activity.id in "
                        + activityInClause;
            }

            if (!StringUtils.isEmpty(positionId))
            {
                additionalPvHql += " and pv.position.id = " + positionId;
                additionalRpHql += " and rp.position.id = " + positionId;
            }

            model = CCHelper.getSearchModel_Validators(module, request,
                    additionalRpHql, additionalPvHql, true, true);
        }
        else if (GRID_STATS_VALIDATORS_SEARCH.equals(grid))
        {
            model = AcrmHelper.getAcrmUserSearchModel(null, request);

            String validator = "(select distinct pv.validator.id from CCPositionValidator pv "
                    + (!StringUtils.isEmpty(activityInClause)
                            ? "where pv.position.activity.id in " + activityInClause
                                    + (!StringUtils.isEmpty(positionId)
                                            ? " and pv.position.id = " + positionId
                                            : "")
                            : (!StringUtils.isEmpty(positionId)
                                    ? "where pv.position.id = " + positionId
                                    : ""))
                    + "))";

            model.getMasterEntity().setStaticWhereHql((!StringUtils
                    .isEmpty(model.getMasterEntity().getStaticWhereHql())
                            ? model.getMasterEntity().getStaticWhereHql()
                            : "")
                    + " and (au.id in (select distinct rp.validatedBy.id from CCRecordPosition rp where rp.validatedBy is not null "
                    + (!StringUtils.isEmpty(activityInClause)
                            ? " and rp.position.activity.id in " + activityInClause
                            : "")
                    + (!StringUtils.isEmpty(positionId)
                            ? " and rp.position.id=" + positionId
                            : "")
                    + ") or au.id in " + validator);
        }
        extraHiddenFields += "}";

        model.setCanViewCriteria(true);
        model.setCanExport(true);
        if (GRID_STATS_VALIDATORS_SEARCH.equals(grid))
        {
            model.setCanEmail(true);
            model.setCanEmailCampaign(true);
        }
        else
        {
            model.setCanEmail(false);
            model.setCanEmailCampaign(false);
        }
        model.setPlugins(new ArrayList<String>());
        model.setAttribute("extraHiddenFields", extraHiddenFields);
        model.setState(SearchModel.STATE_SHOW_RESULTS);

        return model;
    }

    public static SearchModel getSearchModel_TimePeriodActivities(
            HttpServletRequest request, SearchModel model, SiteElement se,
            CCModule module)
    {
        Locale locale = PortalUtils.getLocale(request);

        SearchModel searchModel = new SearchModel();
        searchModel.setOwnerModuleClassName(se.getContentItemClass().getName());
        searchModel.setOwnerModuleId(se.getContentItemId().toString());
        searchModel.setState(SearchModel.STATE_SHOW_RESULTS);
        searchModel.setCanEmail(false);
        searchModel.setCanEmailCampaign(false);
        searchModel.setCanViewDetails(false);
        searchModel.setCanViewCriteria(false);
        searchModel.setShowQuestionOrder(false);
        searchModel.setCanSave(true);
        searchModel.setAttribute("ccModuleId", module.getId().toString());
        searchModel.setAttribute("activityHqlAlias", "a");

        CriteriaModel criteriaModel = new CriteriaModel();
        populateCriteriaModel_CCActivity(criteriaModel, module, false, "timePeriod",
                locale, PortalUtils.getUserLoggedIn(request));

        String entityTitle = "", type = "", grid = request.getParameter("grid");
        if (!StringUtils.isEmpty(grid))
        {
            if ("categories".equalsIgnoreCase(grid))
            {
                entityTitle = "Number of Categories in Period "
                        + request.getParameter("periodName");
                type = ".category";
            }
            else if ("organizations".equalsIgnoreCase(grid))
            {
                entityTitle = "Number of Organizations in Period "
                        + request.getParameter("periodName");
                type = ".organization";
            }
            else if ("departments".equalsIgnoreCase(grid))
            {
                entityTitle = "Number of Departments in Period "
                        + request.getParameter("periodName");
                type = ".department";
            }
        }

        Entity master = new Entity(entityTitle, CCActivity.class, "a", "id",
                criteriaModel, "a" + type, "asc", true);

        String periodId = request.getParameter("periodId"), staticWhereHql = "";
        if (StringUtils.isInteger(periodId))
        {
            staticWhereHql += " and a.period.id=" + periodId;
        }

        master.setStaticWhereHql(staticWhereHql);
        searchModel.setMasterEntity(master);
        return searchModel;
    }

    public static String[] buildHqlForValidatorsData(UserDetailsImpl validator,
            String status, boolean onlyThisValidator, boolean activePeriodsOnly,
            CCModule module, HttpServletRequest request)
    {
        String userClause = "";
        if ((onlyThisValidator && (CCRecordPosition.STATUS_APPROVED.equals(status)
                || CCRecordPosition.STATUS_DECLINED.equals(status)))
                || (onlyThisValidator
                        && PortalUtils.getUserLoggedIn(request).getGroupWithName(
                                PersonGroupHelper.CC_ADMINISTRATOR) != null
                        && !CCRecordPosition.STATUS_PENDING.equals(status)))
        {
            userClause = " and p.validatedBy.id=" + validator.getId();
        }

        String selectClause = "select count(p) ", fromClause = "", whereClause = "",
                activeClause = "";

        if (activePeriodsOnly)
        {
            activeClause = " and p.position.activity.period.activeVal = true ";
        }

        fromClause = " from CCRecordPosition p, CCPositionValidator v ";
        whereClause = " where v.position=p.position and v.validator.id="
                + validator.getId() + activeClause + userClause + " and p.status='"
                + status + "' and p.position.activity.period.module.id="
                + module.getId();

        return new String[] { selectClause, fromClause, whereClause };
    }

    public static CCRecordPositionShortlist getRecordPositionShortlist(
            CCRecord record, CCPosition position)
    {
        CCRecordPositionShortlist rps = null;
        List rpss = PortalUtils.getHt().find(
                "from CCRecordPositionShortlist rps where rps.record=? and rps.position=?",
                new Object[] { record, position });
        if (!rpss.isEmpty())
        {
            rps = (CCRecordPositionShortlist) rpss.get(0);
        }
        return rps;
    }

    public static ModelAndView updateRecordPositionShortlist(
            HttpServletRequest request, CCModule module)
    {
        CCRecord record = CCHelper.getCCRecord(module,
                PortalUtils.getUserLoggedIn(request));
        CCPosition position = CCHelper.getCCPosition(request);
        boolean addToShortlist = RequestUtils.getBooleanParameter(request,
                "addToShortlist", false);
        if (record != null && position != null)
        {
            CCRecordPositionShortlist rps = CCHelper
                    .getRecordPositionShortlist(record, position);
            if (addToShortlist && rps == null)
            {
                rps = new CCRecordPositionShortlist();
                rps.setPosition(position);
                rps.setRecord(record);
                PortalUtils.getHt().saveOrUpdate(rps);
            }
            else if (rps != null)
            {
                PortalUtils.getHt().delete(rps);
            }
        }

        JSONObject ret = new JSONObject();

        try
        {
            ret.append("addedToShortlist", addToShortlist);
        }
        catch (JSONException e)
        {
            e.printStackTrace();
        }
        return NHelper.AJAXResponse(ret.toString());
    }

    public static ModelAndView ajaxLoadRecordPositions(HttpServletRequest request)
    {
        ModelAndView mv = new ModelAndView("cc/cc_recordPositionsAjax");
        mv.addObject(SiteController.SHORT_CIRCUIT_ATTRIBUTE_NAME, Boolean.TRUE);

        final boolean myRecordMode = RequestUtils.getBooleanParameter(request,
                "myRecordMode", false);

        mv.addObject("myRecordMode", myRecordMode);

        GridHelper.runQuery(
                GridQueryFactory.newInstance("cc_recordPositions", request, mv));

        mv.addObject("filter", request.getParameter("filter"));

        return mv;
    }

    public static ModelAndView ajaxLoadTimeTracking(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("cc/cc_timeTracking");
        mv.addObject(SiteController.SHORT_CIRCUIT_ATTRIBUTE_NAME, Boolean.TRUE);

        CCRecordPosition rp = CCHelper.getCCRecordPosition(request);
        mv.addObject("rp", rp);

        CCRecordPositionHelper.populateHourTracking(mv, rp);
        RequestUtils.repopulateMV(request, mv, "confirmationCode");

        return mv;
    }

    public static ModelAndView ajaxUpdateRecordPositionTotalHours(
            HttpServletRequest request, HttpServletResponse response)
            throws JSONException
    {
        JSONObject ret = new JSONObject();

        CCRecordPosition rp = CCHelper.getCCRecordPosition(request);

        Integer totalHours = RequestUtils.getInteger(request, "totalHours");

        if (totalHours != null)
        {
            rp.setHours(totalHours);
            PortalUtils.getHt().update(rp);

            UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

            if (!CCRecordPosition.STATUS_APPROVED.equals(rp.getStatus())
                    && Objects.equals(rp.getRecord().getOwner(), userLoggedIn))
            {
                CCRecordPositionHelper.changeRecordPositionStatus(rp,
                        CCRecordPosition.STATUS_PENDING, userLoggedIn);
            }

            ret.put("success", true);
            ret.put("newTotal", rp.getHours());
        }

        return NHelper.AJAXResponse(ret.toString());
    }

    public static ModelAndView deleteRecordPositionEntry(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        JSONObject ret = new JSONObject();
        CCRecordPositionEntry rpe = CCHelper.getCCRecordPositionEntry(request);

        if (rpe != null)
        {
            PortalUtils.getHt().delete(rpe);

            CCRecordPosition rp = rpe.getRecordPosition();

            CCHelper.updateHourCount(rp);

            UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

            if (!CCRecordPosition.STATUS_APPROVED.equals(rp.getStatus())
                    && Objects.equals(rp.getRecord().getOwner(), userLoggedIn))
            {
                CCRecordPositionHelper.changeRecordPositionStatus(rp,
                        CCRecordPosition.STATUS_PENDING, userLoggedIn);
            }

            ret.put("success", true);
            ret.put("newTotal", rp.getHours());
        }
        else
        {
            ret.put("sucess", false);
        }

        return NHelper.AJAXResponse(ret.toString());
    }

    public static ModelAndView saveRecordPositionEntry(HttpServletRequest request,
            HttpServletResponse response) throws JSONException
    {
        CCRecordPositionEntry rpe = CCHelper.getCCRecordPositionEntry(request);

        rpe.setFromDate(DateUtils.getDatepickerVal(request, "fromDate"));
        rpe.setHours(RequestUtils.getIntParameter(request, "hours", 1));
        rpe.setEntry(request.getParameter("entry"));

        PortalUtils.getHt().saveOrUpdate(rpe);

        CCHelper.updateHourCount(rpe.getRecordPosition());

        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        if (!CCRecordPosition.STATUS_APPROVED
                .equals(rpe.getRecordPosition().getStatus())
                && Objects.equals(rpe.getRecordPosition().getRecord().getOwner(),
                        userLoggedIn))
        {
            CCRecordPositionHelper.changeRecordPositionStatus(
                    rpe.getRecordPosition(), CCRecordPosition.STATUS_PENDING,
                    userLoggedIn);
        }

        JSONObject ret = new JSONObject();
        ret.put("newTotal", rpe.getRecordPosition().getHours());

        return NHelper.AJAXResponse(ret.toString());
    }

    public static void updateHourCount(CCRecordPosition rp)
    {
        rp.setHours(PortalUtils.getHt().findInt(
                "select sum(rpe.hours) from CCRecordPositionEntry rpe where rpe.recordPosition=?",
                rp));
        PortalUtils.getHt().update(rp);
    }

    public static ModelAndView ajaxLoadCCRequests(HttpServletRequest request)
    {
        ModelAndView mv = new ModelAndView("cc/cc_ccRequestsAjax");
        Locale locale = PortalUtils.getLocale(request);
        mv.addObject(SiteController.SHORT_CIRCUIT_ATTRIBUTE_NAME, Boolean.TRUE);

        CCModule module = CCHelper
                .getCCModule(Integer.valueOf(request.getParameter("moduleId")));

        // GET 'MODE'
        String mode = request.getParameter("mode");
        if (mode == null)
        {
            mode = (String) request.getAttribute("mode");
        }
        if (mode == null)
        {
            mode = "pending";
        }

        String whereClause = getCCRequestWhereHql(module, mode,
                "last10days".equals(request.getParameter("filter")), request);

        GridQuery query = new GridQuery();
        query.setGridID("cc_ccRequests");
        query.setGridInstanceId(request.getParameter("gridInstanceId"));
        query.setPrimaryId("p.id");
        query.setFromAndWhereClause(
                " from CCPosition p left join p.approvedBy as approvedBy left join p.deniedBy as deniedBy where "
                        + whereClause);
        query.setRequest(request);
        query.setMv(mv);
        query.setVirtualDataCallback(new CCRequestsVirtualData(whereClause));
        GridHelper.runQuery(query);

        String departmentLabelBilingual = LocaleUtils.isL1(locale)
                ? module.getDepartmentLabel()
                : module.getL2DepartmentLabel();
        if ((mode.equalsIgnoreCase("pending") || mode.equalsIgnoreCase("approved"))
                && !StringUtils.isEmpty(departmentLabelBilingual))
        {
            mv.addObject("directorsIndex", 14);
        }
        mv.addObject("mode", mode);
        mv.addObject("isShowLevelCategory", module.isShowLevelCategory());
        mv.addObject("isShowLevelOrganization", module.isShowLevelOrganization());
        mv.addObject("isShowLevelDepartment", module.isShowLevelDepartment());

        if (!StringUtils.isEmpty(departmentLabelBilingual))
        {
            mv.addObject("departmentLabel", departmentLabelBilingual);
        }

        mv.addObject("filter", request.getParameter("filter"));

        return mv;
    }

    public static boolean isSpiralRobotEnabled(CCModule module,
            UserDetailsImpl loggedIn)
    {
        String currentRole = CCHelper.getCurrentUserRole(module, loggedIn);
        Map<String, Boolean> roles = CCHelper.getUserRoles(loggedIn, module);
        String role = !StringUtils.isEmpty(currentRole) ? currentRole
                : CCHelper.getUserRoleListFromMap(roles).get(0);
        boolean studentCondition = (CCHelper.CC_ROLE_STUDENT.equals(role)
                || CCHelper.CC_ROLE_ALUMNI.equals(role))
                && module.isEnableSpiralRobotForStudents();

        return module.isEnableSpiralRobot() || studentCondition;
    }

    public static String getCCRequestWhereHql(CCModule module, String mode,
            boolean olderThan10Days, HttpServletRequest request)
    {
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);
        String whereHql = "";

        if (mode == null || mode.equalsIgnoreCase("pending"))
        {
            whereHql = CCHelper.getPendingRequestsClause(user, module, request)
                    + (olderThan10Days
                            ? " and p.requestDate not between '"
                                    + DateUtils.formatDate(
                                            DateUtils.subtractDays(new Date(), 10),
                                            DBUtils.DB_DATE_TIME_FORMAT, null)
                                    + "' and '"
                                    + DateUtils.formatDate(new Date(),
                                            DBUtils.DB_DATE_TIME_FORMAT, null)
                                    + "'"
                            : "");
        }
        else if (mode.equalsIgnoreCase("approved"))
        {
            whereHql = "p.activity.period.module.id=" + module.getId().toString()
                    + " and p.approvedBy is not null and p.activity.id in "
                    + getValidActivitiesForUserHQL(user, module, request);
        }
        else if (mode.equalsIgnoreCase("denied"))
        {
            whereHql = "p.activity.period.module.id=" + module.getId().toString()
                    + " and p.deniedBy is not null and p.activity.id in "
                    + getValidActivitiesForUserHQL(user, module, request);
        }

        return whereHql;
    }

    public static String getCCRequestsSelectClause(String mode,
            boolean isShowLevelCategory, boolean isShowLevelOrganization,
            boolean isShowLevelDepartment)
    {
        String selectClause = "select p.activity.id, p.id, p.requestDate, ";

        if (mode.equalsIgnoreCase("approved"))
        {
            selectClause = selectClause
                    + "p.requestAccepted, p.approvedBy.preferredFirstName, p.approvedBy.lastName, ";
        }
        else if (mode.equalsIgnoreCase("denied"))
        {
            selectClause = selectClause
                    + "p.requestDenied, p.deniedBy.preferredFirstName, p.deniedBy.lastName, ";
        }

        return selectClause + (isShowLevelCategory ? "p.activity.category, " : "")
                + (isShowLevelOrganization ? "p.activity.organization, " : "")
                + (isShowLevelDepartment ? "p.activity.department, " : "")
                + "p.activity.activity, p.title ";
    }

    private static Relationship getActivityRelationship_Notes(
            HttpServletRequest request)
    {
        CriteriaModel cm = InteractionHelper.getNotesCriteria(
                INTERACTION_TYPE.CCACTIVITY, "Notes", "nca.note", request);
        Entity entity = new Entity("i18n.common.notes",
                InteractionNoteCCActivityAssigned.class, "nca", "id", cm, "nca.id",
                "asc", false);

        Relationship r = new Relationship("i18n.common.notes",
                Relationship.TYPE.MANY_TO_ONE);
        r.setRelatedEntity(entity);
        r.setForiegnKeyHql("nca.activity.id");
        return r;
    }

    private static Relationship getActivityRelationship_Tasks(
            HttpServletRequest request)
    {
        CriteriaModel cm = InteractionHelper.getTasksCriteria(
                INTERACTION_TYPE.CCACTIVITY, "tca.task", "Tasks",
                PortalUtils.getLocale(request),
                PortalUtils.getUserLoggedIn(request));

        Entity entity = new Entity("i18n.gridSearch.criteriaModel.entities.Tasks",
                InteractionTaskCCActivityAssigned.class, "tca", "id", cm, "tca.id",
                "asc", false);

        Relationship r = new Relationship(
                "i18n.gridSearch.criteriaModel.questionText.Tasks",
                Relationship.TYPE.MANY_TO_ONE);
        r.setRelatedEntity(entity);
        r.setForiegnKeyHql("tca.activity.id");
        return r;
    }

    private static Relationship getPositionRelationship_Notes(
            HttpServletRequest request)
    {
        CriteriaModel cm = InteractionHelper.getNotesCriteria(
                INTERACTION_TYPE.CCPOSITION, "Notes", "nca.note", request);
        Entity entity = new Entity("i18n.common.notes",
                InteractionNoteCCPositionAssigned.class, "nca", "id", cm, "nca.id",
                "asc", false);

        Relationship r = new Relationship("i18n.common.notes",
                Relationship.TYPE.MANY_TO_ONE);
        r.setRelatedEntity(entity);
        r.setForiegnKeyHql("nca.position.id");
        return r;
    }

    private static Relationship getPositionRelationship_Tasks(
            HttpServletRequest request)
    {
        CriteriaModel cm = InteractionHelper.getTasksCriteria(
                INTERACTION_TYPE.CCPOSITION, "tca.task", "Tasks",
                PortalUtils.getLocale(request),
                PortalUtils.getUserLoggedIn(request));
        Entity entity = new Entity("i18n.gridSearch.criteriaModel.entities.Tasks",
                InteractionTaskCCPositionAssigned.class, "tca", "id", cm, "tca.id",
                "asc", false);

        Relationship r = new Relationship(
                "i18n.gridSearch.criteriaModel.questionText.Tasks",
                Relationship.TYPE.MANY_TO_ONE);
        r.setRelatedEntity(entity);
        r.setForiegnKeyHql("tca.position.id");
        return r;
    }

    private static Relationship getRecordRelationship_Notes(
            HttpServletRequest request)
    {
        CriteriaModel cm = InteractionHelper.getNotesCriteria(
                INTERACTION_TYPE.CCRECORD, "Notes", "nca.note", request);
        Entity entity = new Entity("i18n.common.notes",
                InteractionNoteCCRecordAssigned.class, "nca", "id", cm, "nca.id",
                "asc", false);

        Relationship r = new Relationship("i18n.common.notes",
                Relationship.TYPE.MANY_TO_ONE);
        r.setRelatedEntity(entity);
        r.setForiegnKeyHql("nca.record.id");
        return r;
    }

    private static Relationship getRecordRelationship_Tasks(
            HttpServletRequest request)
    {
        CriteriaModel cm = InteractionHelper.getTasksCriteria(
                INTERACTION_TYPE.CCRECORD, "tca.task", "Tasks",
                PortalUtils.getLocale(request),
                PortalUtils.getUserLoggedIn(request));
        Entity entity = new Entity("i18n.gridSearch.criteriaModel.entities.Tasks",
                InteractionTaskCCRecordAssigned.class, "tca", "id", cm, "tca.id",
                "asc", false);

        Relationship r = new Relationship(
                "i18n.gridSearch.criteriaModel.questionText.Tasks",
                Relationship.TYPE.MANY_TO_ONE);
        r.setRelatedEntity(entity);
        r.setForiegnKeyHql("tca.record.id");
        return r;
    }

    public static String getUserTypeKey(CCActivity activity,
            UserDetailsImpl userLoggedIn)
    {
        String currentRole = CCHelper
                .getCurrentUserRole(activity.getPeriod().getModule(), userLoggedIn);
        String userTypeKey = !StringUtils.isEmpty(currentRole) ? currentRole
                : CCHelper.CC_ROLE_ANONYMOUS;

        return userTypeKey;
    }

    public static void populatePositionQuestions(ModelAndView mv, CCModule module,
            CCPosition position, UserDetailsImpl userLoggedIn, boolean canWrite)
    {
        DFModel dfModel = CCPositionQuestionModelHelper
                .getPositionQuestionModel(module).getDFModel();
        DFHelper.populateModel(dfModel, canWrite, position, userLoggedIn);
        mv.addObject("dfModel", dfModel);
    }

    public static ModelAndView ajaxLoadPositionCompetencies(
            HttpServletRequest request, ModelAndView mv)
    {
        String posId = request.getParameter("posId");

        mv.addObject("position", getPosition(request));

        String selectClause = "SELECT cpc.id, cpc.competency.title ";
        String fromWhereClause = "FROM CCPositionCompetency cpc WHERE cpc.position.id = "
                + posId;

        List<Object[]> results = GridHelper.runQuery(request, mv, fromWhereClause,
                selectClause, "cpc.id");

        mv.addObject("gridData", results);

        return mv;
    }

    public static List<NameValuePair> getRecordPositionStatusOptions(Locale locale)
    {
        List<NameValuePair> statusOptions = new ArrayList();
        MessageSource ms = PortalUtils.getMessageSource();

        statusOptions.add(new NameValuePair(
                ms.getMessage("i18n.ccrm_studentCcrTable.Approved", null, locale),
                CCRecordPosition.STATUS_APPROVED));
        statusOptions.add(new NameValuePair(
                ms.getMessage("i18n.ccrm_studentCcrTable.Declined", null, locale),
                CCRecordPosition.STATUS_DECLINED));
        statusOptions.add(new NameValuePair(
                ms.getMessage("i18n.ccrm_studentCcrTable.Pending", null, locale),
                CCRecordPosition.STATUS_PENDING));

        return statusOptions;
    }

    public static List<NameValuePair> getModuleOptions(Locale locale)
    {
        boolean isL1 = LocaleUtils.isL1(locale);
        List<NameValuePair> moduleOptions = getCCModules().stream()
                .map(m -> new NameValuePair(isL1 ? m.getName() : m.getL2Name(),
                        m.getId().toString()))
                .collect(Collectors.toList());
        return moduleOptions;
    }

    /**
     * Checks if this record position meets the requirements for appearing on
     * the associated student's record.
     *
     * @param rp
     * @return true if this position can be shown on the student's record; false
     *         otherwise.
     */
    public static boolean canShowRecordPositionOnPdf(CCRecordPosition rp)
    {
        CCModule module = rp.getRecord().getModule();
        return isStatusRequirementReached(rp)
                && isCompetencyRequirementReached(module, rp)
                && isReflectionRequirementReached(module, rp)
                && isTimeRequirementReached(module, rp);
    }

    public static JSONObject validateRequestToPublish(CCRecordPosition rp,
            Locale locale) throws JSONException
    {
        JSONObject ret = new JSONObject();
        if (!isStatusRequirementReached(rp))
        {
            ret.put("statusRequirement", PortalUtils.getMessageSource().getMessage(
                    "i18n.CCController.Recordhasn7443094956749094", null, locale));
        }
        CCModule module = rp.getRecord().getModule();
        if (!isReflectionRequirementReached(module, rp))
        {
            ret.put("reflectionRequirement",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.CCController.Missingref6249806592800120", null,
                            locale));
        }
        if (!isTimeRequirementReached(module, rp))
        {
            ret.put("timeRequirement", PortalUtils.getMessageSource().getMessage(
                    "i18n.CCController.Morehoursn1164085208757280", null, locale));
        }
        if (!isCompetencyRequirementReached(module, rp))
        {
            ret.put("competencyRequirement",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.CCHelper.Competenci4015454175018611", null,
                            locale));
        }
        return ret;
    }

    public static boolean isStatusRequirementReached(
            CCRecordPosition recordPosition)
    {
        return CCRecordPosition.STATUS_APPROVED.equals(recordPosition.getStatus());
    }

    public static boolean isCompetencyRequirementReached(CCModule module,
            CCRecordPosition recordPosition)
    {
        boolean competencyRequirement = true;
        List<ConfigLevel> levels = CCRecordPositionHelper
                .getCompetencyConfigs(recordPosition);
        int competencyUsecase = CompetencyAchievedHelper
                .getCompetencyUsecase(levels);
        if (!module.isAllowExperiencesWithoutMeetingMinimumCompetencies())
        {
            if (competencyUsecase != Competenciable.COMP_DISABLED
                    && competencyUsecase != Competenciable.COMP_ASSIGNED)
            {
                int minCompetencies = recordPosition.getPosition()
                        .getMinimumCompetencies();
                int achievedCount = PortalUtils.getHt().findInt(
                        "select count(ca.id) from CCRecordPositionAchieved ca where ca.recordPosition=?",
                        recordPosition);
                competencyRequirement = achievedCount > 0 && (minCompetencies == 0
                        || achievedCount >= minCompetencies);
            }
            if (module.isEnableCompetencySelection())
            {
                if (!module.isStudentsReceivePositionCompetencies())
                {
                    int count = recordPosition.getPersonalCompetenciesList() == null
                            ? 0
                            : recordPosition.getPersonalCompetenciesList().size();
                    competencyRequirement = count >= module.getMinCompetencies()
                            && (count <= module.getMaxCompetencies()
                                    || module.getMaxCompetencies() == 0);
                }
            }
            else if (module.isTrackLearningOutcomes())
            {
                String hql = module.isTrackAchievements()
                        ? "select count(c.id) from CCPositionAchievementStudent c where c.recordPosition=?"
                        : "select count(c.id) from CCPositionLearningOutcomeStudent c where c.recordPosition=?";
                int count = (Integer) PortalUtils.getHt().find(hql, recordPosition)
                        .get(0);
                competencyRequirement = count >= module.getMinLOAchievements()
                        && (count <= module.getMaxLOAchievements()
                                || module.getMaxLOAchievements() == 0);
            }
        }
        return competencyRequirement;
    }

    public static boolean isReflectionRequirementReached(CCModule module,
            CCRecordPosition recordPosition)
    {
        boolean reflectionRequirement = true;
        AcrmReflectionConfig conf = module.getReflectionConfigurableSupport()
                .getReflectionConfig(module);
        if (conf != null && conf.isAnyReflectionEnabled()
                && !module.isAllowExperiencesWithoutReflection())
        {
            reflectionRequirement = CCReflectionHelper
                    .getRecordPositionReflectionsCount(recordPosition.getId()) > 0;
        }
        return reflectionRequirement;
    }

    public static boolean isTimeRequirementReached(CCModule module,
            CCRecordPosition recordPosition)
    {
        boolean timeRequirement = true;
        if (module.isEnableTimeTracking()
                && !module.isAllowExperiencesWithoutMeetingMinimumHours())
        {
            if (recordPosition.getHours() < recordPosition.getPosition()
                    .getHoursToTrack())
            {
                timeRequirement = false;
            }
        }
        return timeRequirement;
    }

    /**
     * Enables or disables the specified positions. The status of related
     * activities will also be changed to reflect the positions' visibility.
     *
     * @param posIds
     *            the ids of the positions to be updated
     * @param enable
     *            the value to set the visibility to
     * @param request
     *            no additional parameters are needed in the request
     * @return true if the mass update was successfully initiated; false
     *         otherwise.
     */
    public static boolean massUpdatePositionVisibility(List<Integer> posIds,
            boolean enable, HttpServletRequest request)
    {
        boolean massUpdateInitiated = false;

        if (posIds.size() > 0)
        {
            new MassUpdateCCPositionVisibilityThread(posIds, enable, request)
                    .start();
            massUpdateInitiated = true;
        }

        return massUpdateInitiated;
    }

    public static boolean saveActivityStatus(CCActivity activity, int status,
            HttpServletRequest request)
    {
        boolean success = false;

        if (canEdit(activity, request))
        {
            String statusBeforeUpdate = activity.getStatusAsI18n();
            activity.setStatus(status);
            activity.setUpdatedOn(new Date());
            activity.setUpdatedBy(PortalUtils.getUserLoggedIn(request));

            PortalUtils.getHt().update(activity);

            if (!statusBeforeUpdate.equals(activity.getStatusAsI18n()))
            {
                PortalUtils.createPortalLog(
                        PortalLog.PL_CO_CURRICULAR_ACTIVITY_STATUS_CHANGE,
                        StringUtils.fillInPlaceholders(
                                "The status of activity '[0]' has been changed from '[1]' to '[2]'",
                                new Object[] { activity.getActivity(),
                                        PortalUtils.getI18nMessage(
                                                statusBeforeUpdate, Locale.ENGLISH),
                                        PortalUtils.getI18nMessage(
                                                activity.getStatusAsI18n(),
                                                Locale.ENGLISH) }),
                        activity, PortalUtils.getUserLoggedIn(request));
            }

            updateActivityPositionStatuses(activity);

            success = true;
        }

        return success;
    }

    public static CCActivity getSelectedActivity(CCModule module, String periodId,
            String category, String organization, String department,
            String activity, String activityId)
    {
        final CCActivity a;

        if (StringUtils.isInteger(activityId))
        {
            a = (CCActivity) PortalUtils.getHt().load(CCActivity.class,
                    Integer.valueOf(activityId));
        }
        else
        {
            List args = new ArrayList();
            StringBuilder hql = new StringBuilder("from CCActivity a");
            hql.append(" where a.period.active=true and a.period.module=?");
            args.add(module);

            if (StringUtils.isInteger(periodId))
            {
                hql.append(" and a.period.id=?");
                args.add(periodId);
            }

            hql.append(" and a.category=?");
            args.add(module.isShowLevelCategory() && !StringUtils.isEmpty(category)
                    ? category
                    : "category");

            hql.append(" and a.organization=?");
            args.add(module.isShowLevelOrganization()
                    && !StringUtils.isEmpty(organization) ? organization
                            : "organization");

            hql.append(" and a.department=?");
            args.add(module.isShowLevelDepartment()
                    && !StringUtils.isEmpty(department) ? department
                            : "department");

            hql.append(" and a.activity=?");
            args.add(activity);

            Optional<CCActivity> found = PortalUtils.getHt()
                    .findFirst(hql.toString(), args.toArray());
            a = found.orElse(new CCActivity());
        }

        return a;
    }

    public static void assignTagsToActivity(HttpServletRequest request,
            CCActivity activity)
    {
        JSONObject additionalParams = JSONUtils.newJSONObject("activityId",
                activity.getId(), "ignoreSuccessMessage", true);
        try
        {
            AcrmHelper.processTagAssign(request, PortalUtils.nullMv(),
                    additionalParams, activity, true);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    public static void populateAddNewPositionWizard(CCModule module,
            ModelAndView mv, HttpServletRequest request)
    {
        Locale locale = PortalUtils.getLocale(request);
        String nameParam = LocaleUtils.isL1(locale) ? "name" : "l2Name";
        String visibleClause = null;
        UserDetailsImpl user = PortalUtils.getUserLoggedIn(request);

        CCHelper.populateForAddingPosition(mv, module, locale, user);
        CCPosition position = new CCPosition();
        mv.addObject("position", position);

        if (CCHelper.isAdmin(user, module))
        {
            visibleClause = " and p.active=1 ";
            if (StringUtils.isInteger(request.getParameter("periodId")))
            {
                visibleClause += " or p.id=" + request.getParameter("periodId");
            }
        }
        else
        {
            visibleClause = " and p.activeRequests=1 ";
        }

        String periodSql = "select p.id, p." + nameParam
                + " as name, p.startDate as startDate, p.endDate as endDate"
                + " from cc_period p where p.module=?" + visibleClause;

        List<Map<String, Object>> periods = PortalUtils.getJt().queryForList(periodSql,
                new Object[] { module.getId() });
        mv.addObject("periods", periods);

        if (request.getParameter("periodId") != null)
        {
            mv.addObject("preSelectPeriodId", request.getParameter("periodId"));
        }
        else
        {
            for (Map<String, Object> period : periods)
            {
                if (DateUtils.isBetween((Date) period.get("startDate"),
                        (Date) period.get("endDate"), DateUtils.now()))
                {
                    mv.addObject("preSelectPeriodId", period.get("id"));
                    break;
                }
            }
        }
        Optional<Integer> titleQuestion = PortalUtils.getHt().findFirst(
                "select dfq.id from DFQuestion dfq where dfq.category.model=? and dfq.answerField1='title'",
                new Object[] { CCPositionQuestionModelHelper
                        .getPositionQuestionModel(module).getDFModel() });
        titleQuestion.ifPresent(qId -> mv.addObject("titleQuestionField",
                "question_" + qId.toString()));

        boolean hasTags = false;
        try
        {
            AcrmHelper.processTagAssign(request, mv,
                    JSONUtils.newJSONObject("activityId", -1), new CCActivity(),
                    true);
            List<TagGrouping> tagGroupings = (List<TagGrouping>) mv.getModel()
                    .get("tagGroupings");
            hasTags = tagGroupings.stream()
                    .anyMatch(tg -> !tg.getTagMap().isEmpty());
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        mv.addObject("tagsExists", hasTags);

        mv.addObject("competenciesDisabled",
                module.getCompetencyUsecase() == Competenciable.COMP_DISABLED);
    }

    public static void populateForAddingPosition(ModelAndView mv, CCModule module,
            Locale locale, UserDetailsImpl userLoggedIn)
    {
        if (module.isTrackLearningOutcomes())
        {
            if (module.isTrackAchievements())
            {
                List<CCAchievement> achievements = CCHelper
                        .getActiveAchievements(module, locale);

                List<CCLearningOutcome> outcomes = CCHelper
                        .getLearningOutcomes(module, locale);

                if (module.isGroupAchievements())
                {
                    TreeMap<Object, Map<Object, Object>> achievementMap = new TreeMap<>();

                    for (CCLearningOutcome outcome : outcomes)
                    {
                        if (outcome.isActive())
                        {
                            String outcomeNameBilingual = LocaleUtils.isL1(locale)
                                    ? outcome.getName()
                                    : outcome.getL2Name();
                            if (!achievementMap.containsKey(outcomeNameBilingual))
                            {
                                achievementMap.put(outcomeNameBilingual,
                                        new HashMap<>());
                            }

                            achievementMap.get(outcomeNameBilingual).put("outcome",
                                    outcome);
                            achievementMap.get(outcomeNameBilingual)
                                    .put("outComeSelected", false);

                            List<CCAchievement> as = CCHelper
                                    .getAchievementsByLearningOutcome(outcome,
                                            locale);

                            for (CCAchievement a : as)
                            {
                                if (a.isActive())
                                {
                                    achievementMap.get(outcomeNameBilingual).put(a,
                                            0);
                                }
                            }
                        }
                    }
                    mv.addObject("achievementMap", achievementMap);
                }
                else
                {
                    Map validAchievements = new HashMap();
                    Map checkedOutcomes = new HashMap();

                    for (CCLearningOutcome outcome : outcomes)
                    {
                        if (outcome.isActive())
                        {
                            checkedOutcomes.put(outcome, 0);
                        }
                    }

                    for (CCAchievement a : achievements)
                    {
                        if (a.isActive())
                        {
                            validAchievements.put(a, 0);
                        }
                    }

                    mv.addObject("outcomes", outcomes);
                    mv.addObject("lo", checkedOutcomes);
                    mv.addObject("achievements", achievements);
                    mv.addObject("va", validAchievements);
                }
            }
            else
            {
                List<CCLearningOutcome> learningOutcomes = CCHelper
                        .getLearningOutcomes(module, locale);
                Map validLO = new HashMap();

                for (CCLearningOutcome lo : learningOutcomes)
                {
                    if (lo.isActive())
                    {
                        validLO.put(lo, 0);
                    }
                }

                mv.addObject("learningOutcomes", learningOutcomes);
                mv.addObject("vlo", validLO);
            }
        }

        CCHelper.populatePositionQuestions(mv, module, null, userLoggedIn, true);

        if (module.isEnableActivityDirectors())
        {
            mv.addObject("directors", PortalUtils.getHt()
                    .find("from CCDirectors d where d.module=? order by d."
                            + (LocaleUtils.isL1(locale) ? "name" : "l2Name"),
                            module));
        }
    }

    public static ModelAndView ajaxLoadActivityDetailsEdit(
            HttpServletRequest request, CCModule module)
    {
        ModelAndView mv = new ModelAndView("cc/cc_requestActivityDetailsEditAjax");
        mv.addObject(SiteController.SHORT_CIRCUIT_ATTRIBUTE_NAME, Boolean.TRUE);

        CCHelper.populateActivityQuestions(mv, module, null,
                PortalUtils.getUserLoggedIn(request), true);

        if (!RequestUtils.getBooleanParameter(request, "ccWizardView", false))
        {
            boolean hasTags = false;
            try
            {
                AcrmHelper.processTagAssign(request, mv,
                        JSONUtils.newJSONObject("activityId", -1), new CCActivity(),
                        true);
                List<TagGrouping> tagGroupings = (List<TagGrouping>) mv.getModel()
                        .get("tagGroupings");
                hasTags = tagGroupings.stream()
                        .anyMatch(tg -> !tg.getTagMap().isEmpty());
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
            mv.addObject("hasTags", hasTags);
        }

        Optional<Integer> activityTitleQuestion = PortalUtils.getHt().findFirst(
                "select dfq.id from DFQuestion dfq where dfq.category.model=? and dfq.answerField1='activity'",
                new Object[] { CCActivityQuestionModelHelper
                        .getActivityQuestionModel(module).getDFModel() });
        activityTitleQuestion
                .ifPresent(qId -> mv.addObject("activityNameQuestionField",
                        "question_" + qId.toString()));

        mv.addObject("includedInWizard", request.getParameter("includedInWizard"));

        mv.addObject("activity", new CCActivity());
        mv.addObject("ccModule", module);
        mv.addObject("ccWizardView", request.getParameter("ccWizardView"));

        return mv;
    }

    public static void populatePositionNodes(ModelAndView mv, CCPosition position,
            CCModule module, Locale locale)
    {
        List<Map<String, String>> positionsInActivity = new ArrayList<>();

        String title = LocaleUtils.isL1(locale) ? "ccp.title " : "ccp.l2Title ";

        PortalUtils.getHt()
                .f("select ccp.id, " + title + "from CCPosition ccp "
                        + "join ccp.activity " + "join ccp.activity.period "
                        + "where ccp.activity.period.id=? "
                        + "and ccp.activity.period.module.id=? "
                        + "and ccp.activity.id=? and " + title + " is not null ",
                        new Object[] { position.getActivity().getPeriod().getId(),
                                module.getId(), position.getActivity().getId() })
                .forEach(item -> {
                    Map<String, String> pos = new HashMap<>();
                    pos.put("id", item.get("id").toString());
                    String positionTitle;
                    if (LocaleUtils.isL1(locale))
                    {
                        positionTitle = item.get("title").toString();
                    }
                    else
                    {
                        positionTitle = item.get("l2Title").toString();
                    }
                    pos.put("title", positionTitle);
                    positionsInActivity.add(pos);
                });
        mv.addObject("positionsInActivity", positionsInActivity);
    }

    public static CCPosition saveNewPosition(CCModule module, CCActivity a,
            UserDetailsImpl userLoggedIn, HttpServletRequest request)
    {
        final CCPosition p;
        String currentUserRole = getCurrentUserRole(module, userLoggedIn);

        if (CC_ROLE_STAFF.equals(currentUserRole)
                && "validatorOpt1".equals(request.getParameter("validatorOpt")))
        {
            p = CCRequestPositionHelper.saveNewPositionRequest(request, module, a,
                    request.getParameter("firstName"),
                    request.getParameter("lastName"), request.getParameter("email"),
                    request.getParameter("phone"),
                    request.getParameter("requestPosition"), null, null, null, null,
                    null);

            saveCCPositionValidator(userLoggedIn, p);
        }
        else if (CC_ROLE_ADMIN.equals(currentUserRole)
                || CC_ROLE_ACTIVITY_DIRECTOR.equals(currentUserRole))
        {
            p = CCRequestPositionHelper.saveNewPositionRequest(request, module, a);

            JSONArray jValidators = new JSONArray();
            try
            {
                jValidators = new JSONArray(request.getParameter("validators"));
            }
            catch (JSONException e)
            {
            }

            attachSelectedValidatorsToPosition(
                    JSONUtils.convertJSONArrayToList(jValidators), p);

            if (CC_ROLE_ADMIN.equals(currentUserRole))
            {
                p.setEnabled(true);
                p.setExternalPosition(
                        RequestUtils.isChecked(request, "externalPosition"));
                updatePositionApprovalStatus(p, CCPosition.APPROVAL_STATUS_APPROVED,
                        userLoggedIn);
            }
        }
        else
        {
            p = CCRequestPositionHelper.saveNewPositionRequest(request, module, a);
            if (CC_ROLE_VALIDATOR.equals(currentUserRole))
            {
                saveCCPositionValidator(userLoggedIn, p);
            }
        }

        return p;
    }

    private static void saveCCPositionValidator(UserDetailsImpl userLoggedIn,
            CCPosition position)
    {
        CCPositionValidator ccv = new CCPositionValidator();
        ccv.setPosition(position);
        ccv.setValidator(userLoggedIn);
        PortalUtils.getHt().save(ccv);
    }

    public static void attachSelectedValidatorsToPosition(List validatorIds,
            CCPosition p)
    {
        for (int i = 0; i < validatorIds.size(); i++)
        {
            List<UserDetailsImpl> users = PortalUtils.getHt().find(
                    "from UserDetailsImpl u where u.id=?", validatorIds.get(i));

            if (!users.isEmpty())
            {
                CCPositionValidator ccv = new CCPositionValidator();
                ccv.setPosition(p);
                ccv.setValidator(users.get(0));
                PortalUtils.getHt().save(ccv);
            }
        }

        // Validators selected by the ADMIN shall be automatically
        // enabled...
        enableValidators(p);
    }

    public static void populateActivityCommon(CCActivity activity, CCModule module,
            HttpServletRequest request, ModelAndView mv)
    {
        populateQuickStats(activity);
        populateAccessFlags(mv, activity, request);
        InteractionHelper.populateInteractionActionsBar(mv, request);

        int interactionsCount = 0;
        INTERACTION_TYPE type = INTERACTION_TYPE.valueOf("CCACTIVITY");
        Map noteVisibilityMap = (Map) mv.getModel().get("ica_noteVisibility");
        if (noteVisibilityMap != null
                && noteVisibilityMap.containsKey("CCACTIVITY"))
        {
            String noteSql = InteractionHelper.getInteractionSummaryClause(request,
                    type, activity.getId().toString(), null, "note",
                    "select count(i.id) from interaction_note i where");
            interactionsCount += PortalUtils.getJt().queryForInt(noteSql);
        }
        Map taskVisibilityMap = (Map) mv.getModel().get("ica_taskVisibility");
        if (taskVisibilityMap != null
                && taskVisibilityMap.containsKey("CCACTIVITY"))
        {
            String taskSql = InteractionHelper.getInteractionSummaryClause(request,
                    type, activity.getId().toString(), null, "task",
                    "select count(i.id) from interaction_task i where");
            interactionsCount += PortalUtils.getJt().queryForInt(taskSql);
        }
        Map formVisibilityMap = (Map) mv.getModel().get("ica_formVisibility");
        if (formVisibilityMap != null
                && formVisibilityMap.containsKey("CCACTIVITY"))
        {
            String formSql = InteractionHelper.getInteractionSummaryClause(request,
                    type, activity.getId().toString(), null, "form",
                    "select count(i.id) from interaction_form i where");
            interactionsCount += PortalUtils.getJt().queryForInt(formSql);
        }
        mv.addObject("interactionsCount", interactionsCount);

        mv.addObject("directorsMap",
                getDirectorsMap(activity, PortalUtils.getLocale(request)));

        String nameParam = LocaleUtils.isL1(request) ? "name" : "l2Name";
        mv.addObject("validPeriodsToCloneTo",
                PortalUtils.getJt().queryForList("select p.id as id, p." + nameParam
                        + " as name from cc_period p where p.module=? and p.active=1 order by p."
                        + nameParam, new Object[] { module.getId() }));
        mv.addObject("activity", activity);
    }

    public static void populatePositionCommon(ModelAndView mv,
            HttpServletRequest request, CCPosition position)
    {
        mv.addObject("position", position);
        InteractionHelper.populateInteractionActionsBar(mv, request);
        InteractionEngagementHelper.populateEngagementActivitiesCommon(mv,
                EnumSet.of(TYPE.CCVALIDATED),
                CollectionUtils.newHashMap("posId", position.getId()));

        Locale locale = PortalUtils.getLocale(request);
        CCModule module = (CCModule) OrbisController.getOrbisModule(request);
        mv.addObject("activePeriods", PortalUtils.getHt().find(
                "from CCPeriod p where p.module=? and p.active = true order by p."
                        + (LocaleUtils.isL1(locale) ? "name" : "l2Name"),
                module));
    }

    public static Map<CCDirectors, Boolean> getDirectorsMap(CCActivity activity,
            Locale locale)
    {
        Map<CCDirectors, Boolean> map = new LinkedHashMap<>();

        try
        {
            List<CCDirectors> directors = PortalUtils.getHt()
                    .find("from CCDirectors d where d.module=? order by d."
                            + (LocaleUtils.isL1(locale) ? "name" : "l2Name"),
                            activity.getPeriod().getModule());

            for (CCDirectors ccDirectors : directors)
            {
                map.put(ccDirectors,
                        CCHelper.isActivityDirectors(activity, ccDirectors));
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return map;
    }

    public static void populateValidatorData(HttpServletRequest request,
            ModelAndView mv, UserDetailsImpl validator, CCModule module)
    {
        if (request.getAttribute("positionStats") != null)
        {
            String periodHql = "";

            if (PortalUtils.getUserLoggedIn(request)
                    .getGroupWithName(PersonGroupHelper.CC_ADMINISTRATOR) == null)
            {
                periodHql = " and p.position.activity.period.id in (select tp.id from CCPeriod tp where tp.activeVal=true)";
            }

            List vPositions = PortalUtils.getHt()
                    .find("select p from CCPositionValidator p "
                            + " where p.position.activity.period.module=? "
                            + " and p.validator=?" + periodHql
                            + " order by p.position.activity.period.name, p.position.title ",
                            new Object[] { module, validator });

            mv.addObject("vPositions", vPositions);

            Map positionStats = new HashMap();

            for (Iterator iterator = vPositions.iterator(); iterator.hasNext();)
            {
                CCPositionValidator pv = (CCPositionValidator) iterator.next();
                String hoursClause = "";
                // " and (p.position.hoursToTrack=0 or
                // (p.position.hoursToTrack>0 and
                // p.hours>=p.position.hoursToTrack) ) ";
                Integer pendingCount = (Integer) PortalUtils.getHt().find(
                        "select count(p) from CCRecordPosition p where p.position=? and p.status='Pending'"
                                + hoursClause + periodHql,
                        new Object[] { pv.getPosition() }).get(0);
                Integer approvedCount = (Integer) PortalUtils.getHt().find(
                        "select count(p) from CCRecordPosition p where p.position=? and p.status='Approved'"
                                + hoursClause + periodHql,
                        new Object[] { pv.getPosition() }).get(0);
                Integer declinedCount = (Integer) PortalUtils.getHt().find(
                        "select count(p) from CCRecordPosition p where p.position=? and p.status='Declined'"
                                + hoursClause + periodHql,
                        new Object[] { pv.getPosition() }).get(0);
                positionStats.put(pv.getPosition(), new Object[] { pendingCount,
                        approvedCount, declinedCount });
            }
            mv.addObject("positionStats", positionStats);
        }
        mv.addObject("validator", validator);

        // Summary Numbers
        mv.addObject("rpsPending",
                CCHelper.getValidatorRecordPositionCount(validator,
                        CCRecordPosition.STATUS_PENDING, module, false, true,
                        request));
        mv.addObject("rpsApproved",
                CCHelper.getValidatorRecordPositionCount(validator,
                        CCRecordPosition.STATUS_APPROVED, module, false, true,
                        request));
        mv.addObject("rpsDeclined",
                CCHelper.getValidatorRecordPositionCount(validator,
                        CCRecordPosition.STATUS_DECLINED, module, false, true,
                        request));
        mv.addObject("allRpsPending",
                CCHelper.getValidatorRecordPositionCount(validator,
                        CCRecordPosition.STATUS_PENDING, module, false, false,
                        request));
        mv.addObject("allRpsApproved",
                CCHelper.getValidatorRecordPositionCount(validator,
                        CCRecordPosition.STATUS_APPROVED, module, false, false,
                        request));
        mv.addObject("allRpsDeclined",
                CCHelper.getValidatorRecordPositionCount(validator,
                        CCRecordPosition.STATUS_DECLINED, module, false, false,
                        request));
        // tab count numbers
        mv.addObject("approvedTab",
                CCHelper.getValidatorRecordPositionCount(validator,
                        CCRecordPosition.STATUS_APPROVED, module, false, true,
                        request));
        mv.addObject("declinedTab",
                CCHelper.getValidatorRecordPositionCount(validator,
                        CCRecordPosition.STATUS_DECLINED, module, false, true,
                        request));

    }

    public static Integer getValidatorRecordPositionCount(UserDetailsImpl validator,
            String status, CCModule module, boolean summaryCount,
            boolean activePeriodsOnly, HttpServletRequest request)
    {
        String[] hql = buildHqlForValidatorsData(validator, status, summaryCount,
                activePeriodsOnly, module, request);

        return (Integer) PortalUtils.getHt().find(hql[0] + hql[1] + hql[2]).get(0);
    }

    /**
     * Populates the ModelAndView with a Map that is suitable for rendering a
     * page that allows a user to EDIT a position's learning outcomes.
     */
    public static void populateCCOutcomes_editMode(ModelAndView mv, CCModule module,
            CCPosition pos, boolean addingNewRecordPosition,
            HttpServletRequest request)
    {
        Locale locale = PortalUtils.getLocale(request);

        if (module.isTrackLearningOutcomes())
        {
            int adminOutcomeSelected = addingNewRecordPosition ? 0 : 1;
            boolean showSubset = module.isStudentSelectLOA()
                    && module.isStudentSelectLOASubset();
            boolean showSubsetOnly = PortalUtils.getUserLoggedIn(request) != null
                    && (PortalUtils.getUserLoggedIn(request).isStudent()
                            || PortalUtils.getUserLoggedIn(request).isAlumni())
                    && showSubset;
            if (module.isTrackAchievements())
            {
                List<Integer> aArray = getPositionAchievementAdminAchievementIds(
                        pos);

                List<CCLearningOutcome> outcomes = showSubsetOnly ? PortalUtils
                        .getHt()
                        .find("select pla.learningOutcome from CCPositionLearningOutcome pla where pla.position=?",
                                pos)
                        : getLearningOutcomes(module, locale);

                List<Integer> oArray = getLearningOutcomeIds(pos);

                if (module.isGroupAchievements())
                {
                    TreeMap<Object, Map<Object, Object>> achievementMap = new TreeMap<>();

                    for (CCLearningOutcome outcome : outcomes)
                    {
                        if (outcome.isActive())
                        {
                            String outcomeNameBilingual = LocaleUtils.isL1(locale)
                                    ? outcome.getName()
                                    : outcome.getL2Name();
                            if (!achievementMap.containsKey(outcomeNameBilingual))
                            {
                                achievementMap.put(outcomeNameBilingual,
                                        new HashMap<>());
                            }

                            achievementMap.get(outcomeNameBilingual).put("outcome",
                                    outcome);
                            achievementMap.get(outcomeNameBilingual).put(
                                    "outComeSelected",
                                    oArray.contains(outcome.getId()));

                            List<CCAchievement> anticipatedAchievements = new ArrayList<>();
                            if (showSubset)
                            {
                                anticipatedAchievements = PortalUtils.getHt().find(
                                        "select paa.achievement from CCPositionAchievementAdmin paa where paa.position=? and paa.achievement.learningOutcome=? order by paa.achievement.achievement",
                                        new Object[] { pos, outcome });
                            }
                            List<CCAchievement> achievements = showSubsetOnly
                                    ? anticipatedAchievements
                                    : getAchievementsByLearningOutcome(outcome,
                                            locale);
                            for (CCAchievement a : achievements)
                            {
                                if (aArray.contains(a.getId()))
                                {
                                    achievementMap.get(outcomeNameBilingual).put(a,
                                            adminOutcomeSelected);
                                }
                                else if (showSubset
                                        && !anticipatedAchievements.contains(a))
                                {
                                    achievementMap.get(outcomeNameBilingual).put(a,
                                            2);
                                }
                                else if (a.isActive())
                                {
                                    achievementMap.get(outcomeNameBilingual).put(a,
                                            0);
                                }
                            }
                        }
                    }
                    mv.addObject("achievementMap", achievementMap);
                }
                else
                {
                    Map validAchievements = new HashMap();
                    Map checkedOutcomes = new HashMap();
                    List<CCAchievement> anticipatedAchievements = new ArrayList<>();
                    if (showSubset)
                    {
                        anticipatedAchievements = PortalUtils.getHt().find(
                                "select paa.achievement from CCPositionAchievementAdmin paa where paa.position=? order by paa.achievement.achievement",
                                pos);
                    }
                    List<CCAchievement> achievements = showSubsetOnly
                            ? anticipatedAchievements
                            : getActiveAchievements(module, locale);
                    for (CCLearningOutcome outcome : outcomes)
                    {
                        if (oArray.contains(outcome.getId()))
                        {
                            checkedOutcomes.put(outcome, adminOutcomeSelected);
                        }
                        else if (outcome.isActive())
                        {
                            checkedOutcomes.put(outcome, 0);
                        }
                    }

                    for (CCAchievement a : achievements)
                    {
                        if (aArray.contains(a.getId()))
                        {
                            validAchievements.put(a, adminOutcomeSelected);
                        }
                        else if (showSubset && !anticipatedAchievements.contains(a))
                        {
                            validAchievements.put(a, 2);
                        }
                        else if (a.isActive())
                        {
                            validAchievements.put(a, 0);
                        }
                    }

                    mv.addObject("outcomes", outcomes);
                    mv.addObject("lo", checkedOutcomes);
                    mv.addObject("achievements", achievements);
                    mv.addObject("va", validAchievements);
                }
            }
            else
            {
                List<CCLearningOutcome> anticipatedLO = new ArrayList<>();
                if (showSubset)
                {
                    anticipatedLO = PortalUtils.getHt().find(
                            "select pla.learningOutcome from CCPositionLearningOutcome pla where pla.position=?",
                            pos);
                }
                List<CCLearningOutcome> outcomes = showSubsetOnly ? anticipatedLO
                        : getLearningOutcomes(module, locale);
                List<Integer> oArray = getLearningOutcomeIds(pos);
                Map validLO = new HashMap();

                for (CCLearningOutcome lo : outcomes)
                {
                    if (oArray.contains(lo.getId()))
                    {
                        validLO.put(lo, adminOutcomeSelected);
                    }
                    else if (showSubset && !anticipatedLO.contains(lo))
                    {
                        validLO.put(lo, 2);
                    }
                    else if (lo.isActive())
                    {
                        validLO.put(lo, 0);
                    }
                }

                mv.addObject("learningOutcomes", outcomes);
                mv.addObject("vlo", validLO);
            }
        }
    }

    public static void savePositionCommon(HttpServletRequest request, CCPosition p,
            CCModule module) throws CCException
    {
        Date now = new Date();
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        CCActivity a = getCCActivity(request);
        String positionTitle = request.getParameter("title");

        if (p == null)
        {
            p = new CCPosition();
        }
        else if (StringUtils.isEmpty(p.getTitle())
                || !p.getTitle().equals(positionTitle))
        {
            CCHelper.guardAgainstDuplicatePosition(a, positionTitle);
        }

        if (CC_ROLE_ADMIN.equals(CCHelper.getCurrentUserRole(module,
                PortalUtils.getUserLoggedIn(request))))
        {
            p.setExternalPosition(
                    RequestUtils.isChecked(request, "externalPosition"));
        }

        p.setEnableTimeTracking(
                RequestUtils.isChecked(request, "enableTimeTracking"));
        p.setUseHourLogging(RequestUtils.isChecked(request, "useHourLogging"));

        p.setUpdatedDate(now);
        p.setUpdatedBy(userLoggedIn);
        p.setActivity(a);

        DFHelper.bindAnswers(
                CCPositionQuestionModelHelper.getPositionQuestionModel(module), p,
                request, userLoggedIn, true);
        p.setOwnerUpdateRequested(false);
        PortalUtils.getHt().saveOrUpdate(p);

        try
        {
            deleteCCOutcomes(module, p);
            saveCCOutcomes(request, module, p);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    static void deleteCCOutcomes(CCModule module, CCPosition p)
    {
        if (module.isTrackLearningOutcomes())
        {
            if (module.isTrackAchievements())
            {
                PortalUtils.getJt().update("delete from cc_paa where position = ?",
                        new Object[] { p.getId() });
            }
            PortalUtils.getJt().update("delete from cc_pla where position = ?",
                    new Object[] { p.getId() });

        }
    }

    /**
     * a "guard" method
     *
     * @throws CCException
     */
    public static void guardAgainstDuplicatePosition(CCActivity ccActivity,
            String positionTitle) throws CCException
    {
        if (getPositionCount(ccActivity, positionTitle) > 0)
        {
            throw new CCException(CCException.TYPE.DUPLICATE_POSITION);
        }
    }

    /**
     *
     * @param request
     * @param errorList
     * @param module
     * @param function
     * @return list of usernames not ids
     */
    public static List<String> addStudentRecordToLearningExperience(
            HttpServletRequest request, List<String> errorList, OrbisModule module,
            Function<RequestModuleUser, ContentItem> function)
    {
        List<String> ids = StringUtils.listify(request.getParameter("data"),
                "\r\n");

        for (String id : ids)
        {
            if (id != null)
            {
                String userId = id;
                UserDetailsImpl user = UserDetailsHelper.getUserByUsername(userId);
                if (user == null)
                {
                    errorList.add(userId.toString());
                }
                else
                {
                    ContentItem r = function
                            .apply(new RequestModuleUser(request, module, id));

                    if (r == null)
                    {
                        errorList.add(userId.toString());
                    }
                }
            }
            if (id == null)
            {
                errorList.add(id);
            }
        }
        return ids;
    }

    public static String getMissingCompetenciesWhereFragment(CCModule module,
            String recordPositionAlias)
    {
        String whereFragment = "";

        return whereFragment;
    }

    /**
     *
     * @param text
     *            The text that contains the tokens that need replacing
     * @param firstName
     *            The first name of the recipient
     * @param lastName
     *            The last name of the recipient
     * @param activity
     *            The activity name
     * @param position
     *            The position name
     * @param status
     *            (optional) The name of a related status
     * @return
     */
    public static String processEmailTokens(String text, String firstName,
            String lastName, String activity, String position, String status,
            String validatedByFirstName, String validatedByLastName)
    {
        if (text != null)
        {
            text = text.replaceAll("\\|FIRSTNAME\\|",
                    Matcher.quoteReplacement(firstName == null ? "" : firstName));
            text = text.replaceAll("\\|LASTNAME\\|",
                    Matcher.quoteReplacement(lastName == null ? "" : lastName));
            text = text.replaceAll("\\|ACTIVITY\\|",
                    Matcher.quoteReplacement(activity == null ? "" : activity));
            text = text.replaceAll("\\|POSITION\\|",
                    Matcher.quoteReplacement(position == null ? "" : position));
            if (status != null)
            {
                text = text.replaceAll("\\|STATUS\\|",
                        Matcher.quoteReplacement(status));
            }
            if (validatedByFirstName != null)
            {
                text = text.replaceAll("\\|VALIDATEDBYFIRSTNAME\\|",
                        Matcher.quoteReplacement(validatedByFirstName));
            }
            if (validatedByLastName != null)
            {
                text = text.replaceAll("\\|VALIDATEDBYLASTNAME\\|",
                        Matcher.quoteReplacement(validatedByLastName));
            }
        }

        return text;
    }

    public static void populatePeriodSelection(HttpServletRequest request,
            ModelAndView mv, CCModule module)
    {
        List<OrbisHqlResultSet> periods = PortalUtils.getHt().f("select p.id, "
                + (LocaleUtils.isL1(request) ? "p.name" : "p.l2Name")
                + ", p.startDate, p.endDate from CCPeriod p where p.module=? and (p.active=true or p.activeStudent=true or p.activeVal=true or p.activeRequests=true) order by 3 desc, 2 desc, 1 desc",
                module);

        mv.addObject("availablePeriods", periods);
        mv.addObject("selectedPeriodId", StringUtils.isEmpty(request
                .getParameter("selectedPeriodId")) && !periods.isEmpty() ? periods
                        .stream()
                        .filter(p -> p.get("startDate") != null
                                && p.get("endDate") != null
                                && DateUtils.isBetween((Date) p.get("startDate"),
                                        (Date) p.get("endDate"), new Date()))
                        .map(p -> p.get("id")).findFirst()
                        .orElse(periods.get(0).get("id"))
                        : request.getParameter("selectedPeriodId"));
    }

    /**
     * Creates a map containing various statistics for a period
     *
     * @param keysToPopulate
     *            This should contain the key names of the stats you would like
     *            to be populated in the returned map
     * @param period
     * @param activityInClause
     *            a clause restricting the visibility of certain activities
     * @param positionId
     *            Provide a position Id if you would like the stats to be for a
     *            specific position
     * @return
     */
    public static Map getPeriodStatsMap(Set<String> keysToPopulate, CCPeriod period,
            String activityInClause, Integer positionId)
    {
        Map<String, Object> periodStats = CollectionUtils.fillMap(keysToPopulate,
                -1);

        periodStats.computeIfPresent("records",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(distinct rp.record.id) from CCRecordPosition rp join rp.position join rp.position.activity where rp.position.activity.period = ?"
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and rp.position.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and rp.position.id = "
                                                + positionId
                                        : ""),
                        period));
        periodStats.computeIfPresent("pendingPositions",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(p.id) from CCPosition p join p.activity where p.activity.period = ? and p.status=0 "
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and p.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and p.id = " + positionId.toString()
                                        : ""),
                        period));
        periodStats.computeIfPresent("activePositions",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(p.id) from CCPosition p join p.activity where p.activity.period = ? and p.enabled = true"
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and p.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and p.id = " + positionId.toString()
                                        : ""),
                        period));
        periodStats.computeIfPresent("approvedPositions",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(p.id) from CCPosition p join p.activity where p.activity.period = ?  and p.status=1 "
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and p.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and p.id = " + positionId.toString()
                                        : ""),
                        period));
        periodStats.computeIfPresent("approvedAndEnabledPositions",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(p.id) from CCPosition p join p.activity where p.activity.period = ?  and p.status=1 and p.enabled=true "
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and p.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and p.id = " + positionId.toString()
                                        : ""),
                        period));
        periodStats.computeIfPresent("approvedAndDisabledPositions",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(p.id) from CCPosition p join p.activity where p.activity.period = ?  and p.status=1 and p.enabled=false "
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and p.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and p.id = " + positionId.toString()
                                        : ""),
                        period));
        periodStats.computeIfPresent("declinedPositions",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(p.id) from CCPosition p join p.activity where p.activity.period = ?  and p.status=2 "
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and p.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and p.id = " + positionId.toString()
                                        : ""),
                        period));
        periodStats.computeIfPresent("disabledPositions",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(p.id) from CCPosition p join p.activity where p.activity.period = ? and p.enabled = 0"
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and p.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and p.id = " + positionId.toString()
                                        : ""),
                        period));
        periodStats.computeIfPresent("activeActivities",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(act.id) from CCActivity act where act.period=? and act.status=? "
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? " and act.id in " + activityInClause
                                        : ""),
                        period, CCActivity.STATUS_ACTIVE));
        periodStats.computeIfPresent("disabledActivities",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(act.id) from CCActivity act where act.period=? and act.status=? "
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? " and act.id in " + activityInClause
                                        : ""),
                        period, CCActivity.STATUS_DISABLED));
        periodStats.computeIfPresent("totalPositions",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(p.id) from CCPosition p join p.activity where p.activity.period = ?"
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and p.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and p.id = " + positionId.toString()
                                        : ""),
                        period));
        periodStats.computeIfPresent("pendingValidations",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(rp.id) from CCRecordPosition rp join rp.position join rp.position.activity where rp.status = '"
                                + CCRecordPosition.STATUS_PENDING
                                + "' and rp.position.activity.period = ? and rp.position.program is null"
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and rp.position.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and rp.position.id = "
                                                + positionId.toString()
                                        : ""),
                        period));
        periodStats.computeIfPresent("approvedValidations",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(rp.id) from CCRecordPosition rp join rp.position join rp.position.activity where rp.status = '"
                                + CCRecordPosition.STATUS_APPROVED
                                + "' and rp.position.activity.period = ? and rp.position.program is null"
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and rp.position.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and rp.position.id = "
                                                + positionId.toString()
                                        : ""),
                        period));
        periodStats.computeIfPresent("declinedValidations",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(rp.id) from CCRecordPosition rp join rp.position join rp.position.activity where rp.status = '"
                                + CCRecordPosition.STATUS_DECLINED
                                + "' and rp.position.activity.period = ? and rp.position.program is null"
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and rp.position.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and rp.position.id = "
                                                + positionId.toString()
                                        : ""),
                        period));
        periodStats.computeIfPresent("validatorsWithPending",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(distinct pv.validator.id) from CCPositionValidator pv join pv.position join pv.position.activity where pv.position.id in (select rp.position.id from CCRecordPosition rp where rp.status = '"
                                + CCRecordPosition.STATUS_PENDING
                                + "') and pv.position.activity.period = ?"
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and pv.position.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and pv.position.id = "
                                                + positionId.toString()
                                        : ""),
                        period));
        periodStats.computeIfPresent("validatorsWithApproved",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(distinct rp.validatedBy.id) from CCRecordPosition rp join rp.position join rp.position.activity where rp.status = '"
                                + CCRecordPosition.STATUS_APPROVED
                                + "' and rp.position.activity.period = ?"
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and rp.position.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and rp.position.id = "
                                                + positionId.toString()
                                        : ""),
                        period));
        periodStats.computeIfPresent("validatorsWithDeclined",
                (k, v) -> PortalUtils.getHt().findInt(
                        "select count(distinct rp.validatedBy.id) from CCRecordPosition rp join rp.position join rp.position.activity where rp.status = '"
                                + CCRecordPosition.STATUS_DECLINED
                                + "' and rp.position.activity.period = ? "
                                + (!StringUtils.isEmpty(activityInClause)
                                        ? (" and rp.position.activity.id in "
                                                + activityInClause)
                                        : "")
                                + (positionId != null
                                        ? " and rp.position.id = "
                                                + positionId.toString()
                                        : ""),
                        period));

        if (periodStats.containsKey("validators"))
        {
            List validators = PortalUtils.getHt().find(
                    "select distinct rp.validatedBy.id from CCRecordPosition rp join rp.position join rp.position.activity where rp.validatedBy is not null and rp.position.activity.period = ?"
                            + (!StringUtils.isEmpty(activityInClause)
                                    ? (" and rp.position.activity.id in "
                                            + activityInClause)
                                    : "")
                            + (positionId != null
                                    ? " and rp.position.id = "
                                            + positionId.toString()
                                    : ""),
                    period);
            validators.addAll(PortalUtils.getHt().find(
                    "select distinct pv.validator.id from CCPositionValidator pv join pv.position join pv.position.activity ac where ac.period = ?"
                            + (!StringUtils.isEmpty(activityInClause)
                                    ? (" and ac.id in "
                                            + activityInClause)
                                    : "")
                            + (positionId != null
                                    ? " and pv.position.id = "
                                            + positionId
                                    : ""),
                    period));
            periodStats.replace("validators",
                    CollectionUtils.distinctList(validators).size());
        }

        if (periodStats.containsKey("openTo"))
        {
            String openTo = "None";
            if (period.isActive() && period.isActiveRequests()
                    && period.isActiveStudent() && period.isActiveVal())
            {
                openTo = "All";
            }
            else if (period.isActive() || period.isActiveRequests()
                    || period.isActiveStudent() || period.isActiveVal())
            {
                openTo = "";
                if (period.isActive())
                {
                    openTo += "Admin";
                }
                if (period.isActiveRequests())
                {
                    openTo += "".equals(openTo) ? "Requestors" : ", Requestors";
                }
                if (period.isActiveStudent())
                {
                    openTo += "".equals(openTo) ? "Students" : ", Students";
                }
                if (period.isActiveVal())
                {
                    openTo += "".equals(openTo) ? "Validators" : ", Validators";
                }

            }
            periodStats.replace("openTo", openTo);
        }

        return periodStats;
    }

    public static GridFilter getPendingActivitiesFilter(Locale locale)
    {

        GridFilter f = new GridFilter("type",
                PortalUtils.getI18nMessage(
                        "i18n.CCHelper.PendingAct3476032820561809", locale),
                "custom", "resultId",
                Arrays.asList(NameValuePair.of("type", "pendingActivities")),
                Collections.EMPTY_MAP);
        return f;
    }

    public static GridFilter getActiveActivitiesFilter(Locale locale)
    {
        GridFilter f = new GridFilter("type",
                PortalUtils.getI18nMessage(
                        "i18n.CCHelper.ActiveActi2533820716121359", locale),
                "custom", "resultId",
                Arrays.asList(NameValuePair.of("type", "activeActivities")),
                Collections.EMPTY_MAP);
        return f;
    }

    public static GridFilter getInactiveActivitiesFilter(Locale locale)
    {
        GridFilter f = new GridFilter("type",
                PortalUtils.getI18nMessage(
                        "i18n.CCHelper.InactiveAc5009087956020432", locale),
                "custom", "resultId",
                Arrays.asList(NameValuePair.of("type", "inactiveActivities")),
                Collections.EMPTY_MAP);
        return f;
    }

    public static GridFilter getPendingPositionsFilter(Locale locale)
    {

        GridFilter f = new GridFilter("type",
                PortalUtils.getI18nMessage(
                        "i18n.CCHelper.PendingPos1412553848481000", locale),
                "custom", "resultId",
                Arrays.asList(NameValuePair.of("type", "pendingPositions")),
                Collections.EMPTY_MAP);
        return f;
    }

    public static GridFilter getApprovedAndVisiblePositionsFilter(Locale locale)
    {
        GridFilter f = new GridFilter("type",
                PortalUtils.getI18nMessage(
                        "i18n.CCHelper.ApprovedAn8326176208684251", locale),
                "custom", "resultId",
                Arrays.asList(
                        NameValuePair.of("type", "approvedAndVisiblePositions")),
                Collections.EMPTY_MAP);
        return f;
    }

    public static GridFilter getApprovedAndNotVisiblePositionsFilter(Locale locale)
    {
        GridFilter f = new GridFilter("type",
                PortalUtils.getI18nMessage(
                        "i18n.CCHelper.Approvedan7455563796428494", locale),
                "custom", "resultId",
                Arrays.asList(
                        NameValuePair.of("type", "approvedAndNotVisiblePositions")),
                Collections.EMPTY_MAP);
        return f;
    }

    public static GridFilter getDeclinedPositionsFilter(Locale locale)
    {
        GridFilter f = new GridFilter("type",
                PortalUtils.getI18nMessage(
                        "i18n.CCHelper.DeclinedPo9297723839027918", locale),
                "custom", "resultId",
                Arrays.asList(NameValuePair.of("type", "declinedPositions")),
                Collections.EMPTY_MAP);
        return f;
    }

    public static GridFilter getPendingValidationsFilter(Locale locale)
    {

        GridFilter f = new GridFilter("type",
                PortalUtils.getI18nMessage(
                        "i18n.CCHelper.PendingVal9783560907801740", locale),
                "custom", "resultId",
                Arrays.asList(NameValuePair.of("type", "pendingValidations")),
                Collections.EMPTY_MAP);
        return f;
    }

    public static GridFilter getApprovedValidationsFilter(Locale locale)
    {
        GridFilter f = new GridFilter("type",
                PortalUtils.getI18nMessage(
                        "i18n.CCHelper.ApprovedVa6125464989533155", locale),
                "custom", "resultId",
                Arrays.asList(NameValuePair.of("type", "approvedValidations")),
                Collections.EMPTY_MAP);
        return f;
    }

    public static GridFilter getDeclinedValidationsFilter(Locale locale)
    {
        GridFilter f = new GridFilter("type",
                PortalUtils.getI18nMessage(
                        "i18n.CCHelper.DeclinedVa0825846431714448", locale),
                "custom", "resultId",
                Arrays.asList(NameValuePair.of("type", "declinedValidations")),
                Collections.EMPTY_MAP);
        return f;
    }

    public static GridFilter getPendingValidatorsFilter(Locale locale)
    {

        GridFilter f = new GridFilter("type",
                PortalUtils.getI18nMessage(
                        "i18n.CCHelper.Validators9960329518833517", locale),
                "custom", "resultId",
                Arrays.asList(NameValuePair.of("type", "validatorsWithPending")),
                Collections.EMPTY_MAP);
        return f;
    }

    public static GridFilter getApprovedValidatorsFilter(Locale locale)
    {
        GridFilter f = new GridFilter("type",
                PortalUtils.getI18nMessage(
                        "i18n.CCHelper.Validators1365785074926245", locale),
                "custom", "resultId",
                Arrays.asList(NameValuePair.of("type", "validatorsWithApproved")),
                Collections.EMPTY_MAP);
        return f;
    }

    public static GridFilter getDeclinedValidatorsFilter(Locale locale)
    {
        GridFilter f = new GridFilter("type",
                PortalUtils.getI18nMessage(
                        "i18n.CCHelper.Validators6767095142059263", locale),
                "custom", "resultId",
                Arrays.asList(NameValuePair.of("type", "validatorsWithDeclined")),
                Collections.EMPTY_MAP);
        return f;
    }

    public static String limitPeriodToActiveClause(CCModule module,
            UserDetailsImpl user)
    {
        String visibleClause = "";
        if (isAdmin(user, module) || isActivityDirector(user, module))
        {
            visibleClause = " and p.active=1 ";
        }
        else if (isStaff(user, module) || isActivityOwner(user, module))
        {
            visibleClause = " and p.activeRequests=1 ";
        }
        else if (user.isStudent() || isAlumni(user))
        {
            visibleClause = " and p.activeStudent=1 ";
        }
        else if (isValidator(user))
        {
            visibleClause = " and p.activeVal=1 ";
        }
        return visibleClause;
    }

    /**
     * @param select
     *            aliases can be used: ca - competency achieved, crp - cc record
     *            position, c - competency, pit - proficiency item, prof -
     *            proficiency
     * @param recordId
     *            cc record position id
     */
    public static List<Map<String, Object>> getAchievedRecordPositionCompetencies(Select select,
            Integer recordId)
    {
        Where where = new Where();
        where.add("crp.record=?", recordId);
        where.add(ProficiencyItemHelper
                .getExcludeNegativeAnswerSqlWhereAndStatement("pit"));

        QueryBuilder q = new QueryBuilder();
        q.append(select.getSelectClause());
        q.append(" from competency_achieved ca");
        q.append(
                " inner join cc_record_position crp on ca.recordPosition = crp.id");
        q.append(" inner join competency c on ca.competency = c.id");
        q.append(" join proficiency_item pit on ca.proficiencyItem = pit.id");
        q.append(where.getWhereClause());
        return PortalUtils.getJt().queryForList(q);
    }

    public static CCAdmin checkAdminInModule(CCModule module, Integer userId)
    {
        CCAdmin existingAdmin = null;
        List<CCAdmin> tmpAdmin = PortalUtils.getHt()
                .find("select moduleAdmin.user " + " from CCAdmin moduleAdmin "
                        + " where moduleAdmin.module=? and moduleAdmin.user.id="
                        + userId, module);

        if (tmpAdmin.size() == 1)
        {
            existingAdmin = tmpAdmin.get(0);
        }
        return existingAdmin;
    }

    public static void removeAdminFromModule(UserDetailsImpl admin, CCModule module)
    {
        PortalUtils.getJt().update(
                "delete from cc_admin where uzer = ? and module = ?",
                new Object[] { admin.getId(), module.getId() });
    }

    public static JSONObject publishCCRecordPossition(CCRecordPosition rp,
            UserDetailsImpl userLoggedIn, Locale locale) throws JSONException
    {
        JSONObject ret = new JSONObject();
        if (CCRecordPosition.STATUS_APPROVED.equals(rp.getStatus()))
        {
            PublishHelper.publishCard(rp, rp.getOwner());
            rp.setShowOnPdf(true);
            PortalUtils.getHt().update(rp);
            ret.put("active", rp.isShowOnPdf());
        }
        else
        {
            List<String> errors = CCRecordPositionHelper
                    .validateRequestToPublish(rp, userLoggedIn, locale);

            if (errors.isEmpty())
            {
                PublishHelper.publishCard(rp, rp.getOwner());
                rp.setShowOnPdf(true);

                PortalUtils.getHt().update(rp);
                ret.put("active", rp.isShowOnPdf());
            }
            else
            {
                ret.put("errors", StringUtils.join(errors, "<br>"));
            }
        }
        return ret;
    }

}