package com.orbis.web.content.grid;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.RequestUtils;
import com.orbis.utils.StringUtils;
import com.orbis.utils.query.WhereFragment;
import com.orbis.web.OrbisController;
import com.orbis.web.content.exp.EXPCourseHelper;
import com.orbis.web.content.exp.EXPModule;
import com.orbis.web.content.exp.EXPModuleUser;
import com.orbis.web.content.exp.EXPPostingFacultyOwnerInHqlClause;
import com.orbis.web.content.exp.EXPPostingTagsInHqlClause;

public class EXPHomeActiveCoursesHql implements GridHqlCallback
{
    @Override
    public String getFromClauseFragment(GridHqlCallbackParameters params)
    {
        return " EXPTermCourse tc join tc.course join tc.term ";
    }

    @Override
    public HqlWhereClause getWhereClause(GridHqlCallbackParameters params)
    {
        HttpServletRequest request = params.getRequest();
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        EXPModule module = (EXPModule) OrbisController.getOrbisModule(request);

        List<Integer> selectedTerms = RequestUtils
                .splitToList(request, "selectedTerms", ",").stream()
                .map(Integer::valueOf).collect(Collectors.toList());
        EXPModuleUser moduleUser = EXPModuleUser.getInstance(userLoggedIn, module);
        WhereFragment whereClause = EXPCourseHelper.getCoursesWhereClause("tc",
                moduleUser, selectedTerms);
        for (final GridFilter gridFilter : params.getCustomFilters())
        {
            String type = gridFilter.getValueMap().get("type");
            if ("noStudents".equals(type))
            {
                whereClause.append(
                        new WhereFragment(" not "
                                + EXPCourseHelper.getHasEnrollmentsWhereClause()),
                        "and");
            }
            else if ("inProgress".equals(type))
            {
                whereClause.append(new WhereFragment(
                        EXPCourseHelper.getInProgressWhereClause()), "and");
            }
            else if ("completed".equals(type))
            {
                whereClause.append(
                        new WhereFragment(
                                EXPCourseHelper.getHasEnrollmentsWhereClause()),
                        "and");
                whereClause.append(
                        new WhereFragment(" not "
                                + EXPCourseHelper.getInProgressWhereClause()),
                        "and");

            }
            else if ("program".equals(type))
            {
                whereClause.append(new WhereFragment("tc.course.program=true"), "and");
            }
            else if ("course".equals(type))
            {
                whereClause.append(new WhereFragment("tc.course.program=false"), "and");
            }
        }

        whereClause.append(getTagFilterClause(params.getTagFilters()), "AND");

        for (final GridFilter gridFilter : params.getCustomQueryFilters())
        {
            switch (gridFilter.getColID())
            {
                case "facultyOwners":
                    whereClause.append(
                            new EXPPostingFacultyOwnerInHqlClause("tc.course",
                                    gridFilter.getValues()//
                                            .stream()//
                                            .map(NameValuePair::getValue)//
                                            .map(StringUtils::toInteger)//
                                            .filter(Objects::nonNull)//
                                            .collect(Collectors.toList())),
                            "and");
                    break;
                case "termCourseTags":
                    whereClause.append(new EXPPostingTagsInHqlClause(gridFilter
                            .getValues().stream().map(NameValuePair::getValue)
                            .map(StringUtils::toInteger).filter(Objects::nonNull)
                            .collect(Collectors.toList())), "and");
                default:
                    break;
            }
        }

        return new HqlWhereClause(whereClause);
    }

    public WhereFragment getTagFilterClause(List<GridTagFilter> tagFilters)
    {
        return new WhereFragment(GridTagFilterHelper
                .getTermCourseTagFiltersClause("tc.id", tagFilters));
    }

    @Override
    public String getGroupByClause(GridHqlCallbackParameters params)
    {
        return null;
    }
}