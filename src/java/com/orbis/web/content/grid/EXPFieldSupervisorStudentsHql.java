package com.orbis.web.content.grid;

import jakarta.servlet.http.HttpServletRequest;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.query.WhereFragment;
import com.orbis.web.OrbisController;
import com.orbis.web.content.exp.EXPFieldSupervisorCommonHqlClause;
import com.orbis.web.content.exp.EXPModule;

public class EXPFieldSupervisorStudentsHql implements GridHqlCallback
{
    @Override
    public String getFromClauseFragment(GridHqlCallbackParameters params)
    {
        return " FROM EXPRecord r" //
                + " join r.owner u" //
                + " join r.studentStep ses" //
                + " join ses.step.tct tct" //
                + " join tct.type expType" //
                + " join tct.termCourse tc" //
                + " join tc.course c" //
                + " join tc.term t";
    }

    @Override
    public HqlWhereClause getWhereClause(GridHqlCallbackParameters params)
    {
        HttpServletRequest request = params.getRequest();

        EXPModule module = OrbisController.getOrbisModule(request);

        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        return new HqlWhereClause(getCommonWhereClause("r", module, userLoggedIn));
    }

    @Override
    public String getGroupByClause(GridHqlCallbackParameters params)
    {
        return null;
    }

    public static WhereFragment getCommonWhereClause(String recordAlias,
            EXPModule module, UserDetailsImpl userLoggedIn)
    {
        return new EXPFieldSupervisorCommonHqlClause(recordAlias, userLoggedIn,
                module);
    }
}