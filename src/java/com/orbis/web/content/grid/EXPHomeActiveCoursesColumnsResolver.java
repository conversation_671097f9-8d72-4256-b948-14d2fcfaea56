package com.orbis.web.content.grid;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.DataViewerFilterOptionsMapExtractor;
import com.orbis.portal.MapExtractor;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.QueryBuilder;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.grid.DataViewerGridHelper.GridFilterJSON;
import com.orbis.web.content.grid.dataViewer.DataViewerGridFilter;

import jakarta.servlet.http.HttpServletRequest;

public class EXPHomeActiveCoursesColumnsResolver
        implements DataViewerGridColumnsResolver
{
    @Override
    public List<DataViewerGridItem> getGridItems(UserDetailsImpl currentUser,
            Integer moduleId, Locale locale, HttpServletRequest request)
    {
        List<DataViewerGridItem> ret = new ArrayList<>();

        Map<String, GridColumn> gridCols = GridQueryHelper.getGridColumns(
                "exp_homeActiveCourses", currentUser, moduleId,
                Collections.EMPTY_MAP);

        for (Entry<String, GridColumn> gridColEntry : gridCols.entrySet())
        {
            GridColumn gridCol = gridColEntry.getValue();

            if (!gridCol.isVisible()
                    || !gridCol.isVisiblePermissionSpecific(currentUser))
            {
                continue;
            }

            DataViewerGridFilter filter = new DataViewerGridFilter();

            filter.setType(DataViewerGridFilter.TYPE_OPTIONS);

            DataViewerGridColumn dvCol = new DataViewerGridColumn();

            dvCol.setKey(gridCol.getColID());
            dvCol.setTitle(gridCol.getColumnName());
            dvCol.setId(gridCol.isKeyCol());
            dvCol.setFilter(filter);

            if ("termName".equals(gridCol.getColID()))
            {
                QueryBuilder q = new QueryBuilder();
                q.append(" select t.id, t.name");
                q.append(" from exp_term t");
                q.append(" where t.module=?", moduleId);
                q.append(" order by t.ordinal");

                filter.setOptionsMap(PortalUtils.getJt().query(q,
                        new MapExtractor<>("id", "name")));
                filter.setAdvancedFilter(false);
            }
            else if ("section".equals(gridCol.getColID()))
            {
                QueryBuilder q = new QueryBuilder();
                q.append(" select distinct c.section");
                q.append(" from exp_course c");
                q.append(" where c.module=?", moduleId);
                q.append(" order by c.section");

                filter.setOptionsMap(PortalUtils.getJt().query(q,
                        new DataViewerFilterOptionsMapExtractor("section")));
                filter.setQuickfilter(false);
            }
            else if ("code".equals(gridCol.getColID()))
            {
                QueryBuilder q = new QueryBuilder();
                q.append(" select distinct c.code");
                q.append(" from exp_course c");
                q.append(" where c.module=?", moduleId);
                q.append(" order by c.code");

                filter.setOptionsMap(PortalUtils.getJt().query(q,
                        new DataViewerFilterOptionsMapExtractor("code")));
                filter.setQuickfilter(false);
            }
            else
            {

                dvCol = DataViewerGridColumnHelper.getBasicColumn(gridCol,
                        currentUser, locale);

                if (StringUtils.equalsAny(gridCol.getColID(), "termId", "tcId"))
                {
                    dvCol.setFilter(null);
                }
            }

            if (dvCol != null)
            {
                ret.add(dvCol);
            }
        }

        return ret;
    }

    @Override
    public GridFilter getFilter(GridFilterJSON filter, GridColumn column,
            Locale locale)
    {
        GridFilter ret;

        if ("termName".equals(filter.getKey()))
        {
            ret = DataViewerGridHelper.getCustomOptionsFilter(filter);
        }
        else if (column != null)
        {
            ret = DataViewerGridHelper.getBasicFilter(filter, column, locale);
        }
        else
        {
            ret = null;
        }

        return ret;
    }

}
