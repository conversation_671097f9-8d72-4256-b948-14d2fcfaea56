
package com.orbis.web.content.grid;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

import com.orbis.utils.RequestUtils;
import com.orbis.web.content.exp.EXPPostingHelper;
import org.json.JSONArray;
import org.json.JSONException;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalLogHistoryHql;
import com.orbis.portal.PortalLogHistoryVirtualCallback;
import com.orbis.portal.PortalUtils;
import com.orbis.qf.QFStudentQualificationsHql;
import com.orbis.search.SavedSearchesHql;
import com.orbis.search.SavedSearchesVirtualData;
import com.orbis.utils.DBUtils;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.JSONUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.acrm.AcrmAllOrgDivisionsHql;
import com.orbis.web.content.acrm.AcrmAllOrgDivisionsVirtualData;
import com.orbis.web.content.acrm.AcrmPendingRegistrationsHql;
import com.orbis.web.content.acrm.AcrmPendingRegistrationsVirtualData;
import com.orbis.web.content.acrm.CcrmDoNotEmailListHql;
import com.orbis.web.content.acrm.CcrmDoNotEmailListVirtualData;
import com.orbis.web.content.acrm.CcrmEventsHql;
import com.orbis.web.content.acrm.CcrmEventsVirtualData;
import com.orbis.web.content.acrm.CcrmExternalJobViewsHql;
import com.orbis.web.content.acrm.CcrmSentEmailsHql;
import com.orbis.web.content.acrm.CcrmUsersOptedOutOfEmailCampaignsHql;
import com.orbis.web.content.acrm.CcrmUsersOptedOutOfSystemEmailsHql;
import com.orbis.web.content.acrm.CcrmWorkTermsHql;
import com.orbis.web.content.acrm.CcrmWorkTermsVirtualData;
import com.orbis.web.content.acrm.PendingRegistrationInfoSessionsHql;
import com.orbis.web.content.acrm.ProspectUserWithNotesHql;
import com.orbis.web.content.acrm.ProspectUserWithServiceTeamHql;
import com.orbis.web.content.acrm.ProspectUserWithServiceTeamVirtualData;
import com.orbis.web.content.acrm.pendingRegistrationPostingsHql;
import com.orbis.web.content.cc.CCRecordPositionsHql;
import com.orbis.web.content.cc.CCRecordPositionsVirtualData;
import com.orbis.web.content.coop.CoopAdminAdmissionsHql;
import com.orbis.web.content.coop.CoopAdminAdmissionsVirtualData;
import com.orbis.web.content.coop.CoopCompetencyStatsRecordsHql;
import com.orbis.web.content.coop.CoopProgramEditHql;
import com.orbis.web.content.coop.CoopTermEditProgramsHql;
import com.orbis.web.content.coop.CoopTermJobApplicationVirtualData;
import com.orbis.web.content.coop.CoopTermRecordJobApplicationHql;
import com.orbis.web.content.coop.CoopWtrEmployerHql;
import com.orbis.web.content.coop.CoopWtrEmployerVitualData;
import com.orbis.web.content.coop.PlacementAdminHomeAuditRankingHql;
import com.orbis.web.content.dashboard.CcrmAppointmentsHql;
import com.orbis.web.content.dashboard.DashboardApplicationBundleVirtualData;
import com.orbis.web.content.dashboard.DashboardController;
import com.orbis.web.content.dashboard.DashboardEmployerInteractionFormsVirtualData;
import com.orbis.web.content.dashboard.DashboardHelper;
import com.orbis.web.content.dashboard.DashboardInMemoryOccJobsVirtualData;
import com.orbis.web.content.dashboard.DashboardInterviewsHql;
import com.orbis.web.content.dashboard.DashboardPortalUserInteractionFormsVirtualData;
import com.orbis.web.content.dashboard.DashboardStaffAppointmentsHql;
import com.orbis.web.content.dashboard.DashboardStaffAppointmentsVirtualData;
import com.orbis.web.content.dashboard.DashboardStaffFacultyInteractionFormsVirtualData;
import com.orbis.web.content.dashboard.DashboardStaffInteractionEngagementsHql;
import com.orbis.web.content.dashboard.DashboardStaffInteractionEngagementsVirtualData;
import com.orbis.web.content.dashboard.DashboardStaffInteractionFormsHql;
import com.orbis.web.content.dashboard.DashboardStaffInteractionFormsVirtualData;
import com.orbis.web.content.dashboard.DashboardStaffInteractionMessagesHql;
import com.orbis.web.content.dashboard.DashboardStaffInteractionMessagesVirtualData;
import com.orbis.web.content.dashboard.DashboardStaffInteractionNotesHql;
import com.orbis.web.content.dashboard.DashboardStaffInteractionNotesVirtualData;
import com.orbis.web.content.dashboard.DashboardStaffInteractionTasksHql;
import com.orbis.web.content.dashboard.DashboardStaffInteractionTasksVirtualData;
import com.orbis.web.content.dashboard.DashboardStaffInteractionsHql;
import com.orbis.web.content.dashboard.DashboardStaffInteractionsVirtualCallback;
import com.orbis.web.content.dashboard.DashboardStaffInterviewsVirtualData;
import com.orbis.web.content.dashboard.DashboardStaffMyApplicationsHql;
import com.orbis.web.content.dashboard.DashboardStaffMyJobsHql;
import com.orbis.web.content.dashboard.DashboardStaffMyJobsVirtualData;
import com.orbis.web.content.dashboard.DashboardStaffMyOrgsHql;
import com.orbis.web.content.dashboard.DashboardStaffMyOrgsVirtualData;
import com.orbis.web.content.dashboard.DashboardStaffMyStudentTimeOffsHql;
import com.orbis.web.content.dashboard.DashboardStaffMyStudentTimeOffsVirtualData;
import com.orbis.web.content.dashboard.DashboardStaffMyStudentsHql;
import com.orbis.web.content.dashboard.DashboardStaffMyStudentsSearchingEmployedHql;
import com.orbis.web.content.dashboard.DashboardStaffMyStudentsSearchingUnemployedHql;
import com.orbis.web.content.dashboard.DashboardStaffMyStudentsSearchingWorkingHql;
import com.orbis.web.content.dashboard.DashboardStaffMyStudentsSearchingWorkingVirtualData;
import com.orbis.web.content.dashboard.DashboardStaffMyStudentsVirtualData;
import com.orbis.web.content.dashboard.DashboardStaffMyStudentsWorkingEmployedHql;
import com.orbis.web.content.dashboard.DashboardStaffMyStudentsWorkingUnemployedHql;
import com.orbis.web.content.dashboard.DashboardStudentInteractionFormsVirtualData;
import com.orbis.web.content.dashboard.DashboardStudentMyTimeOffRequestsHql;
import com.orbis.web.content.dashboard.DashboardStudentMyTimeOffRequestsVirtualData;
import com.orbis.web.content.dashboard.DashboardUserCommonMyTasks;
import com.orbis.web.content.dashboard.MyContactsHql;
import com.orbis.web.content.dashboard.MyContactsVirtualData;
import com.orbis.web.content.dashboard.MyOrgDivHql;
import com.orbis.web.content.dashboard.MyOrgDivVirtualData;
import com.orbis.web.content.dashboard.MyProspectsHql;
import com.orbis.web.content.dashboard.MyProspectsVirtualData;
import com.orbis.web.content.dashboard.SharedEmployerJobsGridHql;
import com.orbis.web.content.dashboard.SharedEmployerJobsGridVirtualData;
import com.orbis.web.content.disruptive.DisruptiveHqlCallback;
import com.orbis.web.content.disruptive.DisruptiveItemResponsesHqlCallback;
import com.orbis.web.content.disruptive.DisruptiveVirtualCallback;
import com.orbis.web.content.disruptive.UserDisruptionHql;
import com.orbis.web.content.doc.DocCommonGridHQL;
import com.orbis.web.content.ec.ECUserCompetencySearchSql;
import com.orbis.web.content.ec.ECUserCompetencySearchVirtualData;
import com.orbis.web.content.ec.ECUserRecordSearchSql;
import com.orbis.web.content.ec.ECUserRecordSearchVirtualData;
import com.orbis.web.content.ec.ECUserReflectionSearchSql;
import com.orbis.web.content.ep.EPPostingHql;
import com.orbis.web.content.ep.aggregation.EPExperienceGridHqlCallback;
import com.orbis.web.content.ev.web.globalevents.GlobalEventsEmailLogsHql;
import com.orbis.web.content.exp.EXPExpiringPostingTermOpeningsGridHqlCallback;
import com.orbis.web.content.exp.EXPIndustryPartnerPlaceStudentsHQL;
import com.orbis.web.content.exp.EXPIndustryPartnerPlaceStudentsVirtualData;
import com.orbis.web.content.exp.EXPPostingHelper;
import com.orbis.web.content.exp.EXPPostingPlaceStudentsHQL;
import com.orbis.web.content.exp.EXPPostingPlaceStudentsVirtualData;
import com.orbis.web.content.exp.EXPUnfinishedCustomFormsGridHqlCallback;
import com.orbis.web.content.exp.EXPUnfinishedFacultyEvaluationsGridHqlCallback;
import com.orbis.web.content.exp.EXPUnfinishedHourTrackingStepsGridHqlCallback;
import com.orbis.web.content.exp.EXPUnfinishedIndustryPartnerEvaluationsGridHqlCallback;
import com.orbis.web.content.exp.EXPUnfinishedStudentEvaluationsGridHqlCallback;
import com.orbis.web.content.insights.InsightsManageNAICHql;
import com.orbis.web.content.insights.InsightsManageNOCHql;
import com.orbis.web.content.interaction.InteractionFormAuditLogHql;
import com.orbis.web.content.interaction.InteractionFormsVirtualData;
import com.orbis.web.content.interaction.InteractionHelper;
import com.orbis.web.content.interaction.InteractionMessageTemplatesHql;
import com.orbis.web.content.interaction.InteractionMessagesVirtualData;
import com.orbis.web.content.interaction.InteractionNotesVirtualData;
import com.orbis.web.content.interaction.InteractionSummaryGridHql;
import com.orbis.web.content.interaction.InteractionSummaryGridVirtualCallback;
import com.orbis.web.content.interaction.InteractionTaskAuditLogHql;
import com.orbis.web.content.interaction.InteractionTeamMemberGrid;
import com.orbis.web.content.interaction.InteractionWTRCompetencyNotesVirtualData;
import com.orbis.web.content.interaction.InteractionsHql;
import com.orbis.web.content.interview.InterviewCleanupHql;
import com.orbis.web.content.interview.InterviewCleanupIntervieweeHql;
import com.orbis.web.content.interview.InterviewCleanupVirtualCallback;
import com.orbis.web.content.interview.InterviewRoomUtilizationHql;
import com.orbis.web.content.interview.InterviewRoomUtilizationVitualCallback;
import com.orbis.web.content.interview.InterviewsGridDaysVirtualData;
import com.orbis.web.content.interview.InterviewsGridHqlCallback;
import com.orbis.web.content.interview.InterviewsGridIntervieweesVirtualData;
import com.orbis.web.content.interview.InterviewsGridJobsVirtualData;
import com.orbis.web.content.interview.InterviewsGridSchedulesVirtualData;
import com.orbis.web.content.interview.MockInterviewsHql;
import com.orbis.web.content.interview.MockInterviewsVirtualData;
import com.orbis.web.content.na.NAEmployerJobApplicationsHql;
import com.orbis.web.content.na.NAEmployerJobApplicationsVirtualData;
import com.orbis.web.content.na.NAHelper;
import com.orbis.web.content.na.NAJobApplicationsDownloadHql;
import com.orbis.web.content.na.NASearchPostingsResultHql;
import com.orbis.web.content.na.NAStaffJobApplicationsHql;
import com.orbis.web.content.na.NAStaffJobApplicationsVirtualData;
import com.orbis.web.content.na.NAStudentApplicationHql;
import com.orbis.web.content.na.NAStudentApplicationVirtualData;
import com.orbis.web.content.na.NApplicationAdminDashboadVirtualData;
import com.orbis.web.content.na.NApplicationAdminOverviewTabHql;
import com.orbis.web.content.na.NApplicationBundleVirtualData;
import com.orbis.web.content.np.NHelper;
import com.orbis.web.content.np.NPostingEmployerPostingsHql;
import com.orbis.web.content.np.NPostingEmployerPostingsVirtualData;
import com.orbis.web.content.np.NPostingQualifiedStudentsHql;
import com.orbis.web.content.np.NPostingQualifiedStudentsVirtualData;
import com.orbis.web.content.np.NPostingStatisticsTabHql;
import com.orbis.web.content.np.PostingsGridForAdmins;
import com.orbis.web.content.portal.PortalToolsEmailLogsHql;
import com.orbis.web.content.portal.PortalToolsFailedLoginsHql;
import com.orbis.web.content.portal.PortalToolsHelper;
import com.orbis.web.content.portal.PortalToolsPortalLogHql;
import com.orbis.web.content.pt.PTProgramEnrollmentGridHql;
import com.orbis.web.content.pt.PTProgramEnrollmentVirtualData;
import com.orbis.web.content.res.ResDoubleRoomBookingsHql;
import com.orbis.web.content.res.ResDoubleRoomBookingsVirtualData;
import com.orbis.web.content.res.ResHelper;
import com.orbis.web.content.res.ResOrganizationReservationsHql;
import com.orbis.web.content.res.ResOrganizationReservationsVirtualData;

import jakarta.servlet.http.HttpServletRequest;

public final class GridQueryFactory
{
    public static final String DATA_METHOD = "GridQueryFactoryDataMethod";

    private GridQueryFactory()
    {
    }

    public static GridQuery newInstance(final String gridID,
            final HttpServletRequest request, final ModelAndView mv)
    {
        Locale locale = PortalUtils.getLocale(request);

        GridQuery query = new GridQuery();
        query.setMv(mv);
        query.setRequest(request);
        query.setGridID(gridID);
        query.setGridInstanceId(ServletRequestUtils.getStringParameter(request,
                "gridInstanceId", null));

        if ("dashboard_staffMyStudentsSearchingWorking".equals(gridID))
        {
            query.setGridHqlCallback(
                    new DashboardStaffMyStudentsSearchingWorkingHql());
            query.setCustomFilters(DashboardController
                    .getDashboardMyStudentCustomFilters(request));
            query.setVirtualDataCallback(
                    new DashboardStaffMyStudentsSearchingWorkingVirtualData(true));
        }
        else if ("dashboard_staffMyStudentsSearchingEmployed".equals(gridID))
        {
            query.setGridHqlCallback(
                    new DashboardStaffMyStudentsSearchingEmployedHql());
            query.setCustomFilters(DashboardController
                    .getDashboardMyStudentCustomFilters(request));
            query.setVirtualDataCallback(
                    new DashboardStaffMyStudentsSearchingWorkingVirtualData(false));
        }
        else if ("dashboard_staffMyStudentsSearchingUnemployed".equals(gridID))
        {
            query.setGridHqlCallback(
                    new DashboardStaffMyStudentsSearchingUnemployedHql());
            query.setCustomFilters(DashboardController
                    .getDashboardMyStudentCustomFilters(request));
            query.setVirtualDataCallback(
                    new DashboardStaffMyStudentsSearchingWorkingVirtualData(false));
        }
        else if ("dashboard_staffMyStudentsWorkingEmployed".equals(gridID))
        {
            query.setGridHqlCallback(
                    new DashboardStaffMyStudentsWorkingEmployedHql());
            query.setCustomFilters(DashboardController
                    .getDashboardMyStudentCustomFilters(request));
            query.setVirtualDataCallback(
                    new DashboardStaffMyStudentsSearchingWorkingVirtualData(false));
        }
        else if ("dashboard_staffMyStudentsWorkingUnemployed".equals(gridID))
        {
            query.setGridHqlCallback(
                    new DashboardStaffMyStudentsWorkingUnemployedHql());
            query.setCustomFilters(DashboardController
                    .getDashboardMyStudentCustomFilters(request));
            query.setVirtualDataCallback(
                    new DashboardStaffMyStudentsSearchingWorkingVirtualData(false));
        }
        else if ("dashboard_staffMyOrgs".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardStaffMyOrgsHql());
            query.setVirtualDataCallback(new DashboardStaffMyOrgsVirtualData());
        }
        else if ("dashboard_staffMyOrgDiv".equals(gridID))
        {
            query.setPropertyOverrides(MyOrgDivHql.getMyOrgDivColumnOverrides());
            query.setGridHqlCallback(new MyOrgDivHql());
            query.setVirtualDataCallback(new MyOrgDivVirtualData());
            query.setCustomFilters(DashboardHelper.getJobTypeFilters());
        }
        else if ("cc_recordPositions".equals(gridID))
        {
            query.setCustomFilters(
                    CCRecordPositionsHql.getCCRecordPositionsGridFilters(
                            PortalUtils.getLocale(request)));
            query.setGridHqlCallback(new CCRecordPositionsHql());
            query.setVirtualDataCallback(new CCRecordPositionsVirtualData());
        }
        else if ("cc_recordPositionValidations".equals(gridID))
        {
            query.setGridHqlCallback(new CcRecordPositionValidationsHql());
            query.setVirtualDataCallback(
                    new CcRecordPositionValidationsVirtualData());
        }
        else if ("cc_recordPositionApprovedValidationsNoReflection".equals(gridID))
        {
            query.setGridHqlCallback(
                    new CcRecordPositionApprovedValidationsNoReflectionHql());
            query.setVirtualDataCallback(
                    new CcRecordPositionApprovedValidationsNoReflectionVirtualData());
        }
        else if ("cc_recordPositionApprovedValidationsZeroCompetencies"
                .equals(gridID))
        {
            query.setGridHqlCallback(
                    new CcRecordPositionApprovedValidationsZeroCompetenciesHql());
            query.setVirtualDataCallback(
                    new CcRecordPositionApprovedValidationsZeroCompetenciesVirtualData());
        }
        else if ("cc_recordPositionPendingExternalValidations".equals(gridID))
        {
            query.setGridHqlCallback(
                    new CcRecordPositionPendingExternalValidationsHql());
            query.setVirtualDataCallback(
                    new CcRecordPositionPendingExternalValidationsVirtualData());
        }
        else if ("cc_recordPositionPendingValidations10Days".equals(gridID))
        {
            query.setGridHqlCallback(
                    new CcRecordPositionPendingValidations10DaysHql());
            query.setVirtualDataCallback(
                    new CcRecordPositionPendingValidations10DaysVirtualData());
        }
        else if ("cc_recordPositionApprovedValidations10Days".equals(gridID))
        {
            query.setGridHqlCallback(
                    new CcRecordPositionApprovedValidations10DaysHql());
            query.setVirtualDataCallback(
                    new CcRecordPositionApprovedValidations10DaysVirtualData());
        }
        else if ("cc_recordPositionDeclinedValidations10Days".equals(gridID))
        {
            query.setGridHqlCallback(
                    new CcRecordPositionDeclinedValidations10DaysHql());
            query.setVirtualDataCallback(
                    new CcRecordPositionDeclinedValidations10DaysVirtualData());
        }
        else if ("cc_recordPositionStudentsWithAtleastOnePosition".equals(gridID))
        {
            query.setGridHqlCallback(
                    new CcRecordPositionStudentsWithAtleastOnePositionHql());
            query.setVirtualDataCallback(
                    new CcRecordPositionStudentsWithAtleastOnePositionVirtualData());
        }
        else if ("cc_recordPositionActivities".equals(gridID))
        {
            query.setGridHqlCallback(new CcRecordPositionActivitiesHql());
        }
        else if ("cc_recordPositionPositions".equals(gridID))
        {
            query.setGridHqlCallback(new CcRecordPositionPositionsHql());
        }
        else if ("cc_recordPositionValidators".equals(gridID))
        {
            query.setGridHqlCallback(new CcRecordPositionValidatorsHql());
        }

        else if ("res_organizationReservations".equals(gridID))
        {
            query.setGridHqlCallback(new ResOrganizationReservationsHql());
            query.setVirtualDataCallback(
                    new ResOrganizationReservationsVirtualData());
            ResHelper.setOrganizationReservationsCustomExportFormatters(query,
                    locale);
        }
        else if ("res_floorplanListView".equals(gridID))
        {
            query.setGridHqlCallback(new ResFloorplanListViewHql());
            query.setVirtualDataCallback(new ResFloorplanListViewVirtualData());
        }
        else if ("dashboard_employerEXPPostings".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardEmployerEXPPostingsHql());
            query.setVirtualDataCallback(
                    new DashboardEmployerEXPPostingsVirtualData());
        }
        else if ("dashboard_employerEXPRecords".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardEmployerEXPRecordsHql());
        }
        else if ("dashboard_staffMyContacts".equals(gridID))
        {
            query.setGridHqlCallback(new MyContactsHql());
            query.setCustomFilters(DashboardHelper.getJobTypeFilters());
            query.setVirtualDataCallback(new MyContactsVirtualData());
        }
        else if ("dashboard_staffMyProspects".equals(gridID))
        {
            query.setGridHqlCallback(new MyProspectsHql());
            query.setVirtualDataCallback(new MyProspectsVirtualData());
        }
        else if ("dashboard_staffMyJobs".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardStaffMyJobsHql());
            query.setVirtualDataCallback(new DashboardStaffMyJobsVirtualData());
            List<GridFilter> customFilters = DashboardHelper
                    .getDashboardWidgetSubCategoryFilters("Postings", request);
            customFilters.add(new GridFilter("clusters",
                    new I18nLabel(
                            "i18n.dashboard_staffMyJobs.Cluster1912745131751311")
                            .getTranslation(locale),
                    "custom", null, Collections.EMPTY_LIST, Collections.EMPTY_MAP));
            query.setCustomFilters(customFilters);
        }
        else if ("dashboard_staffMyApplications".equals(gridID))
        {
            query.setVirtualDataCallback(
                    new DashboardApplicationBundleVirtualData());
            query.setCustomFilters(
                    DashboardHelper.getDashboardWidgetSubCategoryFilters(
                            "ApplicationBundle", request));
            query.setGridHqlCallback(new DashboardStaffMyApplicationsHql());
        }
        else if ("dashboard_staffAppointments".equals(gridID))
        {
            query.setVirtualDataCallback(
                    new DashboardStaffAppointmentsVirtualData());
            query.setGridHqlCallback(new DashboardStaffAppointmentsHql());
            query.setCustomFilters(
                    DashboardHelper.getStaffAppointmentsGridFilters(request));
        }
        else if ("dashboard_staffInterviews".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardInterviewsHql());
            query.setVirtualDataCallback(new DashboardStaffInterviewsVirtualData());
            if (!ServletRequestUtils.getBooleanParameter(request, "dshBrdIntvRqsts",
                    false))
            {
                query.setCustomFilters(
                        DashboardHelper.getDashboardWidgetSubCategoryFilters(
                                "Interview", request));
            }
        }
        else if ("dashboard_staffMyEvents".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardStaffMyEventsHql());
            query.setVirtualDataCallback(new DashboardStaffMyEventsVirtualData());
            query.setCustomFilters(DashboardHelper
                    .getDashboardWidgetSubCategoryFilters("Event", request));
        }
        else if ("dashboard_staffMyStudents".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardStaffMyStudentsHql());
            query.setVirtualDataCallback(new DashboardStaffMyStudentsVirtualData());
        }
        else if ("dashboard_staffInteractionNotes".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardStaffInteractionNotesHql());
            List<GridFilter> customFilters = DashboardHelper
                    .getDashboardWidgetSubCategoryFilters("Note", request);
            customFilters.addAll(
                    DashboardStaffInteractionNotesHql.getCustomFilters(request));
            query.setCustomFilters(customFilters);
            query.setVirtualDataCallback(
                    new DashboardStaffInteractionNotesVirtualData());
        }
        else if ("dashboard_staffInteractionTasks".equals(gridID))
        {
            query.setCustomFilters(DashboardHelper
                    .getDashboardWidgetSubCategoryFilters("Task", request));
            query.setGridHqlCallback(new DashboardStaffInteractionTasksHql());
            query.setVirtualDataCallback(
                    new DashboardStaffInteractionTasksVirtualData());
        }
        else if ("dashboard_userCommonMyTasks".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardUserCommonMyTasks());
        }
        else if ("dashboard_userCommonMyMessages".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardStaffInteractionMessagesHql());
            query.setVirtualDataCallback(
                    new DashboardStaffInteractionMessagesVirtualData());
            query.setCustomFilters(
                    DashboardHelper.getInteractionMessageFilters(request));
        }
        else if ("dashboard_staffInteractionForms".equals(gridID)
                || "dashboard_studentInteractionForms".equals(gridID)
                || "dashboard_employerInteractionForms".equals(gridID)
                || "dashboard_userInteractionForms".equals(gridID)
                || "dashboard_facultyInteractionForms".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardStaffInteractionFormsHql());

            UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

            if (userLoggedIn.isPortalStaff())
            {
                query.setVirtualDataCallback(
                        new DashboardStaffInteractionFormsVirtualData());
            }
            else if (userLoggedIn.isStudent())
            {
                query.setVirtualDataCallback(
                        new DashboardStudentInteractionFormsVirtualData());
            }
            else if (userLoggedIn.isEmployer())
            {
                query.setVirtualDataCallback(
                        new DashboardEmployerInteractionFormsVirtualData());
            }
            else if (userLoggedIn.isStaffFaculty())
            {
                query.setVirtualDataCallback(
                        new DashboardStaffFacultyInteractionFormsVirtualData());
            }
            else if (userLoggedIn.isPortalUser())
            {
                query.setVirtualDataCallback(
                        new DashboardPortalUserInteractionFormsVirtualData());
            }
        }
        else if ("dashboard_staffInteractionEngagements".equals(gridID)
                || "dashboard_studentInteractionEngagements".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardStaffInteractionEngagementsHql());
            query.setCustomFilters(DashboardHelper
                    .getDashboardWidgetSubCategoryFilters("Engagement", request));
            query.setVirtualDataCallback(
                    new DashboardStaffInteractionEngagementsVirtualData());
        }
        else if ("dashboard_inMemoryOCCJobs".equals(gridID))
        {
            query.setVirtualGrid(true);
            // Mock clause not to load any results
            query.setFromAndWhereClause("FROM DashboardModule e WHERE (1 != true)");
            query.setVirtualDataCallback(new DashboardInMemoryOccJobsVirtualData());
        }
        else if ("advancedSearch_savedSearches".equals(gridID))
        {
            query.setGridHqlCallback(new SavedSearchesHql());
            query.setVirtualDataCallback(new SavedSearchesVirtualData());
        }
        else if ("integration_reports".equals(gridID))
        {
            query.setGridHqlCallback(new IntegrationReportsHql());
        }
        else if ("ep_postings".equals(gridID))
        {
            query.setGridHqlCallback(new EPPostingHql());
        }
        else if ("integration_processEvents".equals(gridID))
        {
            query.setGridHqlCallback(new IntegrationProcessEventsHql());
        }
        else if ("interview_mockInterviews".equals(gridID))
        {
            query.setGridHqlCallback(new MockInterviewsHql());
            query.setVirtualDataCallback(new MockInterviewsVirtualData());
        }
        else if ("interview_interviews_default".equals(gridID)
                || "interview_interviews_jobs".equals(gridID)
                || "interview_interviews_days".equals(gridID)
                || "interview_interviews_interviewees".equals(gridID))
        {
            query.setGridHqlCallback(new InterviewsGridHqlCallback());

            if ("interview_interviews_default".equals(gridID))
            {
                query.setVirtualDataCallback(
                        new InterviewsGridSchedulesVirtualData());
                query.setGridDataFormatter(new InterviewsDefaultGridDataFormatter());
                query.setPrimaryId("i.id");
                query.setUseDistinctKeyword(true);
            }
            else if ("interview_interviews_jobs".equals(gridID))
            {
                query.setVirtualDataCallback(new InterviewsGridJobsVirtualData());
                query.setPrimaryId("ij.id");
            }
            else if ("interview_interviews_days".equals(gridID))
            {
                query.setVirtualDataCallback(new InterviewsGridDaysVirtualData());
                query.setPrimaryId("iday.id");
            }
            else if ("interview_interviews_interviewees".equals(gridID))
            {
                query.setVirtualDataCallback(
                        new InterviewsGridIntervieweesVirtualData());
                query.setPrimaryId("ie.id");
            }
        }
        else if ("interview_expInterests".equals(gridID))
        {
            query.setGridHqlCallback(new InterviewExpInterestsHQL());
            query.setVirtualDataCallback(new InterviewExpInterestsVirtualData());
        }
        else if ("interview_expApplicants".equals(gridID))
        {
            query.setGridHqlCallback(new InterviewExpApplicantsHQL());
            query.setVirtualDataCallback(new InterviewExpApplicantsVirtualData());
        }
        else if ("interview_cleanupInformation".equals(gridID))
        {
            query.setGridHqlCallback(new InterviewCleanupHql());
            query.setVirtualDataCallback(new InterviewCleanupVirtualCallback());
        }
        else if ("interview_cleanupInformationInterviewee".equals(gridID))
        {
            query.setGridHqlCallback(new InterviewCleanupIntervieweeHql());
        }
        else if ("interview_studentMockInterviews".equals(gridID))
        {
            query.setGridHqlCallback(new InterviewStudentMockInterviewsHql());
            query.setVirtualDataCallback(
                    new InterviewStudentMockInterviewsVirtualData());
        }
        else if ("na_jobApplications_staff".equals(gridID))
        {
            query.setGridHqlCallback(new NAStaffJobApplicationsHql());
            query.setVirtualDataCallback(new NAStaffJobApplicationsVirtualData());
            NAHelper.setStudentAppTagsCustomExportFormatters(query,
                    PortalUtils.getLocale(request));

        }
        else if ("na_adminHomeOverviewTab".equals(gridID))
        {
            List<GridFilter> customFilters = new ArrayList<>();
            customFilters.add(new GridFilter("next5days",
                    new I18nLabel("i18n.GridQueryFactory.Next5Days5716085351556560")
                            .getTranslation(locale),
                    "custom", null, Collections.EMPTY_LIST, Collections.EMPTY_MAP));
            customFilters.add(new GridFilter("last5days",
                    new I18nLabel("i18n.GridQueryFactory.Last5Days5437230144121427")
                            .getTranslation(locale),
                    "custom", null, Collections.EMPTY_LIST, Collections.EMPTY_MAP));
            query.setCustomFilters(customFilters);

            query.setGridHqlCallback(new NApplicationAdminOverviewTabHql());
            query.setVirtualDataCallback(
                    new NApplicationAdminDashboadVirtualData());
        }
        else if ("shared_employer_JobsGrid".equals(gridID))
        {
            query.setVirtualDataCallback(new SharedEmployerJobsGridVirtualData());
            query.setGridHqlCallback(new SharedEmployerJobsGridHql());
            query.setCustomFilters(
                    NHelper.getSharedEmployerJobsGridGridFilters(request));
        }
        else if ("na_jobApplications_employer".equals(gridID))
        {
            query.setVirtualDataCallback(
                    new NAEmployerJobApplicationsVirtualData());
            query.setGridHqlCallback(new NAEmployerJobApplicationsHql());
            NAHelper.setStudentAppTagsCustomExportFormatters(query,
                    PortalUtils.getLocale(request));

            if (!StringUtils
                    .getBooleanValue(request.getParameter("isCreateAppBundle")))
            {
                Boolean hasSubPosting = (Boolean) request
                        .getAttribute("hasSubPosting");
                query.setCustomFilters(NAEmployerJobApplicationsHql
                        .getEmployerApplicationGridFilters(
                                hasSubPosting != null && hasSubPosting == true,
                                locale));
            }
        }
        else if ("na_studentApplicationGrid".equals(gridID))
        {
            query.setGridHqlCallback(new NAStudentApplicationHql());
            query.setVirtualDataCallback(new NAStudentApplicationVirtualData());
        }
        else if ("na_searchPostingsResult".equals(gridID))
        {
            query.setGridHqlCallback(new NASearchPostingsResultHql());
        }
        else if ("coop_coopWtrEmployer".equals(gridID))
        {
            query.setVirtualDataCallback(new CoopWtrEmployerVitualData());
            query.setGridHqlCallback(new CoopWtrEmployerHql());
        }
        else if ("coop_termRecordOffersRanking".equals(gridID))
        {
            query.setVirtualDataCallback(
                    new CoopTermRecordOffersRankingVirtualData());
            query.setGridHqlCallback(new CoopTermRecordOffersRankingHql());
            query.setUseDistinctKeyword(true);
        }
        else if ("coop_programEdit".equals(gridID))
        {
            query.setGridHqlCallback(new CoopProgramEditHql());
        }
        else if ("coop_termEditPrograms".equals(gridID))
        {
            query.setGridHqlCallback(new CoopTermEditProgramsHql());
        }
        else if ("coop_adminAdmissions".equals(gridID))
        {
            query.setGridHqlCallback(new CoopAdminAdmissionsHql());
            query.setVirtualDataCallback(new CoopAdminAdmissionsVirtualData());
        }
        else if ("dashboard_staffAllInteractions".equals(gridID))
        {
            query.setVirtualDataCallback(
                    new DashboardStaffInteractionsVirtualCallback());
            query.setGridHqlCallback(new DashboardStaffInteractionsHql());
        }
        else if ("ccrm_allOrgDivisions".equals(gridID))
        {
            query.setVirtualDataCallback(new AcrmAllOrgDivisionsVirtualData());
            query.setGridHqlCallback(new AcrmAllOrgDivisionsHql());
        }
        else if ("ccrm_allOrgDivContacts".equals(gridID))
        {
            query.setVirtualDataCallback(new AcrmAllOrgDivContactsVirtualData());
            query.setGridHqlCallback(new AcrmAllOrgDivContactsHql());
        }
        else if ("ccrm_allOrgDivJobs".equals(gridID))
        {
            query.setVirtualDataCallback(new AcrmAllOrgDivJobsVirtualData());
            query.setGridHqlCallback(new AcrmAllOrgDivJobsHql());
        }
        else if ("ccrm_allOrgDivWtrs".equals(gridID))
        {
            query.setVirtualDataCallback(new AcrmAllOrgDivWtrsVirtualData());
            query.setGridHqlCallback(new AcrmAllOrgDivWtrsHql());
        }
        else if ("ccrm_appointmentsGrid".equals(gridID))
        {
            query.setGridHqlCallback(new CcrmAppointmentsHql());
        }
        else if ("ccrm_pendingRegistrations".equals(gridID))
        {
            query.setVirtualDataCallback(new AcrmPendingRegistrationsVirtualData());
            query.setGridHqlCallback(new AcrmPendingRegistrationsHql());
        }
        else if ("ccrm_pendingRegistrationPostings".equals(gridID))
        {
            query.setGridHqlCallback(new pendingRegistrationPostingsHql());
        }
        else if ("ccrm_pendingRegistrationInfoSessions".equals(gridID))
        {
            query.setGridHqlCallback(new PendingRegistrationInfoSessionsHql());
        }
        else if ("ccrm_studentInterviews".equals(gridID))
        {
            query.setVirtualDataCallback(new CcrmStudentInterviewsVirtualData());
            query.setGridHqlCallback(new CcrmStudentInterviewsHql());
        }
        else if ("ccrm_workTerms".equals(gridID))
        {
            query.setGridHqlCallback(new CcrmWorkTermsHql());
            query.setVirtualDataCallback(new CcrmWorkTermsVirtualData());
        }
        else if ("ccrm_loginDetails".equals(gridID))
        {
            query.setGridHqlCallback(new AcrmLoginDetailsHql());
        }
        else if ("ccrm_industryPartnerEXPPostings".equals(gridID))
        {
            query.setGridHqlCallback(new AcrmIndustryPartnerEXPPostingsHql());
        }
        else if ("ccrm_industryPartnerEXPRecords".equals(gridID))
        {
            query.setGridHqlCallback(new AcrmIndustryPartnerEXPRecordsHql());
            query.setVirtualDataCallback(
                    new AcrmIndustryPartnerEXPRecordsVirtualData());
        }
        else if ("ccrm_studentEXPRecords".equals(gridID))
        {
            query.setGridHqlCallback(new AcrmStudentEXPRecordsHql());
            query.setVirtualDataCallback(new AcrmStudentEXPRecordsVirtualData());
        }
        else if ("placement_unfilledPositions".equals(gridID))
        {
            query.setVirtualDataCallback(
                    new PlacementUnfilledPositionsVirtualData());
            query.setGridHqlCallback(new PlacementUnfilledPositionsHql());
            query.setUseDistinctKeyword(true);
        }
        else if ("placement_adminHomeAuditTabRanking".equals(gridID))
        {
            query.setGridHqlCallback(new PlacementAdminHomeAuditRankingHql());
        }
        else if ("placement_round_rankings_offer".equals(gridID))
        {
            query.setVirtualDataCallback(
                    new PlacementOfferRoundRankingVirtualData());
            query.setGridHqlCallback(new PlacementRoundRankingHql());
            query.setUseDistinctKeyword(true);
        }
        else if ("placement_round_rankings_preRun".equals(gridID))
        {
            query.setVirtualDataCallback(new PlacementRoundRankingVirtualData());
            query.setGridHqlCallback(new PlacementRoundRankingHql());
            query.setUseDistinctKeyword(true);
        }
        else if ("placement_round_rankings_postRun".equals(gridID))
        {
            query.setVirtualDataCallback(new PlacementRoundRankingVirtualData());
            query.setGridHqlCallback(new PlacementRoundRankingHql());
            query.setUseDistinctKeyword(true);
        }
        else if ("placement_term_allRankings".equals(gridID))
        {
            query.setVirtualDataCallback(new PlacementTermRankingVirtualData());
            query.setGridHqlCallback(new PlacementTermRankingHql());
            query.setUseDistinctKeyword(true);
        }

        else if ("placement_term_rankingNoInterviews".equals(gridID))
        {
            query.setVirtualDataCallback(
                    new PlacementTermRankingNoInterviewsVirtualData());
            query.setGridHqlCallback(new PlacementTermRankingNoInterviewsHql());
        }
        else if ("placement_rankingRound_studentsIn".equals(gridID))
        {
            query.setVirtualDataCallback(new PlacementRoundStudentsInVirtualData());
            query.setGridHqlCallback(new PlacementRoundStudentsInHql());
            query.setUseDistinctKeyword(true);
        }
        else if ("placement_rankingRound_studentsInAfterRun".equals(gridID))
        {
            query.setVirtualDataCallback(
                    new PlacementRoundStudentsInAfterRunVirtualData());
            query.setGridHqlCallback(new PlacementRoundStudentsInAfterRunHql());
            query.setUseDistinctKeyword(true);
        }
        else if ("placement_rankingTerm_allStudents".equals(gridID)
                || "placement_rankingTerm_allStudentsNotRanked".equals(gridID))
        {
            query.setVirtualDataCallback(new PlacementTermAllStudentsVirtualData());
            query.setGridHqlCallback(new PlacementTermAllStudentsHql());
            query.setUseDistinctKeyword(true);
        }
        else if ("placement_rankingRound_jobs".equals(gridID))
        {
            query.setVirtualDataCallback(
                    new PlacementRoundProcessedJobsVirtualData());
            query.setGridHqlCallback(new PlacementRoundProcessedJobsHql());
            query.setUseDistinctKeyword(true);
        }
        else if ("placement_job".equals(gridID))
        {
            query.setVirtualDataCallback(new PlacementJobVirtualData());
            query.setGridHqlCallback(new PlacementJobHql());
            query.setUseDistinctKeyword(true);
        }
        else if ("placement_jobRanking".equals(gridID))
        {
            query.setGridHqlCallback(new PlacementJobRankingHql());
            query.setVirtualDataCallback(new PlacementJobRankingVirtualData());
        }
        else if ("placement_rankingTerm_allJobs".equals(gridID))
        {
            query.setVirtualDataCallback(new PlacementTermAllJobsVirtualData());
            query.setGridHqlCallback(new PlacementTermAllJobsHql());
            query.setUseDistinctKeyword(true);
        }
        else if ("placement_allStudentRankingData".equals(gridID))
        {
            query.setVirtualDataCallback(
                    new PlacementAllStudentRankingDataVirtualData());
            query.setGridHqlCallback(new PlacementAllStudentRankingDataHql());
        }
        else if ("placement_allEmployerRankingData".equals(gridID))
        {
            query.setVirtualDataCallback(
                    new PlacementAllEmployerRankingDataVirtualData());
            query.setGridHqlCallback(new PlacementAllEmployerRankingDataHql());
        }
        else if ("placement_employerHomeRankAndMatch".equals(gridID))
        {
            query.setVirtualDataCallback(
                    new PlacementEmployerHomeRankAndMatchVirtualData());
            query.setGridHqlCallback(new PlacementEmployerHomeRankAndMatchHql());
            query.setUseDistinctKeyword(true);
        }
        else if ("interaction_summary".equals(gridID))
        {
            query.setGridHqlCallback(new InteractionSummaryGridHql());
            query.setVirtualDataCallback(
                    new InteractionSummaryGridVirtualCallback());

            if (InteractionHelper.getInteractionModule().isSystemNotesVisible())
            {
                query.setCustomFilters(
                        InteractionSummaryGridHql.getCustomFilters(request));
                query.getMv().addObject("customFilters",
                        InteractionSummaryGridHql.getCustomFilters(request));
            }
        }
        else if ("interaction_notes_common".equals(gridID)
                || "interaction_notes_wtr_competency".equals(gridID)
                || "interaction_tasks_common".equals(gridID)
                || "interaction_messages_common".equals(gridID)
                || "interaction_engagements_common".equals(gridID)
                || "interaction_forms_common".equals(gridID))
        {
            query.setGridHqlCallback(new InteractionsHql());

            if ("interaction_notes_common".equals(gridID))
            {
                query.setVirtualDataCallback(new InteractionNotesVirtualData());
                query.setCustomFilters(DashboardStaffInteractionNotesHql
                        .getCustomFilters(request));
            }
            else if ("interaction_notes_wtr_competency".equals(gridID))
            {
                query.setVirtualDataCallback(
                        new InteractionWTRCompetencyNotesVirtualData());
            }
            else if ("interaction_tasks_common".equals(gridID))
            {
                query.setVirtualDataCallback(
                        new DashboardStaffInteractionTasksVirtualData());
            }
            else if ("interaction_messages_common".equals(gridID))
            {
                query.setVirtualDataCallback(new InteractionMessagesVirtualData());
            }
            else if ("interaction_forms_common".equals(gridID))
            {
                query.setVirtualDataCallback(new InteractionFormsVirtualData());
            }
            else if ("interaction_engagements_common".equals(gridID))
            {
                query.setVirtualDataCallback(
                        new InteractionEngagementVirtualData());
            }
        }
        else if ("interview_roomUtilization".equals(gridID))
        {
            query.setGridHqlCallback(new InterviewRoomUtilizationHql());
            query.setVirtualDataCallback(
                    new InterviewRoomUtilizationVitualCallback());
        }
        else if ("np_postingQualifiedStudents".equals(gridID))
        {
            query.setGridHqlCallback(new NPostingQualifiedStudentsHql());
            query.setVirtualDataCallback(
                    new NPostingQualifiedStudentsVirtualData());
        }
        else if ("np_postingSavedSearch".equals(gridID))
        {
            query.setGridHqlCallback(new NPostingSavedSearchHql());
            query.setVirtualDataCallback(new NPostingSavedSearchVirtualData());
        }
        else if ("coop_termRecordJobApplications_cas".equals(gridID))
        {
            query.setGridHqlCallback(new CoopTermRecordJobApplicationHql());
            query.setVirtualDataCallback(new CoopTermJobApplicationVirtualData());
        }
        else if ("np_postingsGridForAdmins".equals(gridID))
        {
            PostingsGridForAdmins.populateGridQuery(query);
        }
        else if ("np_employerPostings".equals(gridID))
        {
            query.setGridHqlCallback(new NPostingEmployerPostingsHql());
            query.setVirtualDataCallback(new NPostingEmployerPostingsVirtualData());
        }
        else if ("np_postingStatisticsTab".equals(gridID))
        {
            query.setGridHqlCallback(new NPostingStatisticsTabHql());
        }
        else if ("np_postingEmploymentRecords".equals(gridID))
        {
            query.setGridHqlCallback(new NPostingPostingEmploymentRecordsHql());
        }
        else if ("np_employmentRecords".equals(gridID))
        {
            query.setGridHqlCallback(new NPostingEmploymentRecordsHql());
        }
        else if ("np_employmentOffers".equals(gridID))
        {
            query.setGridHqlCallback(new NPostingEmploymentOffersHql());
        }
        else if ("ccrm_sentEmails".equals(gridID))
        {
            query.setGridHqlCallback(new CcrmSentEmailsHql());
        }
        else if ("ccrm_studentSavedSearch".equals(gridID))
        {
            query.setGridHqlCallback(new CcrmStudentSavedSearchHql());
            query.setVirtualDataCallback(new CcrmStudentSavedSearchVirtualData());
        }
        else if ("cr_students".equals(gridID))
        {
            query.setGridHqlCallback(new CrStudentsHql());
            query.setVirtualDataCallback(new CrStudentsVirtualData());
        }
        else if ("sa_slotDetails_eventHistory".equals(gridID))
        {
            query.setGridHqlCallback(new SASlotDetailsEventHistoryHql());
            query.setVirtualDataCallback(
                    new SASlotDetailsEventHistoryVirtualData());
        }
        else if ("disruptive_manageDisruptiveItems".equals(gridID))
        {
            query.setGridHqlCallback(new DisruptiveHqlCallback());
            query.setVirtualDataCallback(new DisruptiveVirtualCallback());
        }
        else if ("disruptive_disruptiveItemResponses".equals(gridID))
        {
            query.setGridHqlCallback(new DisruptiveItemResponsesHqlCallback());
            // query.setVirtualDataCallback(new DisruptiveVirtualCallback());
        }
        else if ("interaction_teamMemberEntity_organizations".equals(gridID)
                || "interaction_teamMemberEntity_divisions".equals(gridID)
                || "interaction_teamMemberEntity_users".equals(gridID)
                || "interaction_teamMemberEntity_postings".equals(gridID)
                || "interaction_teamMemberEntity_coopRecords".equals(gridID)
                || "interaction_teamMemberEntity_wtrs".equals(gridID))
        {
            InteractionTeamMemberGrid.populateGridQuery(query);
        }
        else if ("portal_logHistory".equals(gridID))
        {
            query.setGridHqlCallback(new PortalLogHistoryHql());
            query.setVirtualDataCallback(new PortalLogHistoryVirtualCallback());
        }
        else if ("portalTools_emailLogs".equals(gridID))
        {
            query.setGridHqlCallback(new PortalToolsEmailLogsHql());
        }
        else if ("portalTools_portalLog".equals(gridID))
        {
            query.setGridHqlCallback(new PortalToolsPortalLogHql());
        }
        else if ("portalTools_failedLogins".equals(gridID))
        {
            query.setGridHqlCallback(new PortalToolsFailedLoginsHql());
        }
        else if ("portalTools_nonAssignablePermissionUsers".equals(gridID))
        {
            query.setFromAndWhereClause(null);
            query.setPrimaryId("u.USER_DETAILS_ID");
            query.setSql(true);
            query.setSqlQuery(PortalToolsHelper.getNonAssignablePermissionUsersSQL(
                    ServletRequestUtils.getIntParameter(request, "pId", 0)));
            query.setSqlRowMapper(new DBUtils.ObjectArrayRowMapper());
        }
        else if ("portalTools_permissionClusterUsers".equals(gridID))
        {
            query.setFromAndWhereClause(null);
            query.setPrimaryId("u.USER_DETAILS_ID");
            query.setSql(true);
            try
            {
                query.setSqlQuery(
                        PortalToolsHelper.getPermissionClusterUsersSQL(Arrays
                                .stream(JSONUtils.toIntArray(new JSONArray(
                                        request.getParameter("pIds"))))
                                .boxed().collect(Collectors.toList())));
            }
            catch (JSONException e)
            {
                e.printStackTrace();
            }
            query.setSqlRowMapper(new DBUtils.ObjectArrayRowMapper());
        }
        else if ("np_postingHistory".equals(gridID))
        {
            query.setGridHqlCallback(new NPostingHistoryHql());
            query.setVirtualDataCallback(new NPostingHistoryVirtualData());
        }
        else if ("exp_recordEmails".equals(gridID))
        {
            query.setGridHqlCallback(new EXPRecordEmailsHql());
        }
        else if ("exp_expTypes".equals(gridID))
        {
            query.setGridHqlCallback(new EXPExpTypesHql());
        }
        else if ("exp_pendingPostings".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPendingPostingsHql());
            query.setVirtualDataCallback(new EXPPendingPostingsVirtualData());
        }
        else if ("exp_pendingQuestionnaires".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPendingQuestionnairesHql());
            query.setVirtualDataCallback(new EXPPendingQuestionnairesVirtualData());
        }
        else if ("exp_pendingRecords".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPendingRecordsHql());
        }
        else if ("exp_pendingReflections".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPendingReflectionsHql());
        }
        else if ("exp_pendingSubRecords".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPendingSubRecordsHql());
        }
        else if ("exp_availablePostingsForUnplacedStudents".equals(gridID))
        {
            query.setGridHqlCallback(
                    new EXPAvailablePostingsForUnplacedStudentsHQL());
            query.setPrimaryId("compositeKey");
            query.setVirtualDataCallback(
                    new EXPAvailablePostingsForUnplacedStudentsVirtualData());
        }
        else if ("exp_termExpTypeStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPTermExpTypeStudentsHQL());
            query.setVirtualDataCallback(new EXPTermExpTypeStudentsVirtualData());
        }
        else if ("exp_completedStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPCompletedStudentsSQL());
            query.setSql(true);
            query.setSqlRowMapper(new DBUtils.ObjectArrayRowMapper());
        }
        else if ("exp_unplacedStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPUnplacedStudentsHQL());
            query.setVirtualDataCallback(new EXPUnplacedStudentsVirtualData());
        }
        else if ("exp_placedStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPlacedStudentsHQL());
            query.setVirtualDataCallback(new EXPPlacedStudentsVirtualData());
        }
        else if ("exp_studentApplicationGrid".equals(gridID))
        {
            query.setGridHqlCallback(new EXPStudentApplicationHql());
        }
        else if ("exp_termCourseTypeNoTeamStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPTermCourseTypeNoTeamStudentsHql());
        }
        else if ("exp_termCourseTypeTeams".equals(gridID))
        {
            query.setGridHqlCallback(new EXPTermCourseTypeTeamsHql());
            query.setVirtualDataCallback(new EXPTermCourseTypeTeamsVirtualData());
        }
        else if ("exp_termCourseTypeStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPTermCourseTypeStudentsHql());
            query.setVirtualDataCallback(
                    new EXPTermCourseTypeStudentsVirtualData());
        }
        else if ("exp_termCourseTypeStudentProgress".equals(gridID))
        {
            query.setGridHqlCallback(new EXPTermCourseTypeStudentProgressHql());
            query.setVirtualDataCallback(
                    new EXPTermCourseTypeStudentProgressVirtualData());
        }
        else if ("exp_statuses".equals(gridID))
        {
            query.setGridHqlCallback(new EXPStatusesSql());
            query.setSql(true);
            query.setSqlRowMapper(new DBUtils.ObjectArrayRowMapper());
        }
        else if ("exp_adminPostingPlacedStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPAdminPostingPlacedStudentsHql());
            query.setVirtualDataCallback(new EXPPostingPlacedStudentsVirtualData());
        }
        else if ("exp_posterPostingPlacedStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPosterPostingPlacedStudentsHql());
            query.setVirtualDataCallback(new EXPPostingPlacedStudentsVirtualData());
        }
        else if ("exp_courseTerms".equals(gridID))
        {
            query.setGridHqlCallback(new EXPCourseTermsHql());
            query.setVirtualDataCallback(new EXPCourseTermsVirtualData());
        }
        else if ("exp_postingViews".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPostingViewsHql());
            query.setVirtualDataCallback(new EXPPostingViewsVirtualData());
        }
        else if ("exp_pendingExpProfiles".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPendingExpProfilesHql());
        }
        else if ("exp_pendingExpTypeProfiles".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPendingExpTypeProfilesHql());
        }
        else if ("exp_courses".equals(gridID))
        {
            query.setGridHqlCallback(new EXPCoursesHql());
        }
        else if ("exp_homeActiveCourses".equals(gridID))
        {
            query.setGridHqlCallback(new EXPHomeActiveCoursesHql());
            query.setVirtualDataCallback(new EXPHomeActiveCoursesVirtualData());
            query.setGridDataFormatter(new EXPHomeActiveCoursesGridDataFormatter());
        }
        else if ("exp_homeActiveStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPHomeActiveStudentsHql());
        }
        else if ("exp_homeActivePostings".equals(gridID))
        {
            query.setGridHqlCallback(new EXPHomeActivePostingsHql());
            query.setVirtualDataCallback(new EXPHomeActivePostingsVirtualData());
            query.setGridDataFormatter(
                    new EXPHomeActivePostingsGridDataFormatter());
        }
        else if ("exp_termCourseActivePostings".equals(gridID))
        {
            query.setGridHqlCallback(new EXPTermCourseActivePostingsHql());
            List<GridFilter> customFilters = new ArrayList<>();
            customFilters.add(EXPPostingHelper.getNotFilledPostingFilter(locale));
            customFilters.add(EXPPostingHelper.getPartFilledPostingFilter(locale));
            customFilters.add(EXPPostingHelper.getFilledPostingFilter(locale));
            query.setCustomFilters(customFilters);
        }
        else if ("exp_postings".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPostingsHql());
            query.setVirtualDataCallback(new EXPPostingsVirtualData());
        }
        else if ("exp_expTypeTermCourses".equals(gridID))
        {
            query.setGridHqlCallback(new EXPExpTypeTermCoursesHql());
            query.setVirtualDataCallback(new EXPExpTypeTermCoursesVirtualData());
        }
        else if ("exp_expTypeRecords".equals(gridID))
        {
            query.setGridHqlCallback(new EXPExpTypeRecordsHql());
            query.setVirtualDataCallback(new EXPExpTypeRecordsVirtualData());
        }
        else if ("exp_termCourseStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPTermCourseStudentsHql());
            query.setVirtualDataCallback(new EXPTermCourseStudentsVirtualData());
        }
        else if ("exp_termCourseStepProgress".equals(gridID))
        {
            query.setGridHqlCallback(new EXPTermCourseStepProgressHql());
            query.setVirtualDataCallback(
                    new EXPTermCourseStepProgressVirtualData());
        }
        else if ("pc_pcProjectRecords".equals(gridID))
        {
            query.setGridHqlCallback(new PCProjectRecordsHQL());
        }
        else if ("pc_projectConnectionsAdmin".equals(gridID))
        {
            query.setGridHqlCallback(new PCProjectConnectionsAdminHQL());
            query.setVirtualDataCallback(
                    new PCProjectConnectionsAdminVirtualData());
        }
        else if ("pc_projectConnectionsFacilitator".equals(gridID))
        {
            query.setGridHqlCallback(new PCProjectConnectionsFacilitatorHQL());
        }
        else if ("pc_facilitators".equals(gridID))
        {
            query.setGridHqlCallback(new PCFacilitatorHQL());
            query.setVirtualDataCallback(new PCFacilitatorVirtualData());
        }
        else if ("pc_facilitatorProjects".equals(gridID))
        {
            query.setGridHqlCallback(new PCFacilitatorProjectsHQL());
            query.setVirtualDataCallback(new PCFacilitatorProjectsVirtualData());
        }
        else if ("exp_expTypePostings".equals(gridID))
        {
            query.setGridHqlCallback(new EXPExpTypePostingsHql());
            query.setVirtualDataCallback(new EXPExpTypePostingsVirtualData());
        }
        else if ("exp_studentAvailableProgramsCourses".equals(gridID))
        {
            query.setGridHqlCallback(new EXPStudentAvailableProgramsCoursesHql());
        }
        else if ("exp_termCourseTypeRankings".equals(gridID))
        {
            query.setGridHqlCallback(new EXPTermCourseTypeRankingsHQL());
            query.setVirtualDataCallback(
                    new EXPTermCourseTypeRankingsVirtualData());
        }
        else if ("exp_termCourseRecords".equals(gridID))
        {
            query.setGridHqlCallback(new EXPTermCourseRecordsHql());
            query.setVirtualDataCallback(new EXPTermCourseRecordsVirtualData());
            if (RequestUtils.getBooleanParameter(request, "fromDataViewer", false))
            {
                List<GridFilter> customFilters = new ArrayList<>();
                customFilters.add(new GridFilter("activeStudents",
                        PortalUtils.getI18nMessage(
                                "i18n.EXPTermCourseTypeHelper.ActiveStud5233754163555769",
                                locale),
                        "custom", null, Collections.EMPTY_LIST,
                        Collections.EMPTY_MAP));
                customFilters.add(new GridFilter("inactiveWithApprovedRecord",
                        PortalUtils.getI18nMessage(
                                "i18n.EXPTermCourseTypeHelper.InactiveSt1396626651412532",
                                locale),
                        "custom", null, Collections.EMPTY_LIST,
                        Collections.EMPTY_MAP));
                customFilters.add(new GridFilter("inactiveWithPendingRecord",
                        PortalUtils.getI18nMessage(
                                "i18n.EXPTermCourseTypeHelper.InactiveSt9647108392441729",
                                locale),
                        "custom", null, Collections.EMPTY_LIST,
                        Collections.EMPTY_MAP));
                query.setCustomFilters(customFilters);
            }
        }
        else if ("exp_jobApplicationsDownload".equals(gridID))
        {
            query.setGridHqlCallback(new EXPJobApplicationsDownloadHql());
            query.setVirtualDataCallback(
                    new EXPJobApplicationsDownloadVirtualData());
        }
        else if ("exp_placeNoRecordsStudentsPostingsGrid".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPlaceNoRecordStudentsPostingsHQL());
            query.setVirtualDataCallback(
                    new EXPPlaceNoRecordStudentsPostingsVirtualData());
        }
        else if ("exp_postingPlaceStudentsGrid".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPostingPlaceStudentsHQL());
            query.setVirtualDataCallback(new EXPPostingPlaceStudentsVirtualData());
        }
        else if ("exp_industryPartnerPlaceStudentsGrid".equals(gridID))
        {
            query.setGridHqlCallback(new EXPIndustryPartnerPlaceStudentsHQL());
            query.setVirtualDataCallback(
                    new EXPIndustryPartnerPlaceStudentsVirtualData());
        }
        else if ("exp_placeNoRecordsStudentsIndustryPartnersGrid".equals(gridID))
        {
            query.setGridHqlCallback(
                    new EXPPlaceNoRecordStudentsIndustryPartnersHQL());
            query.setVirtualDataCallback(
                    new EXPPlaceNoRecordStudentsIndustryPartnersVirtualData());
        }
        else if ("exp_placeNoRecordsStudentsIndustryPartnersOrgsGrid"
                .equals(gridID))
        {
            query.setGridHqlCallback(
                    new EXPPlaceNoRecordStudentsIndustryPartnersOrgsHQL());
            query.setVirtualDataCallback(
                    new EXPPlaceNoRecordStudentsIndustryPartnersOrgsVirtualData());
        }
        else if ("exp_postingAppliedStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPostingAppliedStudentsHQL());
            query.setVirtualDataCallback(
                    new EXPPostingAppliedStudentsVirtualData());
        }
        else if ("exp_preScreeningResponses".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPostingPreScreeningResponsesHQL());
            query.setVirtualDataCallback(new EXPPreScreeningResponsesVirtualData());

        }
        else if ("exp_applications".equals(gridID))
        {
            query.setGridHqlCallback(new EXPApplicationsHQL());
        }
        else if ("exp_interests".equals(gridID))
        {
            query.setGridHqlCallback(new EXPInterestsHQL());
        }
        else if ("exp_facultyExperiences".equals(gridID))
        {
            query.setGridHqlCallback(new EXPFacultyExperiencesHQL());
        }
        else if ("exp_facultyTermCourses".equals(gridID))
        {
            query.setGridHqlCallback(new EXPFacultyTermCoursesHQL());
        }
        else if ("exp_adminPostingInterestedStudents".equals(gridID)
                || "exp_posterPostingInterestedStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPostingInterestedStudentsHQL());
            query.setVirtualDataCallback(
                    new EXPPostingInterestedStudentsVirtualData());
        }
        else if ("exp_facultyMemberQuestsToReview".equals(gridID))
        {
            query.setGridHqlCallback(new EXPFacultyMemberQuestsToReviewHql());
        }
        else if ("exp_facultyMemberAdminWaits".equals(gridID))
        {
            query.setGridHqlCallback(new EXPFacultyMemberAdminWaitsHql());
        }
        else if ("exp_facultyMemberRecordsToApprove".equals(gridID))
        {
            query.setGridHqlCallback(new EXPFacultyMemberRecordsToApproveHql());
        }
        else if ("exp_facultyMemberSubRecordsToApprove".equals(gridID))
        {
            query.setGridHqlCallback(new EXPFacultyMemberSubRecordsToApproveHql());
        }
        else if ("exp_facultyMemberEvalsToDo".equals(gridID))
        {
            query.setGridHqlCallback(new EXPFacultyMemberEvalsToDoHql());
        }
        else if ("exp_facultyMemberGroupsToForm".equals(gridID))
        {
            query.setGridHqlCallback(new EXPFacultyMemberGroupsToFormHql());
        }
        else if ("exp_facultyMemberPlaceStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPFacultyMemberPlaceStudentsHql());
            query.setVirtualDataCallback(
                    new EXPFacultyMemberPlaceStudentsVirtualData());
        }
        else if ("globalEvents_emailLogs".equals(gridID))
        {
            query.setGridHqlCallback(new GlobalEventsEmailLogsHql());
        }
        else if ("ccrm_externalJobViews".equals(gridID))
        {
            query.setGridHqlCallback(new CcrmExternalJobViewsHql());
        }
        else if ("ec_schedule_students".equals(gridID))
        {
            query.setGridHqlCallback(new ECScheduleStudentsGridHql());
            query.setVirtualDataCallback(new ECScheduleStudentsVirtualData());
        }
        else if ("pt_programParticipants".equals(gridID))
        {
            query.setGridHqlCallback(new PTProgramEnrollmentGridHql());
            query.setVirtualDataCallback(new PTProgramEnrollmentVirtualData());
        }
        else if ("ccrm_doNotEmailList".equals(gridID))
        {
            query.setGridHqlCallback(new CcrmDoNotEmailListHql());
            query.setVirtualDataCallback(new CcrmDoNotEmailListVirtualData());
        }
        else if ("ccrm_usersOptedOutOfSystemEmails".equals(gridID))
        {
            query.setGridHqlCallback(new CcrmUsersOptedOutOfSystemEmailsHql());
        }
        else if ("ccrm_usersOptedOutOfEmailCampaigns".equals(gridID))
        {
            query.setGridHqlCallback(new CcrmUsersOptedOutOfEmailCampaignsHql());
        }
        else if ("res_doubleRoomBookings".equals(gridID))
        {
            query.setGridHqlCallback(new ResDoubleRoomBookingsHql());
            query.setVirtualDataCallback(new ResDoubleRoomBookingsVirtualData());
        }
        else if ("ccrm_events".equals(gridID))
        {
            query.setGridHqlCallback(new CcrmEventsHql());
            query.setVirtualDataCallback(new CcrmEventsVirtualData());
            DashboardHelper.setEventGridExportFormatters(query, locale);
        }
        else if ("email_emailCampaigns".equals(gridID))
        {
            query.setGridHqlCallback(new EmailCampaignHql());
            query.setVirtualDataCallback(new EmailCampaignVirtualData());
        }
        else if ("mentorship2_mentorMenteeEmails".equals(gridID))
        {
            query.setGridHqlCallback(new Mentorship2MentorMenteeEmailsHql());
        }
        else if ("mentorship2_mentorTermsTable".equals(gridID))
        {
            query.setGridHqlCallback(new Mentorship2MentorTermsHql());
            query.setVirtualDataCallback(new Mentorship2MentorTermsVirtualData());
        }
        else if ("mentorship2_activeProfiles".equals(gridID))
        {
            query.setGridHqlCallback(new Mentorship2ActiveProfilesHql());
            query.setVirtualDataCallback(
                    new Mentorship2ActiveProfilesVirtualData());
        }
        else if ("mentorship2_termMentees".equals(gridID))
        {
            query.setGridHqlCallback(new Mentorship2TermMenteesHql());
            query.setVirtualDataCallback(new Mentorship2TermMenteesVirtualData());
        }
        else if ("mentorship2_termMentors".equals(gridID))
        {
            query.setGridHqlCallback(new Mentorship2TermMentorsHql());
            query.setVirtualDataCallback(new Mentorship2TermMentorsVirtualData());
        }
        else if ("mentorship2_termConnections".equals(gridID))
        {
            query.setGridHqlCallback(new Mentorship2TermConnectionsHql());
        }
        else if ("mentorship2_profileActiveConnections".equals(gridID))
        {
            query.setGridHqlCallback(new Mentorship2ProfileActiveConnectionsHql());
        }
        else if ("mentorship2_activeTerms".equals(gridID))
        {
            query.setGridHqlCallback(new Mentorship2ActiveTermsHql());
            query.setVirtualDataCallback(new Mentorship2ActiveTermsVirtualData());
        }
        else if ("mentorship2_activeConnections".equals(gridID))
        {
            query.setGridHqlCallback(new Mentorship2ActiveConnectionsHql());
        }
        else if ("mentorship2_mentorPreviousTerms".equals(gridID))
        {
            query.setGridHqlCallback(new Mentorship2MentorPreviousTermsHql());
            query.setVirtualDataCallback(
                    new Mentorship2MentorPreviousTermsVirtualData());
        }
        else if ("mentorship2_menteePreviousTerms".equals(gridID))
        {
            query.setGridHqlCallback(new Mentorship2MenteePreviousTermsHql());
            query.setVirtualDataCallback(
                    new Mentorship2MenteePreviousTermsVirtualData());
        }
        else if ("mentorship2_profilePreviousTermConnections".equals(gridID))
        {
            query.setGridHqlCallback(
                    new Mentorship2ProfilePreviousTermConnectionsHql());
        }

        else if ("mentorship2_mentorMenteeHourTracking".equals(gridID))
        {
            query.setGridHqlCallback(new Mentorship2MentorMenteeHourTrackingHql());
        }
        else if ("ccrm_programTracking".equals(gridID))
        {
            query.setGridHqlCallback(new CcrmProgramTrackingHql());
            query.setVirtualDataCallback(new CcrmProgramTrackingVirtualData());
        }
        else if ("dashboard_commonMyProgramParticipation".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardProgramParticipationCommonHql());
            query.setVirtualDataCallback(
                    new DashboardProgramParticipationCommonVirtualData());
        }
        else if ("coop_programTrackingView".equals(gridID))
        {
            query.setGridHqlCallback(new CoopProgramTrackingHql());
            query.setVirtualDataCallback(new CoopProgramTrackingVirtualData());
        }
        else if ("ccrm_studentCCR".equals(gridID)
                || "dashboard_studentAlumniCommonMyCCR".equals(gridID))
        {
            query.setGridHqlCallback(new CcrmStudentCCRHql());
        }
        else if ("interaction_messageTemplates_common".equals(gridID))
        {
            query.setGridHqlCallback(new InteractionMessageTemplatesHql());
        }
        else if ("dashboard_staffMyStudentTimeOffs".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardStaffMyStudentTimeOffsHql());
            query.setVirtualDataCallback(
                    new DashboardStaffMyStudentTimeOffsVirtualData());
        }
        else if ("doc_commonTable".equals(gridID))
        {
            query.setGridHqlCallback(new DocCommonGridHQL());
        }
        else if ("interaction_taskAuditLog".equals(gridID))
        {
            query.setGridHqlCallback(new InteractionTaskAuditLogHql());
        }
        else if ("interaction_formAuditLog".equals(gridID))
        {
            query.setGridHqlCallback(new InteractionFormAuditLogHql());
        }
        else if ("insights_manageNAIC".equals(gridID))
        {
            query.setGridHqlCallback(new InsightsManageNAICHql());
        }
        else if ("insights_manageNOC".equals(gridID))
        {
            query.setGridHqlCallback(new InsightsManageNOCHql());
        }
        else if ("sa_slotDetails_auditLog".equals(gridID))
        {
            query.setGridHqlCallback(new SASlotDetailsAuditLogHql());
            query.setVirtualDataCallback(new SASlotDetailsAuditLogVirtualData());
        }
        else if ("ec_userRecordSearch".equals(gridID))
        {
            query.setGridHqlCallback(new ECUserRecordSearchSql());
            query.setVirtualDataCallback(new ECUserRecordSearchVirtualData());
            query.setSql(true);
            query.setSqlRowMapper(new DBUtils.ObjectArrayRowMapper());
        }
        else if ("ec_userCompetencySearch".equals(gridID))
        {
            query.setGridHqlCallback(new ECUserCompetencySearchSql());
            query.setVirtualDataCallback(new ECUserCompetencySearchVirtualData());
            query.setSql(true);
            query.setSqlRowMapper(new DBUtils.ObjectArrayRowMapper());
        }
        else if ("ec_userReflectionSearch".equals(gridID))
        {
            query.setGridHqlCallback(new ECUserReflectionSearchSql());
            query.setSql(true);
            query.setSqlRowMapper(new DBUtils.ObjectArrayRowMapper());
        }
        else if ("rb_resumes".equals(gridID))
        {
            query.setGridHqlCallback(new RbResumesHql());
        }
        else if ("ep_experiences".equals(gridID))
        {
            query.setGridHqlCallback(new EPExperienceGridHqlCallback());
        }
        else if ("exp_unfinishedHourTrackingSteps".equals(gridID))
        {
            query.setGridHqlCallback(
                    new EXPUnfinishedHourTrackingStepsGridHqlCallback());
            query.setVirtualDataCallback(
                    new EXPUnfinishedHourTrackingStepsVirtualData());
        }
        else if ("exp_unfinishedStudentEvaluations".equals(gridID))
        {
            query.setGridHqlCallback(
                    new EXPUnfinishedStudentEvaluationsGridHqlCallback());
            query.setVirtualDataCallback(
                    new EXPUnfinishedStudentEvaluationsVirtualData());
        }
        else if ("exp_unfinishedFacultyEvaluations".equals(gridID))
        {
            query.setGridHqlCallback(
                    new EXPUnfinishedFacultyEvaluationsGridHqlCallback());
            query.setVirtualDataCallback(
                    new EXPUnfinishedFacultyEvaluationsVirtualData());
        }
        else if ("exp_unfinishedIndustryPartnerEvaluations".equals(gridID))
        {
            query.setGridHqlCallback(
                    new EXPUnfinishedIndustryPartnerEvaluationsGridHqlCallback());
            query.setVirtualDataCallback(
                    new EXPUnfinishedIndustryPartnerEvaluationsVirtualData());
        }
        else if ("exp_unfinishedCustomForms".equals(gridID))
        {
            query.setGridHqlCallback(new EXPUnfinishedCustomFormsGridHqlCallback());
            query.setVirtualDataCallback(new EXPUnfinishedCustomFormsVirtualData());
        }
        else if ("exp_expiringPostingTermOpenings".equals(gridID))
        {
            query.setGridHqlCallback(
                    new EXPExpiringPostingTermOpeningsGridHqlCallback());

            query.setVirtualDataCallback(
                    new EXPExpiringPostingTermOpeningsVirtualData());
        }
        else if ("ccrm_userDisruptions".equals(gridID))
        {
            query.setGridHqlCallback(new UserDisruptionHql());
        }
        else if ("exp_pendingRecordsNeedingIndustryPartner".equals(gridID))
        {
            query.setGridHqlCallback(
                    new EXPPendingRecordsNeedingIndustryPartnerHql());
        }
        else if ("exp_pendingEnrolledStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPendingEnrolledStudentsHql());
        }
        else if ("exp_posterPostingAppliedStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPosterPostingAppliedStudentsHql());
            query.setVirtualDataCallback(
                    new EXPPosterPostingAppliedStudentsVirtualData());
        }
        else if ("exp_fieldSupervisorStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPFieldSupervisorStudentsHql());
        }
        else if ("exp_massUpdateTCTStepProgress".equals(gridID))
        {
            query.setGridHqlCallback(new EXPMassUpdateTCTStepProgressHql());
        }
        else if ("exp_termCourseStepStudents".equals(gridID))
        {
            query.setGridHqlCallback(new EXPTermCourseTypeStepStudents());
        }
        else if ("pd_organizationProfileResumes".equals(gridID))
        {
            query.setGridHqlCallback(new PDOrganizationProfileResumesHql());
        }
        else if ("pd_organizationProfileOpportunities".equals(gridID))
        {
            query.setGridHqlCallback(new PDOrganizationProfileOpportunitiesSql());
            query.setSql(true);
            query.setSqlRowMapper(new DBUtils.ObjectArrayRowMapper());
        }
        else if ("pd_pendingOrgProfiles".equals(gridID))
        {
            query.setGridHqlCallback(new PDPendingOrganizationProfilesHql());
        }
        else if ("pd_incompleteOrgProfiles".equals(gridID))
        {
            query.setGridHqlCallback(new PDIncompleteOrganizationProfilesHql());
        }
        else if ("qf_studentQualificationsWaitingForApproval".equals(gridID))
        {
            query.setGridHqlCallback(new QFStudentQualificationsHql());
        }
        else if ("na_jobApplicationsDownload".equals(gridID))
        {
            query.setGridHqlCallback(new NAJobApplicationsDownloadHql());
            query.setVirtualDataCallback(new NApplicationBundleVirtualData());
        }
        else if ("coop_competencyStatsRecords".equals(gridID))
        {
            query.setGridHqlCallback(new CoopCompetencyStatsRecordsHql());
        }
        else if ("exp_pendingApprovalQualifiersSteps".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPendingApprovalQualifiersStepsHQL());
        }
        else if ("exp_pendingWorkLogs".equals(gridID))
        {
            query.setGridHqlCallback(new EXPPendingWorkLogsHQL());
        }
        else if ("dashboard_studentMyTimeOffRequests".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardStudentMyTimeOffRequestsHql());
            query.setVirtualDataCallback(
                    new DashboardStudentMyTimeOffRequestsVirtualData());
        }
        else if ("placement_rankingTermUnfilledAfterMatch".equals(gridID))
        {
            query.setGridHqlCallback(
                    new PlacementRankingTermUnfilledAfterMatchHql());
        }
        else if ("placement_rankingTermSubpostedUnfilledAfterMatch".equals(gridID))
        {
            query.setGridHqlCallback(
                    new PlacementRankingTermUnfilledAfterMatchHql());
            query.setVirtualDataCallback(
                    new PlacementRankingTermSubpostedUnfilledAfterMatchVirtualData());
        }
        else if ("ccrm_prospect_user_notes".equals(gridID))
        {
            query.setGridHqlCallback(new ProspectUserWithNotesHql());
            query.setVirtualDataCallback(new ProspectUserWithNotesVirtualData());
        }
        else if ("ccrm_prospect_with_service_team".equals(gridID))
        {
            query.setGridHqlCallback(new ProspectUserWithServiceTeamHql());
            query.setVirtualDataCallback(
                    new ProspectUserWithServiceTeamVirtualData());
        }
        else if ("ccrm_prospect_with_owner".equals(gridID))
        {
            query.setGridHqlCallback(new ProspectUserCommonHql());
            query.setVirtualDataCallback(new ProspectUserCommonVirtualData());
        }
        else if ("ccrm_prospect_user_organization".equals(gridID))
        {
            query.setGridHqlCallback(new ProspectUserCommonHql());
            query.setVirtualDataCallback(new ProspectUserCommonVirtualData());
        }
        else if ("ccrm_prospect_user_active_last_month".equals(gridID))
        {
            query.setGridHqlCallback(new ConvertedProspectActiveLastMonthHql());
            query.setVirtualDataCallback(
                    new ConvertedProspectActiveLastMonthVirtualData());
        }
        else if ("ccrm_prospect_user_no_postings".equals(gridID))
        {
            query.setGridHqlCallback(new ConvertedProspectNoPostingSql());
            query.setVirtualDataCallback(
                    new ConvertedProspectNoPostingVirtualData());
            query.setSqlRowMapper(new DBUtils.ObjectArrayRowMapper());
            query.setSql(true);
        }
        else if ("exp_studentHomeCurrentExperiences".equals(gridID))
        {
            query.setGridHqlCallback(new EXPStudentHomeCurrentExperiencesSql());
            query.setSqlRowMapper(new DBUtils.ObjectArrayRowMapper());
            query.setSql(true);
            query.setVirtualDataCallback(
                    new EXPStudentHomeCurrentExperiencesVirtualData());
        }
        else if ("exp_studentHomeInterestedOpportunities".equals(gridID))
        {
            query.setGridHqlCallback(
                    new EXPStudentHomeInterestedOpportunitiesHql());
            query.setVirtualDataCallback(
                    new EXPStudentHomeInterestedOpportunitiesVirtualData());
            query.setGridDataFormatter(
                    new EXPStudentHomeInterestedOpportunitiesDataFormatter());
        }
        else if ("exp_studentHomeAppliedOpportunities".equals(gridID))
        {
            query.setGridHqlCallback(new EXPStudentHomeAppliedOpportunitiesHql());
            query.setVirtualDataCallback(
                    new EXPStudentHomeInterestedOpportunitiesVirtualData());
            query.setGridDataFormatter(
                    new EXPStudentHomeInterestedOpportunitiesDataFormatter());
        }
        else if ("exp_studentHomeExpiringOpportunities".equals(gridID))
        {
            query.setGridHqlCallback(new EXPStudentHomeExpiringOpportunitiesHql());
            query.setVirtualDataCallback(
                    new EXPStudentHomeInterestedOpportunitiesVirtualData());
            query.setGridDataFormatter(
                    new EXPStudentHomeInterestedOpportunitiesDataFormatter());
        }
        else if ("exp_studentHomeRecentOpportunities".equals(gridID))
        {
            query.setGridHqlCallback(new EXPStudentHomeRecentOpportunitiesHql());
            query.setVirtualDataCallback(
                    new EXPStudentHomeInterestedOpportunitiesVirtualData());
            query.setGridDataFormatter(
                    new EXPStudentHomeInterestedOpportunitiesDataFormatter());
        }
        else if ("dashboard_employerLinkedEvents".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardEmployerLinkedEventsHql());
            query.setVirtualDataCallback(
                    new DashboardEmployerLinkedEventsVirtualData());
        }
        else if ("dashboard_mySubscriptions".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardMySubscriptionsHql());
            query.setVirtualDataCallback(new DashboardMySubscriptionsVirtualData());
        }
        else if ("dashboard_interactions_logs".equals(gridID))
        {
            query.setGridHqlCallback(new DashboardInteractionsLogs());
        }
        else if ("is_adminRegistrationsTable".equals(gridID))
        {
            query.setGridHqlCallback(new InfoSessionRegistrationTableHql());
        }
        else
        {
            query = null;
        }

        if (query != null)
        {
            query.setTagFilters(
                    GridTagFilterHelper.getTagFiltersForGrid(gridID, request));
            query.setInteractionFilters(GridInteractionFilterHelper
                    .getInteractionFiltersForGrid(gridID));
            query.setDfFilters(
                    GridDfFilterHelper.getDfModelFiltersForGrid(gridID, request));
        }

        return query;
    }
}
