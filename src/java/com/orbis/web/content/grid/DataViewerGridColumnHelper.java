package com.orbis.web.content.grid;

import java.util.Date;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.utils.DateUtils;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.dashboard.grids.DashboardMyProgramParticipationColumnResolver;
import com.orbis.web.content.exp.EXPTermCourseTypeRankingsColumnResolver;
import com.orbis.web.content.grid.dataViewer.DataViewerGridFilter;

public class DataViewerGridColumnHelper
{
    private static final Map<String, DataViewerGridColumnsResolver> columnsResolverByGridID;

    private DataViewerGridColumnHelper()
    {
    }

    static
    {
        columnsResolverByGridID = new HashMap<>();
        columnsResolverByGridID.put("exp_homeActiveCourses",
                new EXPHomeActiveCoursesColumnsResolver());
        columnsResolverByGridID.put("exp_homeActiveStudents",
                new EXPHomeActiveStudentsColumnsResolver());
        columnsResolverByGridID.put("exp_homeActivePostings",
                new EXPHomeActivePostingsColumnsResolver());
        columnsResolverByGridID.put("exp_studentHomeCurrentExperiences",
                new EXPStudentHomeCurrentExperiencesColumnResolver());
        columnsResolverByGridID.put("exp_studentHomeAppliedOpportunities",
                new EXPStudentHomeAppliedOpportunitiesColumnResolver());
        columnsResolverByGridID.put("exp_studentHomeInterestedOpportunities",
                new EXPStudentHomeInterestedOpportunitiesColumnResolver());
        columnsResolverByGridID.put("exp_studentHomeExpiringOpportunities",
                new EXPStudentHomeExpiringOpportunitiesColumnResolver());
        columnsResolverByGridID.put("exp_termCourseTypeRankings",
                new EXPTermCourseTypeRankingsColumnResolver());
        columnsResolverByGridID.put("exp_studentHomeRecentOpportunities",
                new EXPStudentHomeRecentOpportunitiesColumnResolver());
        columnsResolverByGridID.put("dashboard_staffMyStudentsSearchingWorking",
                new DashboardStaffMySearchingWorkingStudentsColumnResolver());
        columnsResolverByGridID.put("portal_logHistory",
                new TagLogsColumnsResolver());
        columnsResolverByGridID.put("advancedSearch_savedSearches",
                new SavedSearchColumnResolver());
        columnsResolverByGridID.put("dashboard_employerEXPRecords",
                new DashboardEmployerEXPRecordsColumnResolver());
        columnsResolverByGridID.put("dashboard_inMemoryOCCJobs",
                new DashboardInMemoryOccJobsColumnsResolver());
        columnsResolverByGridID.put("coop_coopWtrEmployer",
                new DashboardEmployerWtrRecordsColumnResolver());
        columnsResolverByGridID.put("np_employmentRecords",
                new DashboardEmployerEmploymentRecordsColumnResolver());
        columnsResolverByGridID.put("interaction_summary",
                new InteractionSummaryColumnResolver());
        columnsResolverByGridID.put("dashboard_staffAllInteractions",
                new InteractionStaffSummaryColumnResolver());
        columnsResolverByGridID.put("dashboard_staffAppointments",
                new StaffAppointmentsColumnResolver());
        columnsResolverByGridID.put("dashboard_staffMyJobs",
                new DashboardMyJobsColumnResolver());
        columnsResolverByGridID.put("dashboard_staffInterviews",
                new DashboardMyInterviewsColumnResolver());
        columnsResolverByGridID.put("dashboard_staffMyApplications",
                new DashboardMyApplicationsColumnResolver());
        columnsResolverByGridID.put("dashboard_staffMyEvents",
                new StaffMyEventsColumnResolver());
        columnsResolverByGridID.put("res_organizationReservations",
                new RoomBookingsColumnResolver());
        columnsResolverByGridID.put("dashboard_userCommonMyMessages",
                new DashboardUserCommonMyMessagesColumnResolver());
        columnsResolverByGridID.put("dashboard_staffInteractionNotes",
                new DashboardStaffInteractionNotesColumnResolver());
        columnsResolverByGridID.put("dashboard_staffInteractionTasks",
                new DashboardStaffInteractionTasksColumnResolver());
        columnsResolverByGridID.put("dashboard_staffInteractionEngagements",
                new DashboardStaffInteractionEngagementsColumnResolver());
        columnsResolverByGridID.put("dashboard_staffInteractionForms",
                new DashboardStaffInteractionFormsColumnResolver());
        columnsResolverByGridID.put("dashboard_staffMyOrgs",
                new DashboardStaffMyOrgsColumnResolver());
        columnsResolverByGridID.put("dashboard_staffMyOrgDiv",
                new DashboardStaffMyOrgDivColumnResolver());
        columnsResolverByGridID.put("dashboard_staffMyProspects",
                new DashboardStaffMyProspectsColumnResolver());
        columnsResolverByGridID.put("dashboard_staffMyContacts",
                new DashboardStaffMyContactsColumnResolver());
        columnsResolverByGridID.put("dashboard_commonMyProgramParticipation",
                new DashboardMyProgramParticipationColumnResolver());
        columnsResolverByGridID.put("dashboard_mySubscriptions",
                new DashboardMySubscriptionsColumnResolver());
        columnsResolverByGridID.put("dashboard_employerEXPPostings",
                new DashboardEmployerEXPPostingsColumnResolver());
        columnsResolverByGridID.put("shared_employer_JobsGrid",
                new DashboardEmployerJobPostingsColumnResolver());
        columnsResolverByGridID.put("ccrm_events", new CcrmEventsColumnResolver());
        columnsResolverByGridID.put("ccrm_appointmentsGrid",
                new CcrmAppointmentsColumnResolver());
        columnsResolverByGridID.put("ccrm_pendingRegistrations",
                new CcrmPendingRegistrationsColumnResolver());
        columnsResolverByGridID.put("interview_interviews_default",
                new InterviewsDefaultColumnResolver());
    }

    public static DataViewerGridColumnsResolver getColumnsResolver(String gridID)
    {
        return columnsResolverByGridID.computeIfAbsent(gridID,
                DefaultGridColumnsResolver::new);
    }

    /**
     * @see GridFilterCollectionTag#getGridFilterType
     */
    public static DataViewerGridColumn getBasicColumn(GridColumn gridCol,
            UserDetailsImpl userLoggedIn, Locale locale)
    {
        DataViewerGridColumn dvCol = null;

        if (!StringUtils.isEmpty(gridCol.getValueClass()))
        {
            dvCol = new DataViewerGridColumn();

            dvCol.setKey(gridCol.getColID());
            dvCol.setTitle(gridCol.getColumnName());
            dvCol.setId(gridCol.isKeyCol());
            dvCol.setCategory(gridCol.getCategory().getCategoryID());

            DataViewerGridFilter filter = new DataViewerGridFilter();

            boolean canFilter = GridHelper.userHasGridPermission("CAN_FILTER",
                    userLoggedIn, gridCol.getCategory().getGrid());

            if (gridCol instanceof GridColumnBoolean
                    || Boolean.class.getName().equals(gridCol.getValueClass()))
            {
                dvCol.setType(DataViewerGridColumn.TYPE_OPTIONS);
                filter.addOption(new I18nLabel(
                        "i18n.DataViewerColumnHelper.Yes0489383299929576", locale)
                        .getTranslation(), "1");
                filter.addOption(new I18nLabel(
                        "i18n.DataViewerColumnHelper.No8539457777013098", locale)
                        .getTranslation(), "0");

                if (canFilter)
                {
                    filter.setType(DataViewerGridColumn.TYPE_OPTIONS);
                    dvCol.setFilter(filter);
                }
            }
            else if (String.class.getName().equals(gridCol.getValueClass()))
            {
                dvCol.setType(DataViewerGridColumn.TYPE_TEXT);

                if (canFilter)
                {
                    filter.setType(DataViewerGridColumn.TYPE_TEXT);
                    dvCol.setFilter(filter);
                }
            }
            else if (StringUtils.equalsAny(gridCol.getValueClass(),
                    Integer.class.getName(), Double.class.getName(),
                    Float.class.getName()))
            {
                dvCol.setType(DataViewerGridColumn.TYPE_NUMBER);

                if (canFilter) {
                    filter.setType(DataViewerGridColumn.TYPE_NUMBER);
                    dvCol.setFilter(filter);
                }
            }
            else if (Date.class.getName().equals(gridCol.getValueClass()))
            {
                dvCol.setType(DataViewerGridColumn.TYPE_DATE);
                dvCol.setDateFormat(DateUtils.getMomentDateTimeFormat(
                        DateUtils.DF_MOMENT_ORBIS_DATE_TIME, locale));

                if (canFilter) {
                    filter.setType(DataViewerGridColumn.TYPE_DATE);
                    dvCol.setFilter(filter);
                }
            }
            else if ("DoNotSort".equalsIgnoreCase(gridCol.getValueClass()))
            {
                dvCol.setSortable(false);

                if (canFilter)
                {
                    dvCol.setType(DataViewerGridColumn.TYPE_TEXT);
                    dvCol.setFilter(filter);
                }

            }
        }

        return dvCol;
    }
}
