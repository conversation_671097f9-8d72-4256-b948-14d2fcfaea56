package com.orbis.web.content.grid;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;

import jakarta.servlet.http.HttpServletRequest;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.QueryBuilder;
import com.orbis.utils.StringUtils;
import com.orbis.web.OrbisController;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.exp.EXPCourseHelper;
import com.orbis.web.content.exp.EXPModule;
import com.orbis.web.content.exp.EXPModuleUser;
import com.orbis.web.content.exp.EXPTermCourseStudentHelper;
import com.orbis.web.content.exp.EXPTermCourseStudentStatus;
import com.orbis.web.content.exp.termCourseType.EXPTermCourseTypeLabel;

public class EXPHomeActiveCoursesVirtualData implements VirtualDataCallback
{
    @Override
    public List<VirtualData> getVirtualData(VirtualDataCallbackParameters params)
    {
        HttpServletRequest request = params.getRequest();
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        EXPModule module = (EXPModule) OrbisController.getOrbisModule(request);
        Locale locale = PortalUtils.getLocale(request);
        EXPModuleUser moduleUser = EXPModuleUser
                .getInitializedInstance(userLoggedIn, module);
        return Arrays.asList(getExpTypesVirtualData(moduleUser, locale),
                getNumberOfStudentsVirtualData(moduleUser),
                getCompletedStudentsVirtualData(moduleUser),
                getFacultyOwnersVirtualData(module),
                getTagsVirtualData(module, locale));
    }

    private static VirtualData getCompletedStudentsVirtualData(
            EXPModuleUser moduleUser)
    {
        return new VirtualData("tcId", "numberOfCompletedStudents", () -> {
            QueryBuilder hql = new QueryBuilder();
            {
                hql.append(" SELECT ");
                hql.append("   tc.id, ");
                hql.append("   COUNT(DISTINCT tcs.id) ");
                hql.append(" FROM EXPTermCourseStudent tcs ");
                hql.append("   JOIN tcs.termCourse tc ");
                hql.append("   JOIN tcs.status tcss ");
                hql.append("   JOIN tcs.student.owner u ");
                hql.append(" WHERE ");
                hql.append("   tcss.type = ? ",
                        EXPTermCourseStudentStatus.Type.COMPLETED.getValue());
                hql.append("   AND ");
                hql.append(UserDetailsHelper.getActiveUserWhereFragment("u"));
                hql.append("   AND ");
                hql.append(EXPCourseHelper.getCoursesWhereClause("tc", moduleUser,
                        null));
                hql.append(" GROUP BY tc.id ");
            }
            return PortalUtils.getHt().find(hql);
        });
    }

    private static VirtualData getNumberOfStudentsVirtualData(
            EXPModuleUser moduleUser)
    {
        return new VirtualData("tcId", "numberOfStudents", () -> {
            QueryBuilder hql = new QueryBuilder();
            {
                hql.append(" SELECT ");
                hql.append("   tc.id, ");
                hql.append("   COUNT(DISTINCT tcs.id) ");
                hql.append(" FROM EXPTermCourseStudent tcs ");
                hql.append("   JOIN tcs.termCourse tc ");
                hql.append("   JOIN tcs.status tcss ");
                hql.append("   JOIN tcs.student.owner u ");
                hql.append(" WHERE ");
                hql.append(EXPTermCourseStudentHelper
                        .getApprovedEnrollmentsWhereFragment("tcss", "u"));
                hql.append("   AND ");
                hql.append(EXPCourseHelper.getCoursesWhereClause("tc", moduleUser,
                        null));
                hql.append(" GROUP BY tc.id ");
            }
            return PortalUtils.getHt().find(hql);
        });
    }

    private static VirtualData getExpTypesVirtualData(EXPModuleUser moduleUser,
            Locale locale)
    {
        return new VirtualData("tcId", "expTypes", () -> {
            QueryBuilder hql = new QueryBuilder();
            {
                hql.append(" SELECT ");
                hql.append("   tc.id, "); // 0
                hql.append(StringUtils.getStringForLocale("tct.name", "tct.l2Name",
                        locale) + ", "); // 1
                hql.append(StringUtils.getStringForLocale("t.name", "t.l2Name",
                        locale)); // 2
                hql.append(" FROM EXPTermCourseType tct ");
                hql.append("   JOIN tct.termCourse tc ");
                hql.append("   JOIN tct.type t ");
                hql.append(" WHERE ");
                hql.append(EXPCourseHelper.getCoursesWhereClause("tc", moduleUser,
                        null));
            }

            List<Object[]> r = PortalUtils.getHt().find(hql);

            Map<Integer, List<String>> expTypesByTC = new HashMap<>();

            for (Object[] o : r)
            {
                expTypesByTC.computeIfAbsent((Integer) o[0], k -> new ArrayList<>())
                        .add(EXPTermCourseTypeLabel.builder()//
                                .tctName((String) o[1]).expTypeName((String) o[2])//
                                .build()//
                                .getLabel());
            }

            List<Object[]> rows = new ArrayList<>();

            for (Entry<Integer, List<String>> e : expTypesByTC.entrySet())
            {
                rows.add(new Object[] { e.getKey(), e.getValue() });
            }

            return rows;
        });
    }

    private static VirtualData getFacultyOwnersVirtualData(EXPModule module)
    {
        return new VirtualData("tcId", "facultyOwners", () -> {

            List<Object[]> displayNames = PortalUtils.getHt()
                    .find("SELECT tc.id, f.preferredFirstName, f.lastName FROM "
                            + "EXPTermCourseFacultyAdvisor tcfm join tcfm.termCourse tc join tcfm.facultyAdvisor f where tc.course.module=?",
                            module);

            Map<Integer, List<String>> facultyOwnersByTC = new HashMap<>();

            for (Object[] o : displayNames)
            {
                Integer tcId = (Integer) o[0];

                String firstName = (String) o[1];
                String lastName = (String) o[2];

                String facultyOwner = "";

                if (StringUtils.isNotEmpty(firstName))
                {
                    facultyOwner += firstName + " " + lastName;
                }

                facultyOwnersByTC.computeIfAbsent(tcId, k -> new ArrayList<>())
                        .add(facultyOwner);
            }

            List<Object[]> rows = new ArrayList<>();

            for (Entry<Integer, List<String>> e : facultyOwnersByTC.entrySet())
            {
                rows.add(new Object[] { e.getKey(), e.getValue() });
            }

            return rows;
        });
    }

    private static VirtualData getTagsVirtualData(EXPModule module, Locale locale)
    {
        return new VirtualData("tcId", "termCourseTags", () -> {

            List<Object[]> rows = new ArrayList<>();
            String tagNamePrefix = LocaleUtils.isL1(locale) ? "a.tag.l1Name"
                    : "a.tag.l2Name";
            String query = "select etc.id, " + tagNamePrefix
                    + " from TagAssign a join a.expTermCourse etc left join a.tag t left join t.category c where etc.course.module=?";
            List<Object[]> academicTags = PortalUtils.getHt().find(query, module);

            Map<Integer, List<String>> values = new HashMap<>();
            academicTags.forEach(tag -> {
                Integer key = (Integer) tag[0];
                String value = (String) tag[1];
                values.computeIfAbsent(key, k -> new ArrayList<>()).add(value);
            });

            values.forEach((key, value) -> rows.add(new Object[] { key, value }));

            return rows;
        });
    }

    @Override
    public void afterMerge(VirtualDataCallbackParameters params)
    {
    }
}
