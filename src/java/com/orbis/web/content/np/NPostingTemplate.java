package com.orbis.web.content.np;

import java.awt.*;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.TreeMap;

import org.hibernate.annotations.ColumnDefault;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.context.MessageSource;
import org.springframework.context.NoSuchMessageException;

import com.aspose.words.Document;
import com.aspose.words.DocumentBuilder;
import com.aspose.words.Font;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.docTemp.DocTemplate;
import com.orbis.docTemp.DocTemplateHelper;
import com.orbis.docTemp.DocumentModel;
import com.orbis.docTemp.DocumentModelItem;
import com.orbis.email.EmailRecipient;
import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.DBUtils;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.placement.NPostingEmailHelper;

import jakarta.persistence.Access;
import jakarta.persistence.AccessType;
import jakarta.servlet.http.HttpServletRequest;

@Access(value = AccessType.FIELD)
public class NPostingTemplate extends DocTemplate
{

    private static final long serialVersionUID = 1931826838512686487L;

    public static final int TYPE_OTHER = 0;

    public static final int TYPE_POSTING_DETAILS = 1;

    public static final int TYPE_APPLICATION_COVER_LETTER = 2;

    private NPostingModule module;

    @Override
    protected Map<Integer, String> generateTypeMap(Locale locale)
    {
        Map<Integer, String> typeMap = new LinkedHashMap<>();
        typeMap.put(TYPE_OTHER,
                new I18nLabel("i18n.EXPTypeDocTemplate.Other6820066960915914")
                        .getTranslation(locale));
        typeMap.put(TYPE_POSTING_DETAILS,
                new I18nLabel("i18n.NPostingTemplate.PostingDet6756292225045608")
                        .getTranslation(locale));
        typeMap.put(TYPE_APPLICATION_COVER_LETTER,
                new I18nLabel("i18n.NPostingTemplate.Applicatio0060101738836317")
                        .getTranslation(locale));
        return typeMap;
    }

    @Override
    public int[] getTypesWithoutDefault()
    {
        return new int[] { TYPE_OTHER };
    }

    @Override
    public String getAdditionalHql(JSONObject additionalParams) throws Exception
    {
        return " t.module.id = " + additionalParams.getInt("moduleId");
    }

    @Override
    public void setAdditionalData(JSONObject additionalParams) throws Exception
    {
        NPostingModule module = PortalUtils.getHt()
                .load(NPostingModule.class, additionalParams.getInt("moduleId"));
        this.setModule(module);
    }

    @Override
    public String getTableName()
    {
        return "np_template";
    }

    @Override
    public List<DocumentModel> populateDocumentData(JSONObject additionalParams,
            Locale locale, List<Integer> itemIds, String[] fieldsUsed,
            UserDetailsImpl userLoggedIn) throws Exception
    {
        final List<DocumentModel> documentModels = new LinkedList<>();

        MessageSource messageSource = PortalUtils.getMessageSource();
        boolean isLocaleL1 = LocaleUtils.isL1(locale);
        NPostingModule module = PortalUtils.getHt()
                .load(NPostingModule.class, additionalParams.getInt("moduleId"));
        if (this.getType() == TYPE_APPLICATION_COVER_LETTER)
        {
            final DocumentModel docModel = new DocumentModel();

            String orderBy = " order by a.user.lastName, a.user.preferredFirstName";

            NPosting posting = null;
            if (additionalParams.has("postingId"))
            {
                posting = PortalUtils.getHt().load(NPosting.class,
                        additionalParams.getInt("postingId"));
            }
            if (posting != null)
            {
                docModel.addItem(new DocumentModelItem("posting", posting));
                if (NHelper.getNApplicationModule(posting.getNPostingModule())
                        .isRandomizeApplicantsInAppBundle())
                {
                    orderBy = "";
                }
            }

            JSONArray applicationIdsJson = additionalParams
                    .getJSONArray("applicationIds");
            List<Integer> applicationIds = new ArrayList<Integer>();

            for (int i = 0; i < applicationIdsJson.length(); i++)
            {
                applicationIds.add(applicationIdsJson.getInt(i));
            }

            List<NApplication> applications = PortalUtils.getHt()
                    .find("from NApplication a where a.id in "
                            + DBUtils.buildInClause(applicationIds) + orderBy);

            docModel.setDocCallback(
                    (doc) -> buildApplicantsTable(doc, applications));

            docModel.addItem(new DocumentModelItem("applications", applications));
            documentModels.add(docModel);
        }
        else
        {
            List<NPosting> postings = new ArrayList<NPosting>();

            if (additionalParams.has("postingId"))
            {
                postings.add(PortalUtils.getHt().load(NPosting.class,
                        additionalParams.getInt("postingId")));
            }
            else if (additionalParams.has("postingIds"))
            {
                postings = PortalUtils.getHt().find(
                        "from NPosting np where np.id in " + DBUtils.buildInClause(
                                additionalParams.getJSONArray("postingIds")));
            }

            boolean regionalServiceTeams = PortalConfigHelper
                    .getPortalConfig(PortalConfig.REGIONAL_SERVICE_TEAMS)
                    .getOrbisValueBoolean();

            for (NPosting posting : postings)
            {
                final DocumentModel docModel = new DocumentModel();
                List<Object[]> jobServiceTeam = PortalUtils.getHt().find(
                        "select jst.teamMemberRole.role.name, jst.teamMemberRole.teamMember.member, rp.title from InteractionPostingServiceTeamMember jst left join jst.teamMemberRole.regionalProgram rp where jst.posting = ? and jst.teamMemberRole.role.onJob = true",
                        posting);

                if (regionalServiceTeams)
                {
                    Map<UserDetailsImpl, String> regionalServiceTeam = new TreeMap<UserDetailsImpl, String>();
                    for (Object[] teamMemberRole : jobServiceTeam)
                    {
                        regionalServiceTeam.merge(
                                (UserDetailsImpl) teamMemberRole[1],
                                teamMemberRole[2] + ", ", String::concat);
                    }
                    for (UserDetailsImpl key : regionalServiceTeam.keySet())
                    {
                        regionalServiceTeam.replace(key,
                                org.apache.commons.lang.StringUtils.removeEnd(
                                        regionalServiceTeam.get(key), ", "));
                    }
                    docModel.addItem(new DocumentModelItem("regionalServiceTeam",
                            new LinkedList(regionalServiceTeam.entrySet())));
                }
                else
                {
                    Map<String, UserDetailsImpl> roleMap = new TreeMap<String, UserDetailsImpl>();
                    for (Object[] teamMemberRole : jobServiceTeam)
                    {
                        roleMap.put((String) teamMemberRole[0],
                                (UserDetailsImpl) teamMemberRole[1]);
                    }
                    docModel.addItem(new DocumentModelItem("jobServiceTeam",
                            new LinkedList(roleMap.entrySet())));
                }

                docModel.addItem(new DocumentModelItem("posting", posting));

                String titleFieldBilingual = isLocaleL1 ? "title" : "l2Title";

                if (module.isUseClusterLevelFormat())
                {
                    Map<String, Collection<String>> selectedClusters = NHelper
                            .getSelectedClustersWithLevels(posting,
                                    titleFieldBilingual);
                    Map<String, List<Map<String, Object>>> clusterMapForDoc = new HashMap<String, List<Map<String, Object>>>();
                    for (Entry<String, Collection<String>> clusterLevels : selectedClusters
                            .entrySet())
                    {
                        List<Map<String, Object>> detailsList = new LinkedList<Map<String, Object>>();

                        for (String level : new ArrayList<String>(
                                clusterLevels.getValue()))
                        {
                            Map<String, Object> details = new LinkedHashMap<String, Object>();

                            String termType = new I18nLabel(
                                    "i18n.common.TermNumber.W")
                                    .getTranslation(locale);
                            details.put("level",
                                    level.replace('W', termType.charAt(0)));
                            details.put(DocTemplateHelper.NOT_DATASOURCE,
                                    Boolean.TRUE);
                            detailsList.add(details);
                        }
                        clusterMapForDoc.put(clusterLevels.getKey(), detailsList);
                    }

                    docModel.addItem(new DocumentModelItem("clusterLevels",
                            new LinkedList(clusterMapForDoc.entrySet())));
                }
                else
                {
                    List<NCluster> clusters = PortalUtils.getHt().find(
                            "select c.NCluster from NPostingCluster c where c.NPosting = ?",
                            posting);
                    List<NPostingProgram> programs = PortalUtils.getHt().find(
                            "select p.NProgram from NPostingProgram p where p.NPosting = ?",
                            posting);

                    docModel.addItem(new DocumentModelItem("clusters", clusters));
                    docModel.addItem(new DocumentModelItem("programs", programs));
                }

                String allDocTypes = "";
                List<String> docTypesRequired = PortalUtils.getHt().find(
                        "select ndt.docType." + (isLocaleL1 ? "name" : "l2Name")
                                + " from NPostingDocType2 ndt where ndt.posting=?",
                        posting);
                for (int i = 0; i < docTypesRequired.size(); i++)
                {
                    allDocTypes += docTypesRequired.get(i) + "\n";
                }

                docModel.addItem(new DocumentModelItem(
                        "applicationDocumentsRequired", allDocTypes));

                try
                {
                    docModel.addItem(new DocumentModelItem("jobLang",
                            messageSource.getMessage(
                                    "i18n.common." + posting.getRegion(), null,
                                    locale)));

                    String cf6 = posting.getCf6() != null
                            ? posting.getCf6().toLowerCase()
                            : null;
                    docModel.addItem(new DocumentModelItem("testReq", messageSource
                            .getMessage("i18n.common." + cf6, null, locale)));
                }
                catch (NoSuchMessageException ex)
                {
                }

                if (this.getType() == TYPE_POSTING_DETAILS)
                {
                    docModel.setDocCallback(
                            (doc) -> renderCKEditorValues(doc, posting, locale));
                }

                documentModels.add(docModel);
            }
        }

        return documentModels;
    }

    public void buildApplicantsTable(Document doc, List<NApplication> applications)
            throws Exception
    {
        DocumentBuilder builder = new DocumentBuilder(doc);
        if (builder.moveToMergeField("applicantsTable"))
        {
            populateTableBuilder(applications, builder);
        }
    }

    private void populateTableBuilder(List<NApplication> applications,
            DocumentBuilder builder) throws Exception
    {
        Color lightGray = new Color(240, 240, 240);
        builder.startTable();

        builder.insertCell();
        builder.getCellFormat().getShading().setBackgroundPatternColor(lightGray);
        builder.getCellFormat().setTopPadding(2);

        builder.write("#");

        builder.insertCell();
        builder.write("APPLICANT");

        builder.insertCell();
        builder.write("MAJOR");

        builder.endRow();

        Integer i = 1;
        for (NApplication app : applications)
        {
            builder.insertCell();
            builder.getCellFormat().clearFormatting();
            builder.getCellFormat().setTopPadding(2);
            if (i % 2 == 0)
            {
                builder.getCellFormat().getShading()
                        .setBackgroundPatternColor(lightGray);
            }
            builder.write(i.toString());

            if (app.getUser() != null)
            {
                builder.insertCell();
                if (app.getUser().getLastName() != null
                        && app.getUser().getPreferredFirstName() != null)
                {
                    builder.write(app.getUser().getLastName() + ", "
                            + app.getUser().getPreferredFirstName());
                }
                else
                {
                    builder.write(" ");
                }

                builder.insertCell();
                if (app.getUser().getMajor1Descr() != null)
                {
                    if ("BA".equals(app.getUser().getS5())
                            && !StringUtils.isEmpty(app.getUser().getMajor4Descr()))
                    {
                        builder.write(app.getUser().getMajor4Descr());
                    }
                    else
                    {
                        builder.write(app.getUser().getMajor1Descr());
                    }
                }
                else
                {
                    builder.write(" ");
                }
            }

            builder.endRow();
            i++;
        }

        builder.endTable();
    }

    public void renderCKEditorValues(Document doc, NPosting posting, Locale locale)
            throws Exception
    {
        DocumentBuilder builder = new DocumentBuilder(doc);
        final Font font = builder.getFont();
        font.setName("Arial");
        font.setSize(10);
        String listStyle = "<style>li {margin:0px} li:last-of-type {margin-bottom: 12px;}</style>";

        if (builder.moveToMergeField("cke5"))
        {
            if (posting.getL2Cf5() != null && !LocaleUtils.isL1(locale))
            {
                builder.insertHtml(listStyle + posting.getL2Cf5(), true);
            }
            else if (posting.getCf5() != null)
            {
                builder.insertHtml(listStyle + posting.getCf5(), true);
            }
        }
        if (builder.moveToMergeField("cke9"))
        {
            if (posting.getL2Cf9() != null && !LocaleUtils.isL1(locale))
            {
                builder.insertHtml(listStyle + posting.getL2Cf9(), true);
            }
            else if (posting.getCf9() != null)
            {
                builder.insertHtml(listStyle + posting.getCf9(), true);
            }
        }
        if (builder.moveToMergeField("cke12"))
        {
            if (posting.getL2Cf12() != null && !LocaleUtils.isL1(locale))
            {
                builder.insertHtml(listStyle + posting.getL2Cf12(), true);
            }
            else if (posting.getCf12() != null)
            {
                builder.insertHtml(listStyle + posting.getCf12(), true);
            }
        }
        if (builder.moveToMergeField("cke14"))
        {
            if (posting.getL2Cf14() != null && !LocaleUtils.isL1(locale))
            {
                builder.insertHtml(listStyle + posting.getL2Cf14(), true);
            }
            else if (posting.getCf14() != null)
            {
                builder.insertHtml(listStyle + posting.getCf14(), true);
            }
        }
        if (builder.moveToMergeField("ckeApplicationInstructions"))
        {
            if (posting.getL2ApplicationInstructions() != null
                    && !LocaleUtils.isL1(locale))
            {
                builder.insertHtml(
                        listStyle + posting.getL2ApplicationInstructions(), true);
            }
            else if (posting.getApplicationInstructions() != null)
            {
                builder.insertHtml(listStyle + posting.getApplicationInstructions(),
                        true);
            }
        }
    }

    public NPostingModule getModule()
    {
        return module;
    }

    public void setModule(NPostingModule module)
    {
        this.module = module;
    }

    @Override
    public boolean isEmailable()
    {
        return true;
    }

    @Override
    public List<EmailRecipient> getEmailRecipients(JSONObject additionalParams,
            HttpServletRequest request) throws Exception
    {
        List<EmailRecipient> recipients = new ArrayList<>();
        Integer postingId = additionalParams.getInt("postingId");
        NPosting posting = PortalUtils.getHt().load(NPosting.class,
                postingId);

        NPostingEmailHelper.PostedByEmailDetails emailRecipient = NPostingEmailHelper
                .getPostedByEmailDetailsBackup(posting);
        if (StringUtils.isNotEmpty(emailRecipient.getEmail()))
        {
            EmailRecipient recipient = new EmailRecipient();
            recipient.setEmail(emailRecipient.getEmail());
            recipient.setUsername(emailRecipient.getUsername());
            recipient.setName(emailRecipient.getFullName());
            recipients.add(recipient);
        }
        return recipients;
    }

    @Override
    public String getAfterEmailAction(JSONObject additionalParams)
    {
        return "displaySchedule";
    }

    @Override
    public JSONObject getSample(JSONObject additionalParams) throws Exception
    {
        JSONObject ret;
        NPostingModule module = PortalUtils.getHt()
                .load(NPostingModule.class, additionalParams.getInt("moduleId"));

        if (this.getType() == TYPE_APPLICATION_COVER_LETTER)
        {
            ret = DocTemplateHelper
                    .loadDocTemplateSample("np_applicationCoverLetter");
        }
        else
        {
            JSONObject postingStructure = DocTemplateHelper
                    .loadDocTemplateSample("postingDetailsDoc");

            if (module.isUseClusterLevelFormat())
            {
                postingStructure.remove("clusters");
                postingStructure.remove("programs");
            }
            else
            {
                postingStructure.remove("clusterLevels");
            }

            ret = postingStructure;
        }

        return ret;
    }

    @Override
    @Access(AccessType.PROPERTY)
    public String getName()
    {
        return super.getName();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public String getL2Name()
    {
        return super.getL2Name();
    }

    @Override
    @Access(AccessType.PROPERTY)
    @ColumnDefault("0")
    public int getType()
    {
        return super.getType();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public String getDocument()
    {
        return super.getDocument();
    }

    @Override
    @Access(AccessType.PROPERTY)
    public String getL2Document()
    {
        return super.getL2Document();
    }

    @Override
    @Access(AccessType.PROPERTY)
    @ColumnDefault("0")
    public boolean isDefaultForType()
    {
        return super.isDefaultForType();
    }

}
