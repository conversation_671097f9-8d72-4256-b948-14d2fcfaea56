package com.orbis.df;

import java.awt.*;
import java.io.IOException;
import java.io.StringReader;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Modifier;
import java.net.MalformedURLException;
import java.net.URL;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang.text.StrSubstitutor;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.context.MessageSource;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.web.servlet.ModelAndView;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.lowagie.text.Document;
import com.lowagie.text.DocumentException;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.FontFactory;
import com.lowagie.text.PageSize;
import com.lowagie.text.Paragraph;
import com.lowagie.text.Rectangle;
import com.lowagie.text.html.simpleparser.HTMLWorker;
import com.lowagie.text.html.simpleparser.StyleSheet;
import com.lowagie.text.pdf.PdfPCell;
import com.lowagie.text.pdf.PdfPTable;
import com.lowagie.text.pdf.PdfTable;
import com.orbis.acegi.providers.dao.hibernate.PersonGroup;
import com.orbis.acegi.providers.dao.hibernate.PersonGroupHelper;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.df.formatter.DFAnswerFormatterFactory;
import com.orbis.df.matcher.DFModelMatcher;
import com.orbis.df.qualifier.DFQualificationChecker;
import com.orbis.df.qualifier.Qualification;
import com.orbis.expressions.Functions;
import com.orbis.portal.CommandQueryTemplate;
import com.orbis.portal.MapExtractor;
import com.orbis.portal.OrbisHqlResultSet;
import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;
import com.orbis.portal.PortalUtils;
import com.orbis.question2.Question2;
import com.orbis.question2.category2.Category2;
import com.orbis.search.SearchModel;
import com.orbis.search.criteria.CriteriaAnswer;
import com.orbis.search.criteria.CriteriaGroup;
import com.orbis.search.criteria.CriteriaModel;
import com.orbis.search.criteria.CriteriaQuestion;
import com.orbis.search.entity.Entity;
import com.orbis.utils.ArrayUtils;
import com.orbis.utils.ChartUtils;
import com.orbis.utils.ClassUtils;
import com.orbis.utils.CollectionUtils;
import com.orbis.utils.DBUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.FilePathUtils;
import com.orbis.utils.HtmlUtils;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.JSONUtils;
import com.orbis.utils.LambdaExceptionUtil;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.QueryBuilder;
import com.orbis.utils.RequestUtils;
import com.orbis.utils.StringUtils;
import com.orbis.utils.ValidationData;
import com.orbis.web.content.ContentItem;
import com.orbis.web.content.ContentItemHelper;
import com.orbis.web.content.Persistable;
import com.orbis.web.content.acrm.AcrmDivisionQuestion;
import com.orbis.web.content.acrm.AcrmHelper;
import com.orbis.web.content.acrm.AcrmOrgQuestion;
import com.orbis.web.content.acrm.AcrmRegistrationQuestionHelper;
import com.orbis.web.content.acrm.dataImport.AcrmDataImport;
import com.orbis.web.content.acrm.dataImport.AcrmDataImportHelper;
import com.orbis.web.content.acrm.dataImport.AcrmDataImportHelper.DATATYPE;
import com.orbis.web.content.crm.DivisionModelEntity;
import com.orbis.web.content.crm.OrganizationModelEntity;
import com.orbis.web.content.file.FilePath;
import com.orbis.web.content.np.NHelper;
import com.orbis.web.content.portal.DeletionNodeHelper;
import com.orbis.web.site.SiteController;
import com.orbis.web.site.SiteElement;

import jakarta.servlet.http.HttpServletRequest;

public class DFHelper
{
    public static final String UPLOAD_FOLDER = "/content/documents/question_uploads/";

    // default styles
    public static Color tableHeaderBg;

    public static Color descBg;

    public static StyleSheet itextDefaultStyle;

    public static Integer UNMARKED = 0;

    public static Integer TEMP_MARKED = 1;

    public static Integer PERM_MARKED = 2;

    static
    {
        // default pdf styles
        itextDefaultStyle = new StyleSheet();
        itextDefaultStyle.loadStyle("itextDefault", "size", "10px");
        itextDefaultStyle.loadStyle("itextDefault", "font-size", "10px");
        itextDefaultStyle.loadStyle("itextDefault", "font-weight", "bold");

        tableHeaderBg = new Color(225, 225, 225);
        descBg = new Color(240, 240, 240);
    }

    private static final String FONT_NAME = FontFactory.HELVETICA;

    private static final Font SM_BOLD_FONT = FontFactory.getFont(FONT_NAME, 10,
            Font.BOLD);

    public static final Font LG_BOLD_FONT = FontFactory.getFont(FONT_NAME, 12,
            Font.BOLD);

    public static final float PDF_FORMAT_LR_MARGIN = 72f;// 1inch = 72pt

    public static List getDFQuestionSiteMappings(HttpServletRequest request)
    {
        Map<String, PortalConfig> allConfigs = PortalConfigHelper
                .getPortalConfigMap();
        MessageSource ms = PortalUtils.getMessageSource();

        List<Object[]> ret = new ArrayList();
        allConfigs.entrySet().stream().filter(e -> e.getKey().startsWith("DFQSM"))
                .forEach(e -> {
                    ret.add(new Object[] { e.getKey(),
                            ms.getMessage(e.getValue().getOrbisValue(), null,
                                    e.getValue().getOrbisValue(),
                                    PortalUtils.getLocale(request)) });
                });
        ret.sort((a, b) -> a[1].toString().compareTo(b[1].toString()));
        return ret;
    }

    public static DFQuestion createQuestion(List<DFQuestion> otherQuestions,
            boolean save, DFModelEntity modelEntity) throws DFException
    {
        DFQuestion question = null;

        if (modelEntity instanceof OrganizationModelEntity)
        {
            question = new AcrmOrgQuestion();
        }
        else if (modelEntity instanceof DivisionModelEntity)
        {
            question = new AcrmDivisionQuestion();
        }
        else
        {
            question = new DFQuestion();
        }

        // fails if capacity is full...
        setInitialType(question, otherQuestions);

        question.setAdminOnly(false);

        if (save)
        {
            PortalUtils.getHt().save(question);

            question.setQuestionText(
                    new I18nLabel("i18n.DFHelper.Question9877805112289958",
                            Arrays.asList(question.getId())));
            question.setPosition(question.getId());

            PortalUtils.getHt().update(question);
        }
        else
        {
            question.setQuestionText(
                    new I18nLabel("i18n.DFHelper.Question9877805112289958",
                            Arrays.asList(otherQuestions == null ? 1
                                    : otherQuestions.size() + 1)));
            question.setPosition(
                    otherQuestions == null ? 0 : otherQuestions.size());
        }

        return question;
    }

    // Unused Method
    public static boolean isTrackingSalaryInfo(DFModel model)
    {
        return ((Integer) PortalUtils.getHt().find(
                "select count(q.id) from DFQuestion q where q.category.model=? and q.questionType=?",
                new Object[] { model, DFQuestion.TYPE_SALARY }).get(0))
                .intValue() > 0;
    }

    public static DFQuestion createQuestion(int type,
            List<DFQuestion> otherQuestions, boolean save) throws DFException
    {
        DFQuestion question = new DFQuestion();

        question.setType(type);

        // fails if type-capacity is full...
        updateAnswerMappings(question, otherQuestions);

        question.setAdminOnly(false);

        if (save)
        {
            PortalUtils.getHt().save(question);

            question.setQuestionText(
                    new I18nLabel("i18n.DFHelper.Question9877805112289958",
                            Arrays.asList(question.getId())));
            question.setPosition(question.getId());

            PortalUtils.getHt().update(question);
        }
        else
        {
            question.setQuestionText(new I18nLabel(
                    "i18n.DFHelper.Question9877805112289958", Arrays.asList(
                            otherQuestions == null ? 0 : otherQuestions.size())));
            question.setPosition(
                    otherQuestions == null ? 0 : otherQuestions.size());
        }

        return question;
    }

    private static void setInitialType(DFQuestion question,
            List<DFQuestion> otherQuestions) throws DFException
    {
        try
        {
            question.setType(DFQuestion.TYPE_TEXT);
            updateAnswerMappings(question, otherQuestions);
        }
        catch (DFException e1)
        {
            try
            {
                question.setType(DFQuestion.TYPE_BOOLEAN);
                updateAnswerMappings(question, otherQuestions);
            }
            catch (DFException e2)
            {
                try
                {
                    question.setType(DFQuestion.TYPE_DATE);
                    updateAnswerMappings(question, otherQuestions);
                }
                catch (DFException e3)
                {
                    try
                    {
                        question.setType(DFQuestion.TYPE_INTEGER);
                        updateAnswerMappings(question, otherQuestions);
                    }
                    catch (DFException e4)
                    {
                        try
                        {
                            question.setType(DFQuestion.TYPE_LARGE_TEXT);
                            updateAnswerMappings(question, otherQuestions);
                        }
                        catch (DFException e5)
                        {
                            throw e5;
                        }
                    }
                }
            }
        }
    }

    public static DFModel createNewDefaultModel(DFModelEntity e,
            DFQuestionInterface questionInterface, boolean isSimplifiedModel)
    {
        return processCreateNewDefaultModel(e, questionInterface, isSimplifiedModel,
                false);
    }

    public static DFModel createNewDefaultModel(DFModelEntity e,
            DFQuestionInterface questionInterface, boolean isSimplifiedModel,
            boolean isSimplifiedModelWithHeaderFooter)
    {
        return processCreateNewDefaultModel(e, questionInterface,
                isSimplifiedModel, isSimplifiedModelWithHeaderFooter);
    }

    private static DFModel processCreateNewDefaultModel(DFModelEntity e,
            DFQuestionInterface questionInterface, boolean isSimplifiedModel,
            boolean isSimplifiedModelWithHeaderFooter)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        DFModel model = new DFModel();
        model.setSimplifiedQuestionsAndQualifiers(isSimplifiedModel);
        model.setSimplifiedModelWithHeaderFooter(
                isSimplifiedModel && isSimplifiedModelWithHeaderFooter);
        model.setModelEntityClassName(e.getClass());
        ht.save(model);

        List<DFCategory> categories = questionInterface.createInitialCategories(e,
                model);

        int categoryPosition = 0;
        List<DFQuestion> questions = new LinkedList<>();
        for (DFCategory category : categories)
        {
            category.setPosition(categoryPosition++);
            PortalUtils.getHt().saveOrUpdate(category);

            List<DFQuestion> defaultQuestions = questionInterface
                    .createInitialQuestions(e, category);

            if (defaultQuestions != null)
            {
                questions.addAll(defaultQuestions);
            }
        }

        List<DFRole> roles = questionInterface.getInitialRoles(e, model);
        if (null != roles && !roles.isEmpty())
        {
            for (DFRole role : roles)
            {
                questionInterface.createInitialRoleGroup(role);

                for (DFQuestion question : questions)
                {
                    questionInterface.createInitialQuestionRole(question, role);
                }
            }
        }
        else
        {
            DFRole role = new DFRole();
            role.setDefaultRead(true);
            role.setDefaultSearch(true);
            role.setDefaultShowInResults(true);
            role.setDefaultShowInReports(true);
            role.setDefaultWrite(true);
            role.setDefaultRequired(false);
            role.setName(
                    PortalUtils.getI18nMessage("i18n.DFHelper.User9557717557911821",
                            LocaleUtils.getDefaultLocale()));
            role.setL2Name(
                    PortalUtils.getI18nMessage("i18n.DFHelper.User9557717557911821",
                            LocaleUtils.getSecondaryLocale()));
            role.setModel(model);
            ht.save(role);

            for (DFQuestion question : questions)
            {
                DFQuestionRole questionRole = new DFQuestionRole();
                questionRole.setCanRead(true);
                questionRole.setCanSearch(true);
                questionRole.setCanShowInResults(true);
                questionRole.setCanWrite(true);
                questionRole.setRequired(question.isMakeRequired());
                questionRole.setDfQuestion(question);
                questionRole.setDfRole(role);
                ht.save(questionRole);
            }

            if (model.isSimplifiedQuestionsAndQualifiers())
            {
                List<PersonGroup> groups = PersonGroupHelper
                        .getPrimaryGroupsFromCache();
                for (PersonGroup group : groups)
                {
                    DFRoleUserGroup roleUserGroup = new DFRoleUserGroup();
                    roleUserGroup.setRole(role);
                    roleUserGroup.setUserGroup(group);
                    ht.save(roleUserGroup);
                }
            }
            else
            {
                DFRoleUserGroup roleUserGroup = new DFRoleUserGroup();
                roleUserGroup.setRole(role);
                roleUserGroup.setUserGroup(PersonGroupHelper.PORTAL_STAFF_GROUP);
                ht.save(roleUserGroup);
            }

        }

        e.setDFModel(model);
        ht.update(e);
        return model;
    }

    public static DFQuestionQualifier createNewQuestionQualifier(
            DFQuestion question, DFQualifierEntity qualifierEntity)
    {
        DFQuestionQualifier ret = null;
        try
        {
            ret = (DFQuestionQualifier) qualifierEntity.getQualifierClass()
                    .newInstance();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
        if (ret != null)
        {
            ret.setQuestion(question);
            ret.setQualifierEntity(qualifierEntity);
        }
        return ret;
    }

    public static DFQualifierEntity getQualifierTypeEntity(
            HttpServletRequest request)
    {
        DFQualifierEntity qualifierEntity = null;

        try
        {
            Class entityClass = ClassUtils
                    .loadClass(request.getParameter("dfQualifierEntityType"));

            Integer dfQualifierTypeId = Integer.valueOf(
                    request.getParameter("dfQualifierEntityId"));

            qualifierEntity = (DFQualifierEntity) PortalUtils.getHt()
                    .load(entityClass, dfQualifierTypeId);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return qualifierEntity;
    }

    /**
     * Use {@link DFQualificationChecker#getWhereClause} instead
     */
    @Deprecated
    public static String getQualifierQuery(List<Object[]> qualifiers, String alias)
    {
        String ret = "";
        for (Object[] qual : qualifiers)
        {
            String qualRet = getQuestionQualifierQuery(qual, alias);
            ret += qualRet;
        }
        return ret;
    }

    /**
     * Use
     * {@link DFQualificationChecker#getQualification}.{@link Qualification#getWhereClause
     * getWhereClause} instead
     */
    @Deprecated
    private static String getQuestionQualifierQuery(Object[] qual, String alias)
    {
        String ret = "";
        if (qual[0].equals(DFQuestion.TYPE_BOOLEAN))
        {
            boolean whatQualifies = "true".equals(qual[1]) ? true : false;
            ret = " and " + alias + "." + qual[4] + "="
                    + (whatQualifies ? "1" : "0");
        }
        else if (qual[0].equals(DFQuestion.TYPE_MULTI_CHOICE))
        {
            String qualifiedChoices = (String) qual[1];
            List<String> qChoices = qualifiedChoices != null
                    ? Arrays.asList(qualifiedChoices.split("\r\n"))
                    : new ArrayList<>();
            for (String choice : qChoices)
            {
                ret += " and " + alias + "." + qual[4] + " like '%^" + choice
                        + "^%'";
            }
        }
        else if (qual[0].equals(DFQuestion.TYPE_INTEGER)
                || qual[0].equals(DFQuestion.TYPE_FLOAT)
                || qual[0].equals(DFQuestion.TYPE_RATING))
        {
            String minQualifies = (String) qual[2];
            String maxQualifies = (String) qual[3];
            if ("max".equals(maxQualifies) && !"min".equals(minQualifies))
            {
                ret += " and " + alias + "." + qual[4] + ">=" + minQualifies;
            }
            else if ("min".equals(minQualifies) && !"max".equals(maxQualifies))
            {
                ret += " and " + alias + "." + qual[4] + "<=" + maxQualifies;
            }
            else if (!"min".equals(minQualifies) && !"max".equals(maxQualifies))
            {
                ret += " and " + alias + "." + qual[4] + " between " + minQualifies
                        + " and " + maxQualifies;
            }
        }
        else if (qual[0].equals(DFQuestion.TYPE_TEXT))
        {
            String whatQualifies = (String) qual[1];
            if (!StringUtils.isEmpty(whatQualifies))
            {
                ret += " and " + alias + "." + qual[4] + " = '" + whatQualifies
                        + "'";
            }
        }
        else if (qual[0].equals(DFQuestion.TYPE_SINGLE_CHOICE))
        {
            String qualifiedChoices = (String) qual[1];
            List<String> qChoices = qualifiedChoices != null
                    ? Arrays.asList(qualifiedChoices.split("\\|"))
                    : new ArrayList<>();
            ret += " and " + alias + "." + qual[4] + " in "
                    + DBUtils.buildInClauseWithQuotes(qChoices);
        }
        else if (qual[0].equals(DFQuestion.TYPE_DATE))
        {
            String minQualifies = (String) qual[2];
            String maxQualifies = (String) qual[3];
            boolean hasFromDate = !StringUtils.isEmpty(minQualifies) && DateUtils
                    .parseDate(minQualifies, DateUtils.DF_SHORT_DATE, null) != null;
            boolean hasToDate = !StringUtils.isEmpty(maxQualifies) && DateUtils
                    .parseDate(maxQualifies, DateUtils.DF_SHORT_DATE, null) != null;
            if (hasFromDate && hasToDate)
            {
                ret += " and " + alias + "." + qual[4] + " >= '" + minQualifies
                        + "' and " + alias + "." + qual[4] + " <= '" + maxQualifies
                        + "'";
            }
        }
        else if (qual[0].equals(DFQuestion.TYPE_FILE_UPLOAD))
        {
            ret += " and " + alias + "." + qual[4] + " is not null";
        }
        else
        {
            ret = "";
        }
        return ret;
    }

    public static void cloneQuestionsWithQualifiers(DFCategory category,
            DFCategory oldCategory, DFQualifierEntity newModelEntity)
    {
        List<Object[]> oldQuestions = PortalUtils.getHt().find(
                "select q, qq from DFQuestionQualifier qq right join qq.question as q where q.category=?",
                oldCategory);

        Class<? extends DFQuestionQualifier> qualifierClass = newModelEntity
                .getQualifierClass();

        String qualifierEntityFieldName = newModelEntity
                .getQualifierEntityFieldName();

        for (Object[] question : oldQuestions)
        {
            DFQuestion q = cloneQuestion(category, (DFQuestion) question[0]);

            DFQuestionQualifier dfqq = (DFQuestionQualifier) question[1];

            if (dfqq != null)
            {
                try
                {
                    DFQuestionQualifier qq = qualifierClass.newInstance();
                    PropertyUtils.setProperty(qq, qualifierEntityFieldName,
                            newModelEntity);
                    qq.setQuestion(q);
                    qq.setAcceptableValues(dfqq.getAcceptableValues());
                    qq.setMaxAcceptableValue(dfqq.getMaxAcceptableValue());
                    qq.setMinAcceptableValue(dfqq.getMinAcceptableValue());
                    PortalUtils.getHt().save(qq);
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void cloneQualifiers(List<DFQuestionQualifier> qualifiers,
            DFQualifierEntity newQualifierEntity)
    {
        for (DFQuestionQualifier dfqq : qualifiers)
        {
            try
            {
                DFQuestionQualifier qq = (DFQuestionQualifier) newQualifierEntity
                        .getQualifierClass().newInstance();
                PropertyUtils.setProperty(qq,
                        newQualifierEntity.getQualifierEntityFieldName(),
                        newQualifierEntity);
                qq.setQuestion(dfqq.getQuestion());
                qq.setAcceptableValues(dfqq.getAcceptableValues());
                qq.setMaxAcceptableValue(dfqq.getMaxAcceptableValue());
                qq.setMinAcceptableValue(dfqq.getMinAcceptableValue());
                PortalUtils.getHt().save(qq);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
    }

    public static DFQuestionQualifier getQuestionQualifier(
            HttpServletRequest request)
    {
        return getQuestionQualifier(getInteger(request, "dfQualifierId"));
    }

    public static DFQuestionQualifier getQuestionQualifier(
            Integer questionQualifierId)
    {
        DFQuestionQualifier questionQualifier = null;

        try
        {
            questionQualifier = (DFQuestionQualifier) PortalUtils.getHt()
                    .load(DFQuestionQualifier.class, questionQualifierId);
        }
        catch (Exception e)
        {
        }

        return questionQualifier;
    }

    public static void populateQualifierSupportedQuestionTypes(ModelAndView mv)
    {
        List<Integer> qualifierSupportedQuestionTypes = Lists.newArrayList(
                DFQuestion.TYPE_TEXT, DFQuestion.TYPE_INTEGER,
                DFQuestion.TYPE_MULTI_CHOICE, DFQuestion.TYPE_BOOLEAN,
                DFQuestion.TYPE_DATE, DFQuestion.TYPE_RATING,
                DFQuestion.TYPE_FILE_UPLOAD, DFQuestion.TYPE_SINGLE_CHOICE,
                DFQuestion.TYPE_FLOAT);
        mv.addObject("qualifierSupportedQuestionTypes",
                qualifierSupportedQuestionTypes);
    }

    public static void saveQuestion(DFQuestion question, DFModelEntity modelEntity,
            List<DFQuestion> otherQuestions, HttpServletRequest request)
            throws DFException
    {
        // Log this question save
        createQuestionLog(question, request);

        List<I18nLabel> errors = new ArrayList<>();

        if (question == null)
        {
            throw new RuntimeException("null Question");
        }

        question.setAdminOnly(request.getParameter("adminOnly") != null);
        question.setPreserveOrdering(
                request.getParameter("preserveOrdering") != null);

        question.setHeader(request.getParameter("header"));
        question.setFooter(request.getParameter("footer"));
        question.setToolTip(request.getParameter("toolTip"));

        question.setHeaderL2(request.getParameter("headerL2"));
        question.setFooterL2(request.getParameter("footerL2"));
        question.setToolTipL2(request.getParameter("toolTipL2"));

        question.setValidationRegEx(request.getParameter("validationRegEx"));
        question.setValidationSample(request.getParameter("validationSample"));

        question.setDefaultChoice(request.getParameter("defaultChoice"));

        String requestMappingKey = request.getParameter("mappingKey");
        String currentMappingKey = question.getMappingKey();
        boolean mappingKeyChanged = (!StringUtils.isEmpty(requestMappingKey)
                && !requestMappingKey.equals(currentMappingKey))
                || (!StringUtils.isEmpty(currentMappingKey)
                        && !currentMappingKey.equals(requestMappingKey));
        question.setMappingKey(requestMappingKey);
        question.setDefaultMappingKey(request.getParameter("mappingDefaultKey"));

        if (!question.isQuestionTextLocked())
        {
            question.setQuestionText(request.getParameter("questionText"));
            question.setQuestionTextL2(request.getParameter("questionTextL2"));
        }

        Integer type = question.getType();
        if (!question.isTypeLocked())
        {
            type = RequestUtils.getInteger(request, "type");
            List<Integer> allowedQuestionTypes = modelEntity
                    .getAllowedQuestionTypes();
            if (type == null)
            {
                errors.add(
                        new I18nLabel("i18n.Question2Helper.invalidQuestionType"));
            }
            else if (CollectionUtils.isNotEmpty(allowedQuestionTypes)
                    && !allowedQuestionTypes.contains(type))
            {
                errors.add(
                        new I18nLabel("i18n.DFHelper.Thisquesti5276625894490999"));
            }
            if (!errors.isEmpty())
            {
                throw new DFException(errors);
            }
        }

        if (!question.isAnswerFieldsSet() || question.getType() != type
                || mappingKeyChanged)
        {
            question.setType(type);
            question.setAnswerField1(null);
            question.setAnswerField1L2(null);
            question.setAnswerField2(null);
            question.setAnswerField3(null);
            question.setAnswerField4(null);
            question.setAnswerField5(null);

            updateAnswerMappings(question, otherQuestions);

            if (type == DFQuestion.TYPE_TOTAL_TRACKED_HOURS)
            {
                question.setType(DFQuestion.TYPE_FLOAT);
                question.setTypeLocked(true);
            }
            if (!StringUtils.isEmpty(question.getMappingKey()))
            {
                question.setAnswerField1(question.getMappingKey());
                if ("profileImage".equalsIgnoreCase(question.getMappingKey())
                        || "backgroundHeaderImage"
                                .equalsIgnoreCase(question.getMappingKey()))
                {
                    question.setAnswerField2(question.getMappingKey());
                }
            }
        }
        if (request.getParameter("siteMapping") != null)
        {
            createDFQuestionSiteMapping(question,
                    request.getParameter("siteMapping"));
        }

        int min = 0;
        int max = 0;
        boolean minMaxCheckExempt = false;
        int displayType = 0;
        boolean usingNA = false;
        String choices = null;
        int limitAnswer = 0;
        String treeNodes = null;
        boolean singleTreePath = false;
        boolean includeOther = false;
        String matrixColumns = null;
        String l2MatrixColumns = null;
        String matrixRows = null;
        String l2MatrixRows = null;
        long maxUploadSize = 0;

        switch (type)
        {
            case DFQuestion.TYPE_TEXT:
                question.setQuestionTextDefault(
                        request.getParameter("questionTextDefault"));
                question.setQuestionTextDefaultL2(
                        request.getParameter("questionTextDefaultL2"));
                break;

            case DFQuestion.TYPE_LARGE_TEXT:
                question.setQuestionLargeTextDefault(
                        request.getParameter("questionLargeTextDefault"));
                question.setQuestionLargeTextDefaultL2(
                        request.getParameter("questionLargeTextDefaultL2"));
                break;

            case DFQuestion.TYPE_BOOLEAN:
            case DFQuestion.TYPE_DATE:
            case DFQuestion.TYPE_STARTEND_DATE:
            case DFQuestion.TYPE_FLOAT:
            case DFQuestion.TYPE_SALARY:
            case DFQuestion.TYPE_SITE_VISIT:
            case DFQuestion.TYPE_TIME_COMMITMENT:
            case DFQuestion.TYPE_HYPERLINK:
            case DFQuestion.TYPE_TOTAL_TRACKED_HOURS:
                break;

            case DFQuestion.TYPE_INTEGER:
                try
                {
                    displayType = Integer
                            .parseInt(request.getParameter("displayType"));
                    minMaxCheckExempt = RequestUtils.getBooleanParameter(request,
                            "minMaxCheckExempt", false);
                    if (!minMaxCheckExempt)
                    {
                        min = Integer.parseInt(request.getParameter("numberMin"));
                        max = Integer.parseInt(request.getParameter("numberMax"));
                        if (displayType == 0 && Math.abs(max - min) > 100)
                        {
                            displayType = 1;
                        }
                        if (min > max)
                        {
                            errors.add(new I18nLabel(
                                    "i18n.Question2Helper.invalidMinMax"));
                        }
                    }
                    else
                    {
                        displayType = 1;
                    }
                }
                catch (Exception e)
                {
                    errors.add(new I18nLabel("i18n.Question2Helper.invalidMinMax"));
                }
                break;

            case DFQuestion.TYPE_MULTI_CHOICE:
                includeOther = !StringUtils
                        .isEmpty(request.getParameter("multiChoiceOther"));
                choices = request.getParameter("choices");
                limitAnswer = StringUtils
                        .isInteger(request.getParameter("limitAnswer"))
                                ? Integer.valueOf(request.getParameter("limitAnswer"))
                                        .intValue()
                                : 0;
                break;

            case DFQuestion.TYPE_SINGLE_CHOICE:
                includeOther = !StringUtils
                        .isEmpty(request.getParameter("multiChoiceOther"));
                choices = request.getParameter("choices");
                break;

            case DFQuestion.TYPE_FILE_UPLOAD:
                try
                {
                    maxUploadSize = Long
                            .parseLong(request.getParameter("maxUploadSize"));
                }
                catch (Exception e)
                {
                    errors.add(new I18nLabel(
                            "i18n.Question2Helper.invalidMaxUploadSize"));
                }
                break;

            case DFQuestion.TYPE_RATING:
                try
                {
                    min = Integer.parseInt(request.getParameter("ratingMin"));
                    max = Integer.parseInt(request.getParameter("ratingMax"));
                    if (min < 0 || min > max || Math.abs(max - min) > 100)
                    {
                        // remember we store NA as -1 when using question2
                        // framework
                        errors.add(new I18nLabel(
                                "i18n.Question2Helper.invalidMinMax"));
                    }
                }
                catch (Exception e)
                {
                    errors.add(new I18nLabel("i18n.Question2Helper.invalidMinMax"));
                }
                usingNA = request.getParameter("ratingUsingNA") != null;
                break;

            case DFQuestion.TYPE_TREE:
                treeNodes = request.getParameter("treeNodes");
                singleTreePath = request.getParameter("singleTreePath") != null;
                break;

            case DFQuestion.TYPE_MATRIX_MULTI:
                matrixColumns = request.getParameter("matrixMultiColumns");
                l2MatrixColumns = request.getParameter("l2MatrixMultiColumns");
                matrixRows = request.getParameter("matrixMultiRows");
                l2MatrixRows = request.getParameter("l2MatrixMultiRows");
                break;

            case DFQuestion.TYPE_MATRIX_SINGLE:
                matrixColumns = request.getParameter("matrixSingleColumns");
                l2MatrixColumns = request.getParameter("l2MatrixSingleColumns");
                matrixRows = request.getParameter("matrixSingleRows");
                l2MatrixRows = request.getParameter("l2MatrixSingleRows");
                break;
            case DFQuestion.TYPE_X_PER_Y:
                try
                {
                    choices = request.getParameter("yChoices");

                    min = Integer.parseInt(request.getParameter("xMin"));
                    max = Integer.parseInt(request.getParameter("xMax"));
                    if (min < 0 || min > max || Math.abs(max - min) > 100)
                    {
                        errors.add(new I18nLabel(
                                "i18n.Question2Helper.invalidMinMax"));
                    }
                }
                catch (Exception e)
                {
                    errors.add(new I18nLabel("i18n.Question2Helper.invalidMinMax"));
                }
                break;
            default:
                throw new RuntimeException("unknown question type: " + type);
        }

        question.setMax(max);
        question.setMin(min);
        question.setMinMaxCheckExempt(minMaxCheckExempt);
        question.setDisplayType(displayType);
        question.setUsingNA(usingNA);
        question.setChoices(choices);
        question.setLimitAnswer(limitAnswer);
        question.setIncludeOther(includeOther);
        question.setTreeNodes(treeNodes);
        question.setSingleTreePath(singleTreePath);
        question.setMatrixColumns(matrixColumns);
        question.setL2MatrixColumns(l2MatrixColumns);
        question.setMatrixRows(matrixRows);
        question.setL2MatrixRows(l2MatrixRows);
        question.setMaxUploadSize(maxUploadSize);

        List<DFQuestionRole> questionRoles = getRolesForQuestion(question);
        if (null == questionRoles || questionRoles.isEmpty())
        {
            questionRoles = new LinkedList<>();
            List<DFRole> roles = getRolesForModel(
                    Integer.toString(question.getCategory().getModel().getId()));
            for (DFRole role : roles)
            {
                DFQuestionRole questionRole = new DFQuestionRole();
                questionRole.setDfQuestion(question);
                questionRole.setDfRole(role);

                questionRoles.add(questionRole);
            }
        }

        for (DFQuestionRole questionRole : questionRoles)
        {
            questionRole.setCanRead(request.getParameter(
                    "canRead_" + questionRole.getDfRole().getId()) != null
                    || question.getCategory().getModel()
                            .isSimplifiedQuestionsAndQualifiers());
            questionRole.setCanWrite(request.getParameter(
                    "canWrite_" + questionRole.getDfRole().getId()) != null
                    || question.getCategory().getModel()
                            .isSimplifiedQuestionsAndQualifiers());
            questionRole.setCanSearch(request.getParameter(
                    "canSearch_" + questionRole.getDfRole().getId()) != null
                    || question.getCategory().getModel()
                            .isSimplifiedQuestionsAndQualifiers());
            questionRole.setCanShowInResults(request.getParameter(
                    "canShowInResults_" + questionRole.getDfRole().getId()) != null
                    || question.getCategory().getModel()
                            .isSimplifiedQuestionsAndQualifiers());
            questionRole.setCanShowInReports(request.getParameter(
                    "canShowInReports_" + questionRole.getDfRole().getId()) != null
                    || question.getCategory().getModel()
                            .isSimplifiedQuestionsAndQualifiers());
            questionRole.setRequired(request.getParameter(
                    "isRequired_" + questionRole.getDfRole().getId()) != null);

            questionRole.setDfQuestion(question);
            PortalUtils.getHt().saveOrUpdate(questionRole);
        }

        if (errors.size() == 0)
        {
            PortalUtils.getHt().saveOrUpdate(question);
        }
        else
        {
            throw new DFException(errors);
        }
    }

    public static void createDFQuestionSiteMapping(DFQuestion question,
            String siteMapping)
    {
        DeletionNodeHelper.deleteContentItems(DFQuestionSiteMapping.class,
                "dfQuestion = " + question.getId(), null);

        if (!StringUtils.isEmpty(siteMapping))
        {
            PortalUtils.getJt().execute(
                    "insert into df_question_site_mapping (siteMapping, dfQuestion) values ("
                            + PortalConfigHelper.getPortalConfig(siteMapping)
                                    .getId()
                            + ", " + question.getId() + ")");
        }
    }

    public static Optional<String> getDFSiteMappingField(DFModel model,
            String dfqsmOrbisKey)
    {
        return PortalUtils.getHt().findFirst(
                "select dfqsm.dfQuestion.answerField1 from DFQuestionSiteMapping dfqsm "
                        + " where dfqsm.siteMapping.orbisKey=? and dfqsm.dfQuestion.category.model=?",
                new Object[] { dfqsmOrbisKey, model });
    }

    public static List<OrbisHqlResultSet> getDfAnswerFieldToOrbisKeyMappings(
            DFModel dfModel)
    {
        StringBuilder hql = new StringBuilder();
        {
            hql.append(" SELECT q.answerField1, dfqsm.siteMapping.orbisKey ");
            hql.append(" FROM DFQuestionSiteMapping dfqsm ");
            hql.append(" JOIN dfqsm.dfQuestion q ");
            hql.append(" WHERE q.category.model.id=? ");
        }
        return PortalUtils.getHt().f(hql.toString(), dfModel.getId());
    }

    public static Optional<DFQuestion> getDFSiteMappingQuestion(DFModel model,
            String dfqsmOrbisKey)
    {
        return PortalUtils.getHt().findFirst(
                "select dfqsm.dfQuestion from DFQuestionSiteMapping dfqsm "
                        + " where dfqsm.siteMapping.orbisKey=? and dfqsm.dfQuestion.category.model=?",
                new Object[] { dfqsmOrbisKey, model });
    }

    public static Optional<String> getDFSiteMappingField(DFModel model,
            String dfqsmOrbisKey, int dfqsmType)
    {
        return PortalUtils.getHt().findFirst(
                "select dfsqm.dfQuestion.answerField1 from DFQuestionSiteMapping dfsqm where dfsqm.dfQuestion.category.model=? and dfsqm.siteMapping.orbisKey=? and dfsqm.dfQuestion.type=? ",
                new Object[] { model, dfqsmOrbisKey, dfqsmType });
    }

    /**
     * If there is no question with the provided DFQSM, an empty DFAnswerFields will
     * be returned.
     */
    public static Map<String, DFAnswerFields> getAnswerFieldsByDFQSM(DFModel model,
            String... dfqsmOrbisKeys)
    {
        QueryBuilder q = new QueryBuilder();
        q.append(
                " select pc.orbisKey, q.answerField1, q.answerField1L2, q.answerField2, q.answerField3, q.answerField4, q.answerField5");
        q.append(" from df_question_site_mapping dfqsm");
        q.append(" join portal_config pc on pc.id=dfqsm.siteMapping");
        q.append(" join df_question q on q.id=dfqsm.dfQuestion");
        q.append(" join df_category c on c.id=q.category ");
        q.append(" where pc.orbisKey in "
                + DBUtils.buildInClauseWithQuotes(dfqsmOrbisKeys));
        q.append(" and c.model=?", model.getId());

        Map<String, DFAnswerFields> ret = new HashMap<>();

        PortalUtils.getJt().query(q, rs -> {

            while (rs.next())
            {
                DFAnswerFields fields = new DFAnswerFields(
                        rs.getString("answerField1"),
                        rs.getString("answerField1L2"),
                        rs.getString("answerField2"), rs.getString("answerField3"),
                        rs.getString("answerField4"), rs.getString("answerField5"));

                ret.putIfAbsent(rs.getString("orbisKey"), fields);
            }

            return null;
        });

        for (String dfqsm : dfqsmOrbisKeys)
        {
            ret.putIfAbsent(dfqsm, new DFAnswerFields());
        }

        return ret;
    }

    public static Map<String, String> getAnswerFieldsByDFQSM(int dfModelId,
            String... dfqsmOrbisKeys)
    {
        QueryBuilder q = new QueryBuilder();
        q.append(" select pc.orbisKey, q.answerField1");
        q.append(" from df_question_site_mapping dfqsm");
        q.append(" join portal_config pc on pc.id=dfqsm.siteMapping");
        q.append(" join df_question q on q.id=dfqsm.dfQuestion");
        q.append(" join df_category c on c.id=q.category ");
        q.append(" where pc.orbisKey in "
                + DBUtils.buildInClauseWithQuotes(dfqsmOrbisKeys));
        q.append(" and c.model=?", dfModelId);

        return PortalUtils.getJt().query(q,
                new MapExtractor<String, String>("orbisKey", "answerField1"));
    }

    public static <T> T getDFSiteMappingAnswer(DFAnswerEntity answerEntity,
            String dfqsmOrbisKey)
    {
        return getDFSiteMappingField(answerEntity.getDfModel(), dfqsmOrbisKey)
                .map(LambdaExceptionUtil
                        .rethrowFunction(answerField -> (T) PropertyUtils
                                .getProperty(answerEntity, answerField)))
                .orElse(null);
    }

    public static List<String> getUsedAnswersField(DFQuestion question,
            List<DFQuestion> otherQuestions)
    {
        List<String> usedAnswerFields = new ArrayList<>();

        if (otherQuestions != null && !otherQuestions.isEmpty())
        {
            // DETERMINE LIST OF 'USED' ANSWER FIELDS (FROM OTHER
            // QUESTIONS)...
            for (DFQuestion q : otherQuestions)
            {

                if (question.getId() != null && question.getId().equals(q.getId()))
                {
                    continue;
                }

                usedAnswerFields.add(q.getAnswerField1());
                usedAnswerFields.add(q.getAnswerField1L2());
                usedAnswerFields.add(q.getAnswerField2());
                usedAnswerFields.add(q.getAnswerField3());
                usedAnswerFields.add(q.getAnswerField4());
                usedAnswerFields.add(q.getAnswerField5());
            }
        }
        return usedAnswerFields;

    }

    public static void updateAnswerMappings(DFQuestion question,
            List<DFQuestion> otherQuestions) throws DFException
    {
        if (question != null)
        {
            List<String> usedAnswerFields = getUsedAnswersField(question,
                    otherQuestions);

            // SET 'UNUSED' ANSWER FIELD(S) TO QUESTION...
            String answerField = null;
            switch (question.getType())
            {
                case DFQuestion.TYPE_TEXT:
                case DFQuestion.TYPE_SINGLE_CHOICE:
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "s" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            break;
                        }
                    }
                    break;

                case DFQuestion.TYPE_FLOAT:
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "f" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            break;
                        }
                    }
                    break;
                case DFQuestion.TYPE_INTEGER:
                case DFQuestion.TYPE_RATING:
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "i" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            break;
                        }
                    }
                    break;

                case DFQuestion.TYPE_BOOLEAN:
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "b" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            break;
                        }
                    }
                    break;

                case DFQuestion.TYPE_DATE:
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "d" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            break;
                        }
                    }
                    break;
                case DFQuestion.TYPE_STARTEND_DATE:
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "d" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "d" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField2(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }
                    break;
                case DFQuestion.TYPE_MULTI_CHOICE:
                case DFQuestion.TYPE_LARGE_TEXT:
                case DFQuestion.TYPE_TREE:
                case DFQuestion.TYPE_MATRIX_MULTI:
                case DFQuestion.TYPE_MATRIX_SINGLE:
                    int maxTFields = getMaxPermittedLargeTextFields();
                    for (int i = 1; i <= maxTFields; i++)
                    {
                        answerField = "t" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            break;
                        }
                    }
                    break;

                case DFQuestion.TYPE_FILE_UPLOAD:
                    // answerField1 will contain the "File Name"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "s" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }

                    // answerField2 will contain the "File URL"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "s" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField2(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }
                    break;

                case DFQuestion.TYPE_SALARY:
                    // answerField1 will contain the "Salary Amount"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "f" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }

                    // answerField2 will contain the "Frequency"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "i" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField2(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }

                    // answerField3 will contain the "Hours per week"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "f" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField3(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }
                    // answerField4 will contain the "Not Paid"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "b" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField4(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }
                    break;

                case DFQuestion.TYPE_SITE_VISIT:
                    // answerField1 will contain the "Site Visit"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "b" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }

                    // answerField2 will contain the "Date"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "d" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField2(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }

                    // answerField3 will contain the "Who"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "s" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField3(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }
                    // answerField4 will contain the "Method"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "i" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField4(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }
                    // answerField5 will contain the "location"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "s" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField5(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }
                    break;

                case DFQuestion.TYPE_X_PER_Y:
                    // answerField1 will contain the "x"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "i" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            break;
                        }
                    }

                    // answerField2 will contain the "y"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "s" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField2(answerField);
                            break;
                        }
                    }
                    break;

                case DFQuestion.TYPE_TIME_COMMITMENT:
                    // answerField1 will contain the hours
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "i" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            break;
                        }
                    }

                    // answerField2 will contain the period
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "s" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField2(answerField);
                            break;
                        }
                    }
                    break;

                case DFQuestion.TYPE_HYPERLINK:
                    // answerField1 will contain the "URL"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "s" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField1(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }

                    // answerField2 will contain the "Link Text"
                    for (int i = 1; i <= 100; i++)
                    {
                        answerField = "s" + i;
                        if (!usedAnswerFields.contains(answerField))
                        {
                            question.setAnswerField2(answerField);
                            usedAnswerFields.add(answerField);
                            break;
                        }
                    }
                    break;
                case DFQuestion.TYPE_TOTAL_TRACKED_HOURS:
                    // answerField1 will map to totalTrackedHours
                    question.setAnswerField1("totalTrackedHours");
                    break;

                default:
                    throw new RuntimeException("Unknown question type");
            }

            if (!question.isAnswerFieldsSet())
            {
                throw new DFException(new I18nLabel(
                        "i18n.Question2Helper.questionCapacityExceeded"));
            }
        }
    }

    /**
     * Binds the answers to a df model from a request. Calls
     * bindAnswers(DFModelEntity modelEntity, DFAnswerEntity answerEntity,
     * HttpServletRequest request, UserDetailsImpl userAnswering, boolean save,
     * Map<Integer, Integer> forceReadHiddenMap) with a null forceReadHiddenMap
     *
     * @param modelEntity
     *            the DFModelEntity for this DFModel
     * @param answerEntity
     *            the DFAnswerEntity to be updated
     * @param request
     *            the request with the answers
     * @param userAnswering
     *            the UserDetailsImpl that is running the request. Usually
     *            PortalUtils.getUserLoggedIn();
     * @param save
     *            whether to save the answerEntity after filling out the fields or
     *            leave the changes unpersisted
     */
    public static void bindAnswers(DFModelEntity modelEntity,
            DFAnswerEntity answerEntity, HttpServletRequest request,
            UserDetailsImpl userAnswering, boolean save)
    {

        bindAnswers(modelEntity, answerEntity, request, userAnswering, save, null,
                true);
    }

    public static void bindAnswers(DFModelEntity modelEntity,
            DFAnswerEntity answerEntity, HttpServletRequest request,
            UserDetailsImpl userAnswering, boolean save, ValidationData valData)
    {

        bindAnswers(modelEntity, answerEntity, request, userAnswering, save, null,
                true, valData);
    }

    public static void bindAnswers(DFModelEntity modelEntity,
            DFAnswerEntity answerEntity, HttpServletRequest request,
            UserDetailsImpl userAnswering, boolean save,
            Map<Integer, Boolean[]> forceLowerPermissionsMap,
            boolean resetForEmptyString)
    {

        ValidationData valData = null;
        bindAnswers(modelEntity, answerEntity, request, userAnswering, save,
                forceLowerPermissionsMap, resetForEmptyString, valData);

    }

    /**
     * Binds the answers to a df model from a request
     *
     * @param modelEntity
     *            the DFModelEntity for this DFModel
     * @param answerEntity
     *            the DFAnswerEntity to be updated
     * @param request
     *            the request with the answers
     * @param userAnswering
     *            the UserDetailsImpl that is running the request. Usually
     *            PortalUtils.getUserLoggedIn();
     * @param save
     *            whether to save the answerEntity after filling out the fields or
     *            leave the changes unpersisted
     * @param resetForEmptyString
     *            TODO
     * @param forceReadHiddenMap
     *            A map to define questions that are read only or hidden. Will
     *            override the write permissions granted by DFRole. Used so that a
     *            question that should be editable that is forced to have different
     *            functionality does not have its value wiped out since it is not
     *            included in the request. This should be the same map that is
     *            provided to the populateModel method that was called to generate
     *            this form. If a question is not in this map it will not be
     *            writable
     * @param valData
     *            container for errors, means of server side validation in case
     *            client side validation fails
     *
     */
    public static void bindAnswers(DFModelEntity modelEntity,
            DFAnswerEntity answerEntity, HttpServletRequest request,
            UserDetailsImpl userAnswering, boolean save,
            Map<Integer, Boolean[]> forceLowerPermissionsMap,
            boolean resetForEmptyString, ValidationData valData)
    {
        if (null == modelEntity || null == modelEntity.getDFModel()
                || null == modelEntity.getDFModel().getId() || null == answerEntity
                || null == request)
        {
            throw new IllegalArgumentException();
        }

        DFModel model = modelEntity.getDFModel();
        answerEntity.setDfModel(model);
        DFRole role = DFHelper.getRoleInModel(modelEntity.getDFModel(),
                userAnswering);
        List<DFQuestion> questions = DFHelper.getCanWriteQuestions(role,
                userAnswering, forceLowerPermissionsMap);
        int dfQuestionNotToWrite = RequestUtils.getIntParameter(request,
                "dfQuestionNotToWrite", -1);
        questions.removeIf(q -> q.getId() == dfQuestionNotToWrite);
        populateRolesForQuestions(questions);
        for (DFQuestion q : questions)
        {
            if (StringUtils.isEmpty(request.getParameter("saveOnCheckboxes"))
                    || (Boolean.valueOf(request.getParameter("saveOnCheckboxes"))
                            && !StringUtils.isEmpty(request
                                    .getParameter("editQuestion" + q.getId()))))
            {
                if (!StringUtils.isEmpty(q.getMappingKey()) && modelEntity
                        .getFieldMappings().containsKey(q.getMappingKey()))
                {
                    q.setAnswerField1(q.getMappingKey());
                    PortalUtils.getHt().saveOrUpdate(q);
                }

                q.setRole(role);
                bindAnswer(q, answerEntity, request, resetForEmptyString);
            }
        }
        model.getHiddenQuestions().clear();
        updateDependencyMap(model, answerEntity, false);
        for (DFQuestion q : questions)
        {
            if ((q.getType() == DFQuestion.TYPE_LARGE_TEXT
                    || q.getType() == DFQuestion.TYPE_TEXT) && q.isRequired()
                    && !model.getHiddenQuestions().contains(q.getId())
                    && valData != null)
            {
                Object fieldValue = null;
                try
                {
                    fieldValue = PropertyUtils.getProperty(answerEntity,
                            q.getAnswerField1());
                }
                catch (Exception e)
                {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
                if (fieldValue == null)
                {
                    valData.addPageError(new I18nLabel(
                            "i18n.AcrmRegistrationAnswer.isrequired3995692370206403",
                            q.getQuestionText()));
                }
            }
        }
        if (answerEntity instanceof DFAddressedAnswerEntity)
        {
            ((DFAddressedAnswerEntity) answerEntity).populateAddressCoord();
        }
        if (save && (valData == null || valData.getErrorCount() == 0))
        {
            answerEntity.setUpdatedBy(userAnswering);
            answerEntity.setDateUpdated(new Date());

            PortalUtils.getHt().saveOrUpdate(answerEntity);
            List<String> cascadeSaves = modelEntity.getCascadeSaves();
            for (String beanPath : cascadeSaves)
            {
                try
                {
                    PortalUtils.getHt().saveOrUpdate(
                            (Persistable) PropertyUtils.getProperty(answerEntity, beanPath));
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * This method will take two DFAnswerEntities and attempt to use the
     * DFQuestionSiteMapping join table to find questions with the same site
     * mapping. These fields will be copied over if possible, or left as the default
     * value
     *
     * @param sourceEntity
     *            the DFAnswerEntity that fields will be pulled from
     * @param destEntity
     *            the new DFAnswerEntity to populate
     */
    public static void copyMappedFieldsBetweenEntities(DFAnswerEntity sourceEntity,
            DFAnswerEntity destEntity)
    {
        List<OverridableFieldsMapping> questionMappings = getQuestionMappedFieldsToCopy(
                sourceEntity, destEntity);
        for (OverridableFieldsMapping mapping : questionMappings)
        {
            ContentItemHelper.copyFieldFromObjectToObject(sourceEntity,
                    mapping.getSourceAnswerField(), destEntity,
                    mapping.getTargetAnswerField());
        }
    }

    /**
     * This method takes two DFAnswerEntities and attempt to use the
     * DFQuestionSiteMapping join table to find questions with the same site
     * mapping. Than it finds all fields of destination entity that will be
     * overridden using {@link #copyMappedFieldsBetweenEntities}
     *
     * @param sourceEntity
     *            the DFAnswerEntity that fields will be pulled from
     * @param destEntity
     *            the DFAnswerEntity to populate
     * @return fields of destination entity that will be overridden after copying
     */
    public static List<String> getOverridableMappedFields(
            DFAnswerEntity sourceEntity, DFAnswerEntity destEntity)
    {
        return getOverridableMappedFields(sourceEntity,
                DFHelper.getQuestionMappedFieldsToCopy(sourceEntity, destEntity));
    }

    /**
     * This method takes UserDetailsImpl and DFAnswerEntity and attempt to use the
     * DFQuestionSiteMapping join table to find questions with the same site
     * mapping. Than it finds all fields of destination entity that will be
     * overridden after copying using {@link #copyMappedFieldsBetweenEntities}
     *
     * @param user
     *            the UserDetailsImpl that fields will be pulled from
     * @param destEntity
     *            the DFAnswerEntity to populate
     * @return fields of destination entity that will be overridden after copying
     */
    public static List<String> getOverridableMappedFields(UserDetailsImpl user,
            DFAnswerEntity destEntity)
    {
        return getOverridableMappedFields(user, AcrmRegistrationQuestionHelper
                .getAcrmReqQuestionSiteMapping(user, destEntity));
    }

    private static List<String> getOverridableMappedFields(Object sourceEntity,
            List<OverridableFieldsMapping> mappings)
    {
        return mappings.stream() //
                .filter(mapping -> com.orbis.utils.PropertyUtils.getPropertyQuietly(
                        sourceEntity, mapping.getSourceAnswerField()) != null)
                .map(OverridableFieldsMapping::getTargetAnswerField)
                .collect(Collectors.toList());
    }

    private static List<OverridableFieldsMapping> getQuestionMappedFieldsToCopy(
            DFAnswerEntity sourceEntity, DFAnswerEntity destEntity)
    {
        return PortalUtils.getHt().<Object[]> findAndStream(
                "select q1.answerField1, q2.answerField1, q1.answerField1L2, "
                        + " q2.answerField1L2, q1.answerField2, q2.answerField2, "
                        + " q1.answerField3, q2.answerField3, q1.answerField4, "
                        + " q2.answerField4, q1.answerField5, q2.answerField5 "
                        + " from DFQuestion q1, DFQuestion q2 "
                        + " where q1.category.model.id=? and q2.category.model.id=? "
                        + " and exists (select qsm1.id from DFQuestionSiteMapping qsm1, DFQuestionSiteMapping qsm2 "
                        + " where qsm1.dfQuestion.id=q1.id and qsm2.dfQuestion.id=q2.id and qsm1.siteMapping.id=qsm2.siteMapping.id) ",
                new Object[] { sourceEntity.getDfModel().getId(),
                        destEntity.getDfModel().getId() })
                .flatMap(mapping -> Stream.of(
                        new OverridableFieldsMapping((String) mapping[0],
                                (String) mapping[1]),
                        new OverridableFieldsMapping((String) mapping[2],
                                (String) mapping[3]),
                        new OverridableFieldsMapping((String) mapping[4],
                                (String) mapping[5]),
                        new OverridableFieldsMapping((String) mapping[6],
                                (String) mapping[7]),
                        new OverridableFieldsMapping((String) mapping[8],
                                (String) mapping[9]),
                        new OverridableFieldsMapping((String) mapping[10],
                                (String) mapping[11])))
                .filter(m -> StringUtils.isNotEmpty(m.getSourceAnswerField(),
                        m.getTargetAnswerField()))
                .collect(Collectors.toList());
    }

    private static List<DFQuestion> getCanWriteQuestions(DFRole role,
            UserDetailsImpl user, Map<Integer, Boolean[]> forceLowerPermissionsMap)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();

        List questions = new ArrayList();
        if (role != null && role.getModel().isSimplifiedQuestionsAndQualifiers())
        {
            questions = ht.find(
                    "select q from DFQuestion q join q.category where q.category.model=? ",
                    role.getModel());
        }
        if (forceLowerPermissionsMap != null && !forceLowerPermissionsMap.isEmpty())
        {
            // find all questions where the question is in the map with an index
            // 1 == true because this is the write index
            questions = ht.find(
                    "select qr.dfQuestion from DFQuestionRole qr join qr.dfQuestion where qr.dfRole=? and qr.canWrite=true "
                            + "and qr.dfQuestion.category is not null and qr.dfQuestion.id in "
                            + DBUtils.buildInClause(
                                    forceLowerPermissionsMap.entrySet().stream()
                                            .filter(o -> ArrayUtils.getValueAtIndex(
                                                    o.getValue(), 1, false))
                                            .map(o -> o.getKey())
                                            .collect(Collectors.toList())),
                    new Object[] { role });
        }
        else if (user != null)
        {
            questions = ht.find(
                    "select qr.dfQuestion from DFQuestionRole qr join qr.dfQuestion where qr.dfRole=? and qr.canWrite=true "
                            + "and qr.dfQuestion.category is not null",
                    new Object[] { role });
        }
        else if (role != null)
        {
            questions = ht.find(
                    "select qr.dfQuestion from DFQuestionRole qr join qr.dfRole join qr.dfQuestion join qr.dfQuestion.category where qr.dfRole.publicRole=true and qr.canWrite=true "
                            + "and qr.dfQuestion.category.model=?",
                    role.getModel());
        }

        return questions;
    }

    public static DFQuestionQualifier getQualifierForSimplifiedQuestion(
            DFQuestion question)
    {
        List qualifiers = PortalUtils.getHt().find(
                "from DFQuestionQualifier qq where qq.question = ?", question);

        DFQuestionQualifier qualifier = null;
        if (qualifiers.size() >= 1)
        {
            qualifier = (DFQuestionQualifier) qualifiers.get(0);
        }
        return qualifier;
    }

    public static List<DFQuestion> getModelQuestions(DFModel model)
    {
        List<DFQuestion> questions = PortalUtils.getHt()
                .find("from DFQuestion q where q.category.model=?", model);
        return questions;
    }

    public static void bindAnswer(DFQuestion question, DFAnswerEntity answerEntity,
            HttpServletRequest request)
    {
        bindAnswer(question, answerEntity, request, true);
    }

    public static Object bindAnswer(DFQuestion question,
            DFAnswerEntity answerEntity, HttpServletRequest request,
            boolean resetForEmptyString)
    {
        if (question == null || answerEntity == null || request == null)
        {
            throw new IllegalArgumentException();
        }
        Object rtn = null;
        switch (question.getType())
        {
            case DFQuestion.TYPE_TEXT:
            case DFQuestion.TYPE_LARGE_TEXT:
                rtn = bindAnswer_Text(request, answerEntity, question,
                        resetForEmptyString);
                break;

            case DFQuestion.TYPE_INTEGER:
                bindAnswer_Integer(request, answerEntity, question,
                        resetForEmptyString);
                break;

            case DFQuestion.TYPE_RATING:
                bindAnswer_Rating(request, answerEntity, question,
                        resetForEmptyString);
                break;

            case DFQuestion.TYPE_BOOLEAN:
                bindAnswer_Boolean(request, answerEntity, question,
                        resetForEmptyString);
                break;

            case DFQuestion.TYPE_DATE:
                bindAnswer_Date(request, answerEntity, question,
                        resetForEmptyString);
                break;

            case DFQuestion.TYPE_MULTI_CHOICE:
                bindAnswer_ChoiceMulti(request, answerEntity, question,
                        resetForEmptyString);
                break;

            case DFQuestion.TYPE_SINGLE_CHOICE:
                bindAnswer_ChoiceSingle(request, answerEntity, question,
                        resetForEmptyString);
                break;

            case DFQuestion.TYPE_FILE_UPLOAD:
                bindAnswer_FileUpload(question, answerEntity, request);
                break;

            case DFQuestion.TYPE_TREE:
                bindAnswer_Tree(request, answerEntity, question,
                        resetForEmptyString);
                break;

            case DFQuestion.TYPE_MATRIX_MULTI:
                bindAnswer_MatrixMulti(request, answerEntity, question,
                        resetForEmptyString);
                break;

            case DFQuestion.TYPE_MATRIX_SINGLE:
                bindAnswer_MatrixSingle(request, answerEntity, question,
                        resetForEmptyString);
                break;

            case DFQuestion.TYPE_FLOAT:
                bindAnswer_Float(request, answerEntity, question,
                        resetForEmptyString);
                break;

            case DFQuestion.TYPE_STARTEND_DATE:
                bindAnswer_StartEnd(request, answerEntity, question,
                        resetForEmptyString);
                break;
            case DFQuestion.TYPE_SITE_VISIT:
                bindAnswer_SiteVisit(request, answerEntity, question,
                        resetForEmptyString);
                break;
            case DFQuestion.TYPE_SALARY:
                bindAnswer_Salary(request, answerEntity, question,
                        resetForEmptyString);
                break;
            case DFQuestion.TYPE_X_PER_Y:
                bindAnswer_XPerY(request, answerEntity, question,
                        resetForEmptyString);
                break;
            case DFQuestion.TYPE_TIME_COMMITMENT:
                bindAnswer_WeeklyTimeCommitment(request, answerEntity, question,
                        resetForEmptyString);
                break;
            case DFQuestion.TYPE_HYPERLINK:
                bindAnswer_Hyperlink(request, answerEntity, question);
                break;
            default:
                throw new RuntimeException("Unknown question type");
        }

        return rtn;
    }

    // private static void bindFieldMapping () {}

    @SuppressWarnings("unused")
    private static void bindAnswer_Hyperlink(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question)
    {
        String url = request
                .getParameter("question_" + question.getId().toString() + "_url");
        String linkText = request.getParameter(
                "question_" + question.getId().toString() + "_linkText");

        boolean isValidUrl = false;
        if (!StringUtils.isEmpty(url))
        {
            try
            {
                new URL(url);
                isValidUrl = true;
            }
            catch (MalformedURLException e)
            {
                e.printStackTrace();
            }
        }
        else
        {
            url = null;
            isValidUrl = true;
        }

        if (isValidUrl)
        {
            bindValue(answerEntity, question.getAnswerField1(), url);
        }

        if (isValidUrl || !StringUtils.isEmpty(linkText))
        {
            if (linkText == null)
            {
                linkText = "";
            }
            bindValue(answerEntity, question.getAnswerField2(), linkText);
        }
    }

    private static void bindAnswer_XPerY(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        String number = request
                .getParameter("question_" + question.getId().toString() + "_x");
        if (StringUtils.isInteger(number))
        {
            bindValue(answerEntity, question.getAnswerField1(),
                    Integer.valueOf(number));
        }
        else if (resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField1(), Integer.valueOf(0));
        }

        if (!StringUtils.isEmpty(request
                .getParameter("question_" + question.getId().toString() + "_y")))
        {
            bindValue(answerEntity, question.getAnswerField2(),
                    request.getParameter(
                            "question_" + question.getId().toString() + "_y"));
        }
    }

    private static void bindAnswer_WeeklyTimeCommitment(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        String number = request
                .getParameter("question_" + question.getId().toString() + "_hours");
        if (StringUtils.isInteger(number))
        {
            bindValue(answerEntity, question.getAnswerField1(),
                    Integer.valueOf(number));
        }
        else if (resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField1(), Integer.valueOf(0));
        }

        if (!StringUtils.isEmpty(request.getParameter(
                "question_" + question.getId().toString() + "_period")))
        {
            bindValue(answerEntity, question.getAnswerField2(),
                    request.getParameter(
                            "question_" + question.getId().toString() + "_period"));
        }
    }

    private static void bindAnswer_Boolean(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        String str = request
                .getParameter("question_" + question.getId().toString());
        Boolean b = getBooleanValue(str);
        if (!StringUtils.isEmpty(str) || resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField1(), b);
        }
    }

    private static void bindAnswer_Date(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        Date date = DateUtils.getDatepickerVal(request,
                "question_" + question.getId().toString());
        if (date != null || resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField1(), date);
        }
    }

    private static void bindAnswer_StartEnd(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        Date startDate = DateUtils.getDatepickerVal(request,
                "question_" + question.getId().toString() + "_startDate");
        Date endDate = DateUtils.getDatepickerVal(request,
                "question_" + question.getId().toString() + "_endDate");
        if (startDate != null || resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField1(), startDate);
        }
        if (endDate != null || resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField2(), endDate);
        }
    }

    private static void bindAnswer_Float(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        Locale locale = PortalUtils.getLocale(request);
        boolean isL2 = LocaleUtils.isL2(locale);

        String input = request
                .getParameter("question_" + question.getId().toString());

        if (input != null)
        {
            if (input.contains(".") && isL2)
            {
                input = input.replace(".", ",");
            }
            Double number = LocaleUtils.parseDouble(input, locale);
            bindValue(answerEntity, question.getAnswerField1(), number);
        }
        else if (resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField1(), 0.0);
        }
    }

    private static void bindAnswer_Integer(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        String number = request
                .getParameter("question_" + question.getId().toString());
        if (StringUtils.isInteger(number))
        {
            bindValue(answerEntity, question.getAnswerField1(),
                    Integer.valueOf(number));
        }
        else if (StringUtils.isNumber(number))
        {
            bindValue(answerEntity, question.getAnswerField1(),
                    Integer.valueOf(Math.round(new Float(number))));
        }
        else if (resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField1(), Integer.valueOf(0));
        }
    }

    private static String bindAnswer_Text(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        String text = request
                .getParameter("question_" + question.getId().toString());
        String textL2 = request
                .getParameter("question_" + question.getId().toString() + "_l2");
        if (!StringUtils.isEmpty(text))
        {
            bindValue(answerEntity, question.getAnswerField1(), text);
            if (!StringUtils.isEmpty(textL2))
            {
                bindValue(answerEntity, question.getAnswerField1L2(), textL2);
            }
        }
        else if (resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField1(), null);
            text = null;
        }
        return text;
    }

    private static void bindAnswer_Rating(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        String rating = request
                .getParameter("question_" + question.getId().toString());
        Integer ratingAsInt = (StringUtils.isInteger(rating) ? Integer.valueOf(rating)
                : null);
        if (ratingAsInt != null && (ratingAsInt <= question.getMax()
                && ratingAsInt >= question.getMin()
                || ratingAsInt == DFQuestion.RATING_NA_DB_VALUE))
        {
            bindValue(answerEntity, question.getAnswerField1(), ratingAsInt);
        }
        else if (resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField1(), null);
        }
    }

    /**
     * DF form JSPs in the spiral robot folder will return a hidden attribute called
     * isSpiralRobot with a boolean value attached to it. Use this to determine if
     * certain values coming in should be treated differently
     * 
     * @param request
     * @return true if the isSpiralRobot value is found and is true, otherwise false
     */
    private static boolean isSpiralRobotForm(HttpServletRequest request)
    {
        return RequestUtils.getBooleanParameter(request, "isSpiralRobot", false);
    }

    private static void bindAnswer_ChoiceMulti(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        boolean isSpiralRobot = isSpiralRobotForm(request);

        String questionId = question.getId().toString();
        String[] choices = request.getParameterValues("question_" + questionId);

        if (isSpiralRobot)
        {
            // The other value comes in via a textbox on the spiral robot side
            String otherVal = request.getParameter("other_" + questionId);

            if (!StringUtils.isEmpty(otherVal))
            {
                // Append the textbox value to the choices array so that the
                // legacy code still works. The other checkbox doesn't show up in
                // the choices array, so it could be null at this point

                List<String> choicesList = choices != null
                        ? Lists.newArrayList(choices)
                        : new ArrayList<>();
                choicesList.add(otherVal);
                choices = choicesList.toArray(new String[0]);
            }
        }

        if (choices != null && choices.length > 0)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < choices.length; i++)
            {
                sb.append("^").append(choices[i]);
            }

            if (StringUtils.isEmpty(sb.toString()))
            {
                bindValue(answerEntity, question.getAnswerField1(), null);
            }
            else
            {
                sb.append("^");
                bindValue(answerEntity, question.getAnswerField1(), sb.toString());
            }
        }
        else if (resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField1(), null);
        }
    }

    private static void bindAnswer_ChoiceSingle(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        if (StringUtils.isEmpty(
                request.getParameter("question_" + question.getId().toString()))
                && StringUtils.isEmpty(request
                        .getParameter("other_" + question.getId().toString())))
        {
            if (resetForEmptyString)
            {
                bindValue(answerEntity, question.getAnswerField1(),
                        StringUtils.getBooleanValue(request.getParameter(
                                "otherSelected_" + question.getId().toString()))
                                        ? "Other"
                                        : null);
            }
        }
        else if (!StringUtils.isEmpty(
                request.getParameter("other_" + question.getId().toString())))
        {
            bindValue(answerEntity, question.getAnswerField1(),
                    request.getParameter("other_" + question.getId().toString()));

        }
        else
        {
            bindValue(answerEntity, question.getAnswerField1(), request
                    .getParameter("question_" + question.getId().toString()));
        }
    }

    private static void bindAnswer_Tree(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        String treeAnswer = request
                .getParameter("question_" + question.getId().toString());
        if (!StringUtils.isEmpty(treeAnswer))
        {
            bindValue(answerEntity, question.getAnswerField1(), treeAnswer);
        }
        else if (resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField1(), null);
        }
    }

    private static void bindAnswer_MatrixSingle(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        Map<String, Map<String, Boolean>> matrixMap = getMatrixMap(answerEntity,
                question, PortalUtils.getDefaultLocale());

        Object[] matrixSet = matrixMap.entrySet().toArray();

        StringBuilder sb = new StringBuilder();

        for (int rowIndex = 0; rowIndex < matrixSet.length; rowIndex++)
        {
            if (!StringUtils.isEmpty(request.getParameter(
                    "question_" + question.getId().toString() + "_" + rowIndex)))
            {
                int colIndex = Integer.valueOf(request.getParameter("question_"
                        + question.getId().toString() + "_" + rowIndex));

                Entry rowEntry = (Entry) matrixSet[rowIndex];
                String rowLabel = (String) rowEntry.getKey();
                Map<String, Boolean> subMap = (Map<String, Boolean>) rowEntry
                        .getValue();
                Object[] subMapSet = subMap.entrySet().toArray();
                Entry subEntry = (Entry) subMapSet[colIndex];
                String colLabel = (String) subEntry.getKey();
                sb.append("^").append(rowLabel + "~" + colLabel);
            }
        }

        if (!StringUtils.isEmpty(sb.toString()))
        {
            sb.append("^");
            bindValue(answerEntity, question.getAnswerField1(), sb.toString());
        }
        else if (resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField1(), null);
        }
    }

    private static void bindAnswer_MatrixMulti(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        String multiMatrixQuestion = request
                .getParameter("question_" + question.getId().toString());

        if (!StringUtils.isEmpty(multiMatrixQuestion))
        {

            String[] multiMatrix = multiMatrixQuestion.split(",");

            Map<String, Map<String, Boolean>> matrixMap = getMatrixMap(answerEntity,
                    question, PortalUtils.getDefaultLocale());

            Object[] matrixSet = matrixMap.entrySet().toArray();

            StringBuilder sb = new StringBuilder();

            for (int i = 0; i < multiMatrix.length; i++)
            {
                String value = multiMatrix[i];

                int rowIndex = Integer
                        .valueOf(value.substring(0, value.indexOf("_")));

                int colIndex = Integer
                        .valueOf(value.substring(value.indexOf("_") + 1));

                Entry entry = (Entry) matrixSet[rowIndex];
                String rowLabel = (String) entry.getKey();
                Map<String, Boolean> subMap = (Map<String, Boolean>) entry
                        .getValue();
                Object[] subMapSet = subMap.entrySet().toArray();
                Entry subEntry = (Entry) subMapSet[colIndex];
                String colLabel = (String) subEntry.getKey();
                sb.append("^").append(rowLabel + "~" + colLabel);
            }

            if (StringUtils.isEmpty(sb.toString()))
            {
                bindValue(answerEntity, question.getAnswerField1(), null);
            }
            else
            {
                sb.append("^");
                bindValue(answerEntity, question.getAnswerField1(), sb.toString());
            }
        }
        else if (resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField1(), null);
        }
    }

    private static void bindAnswer_FileUpload(DFQuestion question,
            DFAnswerEntity answerEntity, HttpServletRequest request)
    {
        String fileUuid = request.getParameter("question_" + question.getId());
        boolean newFile = false;
        boolean removeFile = false;

        // Cancel binding for 'profileImage' and 'backgroundHeaderImage' fields
        // as they will be handled
        // differently
        if ("profileImage".equals(question.getAnswerField1())
                || "backgroundHeaderImage".equals(question.getAnswerField1()))
        {
            return;
        }

        if (isAnswered(question, answerEntity))
        {
            removeFile = StringUtils.isEmpty(fileUuid) || fileUuid.length() != 32;
            if (fileUuid != null && !StringUtils.isEmpty(fileUuid))
            {
                try
                {
                    newFile = !removeFile && !fileUuid.equals(PropertyUtils
                            .getProperty(answerEntity, question.getAnswerField2()));
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
            if (removeFile)
            {
                question.setValidated(true);
            }
        }
        else
        {
            newFile = fileUuid != null && fileUuid.length() == 32;
        }

        if (newFile)
        {
            String filePath = FilePathUtils.getFilePathUrlForUUID(fileUuid, true);
            if (filePath != null)
            {
                String fileName = filePath.substring(filePath.lastIndexOf('/') + 1);
                bindValue(answerEntity, question.getAnswerField1(), fileName);
                bindValue(answerEntity, question.getAnswerField2(), fileUuid);
            }
        }
        else if (removeFile)
        {
            bindValue(answerEntity, question.getAnswerField1(), null);
            bindValue(answerEntity, question.getAnswerField2(), null);
        }
    }

    private static void bindAnswer_Salary(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        String salaryAmountString = null, hoursPerWeekString = null;
        Double salaryAmount = null, hoursPerWeek = null;
        Integer frequency = 0;

        Locale locale = PortalUtils.getLocale(request);
        boolean isL2 = LocaleUtils.isL2(locale);

        salaryAmountString = request.getParameter(
                "question_" + question.getId().toString() + "_amount");
        hoursPerWeekString = request.getParameter(
                "question_" + question.getId().toString() + "_hoursPerWeek");

        frequency = RequestUtils.getInteger(request,
                "question_" + question.getId().toString() + "_frequency");

        boolean unpaid = RequestUtils.getBooleanParameter(request,
                "question_" + question.getId().toString() + "_unpaid",
                Boolean.FALSE);
        if (PortalUtils.isSiteCode(PortalUtils.SHERBROOKE_SITE_CODE))
        {
            unpaid = (salaryAmount == null || salaryAmount == 0)
                    || RequestUtils.getBooleanParameter(request,
                            "question_" + question.getId().toString() + "_unpaid",
                            Boolean.FALSE);
        }
        bindValue(answerEntity, question.getAnswerField4(), unpaid);
        if (salaryAmountString != null)
        {
            if (isL2)
            {
                if (salaryAmountString.contains("."))
                {
                    salaryAmountString = salaryAmountString.replace(".", ",");
                }

            }

            salaryAmount = LocaleUtils.parseDouble(salaryAmountString, locale);
            bindValue(answerEntity, question.getAnswerField1(), salaryAmount);
        }
        else if (resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField1(), 0.0d);
        }
        if (!StringUtils.isEmpty(request.getParameter(
                "question_" + question.getId().toString() + "_frequency")))
        {
            bindValue(answerEntity, question.getAnswerField2(), frequency);
        }
        else if (resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField2(), 0);
        }
        if (hoursPerWeekString != null)
        {
            if (isL2)
            {

                if (hoursPerWeekString.contains("."))
                {
                    hoursPerWeekString = hoursPerWeekString.replace(".", ",");
                }
            }

            hoursPerWeek = LocaleUtils.parseDouble(hoursPerWeekString, locale);

            bindValue(answerEntity, question.getAnswerField3(), hoursPerWeek);
        }
        else if (resetForEmptyString)
        {
            bindValue(answerEntity, question.getAnswerField3(), 0.0d);
        }
    }

    private static Boolean getBooleanValue(String str)
    {
        Boolean ret = false;

        if (str != null)
        {
            str = str.trim();
            ret = (str.equalsIgnoreCase("on") || str.equalsIgnoreCase("yes")
                    || str.equalsIgnoreCase("true") || str.equalsIgnoreCase("1"));
        }

        return ret;
    }

    public static void bindValue(DFAnswerEntity answerEntity, String field,
            Object value)
    {
        if (answerEntity != null && !StringUtils.isEmpty(field))
        {
            // int and boolean answers have special handling...

            if (field.matches("i[0-9]+"))
            {

                if (value == null)
                {
                    value = 0;
                }
                else if (value instanceof String)
                {
                    // expect failure if the string is not in integer format
                    try
                    {
                        value = Integer.parseInt((String) value);
                    }
                    catch (Exception e)
                    {
                        // expect "profileImage" fieldmap
                    }
                }

                // else assume (value instanceof Integer)
                // if it's not, then expect failure.

                try
                {
                    PropertyUtils.setProperty(answerEntity, field, value);
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
            else if (field.matches("b[0-9]+"))
            {
                if (value == null)
                {
                    value = false;
                }
                else if (value instanceof String)
                {
                    value = getBooleanValue((String) value);
                }

                // else assume (value instanceof Boolean)
                // if it's not, then expect failure.

                try
                {
                    PropertyUtils.setProperty(answerEntity, field,
                            (value == null ? 0 : value));
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
            else
            {
                try
                {
                    PropertyUtils.setProperty(answerEntity, field, value);
                }
                catch (Exception e)
                {
                    try
                    {
                        // this was added to work around a CCR import issue
                        // related to the "hours" field in CCPosition
                        PropertyUtils.setProperty(answerEntity, field,
                                Integer.parseInt((String) value));
                    }
                    catch (Exception e1)
                    {
                    }
                }
            }
        }
    }

    public static Integer getInteger(HttpServletRequest request, String key)
    {
        Integer id = null;

        if (request.getAttribute(key) != null)
        {
            id = (Integer) request.getAttribute(key);
        }
        else if (StringUtils.isInteger(request.getParameter(key)))
        {
            id = Integer.valueOf(request.getParameter(key));
        }

        return id;
    }

    public static DFQuestion getQuestion(Integer questionId)
    {
        DFQuestion question = null;

        try
        {
            question = PortalUtils.getHt().get(DFQuestion.class,
                    questionId);
        }
        catch (Exception e)
        {
        }

        return question;
    }

    public static List<DFCategory> getCategories(DFModel model)
    {
        return PortalUtils.getHt()
                .find("from DFCategory c where model=? order by c.position", model);
    }

    public static DFCategory getCategory(HttpServletRequest request)
    {
        return getCategory(getInteger(request, "dfCategoryId"));
    }

    public static DFQuestion getQuestion(HttpServletRequest request)
    {
        return getQuestion(getInteger(request, "dfQuestionId"));
    }

    public static List<DFQuestion> getDFQuestions(DFModel model)
    {
        return PortalUtils.getHt().find(
                "from DFQuestion q where q.category.model=? order by q.position asc",
                model);
    }

    public static List<DFQuestion> getDFQuestionsOrderByCategoryAndQuestionPosition(
            DFModel model)
    {
        return PortalUtils.getHt().find(
                "from DFQuestion q where q.category.model=? order by q.category.position asc, q.position asc",
                model);
    }

    // Unused Method
    public static void updateQuestionOrder(HttpServletRequest request)
    {
        List<String> qIds = StringUtils.listify(request.getParameter("questionIds"),
                "\\|");
        int order = 1;
        for (String id : qIds)
        {
            DFQuestion q = (DFQuestion) PortalUtils.getHt().load(DFQuestion.class,
                    Integer.valueOf(id));
            q.setPosition(order);
            PortalUtils.getHt().update(q);
            order++;
        }
    }

    public static LinkedList<CriteriaQuestion> getCriteriaQuestions(
            List<DFQuestion> questions, String hqlPrefix, String locale)
    {
        return getCriteriaQuestions(questions, hqlPrefix, locale, false, false);
    }

    public static LinkedList<CriteriaQuestion> getCriteriaQuestions(
            List<DFQuestion> questions, String hqlPrefix, String locale,
            boolean forContact, boolean inMaster)
    {
        LinkedList<CriteriaQuestion> ret = new LinkedList<>();

        for (DFQuestion q : questions)
        {
            CriteriaQuestion cq = getCriteriaQuestion(q, hqlPrefix, locale,
                    forContact, inMaster);

            if (cq != null)
            {
                ret.add(cq);
                if (q.getType() == DFQuestion.TYPE_STARTEND_DATE)
                {
                    CriteriaQuestion cq2 = getCriteriaQuestion(q, hqlPrefix, locale,
                            forContact, inMaster);
                    cq2.setQuestionKey(hqlPrefix + "." + q.getAnswerField2());
                    cq2.setQuestionText(PortalUtils.getMessageSource().getMessage(
                            "i18n.gridSearch.criteriaModel.questionText.0End",
                            new Object[] { q.getQuestionText() },
                            new Locale(locale)));
                    ret.add(cq2);
                    cq.setQuestionText(PortalUtils.getMessageSource().getMessage(
                            "i18n.gridSearch.criteriaModel.questionText.0Start",
                            new Object[] { q.getQuestionText() },
                            new Locale(locale)));
                }
                else if (q.getType() == DFQuestion.TYPE_TIME_COMMITMENT)
                {
                    CriteriaQuestion cq2 = getCriteriaQuestion(q, hqlPrefix, locale,
                            forContact, inMaster);
                    cq2.setQuestionKey(hqlPrefix + "." + q.getAnswerField2());

                    cq2.setType(CriteriaQuestion.TYPE_CHOICE_SINGLE);
                    cq2.setOperation(CriteriaQuestion.OPERATION_EQUALS);

                    cq2.setOptionChoices(Map.of("Week", "Week", "Month", "Month",
                            "Semester", "Semester"));

                    cq2.setQuestionText(PortalUtils.getMessageSource().getMessage(
                            "i18n.gridSearch.criteriaModel.questionText.0Frequency",
                            new Object[] { q.getQuestionText() },
                            new Locale(locale)));
                    ret.add(cq2);
                    cq.setQuestionText(PortalUtils.getMessageSource().getMessage(
                            "i18n.gridSearch.criteriaModel.questionText.0Hours",
                            new Object[] { q.getQuestionText() },
                            new Locale(locale)));
                }
                else if (q.getType() == DFQuestion.TYPE_X_PER_Y)
                {
                    CriteriaQuestion cq2 = getCriteriaQuestion(q, hqlPrefix, locale,
                            forContact, inMaster);
                    cq2.setQuestionKey(hqlPrefix + "." + q.getAnswerField2());

                    cq2.setType(CriteriaQuestion.TYPE_CHOICE_SINGLE);
                    cq2.setOperation(CriteriaQuestion.OPERATION_EQUALS);
                    cq2.setOptionChoices(StringUtils.mapify(q.getChoiceList(),
                            q.getChoiceList()));

                    cq2.setQuestionText(PortalUtils.getMessageSource().getMessage(
                            "i18n.gridSearch.criteriaModel.questionText.0Frequency",
                            new Object[] { q.getQuestionText() },
                            new Locale(locale)));
                    ret.add(cq2);
                    cq.setQuestionText(PortalUtils.getMessageSource().getMessage(
                            "i18n.gridSearch.criteriaModel.questionText.0Hours",
                            new Object[] { q.getQuestionText() },
                            new Locale(locale)));
                }
                else if (q.getType() == DFQuestion.TYPE_SALARY)
                {
                    CriteriaQuestion cq2 = getCriteriaQuestion(q, hqlPrefix, locale,
                            forContact, inMaster);
                    Locale l = new Locale(locale);
                    cq2.setQuestionText(PortalUtils.getMessageSource().getMessage(
                            "i18n.CoopHelper.frequency7075481402534270",
                            new Object[] { q.getQuestionText(l) }, l));
                    cq2.setQuestionKey(hqlPrefix + "." + q.getAnswerField2());
                    cq2.setType(CriteriaQuestion.TYPE_CHOICE_SINGLE);
                    Map map = new LinkedHashMap();
                    map.put(String.valueOf(0), PortalUtils.getMessageSource()
                            .getMessage("i18n.common.hours", null, l));
                    map.put(String.valueOf(1), PortalUtils.getMessageSource()
                            .getMessage("i18n.common.days", null, l));
                    map.put(String.valueOf(2), PortalUtils.getMessageSource()
                            .getMessage("i18n.common.weeks", null, l));
                    map.put(String.valueOf(3), PortalUtils.getMessageSource()
                            .getMessage("i18n.common.months", null, l));
                    map.put(String.valueOf(4), PortalUtils.getMessageSource()
                            .getMessage("i18n.common.years", null, l));
                    map.put(String.valueOf(5),
                            PortalUtils.getMessageSource().getMessage(
                                    "i18n.DFHelper.stipendhon6531749232505006",
                                    null, l));
                    cq2.setOptionChoices(map);
                    ret.add(cq2);

                    cq2 = getCriteriaQuestion(q, hqlPrefix, locale, forContact,
                            inMaster);
                    cq2.setQuestionText(PortalUtils.getMessageSource().getMessage(
                            "i18n.CoopHelper.hoursperwe9901485438946568",
                            new Object[] { q.getQuestionText(l) }, l));
                    cq2.setQuestionKey(hqlPrefix + "." + q.getAnswerField3());
                    cq2.setType(CriteriaQuestion.TYPE_NUMBER);
                    ret.add(cq2);
                }
                else if (q.getType() == DFQuestion.TYPE_SITE_VISIT)
                {
                    CriteriaQuestion cq2 = getCriteriaQuestion(q, hqlPrefix, locale,
                            forContact, inMaster);
                    Locale l = new Locale(locale);
                    cq2.setQuestionText(PortalUtils.getMessageSource().getMessage(
                            "i18n.CoopHelper.date0008960726205057",
                            new Object[] { q.getQuestionText(l) }, l));
                    cq2.setQuestionKey(hqlPrefix + "." + q.getAnswerField2());
                    cq2.setType(CriteriaQuestion.TYPE_DATE);
                    ret.add(cq2);

                    cq2 = getCriteriaQuestion(q, hqlPrefix, locale, forContact,
                            inMaster);
                    cq2.setQuestionText(PortalUtils.getMessageSource().getMessage(
                            "i18n.CoopHelper.coordinato9791618194688307",
                            new Object[] { q.getQuestionText(l) }, l));
                    cq2.setQuestionKey(hqlPrefix + "." + q.getAnswerField3());
                    cq2.setType(CriteriaQuestion.TYPE_TEXT);
                    ret.add(cq2);

                    cq2 = getCriteriaQuestion(q, hqlPrefix, locale, forContact,
                            inMaster);
                    cq2.setQuestionText(PortalUtils.getMessageSource().getMessage(
                            "i18n.CoopHelper.method2111452929881802",
                            new Object[] { q.getQuestionText(l) }, l));
                    cq2.setQuestionKey(hqlPrefix + "." + q.getAnswerField4());
                    cq2.setType(CriteriaQuestion.TYPE_CHOICE_SINGLE);
                    Map map = new LinkedHashMap();
                    map.put(String.valueOf(-1), "");
                    map.put(String.valueOf(0),
                            PortalUtils.getMessageSource().getMessage(
                                    "i18n.CoopHelper.InPerson4134961057094932",
                                    null, l));
                    map.put(String.valueOf(1),
                            PortalUtils.getMessageSource().getMessage(
                                    "i18n.CoopHelper.Videoconfe5583702611933858",
                                    null, l));
                    map.put(String.valueOf(2), PortalUtils.getMessageSource()
                            .getMessage("i18n.common.phone", null, l));
                    map.put(String.valueOf(3), PortalUtils.getMessageSource()
                            .getMessage("i18n.common.email.email", null, l));
                    cq2.setOptionChoices(map);
                    ret.add(cq2);

                    cq2 = getCriteriaQuestion(q, hqlPrefix, locale, forContact,
                            inMaster);
                    cq2.setQuestionText(PortalUtils.getMessageSource().getMessage(
                            "i18n.CoopHelper.location7016065250170507",
                            new Object[] { q.getQuestionText(l) }, l));
                    cq2.setQuestionKey(hqlPrefix + "." + q.getAnswerField5());
                    cq2.setType(CriteriaQuestion.TYPE_TEXT);
                    ret.add(cq2);
                }
            }
        }

        return ret;
    }

    public static CriteriaQuestion getCriteriaQuestion(DFQuestion question,
            String hqlPrefix, String locale)
    {
        return getCriteriaQuestion(question, hqlPrefix, locale, false, false);
    }

    public static CriteriaQuestion getCriteriaQuestion(DFQuestion question,
            String hqlPrefix, String locale, boolean forContact, boolean inMaster)
    {
        CriteriaQuestion cq = null;

        String questionText = LocaleUtils.isL1(locale) ? question.getQuestionText()
                : question.getQuestionTextL2();

        switch (question.getType())
        {
            case DFQuestion.TYPE_TEXT:
            case DFQuestion.TYPE_LARGE_TEXT:
            case DFQuestion.TYPE_HYPERLINK:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setType(CriteriaQuestion.TYPE_TEXT);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setColWidth(250);
                break;

            case DFQuestion.TYPE_BOOLEAN:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setType(CriteriaQuestion.TYPE_BOOLEAN);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setColWidth(150);
                break;

            case DFQuestion.TYPE_DATE:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setType(CriteriaQuestion.TYPE_DATE);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setDateFormatForDisplay(LocaleUtils.isEnglish(locale)
                        ? CriteriaQuestion.STANDARD_DATE_FORMAT
                        : CriteriaQuestion.FRENCH_DATE_FORMAT);
                break;

            case DFQuestion.TYPE_INTEGER:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setType(CriteriaQuestion.TYPE_NUMBER);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setColAlignment("right");
                break;

            case DFQuestion.TYPE_RATING:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setType(CriteriaQuestion.TYPE_RATING_RANGE);
                cq.setMin(question.getMin());
                cq.setMax(question.getMax());
                cq.setRatingNaDbValue(DFQuestion.RATING_NA_DB_VALUE);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setColAlignment("right");
                break;

            case DFQuestion.TYPE_MULTI_CHOICE:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setType(CriteriaQuestion.TYPE_CHOICE_MULTI);
                cq.setOperation(CriteriaQuestion.OPERATION_CONTAINS);
                cq.setAnswerDelimiter("^");
                cq.setOptionChoices(StringUtils.mapify(question.getChoiceList(),
                        question.getChoiceList()));
                cq.setOptionIncludeOtherFlag(question.isIncludeOther());
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setColWidth(150);
                cq.setDfMultiChoice(true);
                break;

            case DFQuestion.TYPE_SINGLE_CHOICE:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setType(CriteriaQuestion.TYPE_CHOICE_SINGLE);
                cq.setOperation(CriteriaQuestion.OPERATION_EQUALS);
                cq.setOptionChoices(
                        getCriteriaQuestionSingleChoiceOptions(question, locale));
                cq.setOptionIncludeOtherFlag(question.isIncludeOther());
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setColWidth(150);
                break;

            case DFQuestion.TYPE_FILE_UPLOAD:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setType(CriteriaQuestion.TYPE_FILE_UPLOAD);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setColWidth(150);
                break;

            case DFQuestion.TYPE_TREE:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setType(CriteriaQuestion.TYPE_TREE);
                cq.setTreeOptions(question.getTreeNodes());

                if (question.isSingleTreePath())
                {
                    cq.setDisplayFormatMultiline(
                            CriteriaQuestion.DISPLAY_FORMAT_MULTILINE_SINGLE_PATH);
                }
                break;

            case DFQuestion.TYPE_MATRIX_MULTI:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setType(CriteriaQuestion.TYPE_MATRIX_MULTI);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setMatrixColumnLabels(question.getMatrixColumnLabels());
                cq.setL2MatrixColumnLabels(question.getL2MatrixColumnLabels());
                cq.setMatrixRowLabels(question.getMatrixRowLabels());
                cq.setL2MatrixRowLabels(question.getL2MatrixRowLabels());
                cq.setColWidth(150);
                break;

            case DFQuestion.TYPE_MATRIX_SINGLE:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setType(CriteriaQuestion.TYPE_MATRIX_SINGLE);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setMatrixColumnLabels(question.getMatrixColumnLabels());
                cq.setL2MatrixColumnLabels(question.getL2MatrixColumnLabels());
                cq.setMatrixRowLabels(question.getMatrixRowLabels());
                cq.setL2MatrixRowLabels(question.getL2MatrixRowLabels());
                cq.setColWidth(150);
                break;

            case DFQuestion.TYPE_FLOAT:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setType(CriteriaQuestion.TYPE_FLOAT);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setColAlignment("right");
                break;

            case DFQuestion.TYPE_STARTEND_DATE:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setType(CriteriaQuestion.TYPE_DATE);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setDateFormatForDisplay(LocaleUtils.isEnglish(locale)
                        ? CriteriaQuestion.STANDARD_DATE_FORMAT
                        : CriteriaQuestion.FRENCH_DATE_FORMAT);
                cq.setColWidth(170);
                break;

            case DFQuestion.TYPE_SITE_VISIT:
                cq = new CriteriaQuestion();
                cq.setQuestionText(question.getQuestionText());
                cq.setType(CriteriaQuestion.TYPE_BOOLEAN);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                break;

            case DFQuestion.TYPE_SALARY:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setType(CriteriaQuestion.TYPE_FLOAT);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setColWidth(80);
                break;

            case DFQuestion.TYPE_X_PER_Y:
            case DFQuestion.TYPE_TIME_COMMITMENT:
                cq = new CriteriaQuestion();
                cq.setQuestionText(questionText);
                cq.setType(CriteriaQuestion.TYPE_NUMBER);
                cq.setQuestionKey(hqlPrefix + "." + question.getAnswerField1());
                cq.setColWidth(270);
                break;

            default:
                throw new RuntimeException("Unknown question type");
        }

        if (forContact)
        {
            if (inMaster)
            {
                cq.setVisibleCriteria(false);
                cq.setVisibleResults(true && question.isCanShowInResults());
            }
            else
            {
                cq.setVisibleCriteria(true && question.isCanSearch());
                cq.setVisibleResults(false);
            }
        }
        else
        {
            // APPLY PERMISSION BEHAVIOR...
            cq.setVisibleCriteria(question.isCanSearch());
            cq.setVisibleResults(question.isCanShowInResults());
        }
        cq.setQuestionOrder(question.getPosition());
        cq.setLocale(locale);

        return cq;
    }

    public static int getMaxPermittedLargeTextFields()
    {
        int maxTFields = 10; // (MYSQL limitation)

        PortalConfig pc = PortalConfigHelper.getPortalConfig(PortalConfig.DB_TYPE);

        if (pc != null && pc.getOrbisValue() != null
                && pc.getOrbisValue().equals("MSSQL"))
        {
            maxTFields = 100;
        }

        return maxTFields;
    }

    public static boolean isAnswered(DFQuestion question,
            DFAnswerEntity answerEntity)
    {
        boolean answered = false;

        if (question != null && question.isAnswerFieldsSet()
                && answerEntity != null)
        {
            try
            {
                switch (question.getType())
                {
                    case DFQuestion.TYPE_BOOLEAN:
                    case DFQuestion.TYPE_DATE:
                    case DFQuestion.TYPE_INTEGER:
                    case DFQuestion.TYPE_LARGE_TEXT:
                    case DFQuestion.TYPE_MULTI_CHOICE:
                    case DFQuestion.TYPE_SINGLE_CHOICE:
                    case DFQuestion.TYPE_TEXT:
                    case DFQuestion.TYPE_MATRIX_SINGLE:
                    case DFQuestion.TYPE_MATRIX_MULTI:
                    case DFQuestion.TYPE_FLOAT:
                    case DFQuestion.TYPE_TREE:
                    case DFQuestion.TYPE_TOTAL_TRACKED_HOURS:
                        answered = PropertyUtils.getProperty(answerEntity,
                                question.getAnswerField1()) != null;
                        break;

                    case DFQuestion.TYPE_FILE_UPLOAD:
                        answered = PropertyUtils.getProperty(answerEntity,
                                question.getAnswerField1()) != null
                                && PropertyUtils.getProperty(answerEntity,
                                        question.getAnswerField2()) != null;
                        break;
                    case DFQuestion.TYPE_SALARY:
                        answered = PropertyUtils.getProperty(answerEntity,
                                question.getAnswerField1()) != null
                                && PropertyUtils.getProperty(answerEntity,
                                        question.getAnswerField2()) != null;
                        break;
                    default:
                        break;
                }
            }
            catch (Exception e)
            {
            }
        }

        return answered;
    }

    // Unused Method
    public static List<I18nLabel> validateAnswers(List<DFQuestion> questions,
            DFAnswerEntity answerEntity)
    {
        List<I18nLabel> errors = new ArrayList<>();

        for (DFQuestion question : questions)
        {
            if (question.isRequired() && !question.isValidated())
            {
                try
                {
                    Object answerValue = PropertyUtils.getProperty(answerEntity,
                            question.getAnswerField1());

                    if (answerValue == null)
                    {
                        errors.add(
                                new I18nLabel("i18n.Question2Helper.answerRequired",
                                        question.getQuestionText()));
                    }
                    else if (question.getType() == DFQuestion.TYPE_TREE
                            && !isTreeQuestionAnswered((String) answerValue))
                    {
                        errors.add(
                                new I18nLabel("i18n.Question2Helper.answerRequired",
                                        question.getQuestionText()));
                    }
                }
                catch (Exception e)
                {
                    errors.add(new I18nLabel("i18n.Question2Helper.answerRequired",
                            question.getQuestionText()));
                }
            }
        }

        return errors;
    }

    private static boolean isTreeQuestionAnswered(String jsonArray)
    {
        boolean selected = false;

        try
        {
            JSONArray selectedNodes = new JSONArray(jsonArray);
            selected = selectedNodes.length() > 0;
        }
        catch (Exception e)
        {
        }

        return selected;
    }

    // Unused Method
    public static List<I18nLabel> validate(DFQuestion question)
    {
        List<I18nLabel> errors = new ArrayList<>();

        if (StringUtils.isEmpty(question.getQuestionText()))
        {
            errors.add(new I18nLabel("i18n.Question2Helper.missingQuestionText"));
        }

        switch (question.getType())
        {
            case DFQuestion.TYPE_INTEGER:
                if (question.getMax() < question.getMin())
                {
                    errors.add(new I18nLabel("i18n.Question2Helper.invalidMinMax"));
                }
                break;

            case DFQuestion.TYPE_MULTI_CHOICE:
                if (StringUtils.isEmpty(question.getChoices())
                        || question.getChoiceList().isEmpty())
                {
                    errors.add(
                            new I18nLabel("i18n.Question2Helper.missingChoices"));
                }
                break;
            default:
                break;
        }

        return errors;
    }

    public static String getQuestionTypeLabel(Integer questionType)
    {
        String ret = "";
        switch (questionType)
        {
            case DFQuestion.TYPE_TEXT:
                ret = "TEXT";
                break;
            case DFQuestion.TYPE_INTEGER:
                ret = "NUMERIC INTEGER";
                break;
            case DFQuestion.TYPE_MULTI_CHOICE:
                ret = "MULTI CHOICE";
                break;
            case DFQuestion.TYPE_BOOLEAN:
                ret = "BOOLEAN";
                break;
            case DFQuestion.TYPE_DATE:
                ret = "DATE";
                break;
            case DFQuestion.TYPE_LARGE_TEXT:
                ret = "LARGE TEXT";
                break;
            case DFQuestion.TYPE_RATING:
                ret = "RATING";
                break;
            case DFQuestion.TYPE_FILE_UPLOAD:
                ret = "FILE UPLOAD";
                break;
            case DFQuestion.TYPE_SINGLE_CHOICE:
                ret = "SINGLE CHOICE";
                break;
            case DFQuestion.TYPE_TREE:
                ret = "TREE";
                break;
            case DFQuestion.TYPE_MATRIX_MULTI:
                ret = "MULTI CHOICE MATRIX";
                break;
            case DFQuestion.TYPE_MATRIX_SINGLE:
                ret = "SINGLE CHOICE MATRIX";
                break;
            case DFQuestion.TYPE_FLOAT:
                ret = "FLOAT";
                break;
            case DFQuestion.TYPE_STARTEND_DATE:
                ret = "START END DATE";
                break;
            case DFQuestion.TYPE_SALARY:
                ret = "SALARY";
                break;
            case DFQuestion.TYPE_SITE_VISIT:
                ret = "SITE VISIT";
                break;
            case DFQuestion.TYPE_X_PER_Y:
                ret = "X HOURS PER Y FREQUENCY";
                break;
            case DFQuestion.TYPE_TIME_COMMITMENT:
                ret = "WEEKLY TIME COMMITMENT";
                break;
            case DFQuestion.TYPE_HYPERLINK:
                ret = "HYPERLINK";
                break;
            case DFQuestion.TYPE_TOTAL_TRACKED_HOURS:
                ret = "TOTAL TRACKED HOURS";
                break;
            default:
                ret = "UNKNOWN";
                break;
        }

        return ret;
    }

    /**
     * A JSP helper method for matrix-type questions.
     *
     * @return Map<rowLabel, Map<columnLabel, isChecked>>
     */
    public static Map<String, Map<String, Boolean>> getMatrixMap(
            DFAnswerEntity answerEntity, DFQuestion question, String locale)
    {

        Map<String, Map<String, Boolean>> map = new LinkedHashMap<>();

        try
        {
            List<String> rows = question.getMatrixRowLabels();
            List<String> l2Rows = question.getL2MatrixRowLabels();
            List<String> cols = question.getMatrixColumnLabels();
            List<String> l2Cols = question.getL2MatrixColumnLabels();

            String answerText = null;
            if (answerEntity != null)
            {
                answerText = (String) PropertyUtils.getProperty(answerEntity,
                        question.getAnswerField1());
            }

            rowLoop: for (int r = 0; r < rows.size(); r++)
            {
                String rowLabel = rows.get(r);

                if (!StringUtils.isEmpty(rowLabel))
                {
                    Map subMap = new LinkedHashMap();
                    for (int c = 0; c < cols.size(); c++)
                    {
                        String colLabel = cols.get(c);
                        if (StringUtils.isEmpty(colLabel))
                        {
                            continue rowLoop;
                        }

                        boolean checked = false;
                        if (answerText != null)
                        {
                            checked = answerText.indexOf(
                                    "^" + rowLabel + "~" + colLabel + "^") != -1;
                        }

                        if (!LocaleUtils.isL1(locale) && l2Cols != null
                                && l2Cols.size() >= c)
                        {
                            colLabel = l2Cols.get(c);
                        }

                        subMap.put(colLabel, checked);
                    }

                    if (!LocaleUtils.isL1(locale) && l2Rows != null
                            && l2Rows.size() >= r)
                    {
                        rowLabel = l2Rows.get(r);
                    }

                    map.put(rowLabel, subMap);
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return map;
    }

    public static void circularDependencyCheck(DFModel model)
    {
        Map<DFQuestion, List<DFQuestion>> dependencyMap = model.getDependencyMap();
        Map<DFQuestion, List<DFQuestion>> dependencyMapClone = dependencyMap
                .entrySet().stream().collect(Collectors.toMap(e -> e.getKey(),
                        e -> new ArrayList(e.getValue())));
        Map<DFQuestion, Integer> visitedMap = new HashMap<>();

        List<String> errors = new LinkedList<>();

        try
        {
            dependencyMapClone.entrySet().iterator().next().getKey();

            /*
             * since the graph of questions is disconnected, you have to iterate
             * over unvisited nodes
             */
            for (Map.Entry<DFQuestion, List<DFQuestion>> dependency : dependencyMapClone
                    .entrySet())
            {
                DFQuestion key = dependency.getKey();
                if (visitedMap.get(key) == null)
                {
                    DFS(dependencyMapClone, key, visitedMap, errors);
                }
            }
        }
        catch (Exception e)
        {

        }

        model.setErrors(errors);
    }

    /* Depth first search algorithm */
    public static Map<DFQuestion, Integer> DFS(
            Map<DFQuestion, List<DFQuestion>> dependencyMap, DFQuestion first,
            Map<DFQuestion, Integer> visitedMap, List<String> errors)
    {

        if (TEMP_MARKED.equals(visitedMap.get(first)))
        {
            // not a DAG
            errors.add(first.getQuestionText());
        }
        if (visitedMap.get(first) == null || UNMARKED.equals(visitedMap.get(first)))
        {
            // Keep track of nodes that have been visited
            visitedMap.put(first, TEMP_MARKED);

            // Iterate over this question's adjacent nodes
            List<DFQuestion> adjacentQuestions = dependencyMap.get(first);
            if (adjacentQuestions != null)
            {
                for (DFQuestion question : adjacentQuestions)
                {
                    DFS(dependencyMap, question, visitedMap, errors);

                }
                visitedMap.put(first, PERM_MARKED);
            }
        }
        dependencyMap.remove(first);
        return visitedMap;
    }

    /**
     * Populates the model provided adding all categories, questions, and
     * questionRoles to the model for viewing and editing
     *
     * @param model
     *            the model to populate
     * @param canWrite
     *            whether this is to be in write mode or read mode
     * @param answerEntity
     *            the DFAnswerEntity to populate into this model
     * @param userLoggedIn
     *            the user that will be viewing this form
     */
    public static void populateModel(DFModel model, boolean canWrite,
            DFAnswerEntity answerEntity, UserDetailsImpl userLoggedIn)
    {
        populateModel(false, model, canWrite, answerEntity, null, userLoggedIn,
                null);
    }

    /**
     * Populates the model provided adding all categories, questions, and
     * questionRoles to the model for viewing and editing
     *
     * @param model
     *            the model to populate
     * @param canWrite
     *            whether this is to be in write mode or read mode
     * @param answerEntity
     *            the DFAnswerEntity to populate into this model
     * @param userLoggedIn
     *            the user that will be viewing this form
     * @param forceLowerPermissionsMap
     *            A Map<Integer, Boolean[]> where the key is the question id and the
     *            value is a Boolean[] of canRead, canWrite, canSearch,
     *            canShowInResults, canShowInReports. This is only to lower the
     *            permissions of the question(ex. make a readable question not
     *            readable, it can't make a not readable question readable). If
     *            canWrite == true, the bindAnswers that results from this write
     *            should be the same as this map. If a question is not in this map
     *            it will be treated as all false.
     */
    public static void populateModel(DFModel model, boolean canWrite,
            DFAnswerEntity answerEntity, UserDetailsImpl userLoggedIn,
            Map<Integer, Boolean[]> forceLowerPermissionsMap)
    {
        populateModel(false, model, canWrite, answerEntity, null, userLoggedIn,
                forceLowerPermissionsMap);
    }

    /**
     * Populates the model provided adding all categories, questions, and
     * questionRoles to the model for viewing and editing
     *
     * @param circularDependencyCheck
     *            should only be made true if dfModel getErrors has to used.
     * @param model
     *            the model to populate
     * @param canWrite
     *            whether this is to be in write mode or read mode
     * @param answerEntity
     *            the DFAnswerEntity to populate into this model
     * @param role
     *            the DFRole that this should load in. Overrides the role that
     *            should be assigned for userLoggedIn
     * @param userLoggedIn
     *            the user that will be viewing this form
     * @param forceLowerPermissionsMap
     *            A Map<Integer, Boolean[]> where the key is the question id and the
     *            value is a Boolean[] of canRead, canWrite, canSearch,
     *            canShowInResults, canShowInReports. This is only to lower the
     *            permissions of the question(ex. make a readable question not
     *            readable, it can't make a not readable question readable). If
     *            canWrite == true, the bindAnswers that results from this write
     *            should be the same as this map. If a question is not in this map
     *            it will be treated as all false.
     */
    public static void populateModel(boolean circularDependencyCheck, DFModel model,
            boolean canWrite, DFAnswerEntity answerEntity, DFRole role,
            UserDetailsImpl userLoggedIn,
            Map<Integer, Boolean[]> forceLowerPermissionsMap)
    {
        updateDependencyMap(model, answerEntity, circularDependencyCheck);
        role = role == null ? getRoleInModel(model, userLoggedIn) : role;
        model.setCurrentRole(role);
        model.setCanWrite(false);
        model.setCanRead(false);

        List<DFCategory> categories = PortalUtils.getHt().find(
                "from DFCategory dfc where dfc.model=? order by dfc.position",
                model);

        Map<Integer, List<DFQuestion>> questionsMap = CollectionUtils
                .mapifyOneToMany(
                        "select dfq.category.id, dfq from DFQuestion dfq join dfq.category where dfq.category.model=? order by dfq.position ",
                        new Object[] { model });

        Map<Integer, List<DFQuestionRole>> questionRolesMap = CollectionUtils
                .putResultsSetsToMap(getQuestionRolesForModel(model));

        for (DFCategory category : categories)
        {
            List<DFQuestion> questions = Optional
                    .ofNullable(questionsMap.get(category.getId()))
                    .orElse(new ArrayList<DFQuestion>());
            category.setQuestions(questions);
            for (DFQuestion question : questions)
            {
                List<DFQuestionRole> questionRoles = Optional
                        .ofNullable(questionRolesMap.get(question.getId()))
                        .orElse(new ArrayList<DFQuestionRole>());
                for (DFQuestionRole qr : questionRoles)
                {
                    qr.setCanRead(
                            qr.isCanRead() && (forceLowerPermissionsMap == null
                                    || forceLowerPermissionsMap.isEmpty()
                                    || (forceLowerPermissionsMap
                                            .containsKey(question.getId())
                                            && ArrayUtils.getValueAtIndex(
                                                    forceLowerPermissionsMap
                                                            .get(question.getId()),
                                                    0, false))));
                    qr.setCanWrite(qr.isCanWrite() && canWrite
                            && (forceLowerPermissionsMap == null
                                    || forceLowerPermissionsMap.isEmpty()
                                    || (forceLowerPermissionsMap
                                            .containsKey(question.getId())
                                            && ArrayUtils.getValueAtIndex(
                                                    forceLowerPermissionsMap
                                                            .get(question.getId()),
                                                    1, false))));
                    qr.setCanSearch(
                            qr.isCanSearch() && (forceLowerPermissionsMap == null
                                    || forceLowerPermissionsMap.isEmpty()
                                    || (forceLowerPermissionsMap
                                            .containsKey(question.getId())
                                            && ArrayUtils.getValueAtIndex(
                                                    forceLowerPermissionsMap
                                                            .get(question.getId()),
                                                    2, false))));
                    qr.setCanShowInResults(qr.isCanShowInResults()
                            && (forceLowerPermissionsMap == null
                                    || forceLowerPermissionsMap.isEmpty()
                                    || (forceLowerPermissionsMap
                                            .containsKey(question.getId())
                                            && ArrayUtils.getValueAtIndex(
                                                    forceLowerPermissionsMap
                                                            .get(question.getId()),
                                                    3, false))));
                    qr.setCanShowInReports(qr.isCanShowInReports()
                            && (forceLowerPermissionsMap == null
                                    || forceLowerPermissionsMap.isEmpty()
                                    || (forceLowerPermissionsMap
                                            .containsKey(question.getId())
                                            && ArrayUtils.getValueAtIndex(
                                                    forceLowerPermissionsMap
                                                            .get(question.getId()),
                                                    4, false))));
                    if (qr.getDfRole().equals(role)
                            && (qr.isCanRead() || qr.isCanWrite()))
                    {
                        setChallengeCode(question);
                    }
                    if (qr.getDfRole().equals(role) && qr.isCanWrite())
                    {
                        model.setCanWrite(true);
                    }
                    if (qr.getDfRole().equals(role) && qr.isCanRead())
                    {
                        model.setCanRead(true);
                    }
                }
                question.setQuestionRoles(questionRoles);
                question.setRole(role);

                if (answerEntity != null)
                {
                    if (DFQuestion.TYPE_SINGLE_CHOICE == question.getType()
                            || DFQuestion.TYPE_MULTI_CHOICE == question.getType())
                    {
                        question.setMultiChoices(
                                model.getMultiChoices(answerEntity, question));
                        question.setSingleChoice(
                                model.getSingleChoice(answerEntity, question));
                        if (canWrite)
                        {
                            question.setMultiChoicesJson(model
                                    .getMultiChoicesJson(answerEntity, question));
                        }
                    }
                    else if (DFQuestion.TYPE_FILE_UPLOAD == question.getType())
                    {
                        // Delete a file upload answer for a non-existent file
                        if (DFHelper.isAnswered(question, answerEntity))
                        {
                            boolean valid = false;
                            String uuid = null;
                            try
                            {
                                uuid = PropertyUtils
                                        .getProperty(answerEntity,
                                                question.getAnswerField2())
                                        .toString();
                                if (uuid.indexOf('~') > -1)
                                {
                                    uuid = uuid.substring(uuid.indexOf('~') + 1);
                                }
                                valid = FilePathUtils.getFilePathUrlForUUID(uuid,
                                        true) != null;
                            }
                            catch (Exception e)
                            {
                                e.printStackTrace();
                            }
                            if (!valid)
                            {
                                bindValue(answerEntity, question.getAnswerField1(),
                                        null);
                                bindValue(answerEntity, question.getAnswerField2(),
                                        null);
                                if (uuid != null)
                                {
                                    FilePathUtils.deleteFilePathByUUID(uuid);
                                }
                            }
                        }
                    }
                }
            }
        }

        model.setCategories(categories);
        model.setPopulated(true);
    }

    public static void setChallengeCode(DFQuestion question)
    {
        if (DFQuestion.TYPE_FILE_UPLOAD == question.getType())
        {
            question.setChallengeCode(getQuestionChallengeCode(question));
        }
    }

    public static String getQuestionChallengeCode(DFQuestion question)
    {
        return PortalUtils.getMd5(
                question.getId().toString() + DFQuestion.CHALLENGE_CODE_SALT);
    }

    public static String getDFModelExportJSON(DFModel model)
    {
        String ret = null;

        JSONObject modelObj = new JSONObject();
        JSONObject modelAttributesObj = new JSONObject();
        JSONArray rolesArray = new JSONArray();
        JSONArray questionsArray = new JSONArray();
        JSONArray categoriesArray = new JSONArray();

        List<DFRole> roles = DFHelper.getRolesForModel(model.getId().toString());
        List<DFQuestion> questions = DFHelper.getDFQuestions(model);
        List<DFQuestionSiteMapping> questionSiteMappings = PortalUtils.getHt().find(
                "FROM DFQuestionSiteMapping dfqsm WHERE dfqsm.dfQuestion.category.model.id=?",
                model.getId());
        List<Integer> categoryIds = new ArrayList<>();

        try
        {
            modelAttributesObj.put("canWrite", model.isCanWrite());
            if (!StringUtils.isEmpty(model.getName()))
            {
                modelAttributesObj.put("name", model.getName());
            }
            modelAttributesObj.put("preserveOrdering", model.isPreserveOrdering());
            modelAttributesObj.put("publicAllowed", model.isPublicAllowed());
            modelAttributesObj.put("modelEntityClassName",
                    model.getModelEntityClassName().getName());

            modelObj.put("modelAttributes", modelAttributesObj);

            for (DFRole r : roles)
            {
                JSONObject roleObj = new JSONObject(r.toJSONString());

                List<Integer> roleUserIds = DFHelper.getRoleUsers(r).stream()
                        .map(ru -> ru.getUser()).filter(u -> u != null)
                        .map(UserDetailsImpl::getId).collect(Collectors.toList());
                List<String> roleUserGroupNames = getDfRoleUserGroups(r).stream()
                        .map(rug -> rug.getUserGroup()).filter(u -> u != null)
                        .map(PersonGroup::getName).collect(Collectors.toList());

                JSONArray roleUsersArray = new JSONArray();
                JSONArray roleUserGroupsArray = new JSONArray();

                for (Integer ru : roleUserIds)
                {
                    roleUsersArray.put(ru);
                }
                for (String rug : roleUserGroupNames)
                {
                    roleUserGroupsArray.put(rug);
                }

                roleObj.put("users", roleUsersArray);
                roleObj.put("userGroups", roleUserGroupsArray);
                roleObj.remove("model");

                rolesArray.put(roleObj);
            }

            modelObj.put("roles", rolesArray);

            for (DFQuestion q : questions)
            {
                JSONObject questionObj = new JSONObject(q.toJSONString());
                JSONArray questionRolesArray = new JSONArray();
                JSONArray questionDependenciesArray = new JSONArray();
                List<DFQuestionRole> questionRoles = DFHelper
                        .getRolesForQuestion(q);
                List<DFQuestionDependency> questionDependencies = DFHelper
                        .getQuestionDependencies(q);
                for (DFQuestionRole qr : questionRoles)
                {
                    JSONObject questionRoleObj = new JSONObject(qr.toJSONString());
                    questionRoleObj.remove("dfQuestion");
                    questionRoleObj.put("dfRole",
                            questionRoleObj.getJSONObject("dfRole").getInt("id"));
                    questionRolesArray.put(questionRoleObj);
                }
                questionObj.put("roles", questionRolesArray);

                JSONObject categoryObj = questionObj.getJSONObject("category");
                Integer categoryId = categoryObj.getInt("id");

                categoryObj.remove("model");

                if (!categoryIds.contains(categoryId))
                {
                    categoriesArray.put(categoryObj);
                    categoryIds.add(categoryId);
                }

                questionObj.put("category", categoryId);

                if (!questionDependencies.isEmpty())
                {
                    for (DFQuestionDependency qd : questionDependencies)
                    {
                        String qdString = qd.toJSONString();
                        qdString = qdString.replaceAll(
                                "\"([if]\\d+\":0(\\.0)?|b\\d+\":false),", "");
                        JSONObject questionDependencyObj = new JSONObject(qdString);
                        questionDependencyObj.remove("dfQuestion");
                        questionDependencyObj.put("dfQuestionDependsOn",
                                qd.getDfQuestionDependsOn().getId());
                        questionDependenciesArray.put(questionDependencyObj);
                    }
                    questionObj.put("dependencies", questionDependenciesArray);
                }

                Optional<DFQuestionSiteMapping> siteMapping = questionSiteMappings
                        .stream().filter(qsm -> q.getId()
                                .equals(qsm.getDfQuestion().getId()))
                        .findAny();

                if (siteMapping.isPresent())
                {
                    questionObj.put("siteMapping",
                            siteMapping.get().getSiteMapping().getOrbisKey());
                }

                questionsArray.put(questionObj);

                modelObj.put("questions", questionsArray);
            }

            modelObj.put("categories", categoriesArray);

            ret = modelObj.toString();
        }
        catch (JSONException e)
        {
            e.printStackTrace();
        }

        return ret;
    }

    public static DFModel importModel(DFModelEntity entity, String jsonString)
    {
        Integer modelId = entity.getDFModel().getId();

        return (DFModel) PortalUtils.getTransactionTemplate().execute(status -> {
            DFModel ret = new DFModel();
            String newModelEntityClassName = null;
            Map<Integer, DFQuestion> questionsMap = new HashMap<>();
            Map<Integer, DFRole> rolesMap = new HashMap<>();
            Map<Integer, DFCategory> categoriesMap = new HashMap<>();
            Map<Integer, JSONObject> categoryObjectsMap = new HashMap<>();
            List<DFRole> unusedRoles = DFHelper
                    .getRolesForModel(modelId.toString());
            Map<Integer, JSONArray> questionDependencies = new HashMap<>();
            Map<Integer, PortalConfig> siteMappings = new HashMap<>();

            if (PortalUtils.getJt().queryForInt(
                    "SELECT COUNT(id) FROM df_category WHERE model=?",
                    new Object[] { modelId }) > 0)
            {
                List<DFCategory> existingModelCategories = PortalUtils.getHt()
                        .find("FROM DFCategory dfc WHERE dfc.model.id=?", modelId);
                for (DFCategory dfc : existingModelCategories)
                {
                    DFHelper.populateCategory(dfc);
                    DFHelper.deleteCategory(dfc, entity);
                }
            }

            try
            {
                JSONObject exportObj = new JSONObject(jsonString);
                JSONArray rolesArray = exportObj.getJSONArray("roles");
                JSONArray questionsArray = exportObj.getJSONArray("questions");
                JSONArray categoriesArray = exportObj.has("categories")
                        ? exportObj.getJSONArray("categories")
                        : null;
                boolean sameModelEntityType = false;

                if (exportObj.has("modelAttributes"))
                {
                    JSONObject modelAttributesObj = exportObj
                            .getJSONObject("modelAttributes");
                    ret.setCanWrite(modelAttributesObj.getBoolean("canWrite"));
                    if (modelAttributesObj.has("name"))
                    {
                        ret.setName(modelAttributesObj.getString("name"));
                    }
                    ret.setPreserveOrdering(
                            modelAttributesObj.getBoolean("preserveOrdering"));
                    ret.setPublicAllowed(
                            modelAttributesObj.getBoolean("publicAllowed"));
                    newModelEntityClassName = modelAttributesObj
                            .getString("modelEntityClassName");
                    sameModelEntityType = entity.getClass().getName()
                            .equals(newModelEntityClassName);
                }

                int i = 0;

                if (categoriesArray != null)
                {
                    while (!categoriesArray.isNull(i))
                    {
                        JSONObject categoryObj = categoriesArray.getJSONObject(i++);
                        categoryObjectsMap.put(categoryObj.getInt("id"),
                                categoryObj);
                    }

                    i = 0;
                }

                while (!rolesArray.isNull(i))
                {
                    JSONObject roleObj = rolesArray.getJSONObject(i++);
                    Integer oldRoleId = roleObj.getInt("id");
                    boolean roleNotDeletable = roleObj.getBoolean("notDeletable");
                    JSONObject modelObj = roleObj.has("model")
                            ? roleObj.getJSONObject("model")
                            : null;
                    JSONArray roleUsersArray = roleObj.getJSONArray("users");
                    JSONArray roleUserGroupsArray = roleObj
                            .getJSONArray("userGroups");
                    roleObj.remove("users");
                    roleObj.remove("userGroups");
                    DFRole role = null;
                    Optional<DFRole> matchingRole = unusedRoles.stream()
                            .filter(r -> r.getId().equals(oldRoleId)
                                    && r.isNotDeletable() == roleNotDeletable)
                            .findFirst();
                    if (matchingRole.isPresent())
                    {
                        DFRole existingRole = matchingRole.get();
                        unusedRoles.remove(existingRole);
                        role = new DFRole().fromJSONString(roleObj.toString());
                        existingRole.setDefaultRead(role.isDefaultRead());
                        existingRole.setDefaultSearch(role.isDefaultSearch());
                        existingRole.setDefaultShowInReports(
                                role.isDefaultShowInReports());
                        existingRole.setDefaultShowInResults(
                                role.isDefaultShowInResults());
                        existingRole.setDefaultWrite(role.isDefaultWrite());
                        existingRole.setModel(role.getModel());
                        existingRole.setName(role.getName());
                        existingRole.setL2Name(role.getL2Name());
                        existingRole.setNameNotEditable(role.isNameNotEditable());
                        existingRole.setNotDeletable(role.isNotDeletable());
                        existingRole.setPosition(role.getPosition());
                        existingRole.setPublicRole(role.isPublicRole());
                        PortalUtils.getHt().update(existingRole);
                        rolesMap.put(oldRoleId, existingRole);
                        role = existingRole;
                        PortalUtils.getJt().update(
                                "DELETE FROM df_role_user WHERE role = ?",
                                new Object[] { role.getId() });
                        PortalUtils.getJt().update(
                                "DELETE FROM df_role_user_group WHERE role = ?",
                                new Object[] { role.getId() });
                    }
                    else
                    {
                        roleObj.remove("id");
                        if (modelObj != null)
                        {
                            modelObj.put("id", modelId);
                        }
                        roleObj.put("model", modelObj);

                        DFRole newRole = new DFRole()
                                .fromJSONString(roleObj.toString());
                        if (modelObj == null)
                        {
                            newRole.setModel(entity.getDFModel());
                        }
                        PortalUtils.getHt().save(newRole);
                        rolesMap.put(oldRoleId, newRole);
                        role = newRole;
                    }

                    Integer roleId = role.getId();

                    List<Integer> userIdsList = new ArrayList<>();
                    List<Integer> userGroupIdsList = new ArrayList<>();
                    int r = 0;
                    while (!roleUsersArray.isNull(r))
                    {
                        userIdsList.add(roleUsersArray.getInt(r++));
                    }
                    r = 0;

                    if (!roleUserGroupsArray.isNull(0))
                    {
                        if (!StringUtils
                                .isInteger(roleUserGroupsArray.getString(0)))
                        {
                            while (!roleUserGroupsArray.isNull(r))
                            {

                                List<String> userGroupId = (List<String>) PortalUtils
                                        .getJt()
                                        .query("select id from user_group where name=?",
                                                new Object[] { roleUserGroupsArray
                                                        .getString(r++) },
                                                rs -> {
                                                    List<String> slotIds = new ArrayList<>();
                                                    if (rs.next())
                                                    {
                                                        slotIds.add(
                                                                rs.getString("id"));
                                                    }
                                                    return slotIds;
                                                }

                                        );

                                if (!userGroupId.isEmpty())
                                {
                                    userGroupIdsList.add(StringUtils
                                            .toInteger(userGroupId.get(0)));
                                }
                            }
                        }
                        else
                        {
                            while (!roleUserGroupsArray.isNull(r))
                            {
                                userGroupIdsList
                                        .add(roleUserGroupsArray.getInt(r++));

                            }

                        }
                    }

                    PortalUtils.getJt().batchUpdate(
                            "INSERT INTO df_role_user(role, uzer) VALUES(?, ?)",
                            new BatchPreparedStatementSetter()
                            {
                                @Override
                                public int getBatchSize()
                                {
                                    return userIdsList.size();
                                }

                                @Override
                                public void setValues(PreparedStatement ps, int i)
                                        throws SQLException
                                {
                                    ps.setInt(1, roleId);
                                    ps.setInt(2, userIdsList.get(i));
                                }
                            });

                    PortalUtils.getJt().batchUpdate(
                            "INSERT INTO df_role_user_group(role, userGroup) VALUES(?, ?)",
                            new BatchPreparedStatementSetter()
                            {
                                @Override
                                public int getBatchSize()
                                {
                                    return userGroupIdsList.size();
                                }

                                @Override
                                public void setValues(PreparedStatement ps, int i)
                                        throws SQLException
                                {
                                    ps.setInt(1, roleId);
                                    ps.setInt(2, userGroupIdsList.get(i));
                                }
                            });
                }

                i = 0;

                while (!questionsArray.isNull(i))
                {
                    JSONObject questionObj = questionsArray.getJSONObject(i++);
                    Integer oldQuestionId = questionObj.getInt("id");
                    questionObj.remove("id");

                    if (!questionObj.isNull("category"))
                    {
                        JSONObject categoryObj = null;
                        Integer categoryId = null;

                        if (questionObj.get("category") instanceof Integer)
                        {
                            categoryId = questionObj.getInt("category");
                            categoryObj = categoryObjectsMap.get(categoryId);
                        }
                        else
                        {
                            categoryObj = questionObj.getJSONObject("category");
                            categoryId = categoryObj.getInt("id");
                        }

                        if (!categoriesMap.containsKey(categoryId))
                        {
                            boolean hasModel = categoryObj.has("model");
                            if (hasModel)
                            {
                                JSONObject modelObj = categoryObj
                                        .getJSONObject("model");
                                modelObj.put("id", modelId);
                                if (questionsMap.isEmpty())
                                {
                                    ret.setCanWrite(
                                            modelObj.getBoolean("canWrite"));
                                    if (modelObj.has("name"))
                                    {
                                        ret.setName(modelObj.getString("name"));
                                    }
                                    ret.setPreserveOrdering(modelObj
                                            .getBoolean("preserveOrdering"));
                                    ret.setPublicAllowed(
                                            modelObj.getBoolean("publicAllowed"));
                                    newModelEntityClassName = modelObj
                                            .getString("modelEntityClassName");
                                    sameModelEntityType = entity.getClass()
                                            .getName()
                                            .equals(newModelEntityClassName);
                                }
                                categoryObj.put("model", modelObj);
                            }
                            DFCategory newCategory = new DFCategory()
                                    .fromJSONString(categoryObj.toString());
                            newCategory.setId(null);
                            if (!hasModel)
                            {
                                newCategory.setModel(entity.getDFModel());
                            }
                            PortalUtils.getHt().save(newCategory);
                            categoriesMap.put(categoryId, newCategory);
                        }

                        DFCategory category = categoriesMap.get(categoryId);
                        categoryObj.put("id", category.getId());
                        questionObj.put("category", categoryObj);

                        JSONArray questionRolesArray = questionObj
                                .getJSONArray("roles");
                        JSONArray questionDependenciesArray = null;

                        questionObj.remove("roles");
                        if (questionObj.has("dependencies"))
                        {
                            questionDependenciesArray = questionObj
                                    .getJSONArray("dependencies");
                            questionObj.remove("dependencies");
                        }
                        if (questionObj.has("siteMapping"))
                        {
                            siteMappings.put(oldQuestionId,
                                    PortalConfigHelper.getPortalConfig(
                                            questionObj.getString("siteMapping")));
                            questionObj.remove("siteMapping");
                        }

                        DFQuestion newQuestion = null;

                        if (entity.getClass() == OrganizationModelEntity.class)
                        {
                            newQuestion = new AcrmOrgQuestion()
                                    .fromJSONString(questionObj.toString());
                        }
                        else if (entity.getClass() == DivisionModelEntity.class)
                        {
                            newQuestion = new AcrmDivisionQuestion()
                                    .fromJSONString(questionObj.toString());
                        }
                        else
                        {
                            newQuestion = new DFQuestion()
                                    .fromJSONString(questionObj.toString());
                        }
                        if (!sameModelEntityType)
                        {
                            newQuestion.setAnswerField1(null);
                            newQuestion.setAnswerField1L2(null);
                            newQuestion.setAnswerField2(null);
                            newQuestion.setAnswerField3(null);
                            newQuestion.setAnswerField4(null);
                            newQuestion.setAnswerField5(null);
                        }
                        PortalUtils.getHt().save(newQuestion);
                        questionsMap.put(oldQuestionId, newQuestion);

                        int r = 0;

                        while (!questionRolesArray.isNull(r))
                        {
                            JSONObject questionRoleObj = questionRolesArray
                                    .getJSONObject(r++);
                            questionRoleObj.remove("id");

                            Integer roleId = questionRoleObj
                                    .get("dfRole") instanceof Integer
                                            ? questionRoleObj.getInt("dfRole")
                                            : null;
                            if (roleId == null)
                            {
                                JSONObject roleObj = questionRoleObj
                                        .getJSONObject("dfRole");
                                roleId = roleObj.getInt("id");
                            }
                            questionRoleObj.remove("dfRole");

                            DFQuestionRole newQuestionRole = new DFQuestionRole()
                                    .fromJSONString(questionRoleObj.toString());
                            newQuestionRole.setDfQuestion(newQuestion);
                            newQuestionRole.setDfRole(rolesMap.get(roleId));
                            PortalUtils.getHt().save(newQuestionRole);
                        }

                        if (questionDependenciesArray != null)
                        {
                            questionDependencies.put(oldQuestionId,
                                    questionDependenciesArray);
                        }
                    }
                }

                for (Entry<Integer, JSONArray> qde : questionDependencies
                        .entrySet())
                {
                    DFQuestion question = questionsMap.get(qde.getKey());
                    JSONArray questionDependenciesArray = qde.getValue();
                    int d = 0;

                    while (!questionDependenciesArray.isNull(d))
                    {
                        JSONObject questionDependencyObj = questionDependenciesArray
                                .getJSONObject(d++);
                        Integer dependsOnQuestionId = questionDependencyObj
                                .getInt("dfQuestionDependsOn");

                        questionDependencyObj.remove("id");
                        questionDependencyObj.remove("dfQuestionDependsOn");

                        DFQuestionDependency questionDependency = new DFQuestionDependency()
                                .fromJSONString(questionDependencyObj.toString());
                        questionDependency.setDfQuestion(question);
                        questionDependency.setDfQuestionDependsOn(
                                questionsMap.get(dependsOnQuestionId));

                        PortalUtils.getHt().save(questionDependency);
                    }
                }

                for (DFRole role : unusedRoles)
                {
                    DFHelper.deleteRole(role.getId());
                }

                for (Entry<Integer, PortalConfig> qsm : siteMappings.entrySet())
                {
                    DFQuestion question = questionsMap.get(qsm.getKey());
                    PortalConfig pc = qsm.getValue();
                    if (pc != null)
                    {
                        DFQuestionSiteMapping dfqsm = new DFQuestionSiteMapping();
                        dfqsm.setDfQuestion(question);
                        dfqsm.setSiteMapping(pc);

                        PortalUtils.getHt().save(dfqsm);
                    }
                }

                if (!sameModelEntityType)
                {
                    List<DFQuestion> questionsList = Lists
                            .newArrayList(questionsMap.values());

                    for (int q = 0; q < questionsList.size(); q++)
                    {
                        DFQuestion question = questionsList.get(q);
                        questionsList.remove(q);
                        try
                        {
                            DFHelper.updateAnswerMappings(question, questionsList);
                        }
                        catch (DFException e)
                        {
                            e.printStackTrace();
                        }
                        questionsList.add(q, question);
                    }
                }
            }
            catch (JSONException e)
            {
                ret = null;
                status.setRollbackOnly();
                e.printStackTrace();
            }

            return ret;
        });
    }

    private static void updateDependencyMap(DFModel model,
            DFAnswerEntity answerEntity, boolean circularDependencyCheck)
    {
        // Build question adjacency list if it exists
        Map<DFQuestion, List<DFQuestion>> dependencyMap = new LinkedHashMap<>();
        List<DFQuestionDependency> questionsWithDependencies = PortalUtils.getHt()
                .find("select dfqd from DFQuestionDependency dfqd join dfqd.dfQuestion join dfqd.dfQuestion.category where dfqd.dfQuestion.category.model=?",
                        model);
        // Map for the questions that the root question currently has hidden
        Map<Integer, Set<Integer>> rootIsHidingMap = new HashMap<>();
        // Map for the questions that the root question has a satisfying answer
        // for and are shown. When a question is added to this map as being
        // shown by the root, it will be removed from the rootIsHiding map
        Map<Integer, Set<Integer>> rootIsShowingMap = new HashMap<>();
        try
        {
            for (DFQuestionDependency questionDependency : questionsWithDependencies)
            {
                DFQuestion root = questionDependency.getDfQuestionDependsOn();
                if (!dependencyMap.containsKey(root))
                {
                    List<DFQuestion> dependents = questionsWithDependencies.stream()
                            .filter(q -> q.getDfQuestionDependsOn().getId() == root
                                    .getId())
                            .map(q -> q.getDfQuestion()).distinct()
                            .collect(Collectors.toList());
                    dependencyMap.put(root, dependents);
                }
                model.getLeadingQuestions().add(root.getId());

                if (hideQuestion(answerEntity, questionDependency, root)
                        && !Optional.ofNullable(rootIsShowingMap.get(root.getId()))
                                .map(l -> l.contains(
                                        questionDependency.getDfQuestion().getId()))
                                .orElse(false))
                {
                    if (!rootIsHidingMap.containsKey(root.getId()))
                    {
                        rootIsHidingMap.put(root.getId(), new HashSet<Integer>());
                    }
                    rootIsHidingMap.get(root.getId())
                            .add(questionDependency.getDfQuestion().getId());
                }
                else
                {
                    if (Optional.ofNullable(rootIsHidingMap.get(root.getId()))
                            .map(l -> l.contains(
                                    questionDependency.getDfQuestion().getId()))
                            .orElse(false))
                    {
                        rootIsHidingMap.get(root.getId())
                                .remove(questionDependency.getDfQuestion().getId());
                    }
                    if (!rootIsShowingMap.containsKey(root.getId()))
                    {
                        rootIsShowingMap.put(root.getId(), new HashSet<Integer>());
                    }
                    rootIsShowingMap.get(root.getId())
                            .add(questionDependency.getDfQuestion().getId());
                }
            }
            // find all the questions that are still being hidden by a root
            // question and add them to the hidden questions set to not display
            rootIsHidingMap.values().stream().forEach(s -> s.stream()
                    .forEach(i -> model.getHiddenQuestions().add(i)));
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        model.setDependencyMap(dependencyMap);

        if (circularDependencyCheck)
        {
            circularDependencyCheck(model);
        }
    }

    private static boolean hideQuestion(DFAnswerEntity answerEntity,
            DFQuestionDependency questionDependency, DFQuestion root)
            throws Exception
    {
        boolean ret = true;

        boolean rootHasDefaultAnswer = StringUtils
                .isNotEmpty(root.getDefaultChoice());

        if (answerEntity != null || rootHasDefaultAnswer)
        {
            Object dependencyValue = PropertyUtils.getProperty(questionDependency,
                    root.getAnswerField1());

            boolean isRootAnswered = isAnswered(root, answerEntity);

            Object rootValue = Optional.ofNullable(answerEntity)
                    .map(LambdaExceptionUtil.rethrowFunction(e -> PropertyUtils
                            .getProperty(e, root.getAnswerField1())))
                    .orElseGet(() -> root.getDefaultChoice());

            if (root.getType() == DFQuestion.TYPE_MULTI_CHOICE)
            {
                ret = hideQuestionMultiChoice(dependencyValue, rootValue,
                        isRootAnswered, rootHasDefaultAnswer);
            }
            else if (root.getType() == DFQuestion.TYPE_SINGLE_CHOICE)
            {
                ret = hideQuestionSingleChoice(root, dependencyValue, rootValue,
                        isRootAnswered, rootHasDefaultAnswer);
            }
            else
            {
                ret = hideQuestionDefault(dependencyValue, rootValue,
                        isRootAnswered, rootHasDefaultAnswer);
            }
        }

        return ret;
    }

    private static boolean hideQuestionMultiChoice(Object dependencyValue,
            Object rootValue, boolean isRootAnswered, boolean rootHasDefaultAnswer)
    {
        boolean hasAllValues = false;

        if (dependencyValue != null && rootValue != null
                && (isRootAnswered || rootHasDefaultAnswer))
        {
            Set<String> rootList = Arrays.asList(((String) rootValue).split("\\^"))
                    .stream().filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toSet());

            Set<String> dependencyList = Arrays
                    .asList(((String) dependencyValue).split("\\^")).stream()
                    .filter(StringUtils::isNotEmpty).collect(Collectors.toSet());

            hasAllValues = rootList.equals(dependencyList);
        }

        return (dependencyValue != null
                && ((!isRootAnswered && !rootHasDefaultAnswer) || !hasAllValues))
                || (dependencyValue == null && !isRootAnswered);
    }

    private static boolean hideQuestionSingleChoice(DFQuestion root,
            Object dependencyValue, Object rootValue, boolean isRootAnswered,
            boolean rootHasDefaultAnswer)
    {
        boolean ret = true;

        if (root.isIncludeOther() && "Other".equals(dependencyValue)
                && !"Other".equals(rootValue))
        {
            if (PortalUtils.isSiteInMultilingualMode())
            {
                boolean foundAnswerMatch = false;
                for (String choiceEntry : root.getChoiceList())
                {
                    String[] choices = choiceEntry.split("\\|");
                    if (choices.length > 1 && (rootValue.equals(choices[0])
                            || rootValue.equals(choices[1])))
                    {
                        foundAnswerMatch = true;
                        break;
                    }
                }
                ret = foundAnswerMatch;
            }
            else
            {
                ret = root.getChoiceList().contains(rootValue);
            }
        }
        else if (PortalUtils.isSiteInMultilingualMode() && dependencyValue != null
                && rootValue != null && isRootAnswered)
        {
            boolean foundAnswerMatch = rootValue.equals(dependencyValue);

            if (!foundAnswerMatch)
            {
                for (String choiceEntry : root.getChoiceList())
                {
                    String[] choices = choiceEntry.split("\\|");
                    if (choices.length > 1
                            && (rootValue.equals(choices[0])
                                    || rootValue.equals(choices[1]))
                            && (dependencyValue.equals(choices[0])
                                    || dependencyValue.equals(choices[1])))
                    {
                        foundAnswerMatch = true;
                        break;
                    }
                }
            }

            ret = !foundAnswerMatch;
        }
        else
        {
            ret = (dependencyValue != null
                    && ((!isRootAnswered && !rootHasDefaultAnswer)
                            || !dependencyValue.equals(rootValue)))
                    || (dependencyValue == null && !isRootAnswered);
        }

        return ret;
    }

    private static boolean hideQuestionDefault(Object dependencyValue,
            Object rootValue, boolean isRootAnswered, boolean rootHasDefaultAnswer)
    {
        return (dependencyValue != null
                && ((!isRootAnswered && !rootHasDefaultAnswer)
                        || !dependencyValue.equals(rootValue)))
                || (dependencyValue == null && !isRootAnswered);
    }

    public static DFRole getRoleInModel(DFModel model, UserDetailsImpl userLoggedIn)
    {
        String orderBy = model.isPreserveOrdering() ? "r.position" : "r.name";
        List roles = null;
        if (userLoggedIn == null)
        {
            roles = PortalUtils.getHt().find(
                    "from DFRole r where r.publicRole=true and r.model.id = ? order by "
                            + orderBy,
                    model.getId());
        }
        else
        {
            roles = PortalUtils.getHt().find(
                    "from DFRole r where (r.id in (select durg.role.id from DFRoleUserGroup durg where "
                            + " durg.userGroup.id in (select g.id from UserDetailsImpl u join u.groups g where u.id=?)) "
                            + " or r.id in (select du.role.id from DFRoleUser du where du.user.id=?)) "
                            + " and r.model.id = ? order by " + orderBy,
                    new Object[] { userLoggedIn.getId(), userLoggedIn.getId(), model.getId() });
        }

        DFRole role = null;
        if (roles.size() >= 1)
        {
            role = (DFRole) roles.get(0);
        }
        return role;
    }

    public static void populateCategory(DFCategory category)
    {
        List<DFQuestion> questions = PortalUtils.getHt().find(
                "from DFQuestion dfq where dfq.category=? order by dfq.position asc",
                category);
        category.setQuestions(questions);
    }

    public static boolean isModelAnswerable(DFModel model, UserDetailsImpl user,
            HttpServletRequest request)
    {
        boolean ret = false;

        List<DFQuestion> questions = getModelQuestions(model);
        DFRole role = getRoleInModel(model, PortalUtils.getUserLoggedIn(request));

        for (DFQuestion question : questions)
        {
            List<DFQuestionRole> questionRoles = getRolesForQuestion(question);
            for (DFQuestionRole questionRole : questionRoles)
            {
                if (role == null)
                {
                    ret = false;
                    break;
                }
                if (role.equals(questionRole.getDfRole())
                        && questionRole.isCanWrite() && questionRole.isCanRead())
                {
                    ret = true;
                }
            }
        }
        return ret;
    }

    public static List<DFQuestion> getModelQuestions(DFModel model,
            DFQuestion question)
    {
        String questionFilter = "";
        if (null != question && null != question.getId())
        {
            questionFilter = " and dfq.id != " + question.getId();
        }

        return PortalUtils.getHt().find(
                "from DFQuestion dfq where dfq.category.model=? " + questionFilter,
                model);
    }

    public static DFCategory getCategory(int id)
    {
        DFCategory category = null;

        try
        {
            category = (DFCategory) PortalUtils.getHt().load(DFCategory.class, id);
        }
        catch (Exception e)
        {
        }

        return category;
    }

    public static List<DFRoleUser> getRoleUsers(DFRole role)
    {
        return PortalUtils.getHt().find("from DFRoleUser dru where dru.role.id=?",
                role.getId());
    }

    public static List<DFRoleUserGroup> getDfRoleUserGroups(DFRole role)
    {
        return PortalUtils.getHt()
                .find("from DFRoleUserGroup rug where rug.role.id=?", role.getId());
    }

    public static List<DFRole> getRolesForModel(String modelId)
    {
        return PortalUtils.getHt()
                .find("from DFRole dfr where dfr.model.id='" + modelId + "'");
    }

    public static List<DFQuestionRole> getRolesForQuestion(DFQuestion question)
    {
        String orderBy = question.getCategory().getModel().isPreserveOrdering()
                ? "qr.dfRole.position"
                : "qr.dfRole.name";
        return PortalUtils.getHt().find(
                "from DFQuestionRole qr where qr.dfQuestion=? order by " + orderBy,
                question);
    }

    public static List<Object[]> getQuestionRolesForModel(DFModel model)
    {
        String orderBy = model.isPreserveOrdering() ? "qr.dfRole.position"
                : "qr.dfRole.name";
        return PortalUtils.getHt().find(
                "select qr.dfQuestion.id, qr from DFQuestionRole qr join qr.dfQuestion join qr.dfQuestion.category where qr.dfQuestion.category.model=? order by "
                        + orderBy,
                model);
    }

    public static void populateRolesForQuestions(List<DFQuestion> questions)
    {
        List<Object[]> results = PortalUtils.getHt().find(
                "select qr.dfQuestion.id, qr from DFQuestionRole qr join qr.dfQuestion where qr.dfQuestion.id in "
                        + DBUtils.buildInClause(
                                questions.toArray(new ContentItem[0])));
        Map<Integer, List<DFQuestionRole>> questionRoleMap = new HashMap<>();

        for (Object[] row : results)
        {
            Integer key = (Integer) row[0];
            List<DFQuestionRole> roles = new ArrayList<>();
            if (questionRoleMap.containsKey(key))
            {
                roles = questionRoleMap.get(key);
            }
            roles.add((DFQuestionRole) row[1]);
            questionRoleMap.put(key, roles);
        }

        for (DFQuestion question : questions)
        {
            List<DFQuestionRole> roles = questionRoleMap
                    .get(question.getId().intValue());
            question.setQuestionRoles(roles);
        }
    }

    public static DFQuestionRole getDFQuestionRoleForUserGroup(DFQuestion question,
            PersonGroup group)
    {
        DFQuestionRole questionRole = null;

        List<DFQuestionRole> questionRoles = PortalUtils.getHt().find(
                "from DFQuestionRole qr where qr.dfQuestion=? and exists(select rug.id from DFRoleUserGroup rug where rug.role=qr.dfRole and rug.userGroup=?)",
                new Object[] { question, group });

        if (!questionRoles.isEmpty())
        {
            questionRole = questionRoles.get(0);
        }

        return questionRole;
    }

    public static DFModelEntity getModelEntity(HttpServletRequest request)
    {
        DFModelEntity modelEntity = null;

        try
        {
            Class entityClass = ClassUtils
                    .loadClass(request.getParameter("dfModelEntityType"));

            Integer dfModelEntityId = Integer.valueOf(
                    request.getParameter("dfModelEntityId"));

            modelEntity = (DFModelEntity) PortalUtils.getHt().get(entityClass,
                    dfModelEntityId);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return modelEntity;
    }

    public static int getNextCategoryPosition(DFModel dfModel)
    {
        Integer max = (Integer) PortalUtils.getHt()
                .find("select max(c.position) from DFCategory c where c.model=?",
                        dfModel)
                .get(0);
        return null == max ? 1 : (max.intValue() + 1);
    }

    private static void createQuestionLog(DFQuestion question,
            HttpServletRequest request)
    {
        DFQuestionLog log = new DFQuestionLog();
        log.setDfQuestion(question);
        log.setDateCreated(new Date());
        log.setUpdatedBy(PortalUtils.getUserLoggedIn(request));
        PortalUtils.getHt().saveOrUpdate(log);
    }

    public static void processFieldMappings(String value,
            DFAnswerEntity answerEntity, String field)
    {
        if (answerEntity != null && !StringUtils.isEmpty(field))
        {
            // int and boolean answers have special handling...
            Object answer = value;
            if (field.matches("i[0-9]+"))
            {
                if (StringUtils.isEmpty(value))
                {
                    answer = 0;
                }
                else
                {
                    // expect failure if the string is not in integer format
                    answer = Integer.parseInt(value);
                }

                // else assume (value instanceof Integer)
                // if it's not, then expect failure.

                try
                {
                    PropertyUtils.setProperty(answerEntity, field, answer);
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
            else if (field.matches("b[0-9]+"))
            {
                if (StringUtils.isEmpty(value))
                {
                    answer = false;
                }
                else
                {
                    answer = getBooleanValue(value);
                }

                // else assume (value instanceof Boolean)
                // if it's not, then expect failure.

                try
                {
                    PropertyUtils.setProperty(answerEntity, field,
                            (value == null ? 0 : answer));
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
            else if (field.matches("f[0-9]+") || "totalTrackedHours".equals(field))
            {
                if (StringUtils.isEmpty(value))
                {
                    answer = 0.0;
                }
                else
                {
                    // expect failure if the string is not in integer format
                    answer = Double.parseDouble(value);
                }

                // else assume (value instanceof Integer)
                // if it's not, then expect failure.

                try
                {
                    PropertyUtils.setProperty(answerEntity, field, answer);
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }

            }
            else if (field.matches("d[0-9]+"))
            {
                if (StringUtils.isEmpty(value))
                {
                    answer = new Date();
                }
                else
                {
                    // expect failure if the string is not in integer format
                    answer = DateUtils.parseDate(value,
                            AcrmDataImportHelper.DATE_FORMAT, null);
                    if (answer == null)
                    {
                        answer = DateUtils.parseDate(value,
                                DateUtils.DF_SHORT_DATE_TIME, null);
                    }
                    if (answer == null)
                    {
                        answer = DateUtils.parseDate(value, DateUtils.DF_SHORT_DATE,
                                null);
                    }
                }

                // else assume (value instanceof Integer)
                // if it's not, then expect failure.

                try
                {
                    PropertyUtils.setProperty(answerEntity, field, answer);
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }

            }
            else
            {
                try
                {
                    if (field.matches("s[0-9]+")
                            && ((String) answer).length() > 255)
                    {
                        answer = ((String) answer).substring(0, 254);
                    }

                    PropertyUtils.setProperty(answerEntity, field, answer);
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }
        // try
        // {
        // PropertyUtils.setProperty(entity, fieldMapping, value);
        // }
        // catch (Exception e)
        // {
        // e.printStackTrace();
        // }
    }

    public static ModelAndView ajaxLoadDataImportProgress(
            HttpServletRequest request) throws Exception
    {

        StringBuilder hql = new StringBuilder();
        Date now = new Date();
        String todayStart = DateUtils.formatDate(DateUtils.getStartDate(now),
                DBUtils.DB_DATE_TIME_FORMAT, null);
        String todayEnd = DateUtils.formatDate(DateUtils.getEndDate(now),
                DBUtils.DB_DATE_TIME_FORMAT, null);
        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);

        hql.append(
                "select i.id, i.importDataType, i.importFileName, i.createdBy.preferredFirstName, i.createdBy.lastName, i.createdBy.username, i.status, i.dateImportStart, i.dateImportFinish, i.importProgress, i.importProgressMessage, i.totalDataRows, i.rowsImported, i.rowsFailed, i.importErrorsFileName from AcrmDataImport i where i.status > ")
                .append(AcrmDataImport.STATUS_READY_FOR_IMPORT)
                .append(" and (i.dateImportFinish is null or i.dateImportFinish between '")
                .append(todayStart).append("' and '").append(todayEnd).append("')");

        if (null != userLoggedIn
                .getGroupWithName(PersonGroupHelper.SYSTEM_CONFIGURATION_RIGHTS)
                || "root".equals(userLoggedIn.getUsername()))
        {
            hql.append(" and i.createdBy.id=").append(userLoggedIn.getId());
        }
        hql.append(" order by i.dateImportStart desc");

        List<Object[]> importJobs = PortalUtils.getHt().find(hql.toString());

        ModelAndView mv = new ModelAndView("df/df_importDataProgress");
        mv.addObject(SiteController.SHORT_CIRCUIT_ATTRIBUTE_NAME, Boolean.TRUE);
        mv.addObject("importJobs", importJobs);
        return mv;

    }

    public static Map<DFCategory, LinkedHashMap<DFQuestion, DFQuestionStats>> getQuestionStats(
            DFModel model, Class<? extends DFAnswerEntity> answerEntity,
            String hqlAlias, String staticWhereHql, HttpServletRequest request)
    {
        Locale locale = PortalUtils.getLocale(request);
        DFRole role = getRoleInModel(model, PortalUtils.getUserLoggedIn(request));
        model.setCurrentRole(role);

        Map<DFCategory, LinkedHashMap<DFQuestion, DFQuestionStats>> statsMap = new LinkedHashMap<>();
        List<DFCategory> categories = getCategories(model);
        for (DFCategory category : categories)
        {
            populateCategory(category);
            LinkedHashMap<DFQuestion, DFQuestionStats> questionMap = new LinkedHashMap<>();
            for (DFQuestion question : category.getQuestions())
            {
                List<DFQuestionRole> questionRoles = getRolesForQuestion(question);
                question.setQuestionRoles(questionRoles);
                question.setRole(role);

                if (question.isCanShowInReports())
                {
                    if (StringUtils.isEmpty(hqlAlias))
                    {
                        hqlAlias = "s";
                    }
                    if (staticWhereHql == null)
                    {
                        staticWhereHql = "";
                    }
                }

                DFQuestionStats stats = new DFQuestionStats(question, answerEntity,
                        hqlAlias, staticWhereHql, locale);
                questionMap.put(question, stats);
            }
            if (!questionMap.isEmpty())
            {
                statsMap.put(category, questionMap);
            }
        }
        return statsMap;
    }

    public static Map getQuestionsStats(DFModel model,
            Class<? extends DFAnswerEntity> answerEntity, String hqlAlias,
            String staticWhereHql, HttpServletRequest request)
    {
        Locale locale = PortalUtils.getLocale(request);
        Map ret = new HashMap();

        try
        {
            Map<DFCategory, LinkedHashMap<DFQuestion, DFQuestionStats>> statsMap = getQuestionStats(
                    model, answerEntity, hqlAlias, staticWhereHql, request);

            ret.put("statsMap", statsMap);

            Map<String, Object> chartStats = new HashMap<>();
            for (Map map : statsMap.values())
            {
                Set<Entry> entries = map.entrySet();
                for (Entry<DFQuestion, DFQuestionStats> entry : entries)
                {
                    DFQuestion question = entry.getKey();
                    DFQuestionStats stats = entry.getValue();

                    switch (question.getType())
                    {
                        case DFQuestion.TYPE_TEXT:
                        case DFQuestion.TYPE_DATE:
                        case DFQuestion.TYPE_LARGE_TEXT:
                        case DFQuestion.TYPE_FILE_UPLOAD:
                            createBarChartForText(question, stats, chartStats,
                                    locale);
                            break;
                        case DFQuestion.TYPE_BOOLEAN:
                        case DFQuestion.TYPE_SINGLE_CHOICE:
                        case DFQuestion.TYPE_MULTI_CHOICE:
                            createPieChart(question, stats, chartStats, locale);
                            break;
                        case DFQuestion.TYPE_RATING:
                        case DFQuestion.TYPE_INTEGER:
                            createBarChart(question, stats, chartStats, locale);
                            break;
                        case DFQuestion.TYPE_MATRIX_MULTI:
                        case DFQuestion.TYPE_MATRIX_SINGLE:
                            createStackedBar(question, stats, chartStats);
                            break;
                        case DFQuestion.TYPE_TREE:
                            createTreeBarChart(question, stats, chartStats, locale);
                            break;
                        case DFQuestion.TYPE_FLOAT:
                            createScatterPlot(question, stats, chartStats, locale);
                            break;
                        default:
                            break;
                    }
                }
                ret.put("chartStats", chartStats);
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return ret;
    }

    public static void populateQuestionsStats(ModelAndView mv, DFModel model,
            Class<? extends DFAnswerEntity> answerEntity, String hqlAlias,
            String staticWhereHql, HttpServletRequest request)
    {
        Map map = getQuestionsStats(model, answerEntity, hqlAlias, staticWhereHql,
                request);
        mv.addObject("statsMap", map.get("statsMap"));
        mv.addObject("stats", map.get("chartStats"));
    }

    private static void createBarChartForText(DFQuestion question,
            DFQuestionStats stats, Map<String, Object> chartStats, Locale locale)
    {
        try
        {
            Map chartData = new LinkedHashMap();
            Map<String, Integer> countedChoices = stats.getCountedChoices();

            for (Entry<String, Integer> cc : countedChoices.entrySet())
            {
                chartData.put(cc.getKey(), cc.getValue());
            }

            JSONObject barConfig = ChartUtils.getBarChart("",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.sa_answerStats.NoofRespondents", null, locale),
                    chartData, "", PortalUtils.getMessageSource().getMessage(
                            "i18n.sa_answerStats.SurveyCount", null, locale),
                    true);

            barConfig.getJSONObject("legend").put("enabled", false);

            chartStats.put("typeChart" + question.getId(), barConfig);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    public static void createScatterPlot(DFQuestion question, DFQuestionStats stats,
            Map<String, Object> chartStats, Locale locale)
    {
        try
        {
            HashMap chartData = new LinkedHashMap();
            Map<Double, Double> plotDoublePoints = stats.getPlotFloatPoints();
            for (Entry<Double, Double> cc : plotDoublePoints.entrySet())
            {
                chartData.put(new Double(cc.getKey()), cc.getValue());
            }

            Map<String, Map<Double, Double>> plotSeriesCollection = new HashMap<>();
            plotSeriesCollection.put(question.getQuestionText(), chartData);

            JSONObject scatterConfig = ChartUtils.getScatterPlot("", "",
                    plotSeriesCollection,
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.sa_answerStats.FloatValues", null, locale),
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.sa_answerStats.NoofRespondents", null, locale));

            chartStats.put("typeScatter" + question.getId(), scatterConfig);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    public static void createTreeBarChart(DFQuestion question,
            DFQuestionStats stats, Map<String, Object> chartStats, Locale locale)
    {
        try
        {
            HashMap chartData = new LinkedHashMap();
            Map<String, String> countedTreeNodes = stats.getCountedTreeNodes();
            for (Entry<String, String> cc : countedTreeNodes.entrySet())
            {
                String percentValue = cc.getValue();
                percentValue = percentValue.substring(0, percentValue.indexOf('%'));
                chartData.put(cc.getKey(), new Double(percentValue));
            }

            Map<String, Integer> sortedMapByKeys = new TreeMap<>();
            sortedMapByKeys.putAll(chartData);

            JSONObject barConfig = ChartUtils.getBarChart("",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.sa_answerStats.Respondents", null, locale),
                    sortedMapByKeys, "", PortalUtils.getMessageSource().getMessage(
                            "i18n.sa_answerStats.Percentage", null, locale),
                    true);
            barConfig.getJSONObject("legend").put("enabled", false);
            barConfig.getJSONObject("tooltip").put("formatter",
                    "function(){return this.x + \": \" + this.y + \"%\";}");
            chartStats.put("typeChart" + question.getId(), barConfig);
            chartStats.put("totalResponds", chartData.size());
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    public static void createStackedBar(DFQuestion question, DFQuestionStats stats,
            Map<String, Object> chartStats)
    {
        try
        {
            Map series = new LinkedHashMap();

            List<String> rowLabels = question.getMatrixRowLabels();
            List<String> colLabels = question.getMatrixColumnLabels();

            Map<String, String> calcMatrixChoices = stats.getCalcMatrixChoices();

            for (String col : colLabels)
            {
                String row = "";
                Double[] results = new Double[rowLabels.size()];
                for (int i = 0; i < rowLabels.size(); i++)
                {
                    row = rowLabels.get(i);
                    String statKey = row + "~" + col;

                    if (!StringUtils.isEmpty(calcMatrixChoices.get(statKey)))
                    {
                        String percentValue = calcMatrixChoices.get(statKey);
                        percentValue = percentValue.substring(0,
                                percentValue.indexOf('%'));
                        results[i] = Double.parseDouble(percentValue);
                    }
                    else
                    {
                        results[i] = 0d;
                    }
                }
                series.put(col, results);
            }

            JSONObject barConfig = ChartUtils.getStackedBarChart("",
                    CollectionUtils.convertToStringArray(rowLabels), series,
                    "units", true);

            chartStats.put("typeStacked" + question.getId(), barConfig);
            chartStats.put("totalResponds", stats.getTotalAnswers());
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    // Unused Method
    public static void createBubbleChart3D(DFQuestion question,
            DFQuestionStats stats, Map<String, Object> chartStats)
    {
        try
        {
            Map series = new LinkedHashMap();

            List rowLabels = question.getMatrixRowLabels();
            List colLabels = question.getMatrixColumnLabels();

            Map<String, String> calcMatrixChoices = stats.getCalcMatrixChoices();

            for (Object col : colLabels)
            {
                String row = "";
                Object[] results = new Object[rowLabels.size()];
                for (int i = 0; i < rowLabels.size(); i++)
                {
                    row = (String) rowLabels.get(i);
                    String statKey = row + "~" + col;

                    if (!StringUtils.isEmpty(calcMatrixChoices.get(statKey)))
                    {
                        String percentValue = calcMatrixChoices.get(statKey);
                        percentValue = percentValue.substring(0,
                                percentValue.indexOf('%'));
                        results[i] = new Double(percentValue);
                    }
                    else
                    {
                        results[i] = 0d;
                    }
                }
                series.put(col, results);
            }

            JSONObject bubbleConfig = ChartUtils.getBubbleChart3D(
                    question.getQuestionText(),
                    CollectionUtils.convertToIntegerArray(
                            CollectionUtils.convertToStringArray(rowLabels)),
                    series, "", "units", true);

            chartStats.put("typeChart" + question.getPosition(), bubbleConfig);
            chartStats.put("totalResponds", stats.getTotalAnswers());
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

    }

    public static void createBarChart(DFQuestion question, DFQuestionStats stats,
            Map chartStats, Locale locale)
    {
        try
        {
            HashMap chartData = new LinkedHashMap();
            Map<Integer, Integer> plotPoints = stats.getPlotPoints();
            for (Entry<Integer, Integer> cc : plotPoints.entrySet())
            {
                chartData.put(cc.getKey().toString(), new Double(cc.getValue()));
            }

            Map<String, Integer> sortedMapByKeys = new TreeMap<>();
            sortedMapByKeys.putAll(chartData);

            JSONObject barConfig = ChartUtils.getBarChart("",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.sa_answerStats.NoofRespondents", null, locale),
                    sortedMapByKeys, "Unit",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.sa_answerStats.SurveyCount", null, locale),
                    true);

            barConfig.getJSONObject("legend").put("enabled", false);

            chartStats.put("typeChart" + question.getId(), barConfig);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    public static void deleteModel(DFModelEntity modelEntity)
    {
        DFModel model = modelEntity.getDFModel();
        if (model != null)
        {
            Class<? extends DFAnswerEntity> answerEntity = modelEntity.getAnswerEntityClass();
            CommandQueryTemplate ht = PortalUtils.getHt();

            List<DFQuestion> questions = getModelQuestions(model);

            for (DFQuestion dfQuestion : questions)
            {
                updateQuestionAnswerEntityForDeletion(dfQuestion, answerEntity);
            }

            DeletionNodeHelper.deleteContentItems(DFCategory.class,
                    "model = " + model.getId(), null);

            if (answerEntity != null)
            {
                List<DFAnswerEntity> answers = ht.find("from "
                        + answerEntity.getName() + " ae where ae.dfModel = ?",
                        model);
                for (DFAnswerEntity answer : answers)
                {
                    answer.setDfModel(null);
                    ht.update(answer);
                }
            }

            List<Integer> roleIds = ht
                    .find("select r.id from DFRole r where r.model.id = "
                            + model.getId());
            for (Integer roleId : roleIds)
            {
                deleteRole(roleId);
            }

            modelEntity.setDFModel(null);
            ht.update(modelEntity);
            ht.delete(model);
        }
    }

    public static boolean deleteCategory(DFCategory category,
            DFModelEntity modelEntity)
    {
        List<DFQuestion> questions = category.getQuestions();

        return (boolean) PortalUtils.getTransactionTemplate().execute(status -> {
            boolean ret = true;

            if (modelEntity != null)
            {
                for (DFQuestion question : questions)
                {
                    DFHelper.updateQuestionAnswerEntityForDeletion(question,
                            modelEntity.getAnswerEntityClass());
                }
            }

            if (DeletionNodeHelper.deleteContentItem(category) == 0)
            {
                ret = false;
            }

            return ret;
        });
    }

    public static boolean deleteQuestion(DFQuestion question,
            Class<? extends DFAnswerEntity> answerEntityClass)
    {
        return (boolean) PortalUtils.getTransactionTemplate().execute(status -> {
            boolean ret = true;

            DFHelper.updateQuestionAnswerEntityForDeletion(question,
                    answerEntityClass);

            int deletedNodes = DeletionNodeHelper.deleteContentItem(question);

            if (deletedNodes == 0)
            {
                ret = false;
            }

            return ret;
        });
    }

    public static void deleteQuestionQualifier(DFQuestionQualifier qualifier)
    {
        PortalUtils.getHt().delete(qualifier);
    }

    public static void deleteRole(Integer roleId)
    {
        DeletionNodeHelper.deleteFromTable("df_role", roleId);
    }

    public static void updateQuestionAnswerEntityForDeletion(DFQuestion question,
            Class answerEntityClass)
    {
        List<String> answerFields = new LinkedList<>();
        answerFields.add(question.getAnswerField1());
        answerFields.add(question.getAnswerField2());
        answerFields.add(question.getAnswerField3());
        answerFields.add(question.getAnswerField4());
        answerFields.add(question.getAnswerField5());

        for (int af = 0; af < answerFields.size(); af++)
        {
            String answerField = answerFields.get(af);
            if (answerEntityClass != null && !StringUtils.isEmpty(answerField))
            {
                boolean primitive = ClassUtils.isPrimitive(answerEntityClass,
                        answerField);

                try
                {
                    DFAnswerEntity ae = (DFAnswerEntity) ClassUtils
                            .loadClass(answerEntityClass.getName()).newInstance();
                    if (af == 1
                            && question.getType() == DFQuestion.TYPE_FILE_UPLOAD)
                    {
                        PortalUtils.getJt().query("select " + answerField + " from "
                                + ae.getTableName() + " where " + answerField
                                + " is not NULL and dfModel = "
                                + question.getCategory().getModel().getId(), rs -> {
                                    String filePathUUID = rs.getString(1);
                                    FilePathUtils.deleteFileByUUID(filePathUUID,
                                            true);
                                });
                    }
                    PortalUtils.getJt().execute("update " + ae.getTableName()
                            + " set " + answerField + " = "
                            + (primitive ? "0" : "NULL") + " where dfModel = "
                            + question.getCategory().getModel().getId());
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void prePopulateAnswerEntity(DFAnswerEntity answer, DFModel model)
    {
        List<DFQuestion> questions = getModelQuestions(model);
        for (DFQuestion question : questions)
        {
            if (!StringUtils.isEmpty(question.getDefaultMappingKey()))
            {
                try
                {
                    Object toSet = PropertyUtils.getProperty(answer,
                            question.getAnswerField1());
                    if (toSet == null || StringUtils.isEmpty(toSet.toString()))
                    {
                        PropertyUtils.setProperty(answer,
                                question.getAnswerField1(),
                                PropertyUtils.getProperty(answer,
                                        question.getDefaultMappingKey()));
                    }
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void createPieChart(DFQuestion question, DFQuestionStats stats,
            Map chartStats, Locale locale)
    {
        try
        {
            Map chartData = new LinkedHashMap();
            Map<String, Integer> countedChoices = stats.getCountedChoices();

            for (Entry<String, Integer> cc : countedChoices.entrySet())
            {
                String[] cKey = cc.getKey().contains("|") ? cc.getKey().split("\\|")
                        : new String[] { cc.getKey(), cc.getKey() };
                chartData.put(LocaleUtils.isL1(locale) ? cKey[0] : cKey[1],
                        new Double(cc.getValue()));
            }

            JSONObject pieConfig = ChartUtils.getPieChart("",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.sa_answerStats.totalSurveys", null, locale)
                            + stats.getTotalAnswers(),
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.sa_answerStats.Surveys", null, locale),
                    chartData);

            pieConfig.getJSONObject("legend").put("verticalAlign", "top");
            pieConfig.getJSONObject("legend").put("y", 40);
            pieConfig.getJSONObject("plotOptions").getJSONObject("pie")
                    .getJSONObject("dataLabels")
                    .put("formatter", "function(){return null;}");

            chartStats.put("typeChart" + question.getId(), pieConfig);
        }
        catch (JSONException e)
        {
            e.printStackTrace();
        }
    }

    public static boolean hasQuestions(DFModel model)
    {
        return ((Integer) PortalUtils.getHt()
                .find("select count(q) from DFQuestion q where q.category.model=?",
                        model)
                .get(0)).intValue() > 0;
    }

    public static void populateMassUpdatePage(ModelAndView mv,
            HttpServletRequest request, DFModelEntity modelEntity)
    {
        DFModel model = null;
        if (modelEntity != null)
        {
            model = modelEntity.getDFModel();
            DFHelper.populateModel(model, true, null,
                    PortalUtils.getUserLoggedIn(request));
        }
        mv.addObject("dfModel", model);
        mv.addObject("selectedIds", request.getParameter("selectedIds"));
        mv.addObject("massUpdateAction",
                !StringUtils.isEmpty(request.getParameter("massUpdateAction"))
                        ? request.getParameter("massUpdateAction")
                        : "massUpdateAnswers");
    }

    public static int massUpdateAnswers(HttpServletRequest request,
            DFModelEntity modelEntity) throws Exception
    {
        JSONArray selectedIds = new JSONArray(request.getParameter("selectedIds"));

        for (int i = 0; i < selectedIds.length(); i++)
        {
            DFAnswerEntity answer = PortalUtils.getHt().load(
                    modelEntity.getAnswerEntityClass(), selectedIds.getInt(i));
            DFHelper.bindAnswers(modelEntity, answer, request,
                    PortalUtils.getUserLoggedIn(request), true);
        }

        return selectedIds.length();
    }

    public static DFCategory convertCategory2(DFModel model, Category2 oldCategory,
            boolean persist)
    {
        DFCategory category = new DFCategory();
        category.setModel(model);
        category.setName(oldCategory.getName());
        category.setNameL2(oldCategory.getName());
        category.setPosition(oldCategory.getPosition());

        if (persist)
        {
            PortalUtils.getHt().save(category);
        }

        return category;
    }

    public static DFQuestion convertQuestion2(Question2 oldQuestion, DFModel model,
            DFCategory category, Map<String, DFRole> roleMap)
    {
        DFQuestion question = new DFQuestion();

        question.setAdminOnly(oldQuestion.isAdminOnly());
        question.setAnswerField1(oldQuestion.getAnswerField1());
        question.setAnswerField2(oldQuestion.getAnswerField2());
        question.setAnswerField3(oldQuestion.getAnswerField3());
        question.setAnswerField4(oldQuestion.getAnswerField4());
        question.setAnswerField5(oldQuestion.getAnswerField5());
        question.setCategory(category);
        question.setChoices(oldQuestion.getChoices());
        question.setFieldMappings(oldQuestion.getFieldMappings());
        question.setHeader(oldQuestion.getDescription());
        question.setIncludeOther(oldQuestion.isIncludeOther());
        question.setLimitAnswer(oldQuestion.getLimitAnswer());
        question.setMakeRequired(oldQuestion.isRequired());
        question.setMappingKey(oldQuestion.getMappingKey());
        question.setMatrixColumns(oldQuestion.getMatrixColumns());
        question.setMatrixRows(oldQuestion.getMatrixRows());
        question.setMax(oldQuestion.getMax());
        question.setMaxUploadSize(oldQuestion.getMaxUploadSize());
        question.setMin(oldQuestion.getMin());
        question.setPosition(oldQuestion.getPosition());
        question.setQuestionText(oldQuestion.getQuestionText());
        question.setTreeNodes(oldQuestion.getTreeNodes());
        question.setType(oldQuestion.getType());
        question.setUsingNA(oldQuestion.isUsingNA());
        question.setValidated(oldQuestion.isValidated());
        PortalUtils.getHt().save(question);

        try
        {
            handleUserTypePermissions(oldQuestion.getJsonUserModel(), model,
                    question, roleMap);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return question;
    }

    public static void handleUserTypePermissions(String userModelJsonString,
            DFModel model, DFQuestion question, Map<String, DFRole> roleMap)
            throws Exception
    {
        if (!StringUtils.isEmpty(userModelJsonString))
        {
            JSONArray userModel = new JSONArray(userModelJsonString);
            for (int i = 0; i < userModel.length(); i++)
            {
                JSONObject userTypeObj = userModel.getJSONObject(i);
                if (roleMap.containsKey(userTypeObj.getString("userTypeLabel")))
                {
                    DFRole role = roleMap
                            .get(userTypeObj.getString("userTypeLabel"));

                    DFQuestionRole questionRole = new DFQuestionRole();
                    questionRole.setCanRead(role.isDefaultRead());
                    questionRole.setCanSearch(role.isDefaultSearch());
                    questionRole.setCanShowInResults(role.isDefaultShowInResults());
                    questionRole.setCanShowInReports(role.isDefaultShowInReports());
                    questionRole.setCanWrite(role.isDefaultWrite());
                    questionRole.setRequired(
                            question.isMakeRequired() || role.isDefaultRequired());
                    questionRole.setDfQuestion(question);
                    questionRole.setDfRole(role);
                    PortalUtils.getHt().save(questionRole);
                }
            }
        }
    }

    public static boolean canWrite(DFModel model, UserDetailsImpl user)
    {
        return PortalUtils.getHt().findInt(
                "select count(qr) from DFQuestionRole qr where (qr.dfRole.id in (select drug.role.id from DFRoleUserGroup drug "
                        + "where drug.userGroup.id in (select g.id from UserDetailsImpl u join u.groups g where u=?)) "
                        + "or qr.dfRole.id in (select dru.role from DFRoleUser dru where dru.user=?)) and qr.canWrite=true and qr.dfRole.model=?",
                new Object[] { user, user, model }) > 0;
    }

    public static SearchModel getSearchModel_forDFModel(SiteElement se,
            DFModel dfModel, String entityTitle, Class entityClass, String hqlAlias,
            String ownerUserHql, HttpServletRequest request)
    {
        SearchModel searchModel = new SearchModel();

        if (se != null)
        {
            searchModel.setOwnerModuleClassName(se.getContentItemClass().getName());
            searchModel.setOwnerModuleId(se.getContentItemId().toString());
        }

        searchModel.setState(SearchModel.STATE_CONFIG_MASTER);
        searchModel.setCanSave(true);
        searchModel.setCanExport(true);
        searchModel.setCanEmail(true);
        searchModel.setCanEmailCampaign(true);
        searchModel.setCanViewCriteria(true);
        searchModel.setCanViewDetails(true);
        searchModel.setShowQuestionOrder(false);
        searchModel.setCanReturn(false);

        Entity master = new Entity(entityTitle, entityClass, hqlAlias, "id",
                DFHelper.getCriteriaModelForDFModel(dfModel, entityTitle, hqlAlias,
                        ownerUserHql, request),
                hqlAlias + ".dateCreated", "asc", true);

        if (dfModel != null)
        {
            String staticWhereHql = " " + hqlAlias + ".dfModel=" + dfModel.getId();

            master.setStaticWhereHql(staticWhereHql);
        }
        searchModel.setMasterEntity(master);

        return searchModel;
    }

    public static CriteriaModel getCriteriaModelForDFModel(DFModel dfModel,
            String title, String hqlAlias, String ownerUserHql,
            HttpServletRequest request, Map<Integer, Boolean[]> forceReadHiddenMap)
    {
        return getCriteriaModelForDFModel(dfModel, title, hqlAlias, ownerUserHql,
                true, false, false, null, PortalUtils.getLocale(request),
                PortalUtils.getUserLoggedIn(request), forceReadHiddenMap);
    }

    public static CriteriaModel getCriteriaModelForDFModel(DFModel dfModel,
            String title, String hqlAlias, String ownerUserHql,
            HttpServletRequest request)
    {
        return getCriteriaModelForDFModel(dfModel, title, hqlAlias, ownerUserHql,
                true, false, false, null, PortalUtils.getLocale(request),
                PortalUtils.getUserLoggedIn(request), null);
    }

    public static CriteriaModel getCriteriaModelForDFModel(DFModel dfModel,
            String title, String hqlAlias, String ownerUserHql,
            boolean includeBaseQuestions, boolean forContact, boolean inMaster,
            CriteriaGroup divCG, Locale locale, UserDetailsImpl userLoggedIn)
    {
        return getCriteriaModelForDFModel(dfModel, title, hqlAlias, ownerUserHql,
                includeBaseQuestions, forContact, inMaster, divCG, locale,
                userLoggedIn, null);
    }

    public static CriteriaModel getCriteriaModelForDFModel(DFModel dfModel,
            String title, String hqlAlias, String ownerUserHql,
            boolean includeBaseQuestions, boolean forContact, boolean inMaster,
            CriteriaGroup divCG, Locale locale, UserDetailsImpl userLoggedIn,
            Map<Integer, Boolean[]> forceLowerPermissionsMap)
    {
        CriteriaModel criteriaModel = new CriteriaModel();
        CriteriaGroup cg = new CriteriaGroup(title);

        getCriteriaQuestionsForDfModel(dfModel, hqlAlias, criteriaModel, forContact,
                inMaster, locale, userLoggedIn, forceLowerPermissionsMap);

        if (includeBaseQuestions)
        {
            if ("organization".equalsIgnoreCase(hqlAlias)
                    || hqlAlias.contains("organization"))
            {
                // we are generically using ownerUserHql as userStatus here
                populateBaseCriteriaQuestionsForOrg(hqlAlias, ownerUserHql, cg,
                        locale, forContact);
                if (divCG != null && (hqlAlias.contains("company.organization")
                        || hqlAlias.contains("division.organization")
                        || hqlAlias.contains("crmCompany.organization")))
                {
                    populateBaseCriteriaQuestionsForOrg(
                            hqlAlias.replace(".organization", ""), ownerUserHql,
                            divCG, locale, forContact);
                }
                else if (hqlAlias.contains("cus_company"))
                {
                    populateBaseCriteriaQuestionsForOrg("cus_company", ownerUserHql,
                            divCG, locale, forContact);
                }
                else if (divCG != null)
                {
                    populateBaseCriteriaQuestionsForOrg("c", ownerUserHql, divCG,
                            locale, forContact);
                }
            }
            else if ("c".equalsIgnoreCase(hqlAlias) || hqlAlias.contains("c"))
            {
                // we are generically using ownerUserHql as userStatus here
                populateBaseCriteriaQuestionsForOrg(hqlAlias, ownerUserHql, cg,
                        locale, forContact);
            }
            else
            {
                populateBaseCriteriaQuestions(hqlAlias, ownerUserHql, cg, locale);
            }
            criteriaModel.addCriteriaGroup(cg);
            if (divCG != null)
            {
                criteriaModel.addCriteriaGroup(divCG);

            }
        }

        return criteriaModel;
    }

    public static void populateCriteriaGroupsForDFModel(DFModel dfModel,
            String hqlAlias, CriteriaModel criteriaModel, String locale,
            UserDetailsImpl userLoggedIn)
    {
        criteriaModel.getCriteriaGroups().addAll(getCriteriaGroupsForDFModel(
                dfModel, hqlAlias, locale, userLoggedIn));
    }

    public static List<CriteriaGroup> getCriteriaGroupsForDFModel(DFModel dfModel,
            String hqlAlias, String locale, UserDetailsImpl userLoggedIn)
    {
        var groups = new ArrayList<CriteriaGroup>();
        DFHelper.populateModel(dfModel, false, null, userLoggedIn);
        List<DFCategory> dfCategories = dfModel.getCategories();
        for (DFCategory category : dfCategories)
        {
            var cg = new CriteriaGroup(category.getName());
            cg.setQuestions(getCriteriaQuestions(category.getQuestions(), hqlAlias,
                    locale));
            groups.add(cg);
        }
        return groups;
    }

    public static void getCriteriaQuestionsForDfModel(DFModel dfModel,
            String hqlAlias, CriteriaModel criteriaModel, boolean forContact,
            boolean inMaster, Locale locale, UserDetailsImpl userLoggedIn)
    {
        getCriteriaQuestionsForDfModel(dfModel, hqlAlias, criteriaModel, forContact,
                inMaster, locale, userLoggedIn, null);
    }

    public static void getCriteriaQuestionsForDfModel(DFModel dfModel,
            String hqlAlias, CriteriaModel criteriaModel, boolean forContact,
            boolean inMaster, Locale locale, UserDetailsImpl userLoggedIn,
            Map<Integer, Boolean[]> forceLowerPermissionsMap)
    {
        List<CriteriaGroup> cgs = criteriaModel.getCriteriaGroups();

        CriteriaGroup cg;
        if (dfModel != null)
        {
            DFHelper.populateModel(dfModel, false, null, userLoggedIn,
                    forceLowerPermissionsMap);

            List<DFCategory> dfCategories = dfModel.getCategories();
            for (DFCategory category : dfCategories)
            {
                cg = new CriteriaGroup(LocaleUtils.isL1(locale) ? category.getName()
                        : category.getNameL2());
                cg.setQuestions(
                        DFHelper.getCriteriaQuestions(category.getQuestions(),
                                hqlAlias, locale.toString(), forContact, inMaster));
                cgs.add(cg);
            }
        }
        criteriaModel.setCriteriaGroups(cgs);
    }

    public static List<Object[]> getQualifiers(DFQualifierEntity entity)
    {
        String className = entity.getQualifierClass().getSimpleName();
        String fieldName = entity.getQualifierEntityFieldName();
        return PortalUtils.getHt().find(
                "select qual.question.type, qual.acceptableValues, qual.minAcceptableValue, qual.maxAcceptableValue, qual.question.answerField1, qual.question.id from "
                        + className + " qual where qual." + fieldName + ".id=?",
                entity.getId());
    }

    public static void saveQuestionQualifier(DFQuestionQualifier qualifier,
            HttpServletRequest request)
    {
        int questionType = qualifier.getQuestion().getType();
        if (request.getParameterValues("acceptableValues") != null
                && request.getParameterValues("acceptableValues").length > 1)
        {
            qualifier.setAcceptableValues(StringUtils
                    .join(request.getParameterValues("acceptableValues"), "\r\n")
                    .trim());
        }
        else
        {
            qualifier.setAcceptableValues(request.getParameter("acceptableValues"));
        }
        if (!StringUtils.isEmpty(request.getParameter("minAcceptableValue")))
        {
            if (questionType == DFQuestion.TYPE_DATE)
            {
                Date minDate = DateUtils.getDatepickerVal(request,
                        "minAcceptableValue");
                if (minDate != null)
                {
                    qualifier.setMinAcceptableValue(
                            request.getParameter("minAcceptableValue"));
                }
            }
            else
            {
                qualifier.setMinAcceptableValue(
                        request.getParameter("minAcceptableValue"));
            }
        }
        else
        {
            qualifier.setMinAcceptableValue("min");
        }
        if (!StringUtils.isEmpty(request.getParameter("maxAcceptableValue")))
        {
            if (questionType == DFQuestion.TYPE_DATE)
            {
                Date maxDate = DateUtils.getDatepickerVal(request,
                        "maxAcceptableValue");
                if (maxDate != null)
                {
                    qualifier.setMaxAcceptableValue(
                            request.getParameter("maxAcceptableValue"));
                }
            }
            else
            {
                qualifier.setMaxAcceptableValue(
                        request.getParameter("maxAcceptableValue"));
            }
        }
        else
        {
            qualifier.setMaxAcceptableValue("max");
        }
        PortalUtils.getHt().saveOrUpdate(qualifier);
    }

    public static void populateBaseCriteriaQuestionsForOrg(String hqlAlias,
            String userStatus, CriteriaGroup cg, Locale locale, boolean forContact)
    {
        if (!"studentSearch".equals(userStatus))
        {
            CriteriaQuestion q = new CriteriaQuestion();
            LinkedList<CriteriaQuestion> questions = new LinkedList<>();

            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.dateCreated");
            q.setQuestionKey(hqlAlias + ".dateCreated");
            q.setType(CriteriaQuestion.TYPE_DATE);
            q.setColIndex(99);
            q.setLocale(locale.getLanguage());
            questions.add(q);

            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.dateLastUpdated");
            q.setQuestionKey(hqlAlias + ".dateUpdated");
            q.setType(CriteriaQuestion.TYPE_DATE);
            q.setColIndex(99);
            q.setLocale(locale.getLanguage());
            questions.add(q);

            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.createdByFirstName");
            q.setQuestionKey(
                    hqlAlias + (hqlAlias.contains("cus_company") ? "_createdBy"
                            : ".createdBy") + ".firstName");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColIndex(99);
            q.setLocale(locale.getLanguage());
            questions.add(q);

            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.createdByLastName");
            q.setQuestionKey(
                    hqlAlias + (hqlAlias.contains("cus_company") ? "_createdBy"
                            : ".createdBy") + ".lastName");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColIndex(99);
            q.setLocale(locale.getLanguage());
            questions.add(q);

            q = new CriteriaQuestion();
            q.setQuestionText(
                    "i18n.gridSearch.criteriaModel.questionText.createdByUsername");
            q.setQuestionKey(
                    hqlAlias + (hqlAlias.contains("cus_company") ? "_createdBy"
                            : ".createdBy") + ".username");
            q.setType(CriteriaQuestion.TYPE_TEXT);
            q.setColIndex(99);
            q.setLocale(locale.getLanguage());
            questions.add(q);

            q = new CriteriaQuestion();
            q.setQuestionText(hqlAlias.contains("organization")
                    ? "i18n.gridSearch.criteriaModel.questionText.organizationStatus"
                    : hqlAlias.startsWith("c")
                            ? "i18n.gridSearch.criteriaModel.questionText.DivisionStatus"
                            : "i18n.gridSearch.criteriaModel.questionText.Status");
            q.setQuestionKey(hqlAlias + ".userStatus");
            q.setQuestionOrder(1);
            q.setType(CriteriaQuestion.TYPE_CHOICE);
            PortalConfig enableNewProspectManagement = PortalConfigHelper
                    .getPortalConfig(PortalConfig.ENABLE_NEW_PROSPECT_MANAGEMENT);
            q.setOptionChoices(enableNewProspectManagement != null
                    && enableNewProspectManagement.getOrbisValueBoolean()
                            ? AcrmHelper.getUserStatusMapNewProspect(locale)
                            : AcrmHelper.getUserStatusMap(locale));
            q.setOptionIncludeOtherFlag(false);
            q.setColIndex(0);
            q.setLocale(locale.getLanguage());
            q.setColWidth(150);

            if (!StringUtils.isEmpty(userStatus) && !forContact)
            {
                CriteriaAnswer answer = new CriteriaAnswer();
                answer.setAndOrValue(CriteriaAnswer.CHOICE_OPERATION_CONTAINS);
                Map<String, Boolean> selectionMap = new HashMap<>();
                for (String key : q.getOptionChoices().keySet())
                {
                    selectionMap.put(key, key.equals(userStatus));
                }
                answer.setValueChoices(selectionMap);
                q.setAnswer(answer);
            }
            questions.add(q);

            cg.setQuestions(questions);
        }
    }

    public static void populateBaseCriteriaQuestions(String hqlAlias,
            String ownerUserHql, CriteriaGroup cg, Locale locale)
    {
        CriteriaQuestion q = new CriteriaQuestion();
        LinkedList<CriteriaQuestion> questions = new LinkedList<>();

        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.dateSubmitted");
        q.setQuestionKey(hqlAlias + ".dateCreated");
        q.setType(CriteriaQuestion.TYPE_DATE);
        q.setLocale(locale.getLanguage());
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.dateLastUpdated");
        q.setQuestionKey(hqlAlias + ".dateUpdated");
        q.setType(CriteriaQuestion.TYPE_DATE);
        q.setLocale(locale.getLanguage());
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.ownerFirstName");
        q.setQuestionKey((!StringUtils.isEmpty(ownerUserHql) ? ownerUserHql
                : hqlAlias + ".owner") + ".firstName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setLocale(locale.getLanguage());
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.ownerLastName");
        q.setQuestionKey((!StringUtils.isEmpty(ownerUserHql) ? ownerUserHql
                : hqlAlias + ".owner") + ".lastName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setLocale(locale.getLanguage());
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.ownerUsername");
        q.setQuestionKey((!StringUtils.isEmpty(ownerUserHql) ? ownerUserHql
                : hqlAlias + ".owner") + ".username");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setLocale(locale.getLanguage());
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText("i18n.gridSearch.criteriaModel.questionText.ownerGender");
        q.setQuestionKey((!StringUtils.isEmpty(ownerUserHql) ? ownerUserHql
                : hqlAlias + ".owner") + ".gender");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setLocale(locale.getLanguage());
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.submittedByFirstName");
        q.setQuestionKey(hqlAlias + ".createdBy.firstName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setLocale(locale.getLanguage());
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.submittedByLastName");
        q.setQuestionKey(hqlAlias + ".createdBy.lastName");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setLocale(locale.getLanguage());
        questions.add(q);

        q = new CriteriaQuestion();
        q.setQuestionText(
                "i18n.gridSearch.criteriaModel.questionText.submittedByUsername");
        q.setQuestionKey(hqlAlias + ".createdBy.username");
        q.setType(CriteriaQuestion.TYPE_TEXT);
        q.setLocale(locale.getLanguage());
        questions.add(q);

        cg.setQuestions(questions);
    }

    public static DATATYPE getDataTypeEquivalent(DFQuestion q)
    {
        DATATYPE dataType = null;

        if (Integer.valueOf(DFQuestion.TYPE_TEXT).equals(q.getType()))
        {
            dataType = DATATYPE.TEXT;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_DATE).equals(q.getType()))
        {
            dataType = DATATYPE.DATE;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_INTEGER).equals(q.getType()))
        {
            dataType = DATATYPE.NUMBER;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_BOOLEAN).equals(q.getType()))
        {
            dataType = DATATYPE.BOOLEAN;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_FLOAT).equals(q.getType()))
        {
            dataType = DATATYPE.NUMBER;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_LARGE_TEXT).equals(q.getType()))
        {
            dataType = DATATYPE.TEXT;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_MATRIX_MULTI).equals(q.getType()))
        {
            dataType = DATATYPE.TEXT;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_MATRIX_SINGLE).equals(q.getType()))
        {
            dataType = DATATYPE.TEXT;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_SINGLE_CHOICE).equals(q.getType()))
        {
            dataType = DATATYPE.TEXT;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_MULTI_CHOICE).equals(q.getType()))
        {
            dataType = DATATYPE.TEXT;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_RATING).equals(q.getType()))
        {
            dataType = DATATYPE.NUMBER;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_SALARY).equals(q.getType()))
        {
            dataType = DATATYPE.TEXT;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_SITE_VISIT).equals(q.getType()))
        {
            dataType = DATATYPE.TEXT;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_HYPERLINK).equals(q.getType()))
        {
            dataType = DATATYPE.TEXT;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_FILE_UPLOAD).equals(q.getType()))
        {
            dataType = DATATYPE.TEXT;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_TREE).equals(q.getType()))
        {
            dataType = DATATYPE.TEXT;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_TOTAL_TRACKED_HOURS)
                .equals(q.getType()))
        {
            dataType = DATATYPE.NUMBER;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_STARTEND_DATE).equals(q.getType()))
        {
            dataType = DATATYPE.STARTENDDATE;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_X_PER_Y).equals(q.getType()))
        {
            dataType = DATATYPE.TEXT;
        }
        else if (Integer.valueOf(DFQuestion.TYPE_TIME_COMMITMENT)
                .equals(q.getType()))
        {
            dataType = DATATYPE.TEXT;
        }
        else
        {
            throw new RuntimeException(
                    "Bad DFQuestion question-type: " + q.getType());
        }

        return dataType;
    }

    public static DFAnswerEntity getAnswerEntity(HttpServletRequest request)
    {
        DFAnswerEntity modelEntity = null;

        try
        {
            Class entityClass = ClassUtils
                    .loadClass(request.getParameter("dfAnswerEntityType"));

            Integer dfModelEntityId = Integer.valueOf(
                    request.getParameter("dfAnswerEntityId"));

            modelEntity = (DFAnswerEntity) PortalUtils.getHt().load(entityClass,
                    dfModelEntityId);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return modelEntity;
    }

    public static JSONObject getQualifierBreakdown(DFAnswerEntity answer,
            List<Object[]> qualifiers)
    {
        JSONObject ret = new JSONObject();
        for (Object[] qual : qualifiers)
        {
            boolean qualRet = getQuestionAnswerQualify(answer, qual);
            try
            {
                ret.put(((Integer) qual[5]).toString(), qualRet);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
        return ret;
    }

    public static boolean doesAnswerQualify(DFAnswerEntity answer,
            List<Object[]> qualifiers)
    {
        return new DFQualificationChecker(qualifiers).check(answer);
    }

    public static void populateITextDocument(Document document, DFModel dfModel,
            DFAnswerEntity answerEntity, boolean populateModel,
            UserDetailsImpl userLoggedIn, Locale locale)
    {
        try
        {
            if (populateModel)
            {
                DFHelper.populateModel(dfModel, false, answerEntity, userLoggedIn);
            }
            DFRole role = dfModel.getCurrentRole();

            Set<Integer> hiddenQuestions = dfModel.getHiddenQuestions();

            for (DFCategory cat : dfModel.getCategories())
            {
                PdfPTable detailsSection = new PdfPTable(2);
                applyDefaultTableSettings(detailsSection, 2);

                if (!cat.isHideCategoryTitle())
                {
                    addTableTitle(detailsSection,
                            LocaleUtils.isL1(locale) ? cat.getName()
                                    : cat.getNameL2(),
                            "styled");
                }

                Boolean categoryNotEmpty = false;

                for (DFQuestion q : cat.getQuestions())
                {
                    List<DFQuestionRole> questionRoles = q.getQuestionRoles();

                    if (hiddenQuestions.contains(q.getId()))
                    {
                        continue;
                    }

                    for (DFQuestionRole qr : questionRoles)
                    {
                        if (qr.getDfRole().equals(role) && (qr.isCanRead()))
                        {
                            DFHelper.addQuestion(detailsSection, q, answerEntity,
                                    LocaleUtils.isL1(locale));
                            categoryNotEmpty = true;
                        }
                    }
                }
                if (categoryNotEmpty)
                {
                    addBlankRow(detailsSection);
                    document.add(detailsSection);
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    public static void addQuestion(PdfPTable table, DFQuestion q,
            DFAnswerEntity answers, boolean isL1)
    {
        int qType = q.getType();
        q.setQuestionText(HtmlUtils.removeAllTags(q.getQuestionText()));
        q.setQuestionTextL2(HtmlUtils.removeAllTags(q.getQuestionTextL2()));
        String translation = isL1 ? PortalUtils.getDefaultLocale()
                : PortalUtils.getSecondaryLocale();
        if (qType != DFQuestion.TYPE_FILE_UPLOAD)
        {
            try
            {
                String escapedAnswer = StringUtils.defaultString(
                        BeanUtils.getProperty(answers, q.getAnswerField1()));
                String answer = HtmlUtils.htmlUnescape(escapedAnswer);

                if (qType != DFQuestion.TYPE_LARGE_TEXT)
                {
                    if (qType == DFQuestion.TYPE_MULTI_CHOICE)
                    {
                        if (!StringUtils.isEmpty(answer))
                        {

                            // ^^ means Other choice with empty string
                            String[] subAnswers = StringUtils
                                    .split(answer.replace("^^", "^ ^"), "^");
                            String[] escapedSubAnswers = StringUtils
                                    .split(escapedAnswer.replace("^^", "^ ^"), "^");

                            List<String> choices = q.getChoiceList();

                            for (int c = 0; c < escapedSubAnswers.length; c++)
                            {

                                subAnswers[c] = subAnswers[c].split("\\|")[isL1
                                        || !choices.contains(escapedSubAnswers[c])
                                                ? 0
                                                : 1];

                                if (!choices.contains(escapedSubAnswers[c]))
                                {
                                    String otherTerm = new I18nLabel(
                                            "i18n.df_questionMultiChoiceView.Other")
                                            .getTranslation(translation);
                                    // add 'Other:' before other value
                                    subAnswers[c] = otherTerm + ": "
                                            + subAnswers[c];
                                }
                            }

                            answer = StringUtils
                                    .join(subAnswers, System.lineSeparator())
                                    .replaceAll("\\~", "\\-");
                        }
                    }
                    else if (qType == DFQuestion.TYPE_SINGLE_CHOICE)
                    {
                        boolean isOther = true;

                        for (String choice : q.getChoiceList())
                        {
                            if (choice.contains(answer))
                            {
                                isOther = false;
                            }
                        }

                        if (!StringUtils.isEmpty(escapedAnswer) && isOther)
                        {
                            String otherTerm = new I18nLabel(
                                    "i18n.df_questionMultiChoiceView.Other")
                                    .getTranslation(translation);

                            // empty Other value saved as 'Other' removed since
                            // 'Other' prefix added before
                            if ("Other".equals(answer))
                            {
                                answer = "";
                            }

                            answer = otherTerm + ": " + answer;
                        }
                    }
                    else if (qType == DFQuestion.TYPE_BOOLEAN)
                    {
                        if ("true".equalsIgnoreCase(answer))
                        {
                            answer = new I18nLabel(
                                    "i18n.DFHelper.Yes3155021603454604")
                                    .getTranslation(translation);
                        }
                        else
                        {
                            answer = new I18nLabel(
                                    "i18n.DFHelper.No8891609504761722")
                                    .getTranslation(translation);
                        }
                    }
                    else if (qType == DFQuestion.TYPE_DATE)
                    {
                        if (!StringUtils.isEmpty(answer))
                        {
                            Date date = DateUtils.parseDate(answer,
                                    DBUtils.DB_DATE_TIME_FORMAT + ".S", null);
                            if (date != null)
                            {
                                answer = DateUtils.formatDate(date,
                                        DateUtils.DF_LONG_DATE,
                                        LocaleUtils.toLocale(translation));
                            }
                        }
                        else
                        {
                            answer = "";
                        }
                    }
                    else if (qType == DFQuestion.TYPE_TREE)
                    {
                        if (!StringUtils.isEmpty(answer))
                        {
                            answer = answer.replaceAll("\\|", "\\-").substring(1,
                                    answer.length() - 1);
                            answer = answer.replaceAll("\"", "").replaceAll("\\,",
                                    "\n");
                        }
                    }
                    else if (qType == DFQuestion.TYPE_STARTEND_DATE)
                    {
                        if (!StringUtils.isEmpty(answer))
                        {
                            Date date = DateUtils.parseDate(answer,
                                    DBUtils.DB_DATE_TIME_FORMAT + ".S", null);
                            if (date != null)
                            {
                                answer = DateUtils.formatDate(date,
                                        DateUtils.DF_LONG_DATE_SHORT_TIME, null);
                            }
                        }

                        String endDate = BeanUtils.getProperty(answers,
                                q.getAnswerField2());
                        if (!StringUtils.isEmpty(answer))
                        {
                            Date date = DateUtils.parseDate(endDate,
                                    DBUtils.DB_DATE_TIME_FORMAT + ".S", null);
                            if (date != null)
                            {
                                endDate = DateUtils.formatDate(date,
                                        DateUtils.DF_LONG_DATE_SHORT_TIME, null);
                            }
                            answer += " to " + endDate;
                        }
                        else
                        {
                            answer = "";
                        }

                    }
                    else if (qType == DFQuestion.TYPE_SALARY)
                    {
                        if (!StringUtils.isEmpty(answer))
                        {
                            String frequency = "";

                            switch (Integer.valueOf(BeanUtils.getProperty(answers,
                                    q.getAnswerField2())))
                            {
                                case 0:
                                    frequency = "hour";
                                    break;
                                case 1:
                                    frequency = "day";
                                    break;
                                case 2:
                                    frequency = "week";
                                    break;
                                case 3:
                                    frequency = "month";
                                    break;
                                case 4:
                                    frequency = "year";
                                    break;
                                default:
                                    frequency = "hour";
                            }
                            answer = String.format("%.2f", new Double(answer));
                            answer = "$" + answer + " per " + frequency + " @ ";
                            answer += BeanUtils.getProperty(answers,
                                    q.getAnswerField3()) + " hours/week";
                        }
                    }
                    else if (qType == DFQuestion.TYPE_SITE_VISIT)
                    {
                        if ("Yes".equals(answer))
                        {
                            answer = DateUtils.formatDate(
                                    (Date) PropertyUtils.getProperty(answers,
                                            q.getAnswerField2()),
                                    DateUtils.DF_SHORT_DATE, null);
                            answer += " with "
                                    + PropertyUtils.getProperty(answers,
                                            q.getAnswerField3())
                                    + " via " + PropertyUtils.getProperty(answers,
                                            q.getAnswerField4());
                            if (!StringUtils.isEmpty((String) PropertyUtils
                                    .getProperty(answers, q.getAnswerField5())))
                            {
                                answer += " @ " + PropertyUtils.getProperty(answers,
                                        q.getAnswerField5());
                            }
                        }
                    }
                    else if (qType == DFQuestion.TYPE_RATING)
                    {
                        if (!StringUtils.isEmpty(answer)
                                && Integer.valueOf(answer).intValue() == -1)
                        {
                            answer = "N/A";
                        }
                    }
                    if (qType == DFQuestion.TYPE_MATRIX_SINGLE
                            || qType == DFQuestion.TYPE_MATRIX_MULTI)
                    {
                        if (!StringUtils.isEmpty(answer))
                        {
                            String[] parsedAnswer = answer.split("\\^");
                            NHelper.addBoldCell(table, isL1 ? q.getQuestionText()
                                    : q.getQuestionTextL2());
                            NHelper.addEmptyCell(table);

                            for (int i = 1; i < parsedAnswer.length; i++)
                            {
                                String[] questionAnswer = parsedAnswer[i]
                                        .split("\\~");
                                NHelper.addNormalCell(table, "");
                                NHelper.addBoldCell(table, questionAnswer[0]);
                                NHelper.addNormalCell(table, "");
                                NHelper.addNormalCell(table, questionAnswer[1]);
                                NHelper.addBlankRow(table);
                            }
                        }
                    }
                    else
                    {
                        NHelper.addTextRow(table,
                                isL1 ? q.getQuestionText() : q.getQuestionTextL2(),
                                answer);
                    }
                }
                else
                {
                    NHelper.addBoldCell(table,
                            isL1 ? q.getQuestionText() : q.getQuestionTextL2());
                    String html = Optional.ofNullable(answer)
                            .orElseGet(String::new);
                    List<Element> objects = new ArrayList();
                    try
                    {
                        objects = HTMLWorker.parseToList(new StringReader(
                                "<div class='itextDefault'>" + html + "</div>"),
                                itextDefaultStyle);
                    }
                    catch (IOException e)
                    {
                        e.printStackTrace();
                    }
                    PdfPCell tmpCell = new PdfPCell();
                    tmpCell.setBorder(0);
                    tmpCell.setColspan(2);
                    for (Element element : objects)
                    {
                        tmpCell.addElement(element);
                    }
                    table.addCell(tmpCell);
                }

                if (isQuestionReadOnly(q))
                {
                    addReadOnlyQuestionContent(table, q);
                }
            }
            catch (Exception ex)
            {
                ex.printStackTrace();
            }
        }
    }

    /**
     * Adds the Question's Header and Footer content for Read-Only questions
     *
     * @param table
     *            {@link PdfTable}
     * @param q
     *            {@link DFQuestion}
     */
    private static void addReadOnlyQuestionContent(PdfPTable table, DFQuestion q)
    {
        String header = q.getHeader();
        String footer = q.getFooter();

        boolean nonEmptyHeader = !StringUtils.isEmpty(header);
        boolean nonEmptyFooter = !StringUtils.isEmpty(footer);

        if (nonEmptyHeader)
        {
            NHelper.addRichTextRow(table, header);
        }

        if (nonEmptyHeader && nonEmptyFooter)
        {
            addBlankRow(table);
        }

        if (nonEmptyFooter)
        {
            NHelper.addRichTextRow(table, footer);
        }

        if (nonEmptyHeader || nonEmptyFooter)
        {
            addBlankRow(table);
        }
    }

    public static void applyDefaultTableSettings(PdfPTable pdfPTable,
            Integer numColumns)
    {
        try
        {
            int[] columns = new int[numColumns];
            for (int i = 0; i < numColumns; i++)
            {
                columns[i] = 2;
            }

            pdfPTable.setWidths(columns);
            pdfPTable.setTotalWidth(
                    PageSize.LETTER.getWidth() - PDF_FORMAT_LR_MARGIN * numColumns);
            pdfPTable.setLockedWidth(true);
            pdfPTable.getDefaultCell().setBorder(Rectangle.NO_BORDER);
            pdfPTable.getDefaultCell().setMinimumHeight(15);
            pdfPTable.setSplitRows(true);
            pdfPTable.setSplitLate(false);
        }
        catch (DocumentException e)
        {
            e.printStackTrace();
        }
    }

    public static void addTableTitle(PdfPTable pdfPTable, String titleText,
            String plainStyle)
    {
        PdfPCell titleCell = new PdfPCell(pdfPTable.getDefaultCell());
        titleCell.setColspan(2);
        Paragraph titlePara = new Paragraph(titleText, SM_BOLD_FONT);
        titleCell.setHorizontalAlignment(Element.ALIGN_LEFT);
        titleCell.setPhrase(titlePara);
        titleCell.setPaddingTop(0);
        titleCell.setPaddingBottom(4);
        titleCell.setBackgroundColor(
                "".equals(plainStyle) ? Color.WHITE : Color.LIGHT_GRAY);
        pdfPTable.addCell(titleCell);
    }

    public static void addEmptyCell(PdfPTable table)
    {
        PdfPCell emptyCell = new PdfPCell(table.getDefaultCell());
        emptyCell.setFixedHeight(15);
        emptyCell.setBorder(0);
        table.addCell(emptyCell);
    }

    public static void addBlankRow(PdfPTable table)
    {
        addEmptyCell(table);
        addEmptyCell(table);
    }

    /**
     * Creates a new {@link DFModel} for {@code newModelEntity}, copying over the
     * roles, questions, ect. contained in {@code oldModel}
     *
     * @param newModelEntity
     *            The model entity that will be storing the newly created
     *            {@link DFModel}
     * @param oldModel
     *            The {@link DFModel} to clone
     */
    public static <T extends DFAbstractModelEntity> void createNewModel(
            T newModelEntity, DFModel oldModel)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();

        DFModel newModel = new DFModel();

        newModel.setPublicAllowed(oldModel.isPublicAllowed());

        newModel.setPreserveOrdering(oldModel.isPreserveOrdering());

        newModel.setSimplifiedQuestionsAndQualifiers(
                oldModel.isSimplifiedQuestionsAndQualifiers());

        newModel.setDisableQualifiers(oldModel.isDisableQualifiers());

        newModel.setAllowedQuestionTypes(oldModel.getAllowedQuestionTypes());

        newModel.setMaxQuestions(oldModel.getMaxQuestions());

        newModel.setModelEntityClassName(newModelEntity.getClass());

        newModel.setSimplifiedModelWithHeaderFooter(
                oldModel.isSimplifiedModelWithHeaderFooter());

        ht.save(newModel);

        newModelEntity.setDFModel(newModel);

        ht.saveOrUpdate(newModelEntity);

        if (DFHelper.hasQuestions(oldModel))
        {
            cloneRoles(newModel, oldModel);

            List<DFCategory> categories = ht
                    .find("from DFCategory c where c.model=?", oldModel);

            for (DFCategory dfc : categories)
            {
                DFCategory newCategory = new DFCategory();
                newCategory.setAdminOnly(dfc.isAdminOnly());
                newCategory.setName(dfc.getName());
                newCategory.setNameL2(dfc.getNameL2());
                newCategory.setPosition(dfc.getPosition());
                newCategory.setModel(newModel);
                ht.save(newCategory);

                if (newModelEntity instanceof DFQualifierEntity)
                {
                    cloneQuestionsWithQualifiers(newCategory, dfc,
                            (DFQualifierEntity) newModelEntity);
                }
                else
                {
                    cloneQuestions(newCategory, dfc);
                }
            }

            cloneDependencies(oldModel, newModel);
        }
    }

    public static void cloneDependencies(DFModel oldModel, DFModel newModel)
    {
        PortalUtils.getHt().<Object[]> findAndStream(
                "SELECT qd, nq, nqd FROM DFQuestionDependency qd, DFQuestion nq, DFQuestion nqd WHERE qd.dfQuestion.category.model.id = ? and nq.answerField1=qd.dfQuestion.answerField1 and nqd.answerField1=qd.dfQuestionDependsOn.answerField1 and nq.category.model.id=? and nqd.category.model.id=?",
                new Object[] { oldModel.getId(), newModel.getId(),
                        newModel.getId() })
                .forEach(dfq -> {
                    DFQuestionDependency newDep = (DFQuestionDependency) ((DFQuestionDependency) dfq[0])
                            .clone();
                    newDep.setDfQuestion(((DFQuestion) dfq[1]));
                    newDep.setDfQuestionDependsOn(((DFQuestion) dfq[2]));
                    newDep.setId(null);
                    PortalUtils.getHt().save(newDep);
                });
    }

    public static void cloneRoles(DFModel newModel, DFModel oldModel)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();

        List<DFRole> dfrs = ht.find("from DFRole dfr where dfr.model=?", oldModel);
        for (DFRole dfr : dfrs)
        {
            if (ht.findInt(
                    "select count(r) from DFRole r where r.name=? and r.model=?",
                    dfr.getName(), newModel) == 0)
            {
                DFRole r = new DFRole();
                r.setDefaultRead(dfr.isDefaultRead());
                r.setDefaultSearch(dfr.isDefaultSearch());
                r.setDefaultShowInResults(dfr.isDefaultShowInResults());
                r.setDefaultShowInReports(dfr.isDefaultShowInReports());
                r.setDefaultWrite(dfr.isDefaultWrite());
                r.setDefaultRequired(dfr.isDefaultRequired());
                r.setName(dfr.getName());
                r.setL2Name(dfr.getL2Name());
                r.setNameNotEditable(dfr.isNameNotEditable());
                r.setNotDeletable(dfr.isNotDeletable());
                r.setPosition(dfr.getPosition());
                r.setPublicRole(dfr.isPublicRole());
                r.setModel(newModel);
                ht.save(r);

                List<DFRoleUserGroup> dfrugs = ht
                        .find("from DFRoleUserGroup dfrug where dfrug.role=?", dfr);
                for (DFRoleUserGroup dfrug : dfrugs)
                {
                    DFRoleUserGroup rug = new DFRoleUserGroup();
                    rug.setUserGroup(dfrug.getUserGroup());
                    rug.setRole(r);
                    ht.save(rug);
                }

                List<DFRoleUser> dfrus = ht
                        .find("from DFRoleUser dfru where dfru.role=?", dfr);
                for (DFRoleUser dfru : dfrus)
                {
                    DFRoleUser ru = new DFRoleUser();
                    ru.setUser(dfru.getUser());
                    ru.setRole(r);
                    ht.save(ru);
                }
            }
        }
    }

    public static void cloneQuestions(DFCategory category, DFCategory oldCategory)
    {
        List<DFQuestion> oldQuestions = PortalUtils.getHt()
                .find("select q from DFQuestion q where q.category=?", oldCategory);

        for (DFQuestion dfq : oldQuestions)
        {
            cloneQuestion(category, dfq);
        }
    }

    public static DFQuestionDependency getDFQuestionDependency(
            HttpServletRequest request)
    {
        DFQuestionDependency dqd = null;
        try
        {
            if (!StringUtils.isEmpty(request.getParameter("dependencyId")))
            {
                dqd = (DFQuestionDependency) PortalUtils.getHt().load(
                        DFQuestionDependency.class,
                        Integer.valueOf(request.getParameter("dependencyId")));
            }
            else if (request.getAttribute("dependencyId") != null)
            {
                dqd = (DFQuestionDependency) PortalUtils.getHt().load(
                        DFQuestionDependency.class,
                        (Integer) request.getAttribute("dependencyId"));
            }
        }
        catch (Exception e)
        {

        }
        return dqd;
    }

    public static List<DFQuestionDependency> getQuestionDependencies(
            DFQuestion question)
    {
        return PortalUtils.getHt().find(
                "FROM DFQuestionDependency qd WHERE qd.dfQuestion.id = ?",
                question.getId());
    }

    public static DFRole getPublicRole(DFModel dfModel)
    {
        DFRole role = null;
        List roles = PortalUtils.getHt()
                .find("from DFRole r where r.name=? and r.model=?", new Object[] {
                        new I18nLabel("i18n.DFHelper.Public6470193158689056")
                                .getTranslation(PortalUtils.getDefaultLocale()),
                        dfModel });

        if (roles != null && !roles.isEmpty())
        {
            role = (DFRole) roles.get(0);
        }

        return role;
    }

    public static DFRoleUserGroup getDfRoleUserGroup(DFModel dfModel,
            UserDetailsImpl userLoggedin)
    {
        DFRoleUserGroup rug = null;

        List roles = PortalUtils.getHt().find(
                "from DFRoleUserGroup rug where rug.role=?",
                new Object[] { getRoleInModel(dfModel, userLoggedin) });

        if (roles != null && !roles.isEmpty())
        {
            rug = (DFRoleUserGroup) roles.get(0);
        }

        return rug;
    }

    public static DFQuestion getQuestion(String answerField1, DFModel dfModel,
            HttpServletRequest request)
    {
        return getQuestion(answerField1, dfModel,
                PortalUtils.getUserLoggedIn(request));
    }

    public static DFQuestion getQuestion(String answerField1, DFModel dfModel,
            UserDetailsImpl userLoggedIn)
    {
        DFQuestion q = null;

        List qs = PortalUtils.getHt().find(
                "from DFQuestion q where q.answerField1=? and q.category.model=?",
                new Object[] { answerField1, dfModel });

        if (qs != null && !qs.isEmpty())
        {
            q = (DFQuestion) qs.get(0);
            q.setQuestionRoles(DFHelper.getRolesForQuestion(q));
            q.setRole(getRoleInModel(dfModel, userLoggedIn));
        }
        return q;
    }

    public static boolean canWrite(DFQuestion question, UserDetailsImpl user)
    {
        return getRolesForQuestion(question).stream().map(DFQuestionRole::getDfRole)
                .anyMatch(r -> r.equals(
                        getRoleInModel(question.getCategory().getModel(), user)));
    }

    public static boolean canWrite(DFQuestion question, DFRole currentRole)
    {
        return question.getQuestionRoles() != null && question.getQuestionRoles()
                .stream().filter(qr -> qr.isCanWrite()).map(qr -> qr.getDfRole())
                .anyMatch(r -> r.equals(currentRole));
    }

    private static void bindAnswer_SiteVisit(HttpServletRequest request,
            DFAnswerEntity answerEntity, DFQuestion question,
            boolean resetForEmptyString)
    {
        boolean siteVisit = false;
        Date when = null;
        String who = null, where = null;
        int method = 0;

        try
        {
            siteVisit = getBooleanValue(request
                    .getParameter("question_" + question.getId() + "_siteVisit"));
            if (siteVisit)
            {
                when = DateUtils.getDatepickerVal(request,
                        "question_" + question.getId().toString() + "_when");
                who = request.getParameter(
                        "question_" + question.getId().toString() + "_who");
                method = Integer.valueOf(request.getParameter(
                        "question_" + question.getId().toString() + "_method"))
                        .intValue();
                if (method == 0)
                {
                    where = request.getParameter(
                            "question_" + question.getId().toString() + "_where");
                }
            }
            else
            {
                method = -1;
            }
        }
        catch (Exception e)
        {
        }

        bindValue(answerEntity, question.getAnswerField1(), siteVisit);
        bindValue(answerEntity, question.getAnswerField2(), when);
        bindValue(answerEntity, question.getAnswerField3(), who);
        bindValue(answerEntity, question.getAnswerField4(), method);
        bindValue(answerEntity, question.getAnswerField5(), where);
    }

    private static DFQuestion cloneQuestion(DFCategory category,
            DFQuestion oldQuestion)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        DFQuestion q = new DFQuestion();
        q.setCategory(category);
        q.setAdminOnly(oldQuestion.isAdminOnly());
        q.setAnswerField1(oldQuestion.getAnswerField1());
        q.setAnswerField1L2(oldQuestion.getAnswerField1L2());
        q.setAnswerField2(oldQuestion.getAnswerField2());
        q.setAnswerField3(oldQuestion.getAnswerField3());
        q.setAnswerField4(oldQuestion.getAnswerField4());
        q.setAnswerField5(oldQuestion.getAnswerField5());
        q.setChoices(oldQuestion.getChoices());
        q.setDefaultChoice(oldQuestion.getDefaultChoice());
        q.setDefaultMappingKey(oldQuestion.getDefaultMappingKey());
        q.setFooter(oldQuestion.getFooter());
        q.setFooterL2(oldQuestion.getFooterL2());
        q.setHeader(oldQuestion.getHeader());
        q.setHeaderL2(oldQuestion.getHeaderL2());
        q.setIncludeOther(oldQuestion.isIncludeOther());
        q.setLimitAnswer(oldQuestion.getLimitAnswer());
        q.setPreserveOrdering(oldQuestion.isPreserveOrdering());
        q.setMappingKey(oldQuestion.getMappingKey());
        q.setMatrixColumns(oldQuestion.getMatrixColumns());
        q.setL2MatrixColumns(oldQuestion.getL2MatrixColumns());
        q.setMatrixRows(oldQuestion.getMatrixRows());
        q.setL2MatrixRows(oldQuestion.getL2MatrixRows());
        q.setMax(oldQuestion.getMax());
        q.setMinMaxCheckExempt(oldQuestion.isMinMaxCheckExempt());
        q.setMaxUploadSize(oldQuestion.getMaxUploadSize());
        q.setMin(oldQuestion.getMin());
        q.setMultiChoicesJson(oldQuestion.getMultiChoicesJson());
        q.setPosition(oldQuestion.getPosition());
        q.setQuestionText(oldQuestion.getQuestionText());
        q.setQuestionTextL2(oldQuestion.getQuestionTextL2());
        q.setQuestionTextLocked(oldQuestion.isQuestionTextLocked());
        q.setSingleChoice(oldQuestion.getSingleChoice());
        q.setSingleTreePath(oldQuestion.isSingleTreePath());
        q.setToolTip(oldQuestion.getToolTip());
        q.setToolTipL2(oldQuestion.getToolTipL2());
        q.setTreeNodes(oldQuestion.getTreeNodes());
        q.setType(oldQuestion.getType());
        q.setTypeLocked(oldQuestion.isTypeLocked());
        q.setUsingNA(oldQuestion.isUsingNA());
        q.setValidated(oldQuestion.isValidated());
        q.setValidationRegEx(oldQuestion.getValidationRegEx());
        q.setValidationSample(oldQuestion.getValidationSample());
        ht.save(q);

        List<DFQuestionRole> dfqrs = ht.find(
                "from DFQuestionRole dfqr where dfqr.dfQuestion=?", oldQuestion);
        for (DFQuestionRole dfqr : dfqrs)
        {
            List<DFRole> roles = ht.find(
                    "from DFRole r where r.name=? and r.model=?", new Object[] {
                            dfqr.getDfRole().getName(), category.getModel() });
            if (!roles.isEmpty())
            {
                DFRole role = roles.get(0);

                DFQuestionRole qr = (DFQuestionRole) dfqr.clone();
                qr.setDfQuestion(q);
                qr.setDfRole(role);
                ht.save(qr);
            }
        }

        return q;
    }

    private static boolean getQuestionAnswerQualify(DFAnswerEntity answer,
            Object[] qual)
    {
        Qualification qualification = DFQualificationChecker
                .getQualification((Integer) qual[0]);

        return qualification.check(answer, qual);
    }

    public static DFModelMatcher getModelMatcher(DFModel model)
    {
        return PortalUtils.getHt()
                .<DFModelMatcher> findFirst(
                        "from DFModelMatcher m where m.primary=? or m.secondary=?",
                        new Object[] { model, model })
                .orElse(null);
    }

    public static JSONArray getModelMatcherPrimaryQuestions(DFModel model)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        return getModelMatcherQuestionsJSONArray(
                ht.find("select q.id, q.type, q.questionText from DFQuestion q "
                        + " where q.category.model=? and "
                        + getModelMatchQuestionTypeInclause("q"), model));
    }

    private static String getModelMatchQuestionTypeInclause(String hqlAlias)
    {
        return new StringBuilder().append(hqlAlias).append(".type in ")
                .append(DBUtils.buildInClause(Lists.newArrayList(
                        DFQuestion.TYPE_TEXT, DFQuestion.TYPE_BOOLEAN,
                        DFQuestion.TYPE_DATE, DFQuestion.TYPE_SINGLE_CHOICE,
                        DFQuestion.TYPE_MULTI_CHOICE, DFQuestion.TYPE_FLOAT,
                        DFQuestion.TYPE_INTEGER)))
                .toString();
    }

    private static JSONArray getModelMatcherQuestionsJSONArray(
            List<Object[]> questions)
    {
        return questions.stream()
                .map(q -> new JSONObject(
                        ImmutableMap.of("id", q[0], "type", q[1], "text", q[2])))
                .collect(JSONUtils.toJSONArray());
    }

    public static JSONArray getModelMatcherSecondaryQuestions(DFModel model)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        return getModelMatcherQuestionsJSONArray(
                ht.find("select q.id, q.type, q.questionText from DFQuestion q "
                        + " where q.category.model in (select m.secondary.id from DFModelMatcher m where m.primary=?) "
                        + " and " + getModelMatchQuestionTypeInclause("q"), model));
    }

    public static void populateDFOffPageCommon(HttpServletRequest request,
            ModelAndView mv, DFModelEntity modelEntity)
    {
        mv.addObject("dfModelEntityType",
                request.getParameter("dfModelEntityType"));
        mv.addObject("dfModelEntityId", request.getParameter("dfModelEntityId"));
        mv.addObject("model", modelEntity.getDFModel());
    }

    public static JSONObject getModelMatcherData(DFModelMatcher matcher)
            throws JSONException
    {
        JSONObject matcherJson = new JSONObject();

        if (matcher != null)
        {
            matcherJson.put("secondary", new JSONObject()
            {
                {
                    put("name", matcher.getSecondary().getName());
                    put("id", matcher.getSecondary().getId());
                }
            });

            JSONArray questionMatchers = matcher
                    .getQuestionMatchers().stream().map(
                            q -> new JSONObject(ImmutableMap.of("q1",
                                    new JSONObject(ImmutableMap.of("id",
                                            q.getPrimary().getId(), "text",
                                            q.getPrimary().getQuestionText(),
                                            "type", q.getPrimary().getType())),
                                    "q2",
                                    new JSONObject(ImmutableMap.of("id",
                                            q.getSecondary().getId(), "text",
                                            q.getSecondary().getQuestionText(),
                                            "type", q.getSecondary().getType())),
                                    "op", q.getOpType())))
                    .collect(JSONUtils.toJSONArray());

            matcherJson.put("questions", questionMatchers);
        }

        return matcherJson;
    }

    public static List<Object[]> getDFQuestionFiltersForCachedSearch(
            ModelAndView mv, UserDetailsImpl userLoggedIn, DFModel dfModel,
            Locale locale)
    {
        boolean isL1 = LocaleUtils.isL1(locale);
        String hql = "select q.type, q.questionText"
                + (LocaleUtils.isL1(locale) ? "" : "L2")
                + ", q.answerField1 from DFQuestion q where q.id in"
                + " (select qr.dfQuestion.id from DFQuestionRole qr"
                + " where qr.canSearch=true and qr.dfRole=?) ";
        DFRole role = getRoleInModel(dfModel, userLoggedIn);

        List<Object[]> boolQuestions = PortalUtils.getHt()
                .find(hql + "and q.type=? order by q."
                        + (isL1 ? "questionText" : "questionTextL2"),
                        new Object[] { role, DFQuestion.TYPE_BOOLEAN });
        mv.addObject("boolTypeQuestions", boolQuestions);

        try
        {
            JSONArray boolTypeJson = new JSONArray();

            for (Object[] bq : boolQuestions)
            {
                JSONObject option = new JSONObject();
                option.put("label", bq[1]);
                option.put("value", bq[2]);
                boolTypeJson.put(option);
            }

            mv.addObject("boolTypeJson", boolTypeJson);
        }
        catch (JSONException e)
        {
            e.printStackTrace();
        }
        List<Object[]> otherQuestions = PortalUtils.getHt().find(
                hql + "and (q.type=? or q.type=? or q.type=?) order by q.position",
                new Object[] { role, DFQuestion.TYPE_INTEGER, DFQuestion.TYPE_FLOAT,
                        DFQuestion.TYPE_DATE });
        mv.addObject("otherTypeQuestions", otherQuestions);

        List<Object[]> allQuestions = new ArrayList<Object[]>();
        allQuestions.addAll(boolQuestions);
        allQuestions.addAll(otherQuestions);
        return allQuestions;
    }

    public static boolean isQuestionReadOnly(DFQuestion question)
    {
        return DFHelper.getRolesForQuestion(question)//
                .stream()//
                .filter(r -> r.isCanRead())//
                .noneMatch(r -> r.isCanWrite());
    }

    public static String formatMatrixValue(String row, String col)
    {
        StringBuilder sb = new StringBuilder();
        {
            sb.append("^");
            sb.append(row);
            sb.append("~");
            sb.append(col);
        }
        return sb.toString();
    }

    public static List<DFQuestion> getDFQuestions(DFCategory category)
    {
        return PortalUtils.getHt().find(
                "from DFQuestion q where q.category = ? order by q.category.position asc, q.position asc",
                category);
    }

    public static DFQuestion totalTrackedHoursDisableWrite(DFModel dfModel)
    {
        // find the track hours question for this df model
        Optional<DFQuestion> hoursQuestion = dfModel.getQuestions().stream()
                .filter(q -> "totalTrackedHours".equals(q.getAnswerField1()))
                .findFirst();
        if (hoursQuestion.isPresent())
        {
            // make the track hours question read only for
            // all
            // roles
            hoursQuestion.get().getQuestionRoles().stream()
                    .forEach(qr -> qr.setCanWrite(false));
            return hoursQuestion.get();
        }
        return null;
    }

    public static String getTotalTrackedHoursQuery(HashMap map)
    {
        StringBuilder sb = new StringBuilder();
        sb.append(" update rec ");
        sb.append(" set rec.totalTrackedHours = ( ");
        sb.append("   select case  ");
        sb.append("    when exists ( ");
        sb.append("     select trac.id ");
        sb.append("     from ${exp_record_hour_tracking} trac ");
        sb.append("     where trac.${main_record_field} = rec.id ");
        sb.append("     and trac.status <> ${EXPRecordAbstract.STATUS_DECLINED} ");
        sb.append("     ) ");
        sb.append("     then sum(${exp_record_hour_tracking}.numHours) ");
        sb.append("    else 0 ");
        sb.append("    end ");
        sb.append("   from ${exp_record_hour_tracking} ");
        sb.append(
                "   where ${exp_record_hour_tracking}.${main_record_field} = rec.id ");
        if (map.containsKey("discriminator_field")
                && map.containsKey("discriminator_value"))
        {
            sb.append(
                    " and ${exp_record_hour_tracking}.${discriminator_field} = '${discriminator_value}' ");
        }
        sb.append("     and ${exp_record_hour_tracking}.status  ");
        sb.append("         <> ${EXPRecordAbstract.STATUS_DECLINED} ");
        sb.append("   ) ");
        sb.append(" from ${exp_record} rec ");
        sb.append(" where rec.id = ${record.getId()} ");

        String query = new StrSubstitutor(map).replace(sb);
        return query;
    }

    public static void createTotalHoursCompletedQuestions(
            String recordModelTableName, String headerText)
    {
        HashMap map = new HashMap();
        map.put("record_model", recordModelTableName);
        map.put("header", headerText);
        StrSubstitutor strSub = new StrSubstitutor(map);
        PortalUtils.getJt().execute(strSub.replace("insert into df_question "
                + "(questionText, questionTextL2, category, answerField1, type, typeLocked, deleteLocked, header, headerL2, questionTextLocked, position, adminOnly, min, max, usingNA, includeOther, singleTreePath, limitAnswer, questionType, displayType, prepopulateSrchFlag, agencySearchableByStudents, showOnReg, showInHeader, showOnProspect, showOnOverview, prospectMandatory, preserveOrdering) "
                + "select 'Total Hours Completed','Total Hours Completed', category.id, 'totalTrackedHours', 12, 1, 1, '${header}', '${header}', 0, coalesce((select max(lpq.position) from df_question lpq where lpq.category = category.id), -1) + 1, 0, 0, 0, 0, 0, 0, 0, 'com.orbis.df.DFQuestion', 0, 0, 0, 0, 0, 0, 0, 0, 0 "
                + "from ${record_model} etrm "
                + "inner join df_model model on etrm.DFModel=model.id "
                + "left join df_category category on model.id=category.model "
                + "where category.id = (select top 1 dfc.id from df_category dfc where dfc.model = model.id) and not exists (select q.id from df_question q where q.answerField1='totalTrackedHours' and q.category=category.id)"));
        PortalUtils.getJt().execute(strSub.replace("insert into df_question_role "
                + "(dfQuestion, dfRole, canWrite, canRead, canSearch, canShowInResults, canShowInReports, required) "
                + "select question.id, role.id, role.defaultWrite, role.defaultRead, role.defaultSearch, role.defaultShowInResults, role.defaultShowInReports, role.defaultRequired "
                + "from ${record_model} etrm "
                + "inner join df_model model on etrm.DFModel=model.id "
                + "left join df_category category on model.id=category.model "
                + "left join df_question question on category.id=question.category "
                + "left join df_role role on model.id=role.model "
                + "where question.answerField1='totalTrackedHours' and not exists (select dfq.id from df_question_role dfq where dfq.dfQuestion=question.id)"));
    }

    public static DFRole addPublicRoleToModel(DFModel model)
    {
        DFRole publicRole = new DFRole();
        publicRole.setDefaultRead(true);
        publicRole.setDefaultSearch(true);
        publicRole.setDefaultShowInResults(true);
        publicRole.setDefaultShowInReports(true);
        publicRole.setDefaultWrite(true);
        publicRole.setDefaultRequired(false);
        publicRole.setName(PortalUtils.getMessageSource().getMessage(
                "i18n.DFHelper.Public6470193158689056", null,
                LocaleUtils.getDefaultLocale()));
        publicRole.setL2Name(PortalUtils.getMessageSource().getMessage(
                "i18n.DFHelper.Public6470193158689056", null,
                LocaleUtils.getSecondaryLocale()));
        publicRole.setModel(model);
        publicRole.setPublicRole(true);
        publicRole.setNameNotEditable(true);
        publicRole.setNotDeletable(true);
        publicRole.setPosition(getRolesForModel(model.getId().toString()).size());
        PortalUtils.getHt().save(publicRole);
        return publicRole;
    }

    public static void copyFileUploadsForClonedEntity(DFAnswerEntity answerEntity)
    {
        if (answerEntity == null || answerEntity.getDfModel() == null)
        {
            return;
        }

        List<OrbisHqlResultSet> fileUploadAnswerFields = PortalUtils.getHt().f(
                "SELECT dfq.answerField1, dfq.answerField2 FROM DFQuestion dfq WHERE dfq.type=? AND dfq.category.model=?",
                DFQuestion.TYPE_FILE_UPLOAD, answerEntity.getDfModel());

        for (OrbisHqlResultSet fileUploadAnswers : fileUploadAnswerFields)
        {
            String fileNameProperty = fileUploadAnswers.select("answerField1");
            String filePathUUIDProperty = fileUploadAnswers.select("answerField2");
            try
            {
                String filePathUUID = (String) PropertyUtils
                        .getProperty(answerEntity, filePathUUIDProperty);
                if (!StringUtils.isEmpty(filePathUUID))
                {
                    FilePath clonedFilePath = FilePathUtils.cloneFile(
                            FilePathUtils.getFilePathForUUID(filePathUUID));
                    if (clonedFilePath != null)
                    {
                        String clonedFilePathUrl = clonedFilePath
                                .getExistingFilePath();
                        PropertyUtils.setProperty(answerEntity, fileNameProperty,
                                clonedFilePathUrl.substring(
                                        clonedFilePathUrl.lastIndexOf('/') + 1));
                        PropertyUtils.setProperty(answerEntity,
                                filePathUUIDProperty, clonedFilePath.getId());
                    }
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
    }

    public static void deleteFileUploadsForDeletedEntity(
            DFAnswerEntity answerEntity)
    {
        if (answerEntity == null || answerEntity.getDfModel() == null)
        {
            return;
        }

        List<String> fileUploadUUIDAnswerFields = PortalUtils.getHt().find(
                "SELECT dfq.answerField2 FROM DFQuestion dfq WHERE dfq.type=? AND dfq.category.model=?",
                new Object[] { DFQuestion.TYPE_FILE_UPLOAD,
                        answerEntity.getDfModel() });
        for (String fileUploadUUIDProperty : fileUploadUUIDAnswerFields)
        {
            try
            {
                String filePathUUID = (String) PropertyUtils
                        .getProperty(answerEntity, fileUploadUUIDProperty);
                if (!StringUtils.isEmpty(filePathUUID))
                {
                    FilePathUtils.deleteFileByUUID(filePathUUID, true);
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
    }

    public static void populateReadModel(DFAnswerEntity answerEntity,
            UserDetailsImpl userLoggedIn)
    {
        populateModel(answerEntity, false, userLoggedIn);
    }

    public static void populateWriteModel(DFAnswerEntity answerEntity,
            UserDetailsImpl userLoggedIn)
    {
        populateModel(answerEntity, true, userLoggedIn);
    }

    public static void populateWriteModel(DFModel model,
            UserDetailsImpl userLoggedIn)
    {
        populateModel(model, true, null, userLoggedIn);
    }

    public static void populateWriteModel(DFModel model,
            DFAnswerEntity answerEntity, UserDetailsImpl userLoggedIn)
    {
        populateModel(model, true, answerEntity, userLoggedIn);
    }

    private static void populateModel(DFAnswerEntity answerEntity, boolean canWrite,
            UserDetailsImpl userLoggedIn)
    {
        populateModel(answerEntity.getDfModel(), canWrite, answerEntity,
                userLoggedIn);
    }

    /**
     * Generates a lowerPermissionsMap with all permissions set to the highest
     * level. The caller can then manipulate the returned map and lower the
     * permissions for some of the questions if they need to.
     */
    public static Map<Integer, Boolean[]> getBasicForceLowerPermissionsMap(
            DFModel model)
    {
        List<Integer> questionIds = PortalUtils.getJt()
                .query("select q.id" + " from df_question q"
                        + " join df_category c on c.id=q.category"
                        + " where c.model=?", new Object[] { model.getId() },
                        (RowMapper<Integer>) new DBUtils.IntegerRowMapper());

        Map<Integer, Boolean[]> ret = questionIds.stream()
                .collect(Collectors.toMap(questionId -> questionId, questionId -> {
                    Boolean[] value = new Boolean[5];
                    value[0] = true;
                    value[1] = true;
                    value[2] = true;
                    value[3] = true;
                    value[4] = true;
                    return value;
                }));
        return ret;
    }

    /**
     * Formats DF answer into a user-friendly string
     */
    public static String getFormattedDFAnswer(DFQuestion question,
            DFAnswerEntity answerEntity, Locale locale)
            throws IllegalAccessException, NoSuchMethodException,
            InvocationTargetException
    {
        return DFAnswerFormatterFactory.getFormatter(question.getType())
                .format(question, answerEntity, locale);
    }

    private static Map<String, String> getCriteriaQuestionSingleChoiceOptions(
            DFQuestion question, String locale)
    {
        Map<String, String> choices;
        if (PortalUtils.isSiteInMultilingualMode())
        {
            choices = StringUtils.mapify(question.getChoiceList(),
                    question.getChoiceList());
        }
        else
        {
            choices = Functions.getBilingualMapFromString(question.getChoices(),
                    locale);
        }
        return choices;
    }

    public static void copyEntityAnswers(DFAnswerEntity source,
            DFAnswerEntity target)
    {
        Field[] fields = DFAnswerEntity.class.getDeclaredFields();
        for (Field field : fields)
        {
            if (!Modifier.isStatic(field.getModifiers()))
            {
                ContentItemHelper.copyFieldFromObjectToObject(source,
                        field.getName(), target, field.getName());
            }
        }
    }

    public static List<String> getOrbisValuesInUse(DFCategory category,
            DFQuestion question, List<Object[]> dfQuestionSiteMappings)
    {
        List<String> values = new ArrayList<>();
        for (Object[] dfqsm : dfQuestionSiteMappings)
        {
            String orbisValue = (String) dfqsm[0];
            QueryBuilder qb = new QueryBuilder();
            qb.append(" select dfqsm.dfQuestion from DFQuestionSiteMapping dfqsm ");
            qb.append(" where dfqsm.dfQuestion.category.model=? ",
                    category.getModel());
            if (question.getId() != null)
            {
                qb.append(" and dfqsm.dfQuestion<>? ", question);
            }
            qb.append(" and dfqsm.siteMapping.orbisKey=? ", orbisValue);

            List<DFQuestion> questionsWithSelectedMapping = PortalUtils.getHt()
                    .find(qb);

            if (questionsWithSelectedMapping.size() > 0)
            {
                values.add(orbisValue);
            }
        }
        return values;
    }
}
