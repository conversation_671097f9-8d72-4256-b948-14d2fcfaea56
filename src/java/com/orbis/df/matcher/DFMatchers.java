package com.orbis.df.matcher;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.orbis.df.DFAnswerEntity;
import com.orbis.df.DFModel;
import com.orbis.df.DFQuestion;
import com.orbis.portal.PortalUtils;
import com.orbis.utils.DateUtils;

public final class DFMatchers
{
    private DFMatchers()
    {
    }

    public static DFMatchResult match(DFAnswerEntity first, DFAnswerEntity second, List<DFMatchMapping> mappings)
    {
        List<DFMatcher> matchers = mappings.stream()
                        .map(m -> DFMatchers.getMatcher(first, second, m))
                        .collect(Collectors.toList());

        return new DFMatchResult(first, second, matchers);
    }
    public static DFMatchResult match(DFAnswerEntity first, DFAnswerEntity second)
    {
        List<DFMatcher> matchers = getMatchMapping(first.getDfModel(),
                second.getDfModel()).stream()
                        .map(m -> DFMatchers.getMatcher(first, second, m))
                        .collect(Collectors.toList());

        return new DFMatchResult(first, second, matchers);
    }

    public static List<DFMatchMapping> getMatchMapping(DFModel first,
            DFModel second)
    {
        return PortalUtils.getHt().find(
                "from DFMatchMapping m where (m.modelMatcher.primary=? and m.modelMatcher.secondary=?) "
                        + " or (m.modelMatcher.primary=? and m.modelMatcher.secondary=?)",
                new Object[] { first, second, second, first });
    }

    private static DFMatcher getMatcher(DFAnswerEntity first, DFAnswerEntity second,
            DFMatchMapping mapping)
    {
        DFMatcher matcher = null;
        List<DFMatchCandidate> candidates = getMatchCandidates(mapping, first,
                second);

        switch (mapping.getOpType())
        {
            case DFMatchMapping.OP_EQUALS:
                matcher = equals(candidates.get(0), candidates.get(1));
                break;
            case DFMatchMapping.OP_NOT_EQUALS:
                matcher = notEquals(candidates.get(0), candidates.get(1));
                break;
            case DFMatchMapping.OP_GREATER_THAN:
                matcher = greaterThan(candidates.get(0), candidates.get(1));
                break;
            case DFMatchMapping.OP_GREATER_THAN_EQUAL:
                matcher = greaterThanEquals(candidates.get(0), candidates.get(1));
                break;
            case DFMatchMapping.OP_LESS_THAN:
                matcher = lessThan(candidates.get(0), candidates.get(1));
                break;
            case DFMatchMapping.OP_LESS_THAN_EQUAL:
                matcher = lessThanEquals(candidates.get(0), candidates.get(1));
                break;
            case DFMatchMapping.OP_ANY:
                matcher = any(candidates.get(0), candidates.get(1));
                break;
            case DFMatchMapping.OP_ALL:
                matcher = all(candidates.get(0), candidates.get(1));
                break;
            default:
                break;
        }

        return matcher;
    }

    private static List<DFMatchCandidate> getMatchCandidates(DFMatchMapping mapping,
            DFAnswerEntity first, DFAnswerEntity second)
    {
        DFMatchCandidate firstCandidate = new DFMatchCandidate(first,
                mapping.getPrimary().getCategory().getModel()
                        .equals(first.getDfModel()) ? mapping.getPrimary()
                                : mapping.getSecondary());
        DFMatchCandidate secondCandidate = new DFMatchCandidate(second,
                mapping.getPrimary().getCategory().getModel()
                        .equals(second.getDfModel()) ? mapping.getPrimary()
                                : mapping.getSecondary());

        return Lists.newArrayList(firstCandidate, secondCandidate);
    }

    public static AnyMatcher any(DFMatchCandidate primary,
            DFMatchCandidate secondary)
    {
        return new AnyMatcher(primary, secondary);
    }

    private static final class AnyMatcher extends AbstractDFMatcher
    {
        public AnyMatcher(DFMatchCandidate primary, DFMatchCandidate secondary)
        {
            super(primary, secondary);
        }

        @Override
        public boolean matches()
        {
            boolean matches = false;

            try
            {
                Object primaryVal = getPrimaryValue();
                Object secondaryVal = getSecondaryValue();

                if (primary.getQuestion().getType() == DFQuestion.TYPE_MULTI_CHOICE
                        && primaryVal != null && secondaryVal != null)
                {
                    List x = Arrays.asList(
                            secondaryVal.toString().substring(1).split("\\^"));
                    matches = Arrays
                            .stream(primaryVal.toString().substring(1).split("\\^"))
                            .anyMatch(a -> x.contains(a));
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }

            return matches;
        }

    }

    public static AllMatcher all(DFMatchCandidate primary,
            DFMatchCandidate secondary)
    {
        return new AllMatcher(primary, secondary);
    }

    private static final class AllMatcher extends AbstractDFMatcher
    {
        public AllMatcher(DFMatchCandidate primary, DFMatchCandidate secondary)
        {
            super(primary, secondary);
        }

        @Override
        public boolean matches()
        {
            boolean matches = false;

            try
            {
                Object primaryVal = getPrimaryValue();
                Object secondaryVal = getSecondaryValue();

                if (primary.getQuestion().getType() == DFQuestion.TYPE_MULTI_CHOICE
                        && primaryVal != null && secondaryVal != null)
                {
                    List x = Arrays.asList(
                            secondaryVal.toString().substring(1).split("\\^"));

                    matches = Arrays
                            .stream(primaryVal.toString().substring(1).split("\\^"))
                            .allMatch(a -> x.contains(a));
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }

            return matches;
        }

    }

    public static EqualMatcher equals(DFMatchCandidate primary,
            DFMatchCandidate secondary)
    {
        return new EqualMatcher(primary, secondary);
    }

    private static final class EqualMatcher extends AbstractDFMatcher
    {
        public EqualMatcher(DFMatchCandidate primary, DFMatchCandidate secondary)
        {
            super(primary, secondary);
        }

        @Override
        public boolean matches()
        {
            boolean matches = false;

            try
            {
                Object primaryVal = getPrimaryValue();
                Object secondaryVal = getSecondaryValue();

                if (primaryVal instanceof Date)
                {
                    matches = DateUtils.isEquals((Date) primaryVal,
                            (Date) secondaryVal);
                }
                else
                {
                    matches = primaryVal.equals(secondaryVal);
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }

            return matches;
        }
    }

    public static NotEqualMatcher notEquals(DFMatchCandidate primary,
            DFMatchCandidate secondary)
    {
        return new NotEqualMatcher(primary, secondary);
    }

    private static final class NotEqualMatcher extends AbstractDFMatcher
    {
        public NotEqualMatcher(DFMatchCandidate primary, DFMatchCandidate secondary)
        {
            super(primary, secondary);
        }

        @Override
        public boolean matches()
        {
            return !new EqualMatcher(primary, secondary).matches();
        }

    }

    public static GTMatcher greaterThan(DFMatchCandidate primary,
            DFMatchCandidate secondary)
    {
        return new GTMatcher(primary, secondary);
    }

    private static final class GTMatcher extends AbstractDFMatcher
    {
        public GTMatcher(DFMatchCandidate primary, DFMatchCandidate secondary)
        {
            super(primary, secondary);
        }

        @Override
        public boolean matches()
        {
            boolean matches = false;

            try
            {
                Object primaryVal = getPrimaryValue();
                Object secondaryVal = getSecondaryValue();

                if (primaryVal instanceof Date)
                {
                    matches = DateUtils.isAfter((Date) primaryVal,
                            (Date) secondaryVal);
                }
                else if (primaryVal instanceof Integer)
                {
                    matches = (Integer) primaryVal > (Integer) secondaryVal;
                }
                else if (primaryVal instanceof Double)
                {
                    matches = (Double) primaryVal > (Double) secondaryVal;
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }

            return matches;
        }
    }

    public static GTEMatcher greaterThanEquals(DFMatchCandidate primary,
            DFMatchCandidate secondary)
    {
        return new GTEMatcher(primary, secondary);
    }

    private static final class GTEMatcher extends AbstractDFMatcher
    {
        public GTEMatcher(DFMatchCandidate primary, DFMatchCandidate secondary)
        {
            super(primary, secondary);
        }

        @Override
        public boolean matches()
        {
            boolean matches = false;

            try
            {
                Object primaryVal = getPrimaryValue();
                Object secondaryVal = getSecondaryValue();

                if (primaryVal instanceof Date)
                {
                    matches = DateUtils.isAfterOrEquals((Date) primaryVal,
                            (Date) secondaryVal);
                }
                else if (primaryVal instanceof Integer)
                {
                    matches = (Integer) primaryVal >= (Integer) secondaryVal;
                }
                else if (primaryVal instanceof Double)
                {
                    matches = (Double) primaryVal >= (Double) secondaryVal;
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }

            return matches;
        }
    }

    public static LTMatcher lessThan(DFMatchCandidate primary,
            DFMatchCandidate secondary)
    {
        return new LTMatcher(primary, secondary);
    }

    private static final class LTMatcher extends AbstractDFMatcher
    {
        public LTMatcher(DFMatchCandidate primary, DFMatchCandidate secondary)
        {
            super(primary, secondary);
        }

        @Override
        public boolean matches()
        {
            boolean matches = false;

            try
            {
                Object primaryVal = getPrimaryValue();
                Object secondaryVal = getSecondaryValue();

                if (primaryVal instanceof Date)
                {
                    matches = DateUtils.isBefore((Date) primaryVal,
                            (Date) secondaryVal);
                }
                else if (primaryVal instanceof Integer)
                {
                    matches = (Integer) primaryVal < (Integer) secondaryVal;
                }
                else if (primaryVal instanceof Double)
                {
                    matches = (Double) primaryVal < (Double) secondaryVal;
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }

            return matches;
        }
    }

    public static LTEMatcher lessThanEquals(DFMatchCandidate primary,
            DFMatchCandidate secondary)
    {
        return new LTEMatcher(primary, secondary);
    }

    private static final class LTEMatcher extends AbstractDFMatcher
    {
        public LTEMatcher(DFMatchCandidate primary, DFMatchCandidate secondary)
        {
            super(primary, secondary);
        }

        @Override
        public boolean matches()
        {
            boolean matches = false;

            try
            {
                Object primaryVal = getPrimaryValue();
                Object secondaryVal = getSecondaryValue();

                if (primaryVal instanceof Date)
                {
                    matches = DateUtils.isBeforeOrEquals((Date) primaryVal,
                            (Date) secondaryVal);
                }
                else if (primaryVal instanceof Integer)
                {
                    matches = (Integer) primaryVal <= (Integer) secondaryVal;
                }
                else if (primaryVal instanceof Double)
                {
                    matches = (Double) primaryVal <= (Double) secondaryVal;
                }
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }

            return matches;
        }
    }

    public static final class DFMatchResult
    {
        private final DFAnswerEntity primary;

        private final DFAnswerEntity secondary;

        private final List<DFMatcher> matchers;

        public DFMatchResult(DFAnswerEntity primary, DFAnswerEntity secondary,
                List<DFMatcher> matchers)
        {
            this.primary = primary;
            this.secondary = secondary;
            this.matchers = matchers;
        }

        public List<DFMatcher> getMatchers()
        {
            return matchers;
        }

        public DFAnswerEntity getPrimary()
        {
            return primary;
        }

        public DFAnswerEntity getSecondary()
        {
            return secondary;
        }

        public double getCompatibility()
        {
            double compatibility = 0d;

            try
            {
                compatibility = (double) matchers.stream().filter(m -> m.matches())
                        .collect(Collectors.toList()).size() / matchers.size();
            }
            catch (Exception e)
            {
            }

            return compatibility;
        }
    }
}
