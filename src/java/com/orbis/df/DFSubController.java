package com.orbis.df;

import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.apache.commons.lang.ArrayUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.ReflectionActionController;
import org.springframework.web.bind.ServletRequestUtils;
import org.springframework.web.servlet.ModelAndView;

import com.google.common.collect.ImmutableMap;
import com.orbis.acegi.providers.dao.hibernate.PersonGroup;
import com.orbis.acegi.providers.dao.hibernate.PersonGroupHelper;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.df.matcher.DFMatchMapping;
import com.orbis.df.matcher.DFModelMatcher;
import com.orbis.form.FormHelper;
import com.orbis.portal.CommandQueryTemplate;
import com.orbis.portal.PortalUtils;
import com.orbis.spring.servlet.ByteArrayDownloadView;
import com.orbis.utils.ClassUtils;
import com.orbis.utils.FileUtils;
import com.orbis.utils.FlashMessageUtils;
import com.orbis.utils.I18nLabel;
import com.orbis.utils.JSONUtils;
import com.orbis.utils.LocaleUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.acrm.AcrmDivisionQuestion;
import com.orbis.web.content.acrm.AcrmHelper;
import com.orbis.web.content.acrm.AcrmOrgQuestion;
import com.orbis.web.content.acrm.dataImport.AcrmDataImportHelper;
import com.orbis.web.content.crm.OrganizationModelEntity;
import com.orbis.web.content.exp.EXPPostingQualifierModel;
import com.orbis.web.content.np.NHelper;
import com.orbis.web.site.SiteController;

import jakarta.servlet.ServletContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public class DFSubController extends ReflectionActionController
{
    private static Map<DFQuestionInterface, DFSubController> controllerInstances = new HashMap<>();

    private DFQuestionInterface questionInterface;

    private CommandQueryTemplate ht = PortalUtils.getHt();

    private DFSubController()
    {
    }

    private DFSubController(DFQuestionInterface questionInterface,
            ServletContext servletContext)
    {
        this.questionInterface = questionInterface;

    }

    public static DFSubController getInstance(DFQuestionInterface questionInterface,
            ServletContext servletContext)
    {
        DFSubController controller = controllerInstances.get(questionInterface);
        if (controller == null)
        {
            controller = new DFSubController(questionInterface, servletContext);
            controllerInstances.put(questionInterface, controller);
        }
        return controller;
    }

    public ModelAndView processRequest(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = null;

        try
        {
            mv = invokeNamedMethod(request.getParameter("subAction"), request,
                    response);
        }
        catch (Exception e)
        {
            mv = displayError("An exception has occurred: " + e.getMessage());
            e.printStackTrace();
        }

        return mv;
    }

    /**
     * LOAD QUESTION EDITOR
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    public ModelAndView displayQuestionQualifierEdit(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = this.questionInterface
                .displayDFQuestionQualifierEdit(request);

        DFQuestion question = null;
        DFQuestionQualifier qualifier = DFHelper.getQuestionQualifier(request);
        if (null == qualifier)
        {
            qualifier = new DFQuestionQualifier();

            if (StringUtils.isInteger(request.getParameter("dfQuestionId")))
            {
                question = DFHelper.getQuestion(request);
            }
            else
            {
                throw new RuntimeException("missing param: dfQuestionId");
            }
        }
        else
        {
            question = qualifier.getQuestion();
        }

        mv.addObject("dfQualifier", qualifier);
        mv.addObject("dfQuestion", question);
        mv.addObject("dfQualifierEntityType",
                request.getParameter("dfQualifierEntityType"));
        mv.addObject("dfQualifierEntityId",
                request.getParameter("dfQualifierEntityId"));

        return mv;
    }

    /**
     * SAVE QUESTION QUALIFIER
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    public ModelAndView saveQuestionQualifier(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        DFQualifierEntity qualifierEntity = DFHelper
                .getQualifierTypeEntity(request);
        DFQuestion question = DFHelper.getQuestion(request);
        ModelAndView mv = questionInterface.saveDFQuestionQualifier(request,
                response, qualifierEntity, question);
        return mv;
    }

    /**
     * DELETE QUESTION QUALIFIER
     *
     */
    public ModelAndView deleteQuestionQualifier(HttpServletRequest request,
            HttpServletResponse response)
    {
        DFQuestionQualifier qualifier = DFHelper.getQuestionQualifier(request);
        DFQualifierEntity qualifierEntity = DFHelper
                .getQualifierTypeEntity(request);
        DFHelper.deleteQuestionQualifier(qualifier);
        ModelAndView mv = questionInterface.displayDFQuestionQualifierEditor(
                request, response, qualifierEntity);
        return mv;
    }

    public ModelAndView displayDFQuestionQualifierEditor(HttpServletRequest request,
            HttpServletResponse response)
    {
        DFQualifierEntity qualifierEntity = DFHelper
                .getQualifierTypeEntity(request);
        ModelAndView mv = questionInterface.displayDFQuestionQualifierEditor(
                request, response, qualifierEntity);
        return mv;
    }

    public ModelAndView getQualifierBreakdownJSON(HttpServletRequest request,
            HttpServletResponse response)
    {
        DFQualifierEntity entity = DFHelper.getQualifierTypeEntity(request);
        List<Object[]> qualifiers = DFHelper.getQualifiers(entity);
        DFAnswerEntity answer = DFHelper.getAnswerEntity(request);
        JSONObject qualifierBreakdown = DFHelper.getQualifierBreakdown(answer,
                qualifiers);
        return NHelper.AJAXResponse(qualifierBreakdown.toString());
    }

    public ModelAndView displayDependencyEditor(HttpServletRequest request,
            HttpServletResponse response)
    {
        DFModel model = null;
        ModelAndView mv = new ModelAndView("df/df_dependencyEditor");
        try
        {
            model = (DFModel) ht.load(DFModel.class,
                    Integer.valueOf(request.getParameter("modelId")));
        }
        catch (Exception e)
        {
            DFModelEntity modelEntity = DFHelper.getModelEntity(request);
            model = modelEntity.getDFModel();
        }

        DFHelper.populateModel(true, model, false, null, null,
                PortalUtils.getUserLoggedIn(request), null);
        mv.addObject("errors", model.getErrors());

        List<DFQuestion> allQuestions = ht
                .find("from DFQuestion dfq where dfq.category.model=?", model);

        List<String> propNames = Arrays.asList(com.orbis.utils.DBUtils
                .getEntityPersister(DFQuestionDependency.class).getPropertyNames());
        allQuestions.removeIf(x -> !propNames.contains(x.getAnswerField1()));
        if (allQuestions.isEmpty())
        {
            FlashMessageUtils.warn(request,
                    PortalUtils.getI18nMessage(
                            "i18n.DFSubController.Cannotcrea4702177987572266",
                            PortalUtils.getLocale(request)));
        }

        mv.addObject("allQuestions", allQuestions);
        mv.addObject("modelId", model.getId());

        List<DFQuestionDependency> dependencies = PortalUtils.getHt().find(
                "from DFQuestionDependency dfqd where dfqd.dfQuestion.category.model=?",
                model);

        mv.addObject("dependencies", dependencies);
        mv.addObject("dfModelEntityType",
                request.getParameter("dfModelEntityType"));
        mv.addObject("dfModelEntityId", request.getParameter("dfModelEntityId"));

        if ("edit".equals(request.getParameter("mode")))
        {
            // for edit mode
            DFQuestionDependency dependency = DFHelper
                    .getDFQuestionDependency(request);

            if (dependency != null)
            {
                mv.addObject("dependency", dependency);
            }
        }

        return mv;
    }

    public ModelAndView saveDependency(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        DFModel model = null;

        boolean success = false;

        DFQuestion source = (DFQuestion) ht.load(DFQuestion.class,
                Integer.valueOf(request.getParameter("dfQuestion")));
        DFQuestion dependsOn = (DFQuestion) ht.load(DFQuestion.class,
                Integer.valueOf(request.getParameter("dfQuestionDependsOn")));

        String answer = request.getParameter("dfQuestion_" + dependsOn.getId());
        if (StringUtils.isEmpty(answer) && !StringUtils
                .isEmpty(request.getParameter("question_" + dependsOn.getId())))
        {
            answer = request.getParameter("question_" + dependsOn.getId());
        }

        boolean dependencyToNameFieldAttempt = false;
        if ((dependsOn instanceof AcrmDivisionQuestion
                || dependsOn instanceof AcrmOrgQuestion)
                && !StringUtils.isEmpty(dependsOn.getAnswerField1())
                && "name".equals(dependsOn.getAnswerField1()))
        {
            dependencyToNameFieldAttempt = true;
        }

        if (!dependencyToNameFieldAttempt
                && !dependencyExists(source, dependsOn, answer))
        {
            DFQuestionDependency dependency = DFHelper
                    .getDFQuestionDependency(request);

            if (dependency != null)
            {
                model = dependency.getDfQuestion().getCategory().getModel();
            }
            else
            {
                model = source.getCategory().getModel();
                dependency = new DFQuestionDependency();
            }

            DFHelper.bindAnswer(dependsOn, dependency, request);
            dependency.setDfQuestion(source);
            dependency.setDfQuestionDependsOn(dependsOn);

            ht.saveOrUpdate(dependency);

            if (model.getModelEntityClassName() == null)
            {
                model.setModelEntityClassName(ClassUtils
                        .loadClass(request.getParameter("dfModelEntityType")));
                ht.update(model);
            }

            DFHelper.populateModel(model,
                    !StringUtils.isEmpty(request.getParameter("editMode")),
                    dependency, PortalUtils.getUserLoggedIn(request));
            success = true;
        }

        ModelAndView mv = displayDependencyEditor(request, response);
        if (success)
        {
            mv.addObject("successMessage",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.df_dependencyEditor.Dependencyhasbeencreatedsuccessfully",
                            null, PortalUtils.getLocale(request)));
        }
        else if (dependencyToNameFieldAttempt)
        {
            mv.addObject("errorMessage",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.df_dependencyEditor.DependencyToNameField", null,
                            PortalUtils.getLocale(request)));

        }
        else
        {
            mv.addObject("errorMessage",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.df_dependencyEditor.DependencyAlreadyExists",
                            null, PortalUtils.getLocale(request)));
        }
        return mv;
    }

    private boolean dependencyExists(DFQuestion source, DFQuestion dependsOn,
            String answer)
    {
        Object[] params = new Object[] { source, dependsOn };
        if (!StringUtils.isEmpty(answer))
        {
            if ("yes".equalsIgnoreCase(answer))
            {
                answer = "1";
            }
            else if ("no".equalsIgnoreCase(answer))
            {
                answer = "0";
            }

            params = ArrayUtils.add(params, answer);
        }

        return (Integer) PortalUtils.getHt().find(
                "select count(d) from DFQuestionDependency d where d.dfQuestion = ? and d.dfQuestionDependsOn = ? "
                        + "and d." + dependsOn.getAnswerField1()
                        + (StringUtils.isEmpty(answer) ? " is null"
                                : " like '%" + answer.replace("'", "''") + "%'"),
                new Object[] { source, dependsOn }).get(0) > 0;
    }

    public ModelAndView ajaxDeleteDependency(HttpServletRequest request,
            HttpServletResponse response)
    {
        String retStatus = "successful";

        try
        {
            DFQuestionDependency dependency = DFHelper
                    .getDFQuestionDependency(request);
            if (dependency != null)
            {
                ht.delete(dependency);
            }
        }
        catch (Exception e)
        {
            retStatus = "error";
            e.printStackTrace();
        }

        return NHelper.AJAXResponse(retStatus);
    }

    public ModelAndView loadDependsOnQuestions(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("df/df_ajaxDependsOnQuestion");

        // for edit mode
        DFQuestionDependency dependency = DFHelper.getDFQuestionDependency(request);

        if (dependency != null)
        {
            mv.addObject("dependency", dependency);
        }

        List<DFQuestion> dependsOnQuestions = ht
                .find("from DFQuestion dfq where dfq.id <> "
                        + request.getParameter("dfQuestionId")
                        + " and dfq.category.model.id="
                        + request.getParameter("modelId"));
        mv.addObject("dependsOnQuestions", dependsOnQuestions);
        mv.addObject("modelId", request.getParameter("modelId"));

        mv.addObject("dfModelEntityType",
                request.getParameter("dfModelEntityType"));
        mv.addObject("dfModelEntityId", request.getParameter("dfModelEntityId"));
        return mv;
    }

    public ModelAndView loadAnswerQuestion(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("df/df_ajaxAnswerQuestion");

        List<DFQuestion> questions = ht.find("from DFQuestion dfq where dfq.id = "
                + request.getParameter("dfQuestionId")
                + " and dfq.category.model.id=" + request.getParameter("modelId"));

        if (questions != null && questions.size() > 0)
        {
            DFQuestion question = questions.get(0);

            DFQuestionDependency dependency = DFHelper
                    .getDFQuestionDependency(request);

            if (dependency != null)
            {
                if (DFHelper.isAnswered(question, dependency))
                {
                    DFHelper.populateModel(question.getCategory().getModel(), true,
                            dependency, PortalUtils.getUserLoggedIn(request));
                    mv.addObject("DFAnswerrEntity", dependency);
                }
                mv.addObject("dependency", dependency);
            }
            mv.addObject("dfQuestion", question);
        }

        mv.addObject("modelId", request.getParameter("modelId"));

        mv.addObject("dfModelEntityType",
                request.getParameter("dfModelEntityType"));
        mv.addObject("dfModelEntityId", request.getParameter("dfModelEntityId"));
        return mv;
    }

    public ModelAndView displayImport(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("df/df_importDataTemplateBuilder");

        mv.addObject("importUsecase",
                AcrmDataImportHelper.getImportUsecase(request));
        mv.addObject("dfModelEntityType",
                request.getParameter("dfModelEntityType"));
        mv.addObject("dfModelEntityId", request.getParameter("dfModelEntityId"));

        return mv;
    }

    public ModelAndView displayExportModel(HttpServletRequest request,
            HttpServletResponse response)
    {

        DFModelEntity entity = DFHelper.getModelEntity(request);
        DFModel model = entity.getDFModel();
        ModelAndView mv = null;

        if (model == null)
        {
            model = DFHelper.createNewDefaultModel(entity, this.questionInterface,
                    false);
        }

        DFHelper.populateModel(model, true, null,
                PortalUtils.getUserLoggedIn(request));

        if (model.getModelEntityClassName() == null)
        {
            try
            {
                model.setModelEntityClassName(ClassUtils
                        .loadClass(request.getParameter("dfModelEntityType")));
            }
            catch (ClassNotFoundException ignored)
            {
            }
            ht.update(model);
        }

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        DataOutputStream out = new DataOutputStream(byteArrayOutputStream);

        try
        {
            String modelJson = DFHelper.getDFModelExportJSON(model);
            if (modelJson != null)
            {
                out.write(modelJson.getBytes());
                byteArrayOutputStream.flush();
                byteArrayOutputStream.close();
            }
            else
            {
                try
                {
                    mv = displayDFQuestionEditor(request, response);
                    FlashMessageUtils.error(request, new I18nLabel(
                            "i18n.DFSubController.Somethingw5325376426921011")
                                    .getTranslation(
                                            PortalUtils.getLocale(request)));
                }
                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }
        catch (IOException e)
        {
            e.printStackTrace();
        }

        String answerEntityClassName = "Unknown";
        try
        {
            answerEntityClassName = this.questionInterface
                    .getAnswerEntityClass(entity).getName();
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        if (mv == null)
        {
            String fileName = answerEntityClassName + ".json";
            mv = new ModelAndView(
                    new ByteArrayDownloadView(fileName, byteArrayOutputStream));
        }

        return mv;
    }

    /**
     * Import a selected DF Model JSON or revert the DF Model to its associated
     * default JSON
     *
     * @param request
     *            Request object
     * @param response
     *            Response object
     * @return The DF editor after importing the model
     *
     *         TODO: Uncomment the default json case when all DF Models that have a
     *         populated initial state have their own JSON files; this will cause
     *         reverts on any models without their own JSON files to be reset to the
     *         default DF Model with a single category and question
     */
    public ModelAndView displayImportModel(HttpServletRequest request,
            HttpServletResponse response)
    {
        DFModelEntity entity = DFHelper.getModelEntity(request);

        final String jsonString;
        final boolean revert = ServletRequestUtils.getBooleanParameter(request,
                "revert", false);

        if (revert)
        {
            String dir = "/WEB-INF/conf/defaults/DFModel/";
            String filePath = dir.concat(
                    this.questionInterface.getAnswerEntityClass(entity).getName())
                    .concat(".json");
            /*
             * if (!FileUtils
             * .fileExists(PortalUtils.getRealPath("/").concat(filePath))) {
             * filePath = dir.concat("default.json"); }
             */
            jsonString = PortalUtils.getResourceAsString(filePath, false);
        }
        else
        {
            byte[] jsonFileBytes = null;

            try
            {
                jsonFileBytes = FileUtils.getUploadedFile(request, "modelJson")
                        .getBytes();
            }
            catch (IOException e)
            {
                e.printStackTrace();
            }

            if (jsonFileBytes != null)
            {
                jsonString = new String(jsonFileBytes);
            }
            else
            {
                jsonString = null;
            }
        }

        ModelAndView mv = null;

        try
        {
            mv = displayDFQuestionEditor(request, response);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        if (jsonString != null && mv != null)
        {
            DFModel newModel = DFHelper.importModel(entity, jsonString);

            if (newModel != null)
            {
                DFModel model = entity.getDFModel();

                DFHelper.populateModel(model, true, null,
                        PortalUtils.getUserLoggedIn(request));

                model.setCanWrite(newModel.isCanWrite());
                model.setName(newModel.getName());
                model.setPreserveOrdering(newModel.isPreserveOrdering());
                model.setPublicAllowed(newModel.isPublicAllowed());
                try
                {
                    model.setModelEntityClassName(ClassUtils
                            .loadClass(request.getParameter("dfModelEntityType")));
                }
                catch (ClassNotFoundException ignored)
                {
                }
                PortalUtils.getHt().update(model);

                FlashMessageUtils.success(request, revert
                        ? "i18n.DFSubController.Modelrever2728382908646231"
                        : "i18n.DFSubController.Modelrever2728382908646232");
            }
            else
            {

                FlashMessageUtils.error(request, revert
                        ? "i18n.DFSubController.Somethingw2095251176945490"
                        : "i18n.DFSubController.Somethingw2095251176945491");
            }
        }

        return mv;
    }

    public ModelAndView importData_downloadTemplate(HttpServletRequest request,
            HttpServletResponse response)
    {
        return AcrmDataImportHelper.downloadTemplate(request);
    }

    public ModelAndView importData_uploadSpreadsheet(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("/df/df_importDataStaging");
        mv.addObject("importUsecase", request.getParameter("importUsecase"));
        mv.addObject("currentTabSelected", "import");

        AcrmDataImportHelper.handleDataImportFileUpload(
                request.getParameter("importSpreadsheet"),
                PortalUtils.getUserLoggedIn(request), mv,
                PortalUtils.getLocale(request));

        return mv;
    }

    public ModelAndView importData_startImport(HttpServletRequest request,
            HttpServletResponse response)
    {
        AcrmDataImportHelper.startImport(request);
        ModelAndView mv = displayImport(request, response);
        mv.addObject("importUsecase",
                AcrmDataImportHelper.getImportUsecase(request));
        // mv.addObject("", modelObject);
        mv.addObject("successMessage",
                PortalUtils.getMessageSource().getMessage(
                        "i18n.df_questionImport.Importingnewdatahasbegun", null,
                        PortalUtils.getLocale(request)));
        return mv;
    }

    public ModelAndView importData_downloadDataImportFile(
            HttpServletRequest request, HttpServletResponse response)
    {
        ModelAndView mv = AcrmDataImportHelper.downloadDataImportFile(request);

        if (mv == null)
        {
            mv = displayImport(request, response);
            mv.addObject("errorMessage",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.df_downloadDataImportFile.Error81375913522", null,
                            PortalUtils.getLocale(request)));
        }

        return mv;
    }

    public ModelAndView importData_checkImportProgress(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        return DFHelper.ajaxLoadDataImportProgress(request);
    }

    public ModelAndView lookupNewUsers(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        return AcrmHelper.lookupUsers(request.getParameter("term"), null, 20,
                false);
    }

    public ModelAndView addUserToRole(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = null;
        boolean saved = false;
        if (!StringUtils.isEmpty("roleId") && !StringUtils.isEmpty("userId"))
        {
            DFRole role = (DFRole) ht.load(DFRole.class,
                    Integer.valueOf(request.getParameter("roleId")));
            UserDetailsImpl au = (UserDetailsImpl) ht.load(UserDetailsImpl.class,
                    Integer.valueOf(request.getParameter("userId")));

            List<DFRoleUser> roleUsers = ht.find(
                    "from DFRoleUser dru where dru.role=? and dru.user=?",
                    new Object[] { role, au });

            if (0 == roleUsers.size())
            {
                DFRoleUser roleUser = new DFRoleUser();
                roleUser.setRole(role);
                roleUser.setUser(au);
                ht.save(roleUser);
                saved = true;
            }

        }
        mv = displayEditRole(request, response);
        mv.addObject("saved", saved);
        return mv;
    }

    public ModelAndView displayPreviewForm(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = this.questionInterface.displayPreviewForm(request);

        DFModelEntity modelEntity = DFHelper.getModelEntity(request);
        DFModel dfModel = modelEntity.getDFModel();
        DFHelper.populateModel(dfModel, true, null,
                PortalUtils.getUserLoggedIn(request));
        mv.addObject("dfModel", dfModel);
        mv.addObject("dfModelEntityType",
                request.getParameter("dfModelEntityType"));
        mv.addObject("dfModelEntityId", request.getParameter("dfModelEntityId"));
        return mv;
    }

    public ModelAndView deleteUserFromRole(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = null;
        String userId = request.getParameter("userId");
        String roleId = request.getParameter("roleId");

        if (!StringUtils.isEmpty(userId) && !StringUtils.isEmpty(roleId))
        {
            PortalUtils.getJt().update(
                    "delete from df_role_user where role = ? and uzer = ?",
                    new Object[] { roleId, userId });
        }

        mv = displayEditRole(request, response);
        mv.addObject("deleteMemberSuccess", Boolean.TRUE);
        return mv;
    }

    public ModelAndView displayManageRoles(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = this.questionInterface.displayManageRoles(request);

        DFModelEntity modelEntity = DFHelper.getModelEntity(request);

        String orderBy = modelEntity.getDFModel().isPreserveOrdering()
                ? "dr.position"
                : "dr.name";
        List<DFRole> roles = PortalUtils.getHt()
                .find("from DFRole dr where dr.model.id="
                        + modelEntity.getDFModel().getId() + " order by "
                        + orderBy);

        mv.addObject("dfRoles", roles);
        mv.addObject("dfModelEntityType",
                request.getParameter("dfModelEntityType"));
        mv.addObject("dfModelEntityId", request.getParameter("dfModelEntityId"));
        mv.addObject("model", modelEntity.getDFModel());
        return mv;
    }

    public ModelAndView changeAllowPublicRole(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        DFModelEntity modelEntity = DFHelper.getModelEntity(request);
        DFModel model = modelEntity.getDFModel();
        model.setPublicAllowed(!model.isPublicAllowed());
        ht.saveOrUpdate(model);

        if (model.isPublicAllowed())
        {
            DFRole publicRole = DFHelper.addPublicRoleToModel(model);

            List<DFQuestion> questions = ht
                    .find("from DFQuestion as q where q.category.model=?", model);

            for (DFQuestion question : questions)
            {
                DFQuestionRole newRole = new DFQuestionRole();
                newRole.setCanRead(publicRole.isDefaultRead());
                newRole.setCanWrite(publicRole.isDefaultWrite());
                newRole.setCanSearch(publicRole.isDefaultSearch());
                newRole.setRequired(publicRole.isDefaultRequired());
                newRole.setCanShowInResults(publicRole.isDefaultShowInResults());
                newRole.setCanShowInReports(publicRole.isDefaultShowInReports());
                newRole.setDfQuestion(question);
                newRole.setDfRole(publicRole);
                ht.saveOrUpdate(newRole);
            }
        }
        else
        {
            DFRole publicRole = (DFRole) PortalUtils.getHt()
                    .find("from DFRole r where r.publicRole=true and r.model.id=?",
                            model.getId())
                    .get(0);
            PortalUtils.getJt().update(
                    "delete from df_question_role where dfRole = ?",
                    new Object[] { publicRole.getId() });
            ht.delete(publicRole);
        }

        ModelAndView mv = displayManageRoles(request, response);
        return mv;
    }

    public ModelAndView changePreserveOrdering(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        DFModelEntity modelEntity = DFHelper.getModelEntity(request);
        DFModel model = modelEntity.getDFModel();
        model.setPreserveOrdering(!model.isPreserveOrdering());
        ht.saveOrUpdate(model);
        ModelAndView mv = displayManageRoles(request, response);
        return mv;
    }

    public ModelAndView displayAuditLogs(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = this.questionInterface.displayAuditLogs(request);

        List<DFQuestionLog> logs = ht.find(
                "from DFQuestionLog log where log.dfQuestion.category.model.id="
                        + request.getParameter("dfModelEntityId"));
        mv.addObject("logs", logs);
        mv.addObject("dfModelEntityType",
                request.getParameter("dfModelEntityType"));
        mv.addObject("dfModelEntityId", request.getParameter("dfModelEntityId"));
        return mv;
    }

    public ModelAndView displayDFQuestionEditor(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        DFModelEntity modelEntity = DFHelper.getModelEntity(request);
        return this.questionInterface.displayDFQuestionEditor(request, response,
                modelEntity);
    }

    public ModelAndView saveRolePosition(HttpServletRequest request,
            HttpServletResponse response)
    {
        String roleIdsValue = request.getParameter("roleIds");
        String[] roleIds = roleIdsValue.split(",");

        for (int i = 0; i < roleIds.length; i++)
        {
            PortalUtils.getJt().update(
                    "update df_role set position = ? where id = ?",
                    new Object[] { i, roleIds[i] });
        }

        return NHelper.AJAXResponse("success");
    }

    public ModelAndView deleteRole(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        Integer roleId = Integer.valueOf(request.getParameter("roleId"));

        DFHelper.deleteRole(roleId);
        ModelAndView mv = displayManageRoles(request, response);

        mv.addObject("roleDeleted", Boolean.TRUE);
        return mv;
    }

    public ModelAndView displayEditRole(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = this.questionInterface.displayEditRole(request);

        DFModelEntity modelEntity = DFHelper.getModelEntity(request);
        DFRole role = null;

        if (StringUtils.isEmpty(request.getParameter("roleId")))
        {
            // Create new
            role = new DFRole();
            role.setModel(modelEntity.getDFModel());
            role.setName(PortalUtils.getMessageSource().getMessage(
                    "i18n.DFSubController.NewRole2003328973424030", null,
                    LocaleUtils.getDefaultLocale()));
            role.setL2Name(PortalUtils.getMessageSource().getMessage(
                    "i18n.DFSubController.NewRole2003328973424030", null,
                    LocaleUtils.getSecondaryLocale()));
            role.setPosition(DFHelper
                    .getRolesForModel(modelEntity.getDFModel().getId().toString())
                    .size());
            ht.save(role);
        }
        else
        {
            // Edit
            role = (DFRole) ht.load(DFRole.class,
                    Integer.valueOf(request.getParameter("roleId")));
        }

        List<PersonGroup> primaryGroups = PersonGroupHelper
                .getPrimaryGroupsFromCache();
        List<PersonGroup> secondaryGroups = PersonGroupHelper.getSecondaryGroups();

        List roleGroupIds = PortalUtils.getHt().find(
                "select rug.userGroup.id from DFRoleUserGroup rug where rug.role=?",
                role);

        Map<PersonGroup, String> primaryGroupMap = new LinkedHashMap<>();

        for (PersonGroup group : primaryGroups)
        {
            if (roleGroupIds.contains(group.getId()))
            {
                primaryGroupMap.put(group, "checked");
            }
            else
            {
                primaryGroupMap.put(group, "");
            }
        }

        Map<PersonGroup, String> secondaryGroupMap = new LinkedHashMap<>();

        for (PersonGroup group : secondaryGroups)
        {
            if (roleGroupIds.contains(group.getId()))
            {
                secondaryGroupMap.put(group, "checked");
            }
            else
            {
                secondaryGroupMap.put(group, "");
            }
        }

        mv.addObject("primaryGroups", primaryGroupMap);
        mv.addObject("secondaryGroups", secondaryGroupMap);
        mv.addObject("role", role);
        mv.addObject("roleUsers", DFHelper.getRoleUsers(role));
        mv.addObject("dfModelEntityType",
                request.getParameter("dfModelEntityType"));
        mv.addObject("dfModelEntityId", request.getParameter("dfModelEntityId"));
        return mv;
    }

    public ModelAndView saveRole(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = null;
        String[] groupIds = request.getParameterValues("group");
        String roleId = request.getParameter("roleId");

        PortalUtils.getJt().update("delete from df_role_user_group where role = ?",
                new Object[] { roleId });

        DFRole role = (DFRole) ht.load(DFRole.class, Integer.valueOf(roleId));

        role.setDefaultRead(null != request.getParameter("canRead"));
        role.setDefaultWrite(null != request.getParameter("canWrite"));
        role.setDefaultSearch(null != request.getParameter("canSearch"));
        role.setDefaultShowInResults(
                null != request.getParameter("canShowInResults"));
        role.setDefaultShowInReports(
                null != request.getParameter("canShowInReports"));
        role.setDefaultRequired(null != request.getParameter("isRequired"));

        role.setName(com.orbis.utils.RequestUtils.limitTo255(request, "roleName"));
        role.setL2Name(com.orbis.utils.RequestUtils.limitTo255ForL2Fields(request,
                "l2RoleName", "roleName"));
        role.setPublicRole(null != request.getParameter("isPublicRole"));

        if (groupIds != null)
        {
            for (int i = 0; i < groupIds.length; i++)
            {
                DFRoleUserGroup roleUserGroup = new DFRoleUserGroup();
                PersonGroup group = (PersonGroup) ht.load(PersonGroup.class,
                        Integer.valueOf(groupIds[i]));
                roleUserGroup.setRole(role);
                roleUserGroup.setUserGroup(group);
                ht.save(roleUserGroup);
            }
        }
        DFModelEntity entity = DFHelper.getModelEntity(request);
        List<DFQuestionRole> questionRoles = ht
                .find("from DFQuestionRole dfqr where dfqr.dfRole=?", role);
        if (questionRoles.size() == 0)
        {
            // Create question roles for questions as necessary
            List<DFQuestion> questions = ht.find(
                    "from DFQuestion as q where q.category.model=?",
                    entity.getDFModel());
            // Delete all questionroles with this role first then readd

            PortalUtils.getJt().update(
                    "delete from df_question_role where dfRole = ?",
                    new Object[] { role.getId() });

            for (DFQuestion question : questions)
            {
                DFQuestionRole newRole = new DFQuestionRole();
                newRole.setCanRead(role.isDefaultRead());
                newRole.setCanWrite(role.isDefaultWrite());
                newRole.setCanSearch(role.isDefaultSearch());
                newRole.setCanShowInResults(role.isDefaultShowInResults());
                newRole.setCanShowInReports(role.isDefaultShowInReports());
                newRole.setRequired(role.isDefaultRequired());
                newRole.setDfQuestion(question);
                newRole.setDfRole(role);
                ht.saveOrUpdate(newRole);
            }
        }

        ht.saveOrUpdate(role);

        mv = displayEditRole(request, response);
        mv.addObject("successMessage",
                PortalUtils.getMessageSource().getMessage(
                        "i18n.df_editRole.RoleSaved", null,
                        PortalUtils.getLocale(request)));
        return mv;
    }

    public ModelAndView ajaxLoadCategoryEditor(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = getShortCircuitView("df/df_categoriesEdit");

        try
        {
            DFModelEntity e = DFHelper.getModelEntity(request);

            DFModel model = e.getDFModel();

            if (model == null)
            {
                model = DFHelper.createNewDefaultModel(e, this.questionInterface,
                        false);
            }
            DFHelper.populateModel(model, true, null,
                    PortalUtils.getUserLoggedIn(request));
            mv.addObject("model", model);

            String dir = "/WEB-INF/conf/defaults/DFModel/";
            String filePath = dir
                    .concat(Optional
                            .ofNullable(
                                    this.questionInterface.getAnswerEntityClass(e))
                            .map(Class::getName)
                            .orElseGet(() -> e.getClass().getName()))
                    .concat(".json");
            mv.addObject("allowRevert", FileUtils
                    .fileExists(PortalUtils.getRealPath("/").concat(filePath)));
            getUiI18nElementOverrides(e, mv, request);

        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return mv;
    }

    /**
     * this should be should eventually be moved to question interface
     */
    private void getUiI18nElementOverrides(DFModelEntity e, ModelAndView mv,
            HttpServletRequest request)
    {
        if (e instanceof EXPPostingQualifierModel)
        {

            mv.addObject("addQuestionButtonText", PortalUtils
                    .getI18nMessage("i18n.DFSubController.AddAQualifier", request));
        }
        // TODO Auto-generated method stub
    }

    /**
     * LOAD QUESTION EDITOR
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    public ModelAndView displayQuestionEdit(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        ModelAndView mv = this.questionInterface.displayDFQuestionEdit(request);

        DFModelEntity modelEntity = DFHelper.getModelEntity(request);

        DFCategory category;
        DFQuestion question = DFHelper.getQuestion(request);
        if (question == null)
        {
            if (modelEntity instanceof OrganizationModelEntity)
            {
                question = new AcrmOrgQuestion();
            }
            else
            {
                question = new DFQuestion();
            }
            if (StringUtils.isInteger(request.getParameter("dfCategoryId")))
            {
                category = DFHelper.getCategory(request);

                if (category != null && category.getModel() != null && !category
                        .getModel().getAllowedQuestionTypesList().isEmpty())
                {
                    question.setType(category.getModel()
                            .getAllowedQuestionTypesList().get(0));
                }
            }
            else
            {
                throw new RuntimeException("missing param: dfCategoryId");
            }
        }
        else
        {
            question.setQuestionRoles(DFHelper.getRolesForQuestion(question));
            category = question.getCategory();
        }

        question.setFieldMappings(modelEntity.getFieldMappings());
        question.setFieldDefaultMappings(modelEntity.getFieldDefaultMappings());

        mv.addObject("allowedQuestionTypes", modelEntity.getAllowedQuestionTypes());
        mv.addObject("dfCategory", category);
        mv.addObject("dfQuestion", question);
        mv.addObject("dfModelEntityType",
                request.getParameter("dfModelEntityType"));
        mv.addObject("dfModelEntityId", request.getParameter("dfModelEntityId"));

        List<Object[]> dfQuestionSiteMappings = DFHelper.getDFQuestionSiteMappings(
                request);
        mv.addObject("dfQuestionSiteMappings", dfQuestionSiteMappings);
        mv.addObject("orbisValuesInUse",
                DFHelper.getOrbisValuesInUse(category, question,
                        dfQuestionSiteMappings));
        mv.addObject("selectedSiteMapping", PortalUtils.getHt().find(
                "select dfqsm.siteMapping.orbisKey from DFQuestionSiteMapping dfqsm where dfqsm.dfQuestion.id=?",
                question.getId()).stream().findFirst().orElse(null));

        String dfModelId = request.getParameter("dfModelId");
        if (StringUtils.isInteger(dfModelId))
        {
            mv.addObject("dfRoles", DFHelper.getRolesForModel(dfModelId));
        }
        if (modelEntity.getDFModel().isSimplifiedQuestionsAndQualifiers())
        {
            if (question.getId() != null)
            {
                mv.addObject("dfQualifier",
                        DFHelper.getQualifierForSimplifiedQuestion(question));
            }
            mv.addObject("dfQualifierEntityType", modelEntity.getClass().getName());
            mv.addObject("dfQualifierEntityId", modelEntity.getId());
        }

        return mv;
    }

    public ModelAndView ajaxAddCategory(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        DFModelEntity modelEntity = DFHelper.getModelEntity(request);

        if (modelEntity != null)
        {
            DFCategory newCategory = new DFCategory();
            newCategory.setModel(modelEntity.getDFModel());
            newCategory.setAdminOnly(false);
            newCategory.setPosition(
                    DFHelper.getNextCategoryPosition(modelEntity.getDFModel()));
            ht.save(newCategory);

            newCategory.setName(new I18nLabel("i18n.df_ajaxAddCategory.newCategory",
                    Arrays.asList(newCategory.getId())));
            ht.update(newCategory);
        }

        return NHelper.AJAXResponse("successful");
    }

    public ModelAndView ajaxLoadCategoryNames(HttpServletRequest request,
            HttpServletResponse response)
    {
        JSONArray jsonArray = new JSONArray();
        DFCategory category = DFHelper.getCategory(request);

        if (category != null)
        {
            try
            {
                jsonArray.put(0, category.getName());
                jsonArray.put(1, category.getNameL2());
                jsonArray.put(2, category.isHideCategoryTitle());
            }
            catch (JSONException e)
            {
                e.printStackTrace();
            }
        }

        return NHelper.AJAXResponse(jsonArray.toString());
    }

    /**
     * EDIT CATEGORY
     */
    public ModelAndView ajaxSaveCategory(HttpServletRequest request,
            HttpServletResponse response)
    {
        DFCategory category = DFHelper.getCategory(request);
        PortalUtils.getLocale(request);
        String categoryName = request.getParameter("categoryName");
        String categoryNameL2 = request.getParameter("categoryNameL2");
        Boolean hideCategoryTitle = Boolean
                .valueOf(request.getParameter("hideCategoryTitle"));
        if (category != null)
        {
            category.setName(categoryName);
            category.setNameL2(categoryNameL2);
            category.setHideCategoryTitle(hideCategoryTitle);
            ht.update(category);
        }
        else
        {
            try
            {
                DFModelEntity modelEntity = DFHelper.getModelEntity(request);

                category = new DFCategory();
                category.setName(categoryName);
                category.setNameL2(categoryNameL2);
                category.setHideCategoryTitle(hideCategoryTitle);
                category.setModel(modelEntity.getDFModel());
                ht.save(category);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }

        return NHelper.AJAXResponse("successful");
    }

    /**
     * DELETE CATEGORY
     */
    public ModelAndView ajaxDeleteCategory(HttpServletRequest request,
            HttpServletResponse response)
    {
        DFCategory category = DFHelper.getCategory(request);
        DFHelper.populateCategory(category);
        DFModelEntity modelEntity = DFHelper.getModelEntity(request);
        String retStatus = DFHelper.deleteCategory(category, modelEntity)
                ? "successful"
                : "error";

        return NHelper.AJAXResponse(retStatus);
    }

    /**
     * DELETE QUESTION
     *
     */
    public ModelAndView ajaxDeleteQuestion(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        DFQuestion question = DFHelper.getQuestion(request);
        DFModelEntity modelEntity = DFHelper.getModelEntity(request);
        Class answerEntityClass = questionInterface
                .getAnswerEntityClass(modelEntity);

        String retStatus = DFHelper.deleteQuestion(question, answerEntityClass)
                ? "successful"
                : "error";

        Integer questionsCount = PortalUtils.getHt().findInt(
                "select count(q) from DFQuestion q where q.category.model.id=?",
                question.getCategory().getModel().getId());

        JSONObject ret = JSONUtils.builder().put("status", retStatus)
                .put("questionsCount", questionsCount).build();

        questionInterface.afterDeleteQuestion(question, modelEntity, request);

        return NHelper.AJAXResponse(ret.toString());
    }

    /**
     * SAVE QUESTION
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    public ModelAndView saveQuestion(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        DFModelEntity modelEntity = DFHelper.getModelEntity(request);
        DFQuestion question = DFHelper.getQuestion(request);
        DFCategory category = DFHelper.getCategory(request);

        ModelAndView mv = null;

        boolean error = false;
        int line = 0;
        String choices = request.getParameter("choices");
        if (!StringUtils.isEmpty(choices))
        {
            String[] choicesLines = choices.split("\n");
            for (int i = 0; i < choicesLines.length; i++)
            {
                if (choicesLines[i].length() >= 255)
                {
                    error = true;
                    line = i + 1;
                }
            }
        }

        if (!error)
        {
            if (question != null)
            {
                List<DFQuestion> otherQuestions = DFHelper
                        .getModelQuestions(modelEntity.getDFModel(), question);
                try
                {
                    DFHelper.saveQuestion(question, modelEntity, otherQuestions,
                            request);
                    questionInterface.afterSaveQuestion(question, modelEntity,
                            request);
                }
                catch (DFException e)
                {
                    mv = displayQuestionEdit(request, response);
                    mv.addObject("i18nErrors", e.getErrors());
                }
            }
            else if (category != null) // add
            {
                List<DFQuestion> otherQuestions = DFHelper
                        .getModelQuestions(modelEntity.getDFModel(), null);

                question = DFHelper.createQuestion(otherQuestions, true,
                        modelEntity);
                question.setCategory(category);
                try
                {
                    DFHelper.saveQuestion(question, modelEntity, otherQuestions,
                            request);
                    questionInterface.afterSaveQuestion(question, modelEntity,
                            request);
                }
                catch (DFException e)
                {
                    mv = displayQuestionEdit(request, response);
                    mv.addObject("i18nErrors", e.getErrors());
                }
            }
            if (modelEntity.getDFModel().isSimplifiedQuestionsAndQualifiers()
                    && (modelEntity instanceof DFQualifierEntity))
            {
                DFQualifierEntity qualifierEntity = DFHelper
                        .getQualifierTypeEntity(request);
                DFQuestionQualifier qualifier = DFHelper
                        .getQuestionQualifier(request);
                if (null == qualifier)
                {
                    qualifier = DFHelper.createNewQuestionQualifier(question,
                            qualifierEntity);
                }
                DFHelper.saveQuestionQualifier(qualifier, request);
            }
        }
        else
        {
            mv = displayQuestionEdit(request, response);
            mv.addObject("errorMessage",
                    PortalUtils.getMessageSource().getMessage(
                            "i18n.df_question_editor.OneorMoreChoice952394949",
                            null, PortalUtils.getLocale(request)) + " " + line);
            mv.addObject("choices", choices);
        }

        if (mv == null)
        {
            mv = questionInterface.displayDFQuestionEditor(request, response,
                    modelEntity);

            FlashMessageUtils.success(request,
                    new I18nLabel("i18n.df_question_editor.QuestionSaved")
                            .getTranslation(PortalUtils.getLocale(request)));
        }

        return mv;
    }

    public ModelAndView ajaxGetDFQuestionsWithSelectedOrbisValue(
            HttpServletRequest request, HttpServletResponse response)
            throws JSONException
    {
        Integer questionId = ServletRequestUtils.getIntParameter(request,
                "dfQuestionId", 0);
        Integer categoryId = ServletRequestUtils.getIntParameter(request,
                "dfCategoryId", 0);
        String orbisValue = ServletRequestUtils.getStringParameter(request,
                "selectedOrbisValue", "");

        List<DFQuestion> questionsWithSelectedMapping = PortalUtils.getHt()
                .find("select dfqsm.dfQuestion from DFQuestionSiteMapping dfqsm "
                        + "where dfqsm.dfQuestion.id<>? and dfqsm.dfQuestion.category.id=? "
                        + "and dfqsm.siteMapping.orbisKey=?",
                        new Object[] { questionId, categoryId, orbisValue });

        JSONObject ret = JSONUtils.builder()
                .put("questionsWithSelectedMapping", questionsWithSelectedMapping)
                .build();
        return NHelper.AJAXResponse(ret.toString());
    }

    public ModelAndView reloadForm(HttpServletRequest request,
            HttpServletResponse response)
    {
        boolean showDFForm2 = ServletRequestUtils.getBooleanParameter(request,
                "showDFForm2", false);
        ModelAndView mv = getShortCircuitView(
                showDFForm2 ? "df/df_form2_a" : "df/df_form1_a");
        if (ServletRequestUtils.getBooleanParameter(request, "forceSpiralRobot",
                false))
        {
            PortalUtils.forceSpiralRobot(request);
        }
        DFModel model = ht.load(DFModel.class,
                Integer.valueOf(request.getParameter("modelId")));
        DFModelEntity modelEntity = null;
        List<DFModelEntity> mes = ht
                .find("from " + model.getModelEntityClassName().getSimpleName()
                + " me where me.DFModel.id=?", model.getId());
        if (mes.size() == 1)
        {
            modelEntity = mes.get(0);
        }
        DFAnswerEntity answer = (DFAnswerEntity) StringUtils
                .unserializeObject(request.getParameter("answerEntity"));
        if ((modelEntity != null) && (answer == null))
        {
            try
            {
                answer = modelEntity.getAnswerEntityClass().getDeclaredConstructor()
                        .newInstance();
                answer.setDfModel(model);
            }
            catch (Exception e)
            {
            }
        }
        questionInterface.reloadFormPreBindAnswers(request, answer);
        Map<Integer, Boolean[]> forceLowerPermissionsMap = questionInterface
                .getForceLowerPermissionsMap(request, answer);
        DFHelper.bindAnswers(modelEntity, answer, request,
                PortalUtils.getUserLoggedIn(request), false,
                forceLowerPermissionsMap,
                StringUtils.isEmpty(request.getParameter("resetForEmptyString")));

        DFHelper.populateModel(false, model, true, answer, null,
                PortalUtils.getUserLoggedIn(request), forceLowerPermissionsMap);
        mv.addObject("DFModel", model);
        mv.addObject("DFAnswerrEntity", answer);
        mv.addObject("showDFForm2", showDFForm2);
        mv.addObject("dfRenderInBox", ServletRequestUtils
                .getBooleanParameter(request, "dfRenderInBox", false));
        mv.addObject("dfShowCategories", ServletRequestUtils
                .getBooleanParameter(request, "dfShowCategories", false));
        mv.addObject("formHorizontal", ServletRequestUtils
                .getBooleanParameter(request, "formHorizontal", false));

        FormHelper.removeHiddenDFQuestions(request, model,
                request.getParameter("formValidationToken"));
        mv.addObject(FormHelper.FORM_VALIDATION_TOKEN_KEY,
                request.getParameter("formValidationToken"));
        mv.addObject("showQuestionCheckboxes", ServletRequestUtils
                .getBooleanParameter(request, "showQuestionCheckboxes", false));
        return mv;
    }

    /**
     * SAVE CATEGORY MODEL
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    public ModelAndView ajaxSaveCategoryModel(HttpServletRequest request,
            HttpServletResponse response) throws Exception
    {
        try
        {
            // dumpRequestParameters(request);

            DFModelEntity modelEntity = DFHelper.getModelEntity(request);
            String changedModel = request.getParameter("model");

            syncModel(modelEntity.getDFModel(), request, changedModel);

            saveCategoryModel(modelEntity.getDFModel(), request);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return NHelper.AJAXResponse("successful");

    }

    public ModelAndView displayFormEdit(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = new ModelAndView("df/df_formEdit");
        DFHelper.populateDFOffPageCommon(request, mv,
                DFHelper.getModelEntity(request));

        return mv;
    }

    public ModelAndView saveForm(HttpServletRequest request,
            HttpServletResponse response)
    {
        DFModel model = NHelper.getEntity(DFModel.class, "modelId", request);

        model.setName(request.getParameter("name"));
        ht.update(model);

        ModelAndView mv = displayFormEdit(request, response);
        mv.addObject("successMessage",
                new I18nLabel("i18n.DFSubController.Formsaveds3784356643949495")
                        .getTranslation(PortalUtils.getLocale(request)));
        return mv;
    }

    public ModelAndView displayModelMatcher(HttpServletRequest request,
            HttpServletResponse response)
    {
        ModelAndView mv = this.questionInterface.displayModelMatcher(request);

        DFModelEntity modelEntity = DFHelper.getModelEntity(request);
        DFHelper.populateDFOffPageCommon(request, mv, modelEntity);

        mv.addObject("matcher", DFHelper.getModelMatcher(modelEntity.getDFModel()));

        return mv;
    }

    public ModelAndView ajaxLoadInitModelMatcherData(HttpServletRequest request,
            HttpServletResponse response)
    {
        JSONObject data = new JSONObject();

        try
        {
            DFModel model = NHelper.getEntity(DFModel.class, "modelId", request);

            List<Object[]> secondaryModels = ht.find(
                    "select m.id, m.name from DFModel m where m.id <> ? and m.name is not null",
                    model.getId());

            JSONArray collect = secondaryModels.stream().map(
                    m -> new JSONObject(ImmutableMap.of("id", m[0], "name", m[1])))
                    .collect(JSONUtils.toJSONArray());

            data.put("secondaryModels", collect);

            data.put("primaryQuestions",
                    DFHelper.getModelMatcherPrimaryQuestions(model));
            data.put("secondaryQuestions",
                    DFHelper.getModelMatcherSecondaryQuestions(model));
            data.put("matcher",
                    DFHelper.getModelMatcherData(DFHelper.getModelMatcher(model)));
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return NHelper.AJAXResponse(data.toString());
    }

    public ModelAndView ajaxLoadModelMatchSecondaryQuestions(
            HttpServletRequest request, HttpServletResponse response)
    {
        JSONArray data = new JSONArray();

        try
        {
            List<Object[]> questions = ht.find(
                    "select q.id, q.questionText, q.type from DFQuestion q where q.category.model=?",
                    request.getParameter("modelId"));
            data = questions
                    .stream().map(q -> new JSONObject(ImmutableMap.of("id", q[0],
                            "text", q[1], "type", q[2])))
                    .collect(JSONUtils.toJSONArray());
        }
        catch (Exception e)
        {
        }

        return NHelper.AJAXResponse(data.toString());
    }

    public ModelAndView saveModelMatcher(HttpServletRequest request,
            HttpServletResponse response)
    {
        JSONObject ret = new JSONObject();

        try
        {
            JSONObject jsonObject = new JSONObject(request.getParameter("payload"));

            DFModel model = NHelper.getEntity(DFModel.class, "modelId", request);
            DFModelMatcher matcher = DFHelper.getModelMatcher(model);

            if (matcher == null)
            {
                matcher = new DFModelMatcher();
                matcher.setPrimary(model);
                matcher.setSecondary((DFModel) ht.load(DFModel.class,
                        jsonObject.getJSONObject("secondary").getInt("id")));
                ht.save(matcher);
            }

            PortalUtils.getJt().update(
                    "delete from df_match_mapping where modelMatcher=?",
                    new Object[] { matcher.getId() });

            JSONArray jsonArray = jsonObject.getJSONArray("questions");
            for (int i = 0; i < jsonArray.length(); i++)
            {
                JSONObject qm = jsonArray.getJSONObject(i);

                DFMatchMapping questionMatcher = new DFMatchMapping();
                questionMatcher.setModelMatcher(matcher);
                questionMatcher.setPrimary((DFQuestion) ht.load(DFQuestion.class,
                        qm.getJSONObject("q1").getInt("id")));
                questionMatcher.setSecondary((DFQuestion) ht.load(DFQuestion.class,
                        qm.getJSONObject("q2").getInt("id")));
                questionMatcher.setOpType(qm.getInt("op"));
                ht.save(questionMatcher);
            }

            ret.put("success", true);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return NHelper.AJAXResponse(ret.toString());
    }

    public void saveCategoryModel(DFModel model, HttpServletRequest request)
    {
        ht.saveOrUpdate(model);
    }

    private void syncModel(DFModel currentModel, HttpServletRequest request,
            String changedModel)
    {
        try
        {
            JSONArray jsonChangedModel = new JSONArray(changedModel);
            addCategories(jsonChangedModel, currentModel, request);
            moveQuestions(jsonChangedModel, currentModel);
            // deleteRemovedCategory(jsonChangedModel, currentModel);
            calculatePositions(jsonChangedModel, currentModel);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }
    }

    private void calculatePositions(JSONArray jsonChangedModel,
            DFModel currentModel) throws JSONException
    {

        List<DFCategory> categories = ht.find("from DFCategory c where c.model=?",
                currentModel);
        List<DFQuestion> questions = ht
                .find("from DFQuestion q where q.category.model=?", currentModel);
        for (DFCategory category : categories)
        {
            for (int i = 0; i < jsonChangedModel.length(); i++)
            {
                if (!jsonChangedModel.getJSONObject(i).isNull("id")
                        && jsonChangedModel.getJSONObject(i)
                                .getInt("id") == category.getId())
                {
                    category.setPosition(i);
                    ht.update(category);
                }
            }
        }
        for (DFQuestion question : questions)
        {
            for (int i = 0; i < jsonChangedModel.length(); i++)
            {
                for (int j = 0; j < jsonChangedModel.getJSONObject(i)
                        .getJSONArray("questionIds").length(); j++)
                {
                    if (!jsonChangedModel.getJSONObject(i).isNull("questionIds")
                            && jsonChangedModel.getJSONObject(i)
                                    .getJSONArray("questionIds")
                                    .getInt(j) == question.getId())
                    {
                        question.setPosition(j);
                        ht.update(question);
                    }
                }
            }
        }

    }

    private void moveQuestions(JSONArray jsonChangedModel, DFModel currentModel)
            throws JSONException
    {
        List<DFQuestion> questions = ht
                .find("from DFQuestion q where q.category.model=?", currentModel);

        for (DFQuestion question : questions)
        {
            for (int i = 0; i < jsonChangedModel.length(); i++)
            {
                if (!jsonChangedModel.getJSONObject(i).isNull("id"))
                {
                    for (int j = 0; j < jsonChangedModel.getJSONObject(i)
                            .getJSONArray("questionIds").length(); j++)
                    {
                        if (question.getCategory() != null)
                        {
                            if (question.getId() == jsonChangedModel
                                    .getJSONObject(i).getJSONArray("questionIds")
                                    .getInt(j)
                                    && question.getCategory()
                                            .getId() != jsonChangedModel
                                                    .getJSONObject(i).getInt("id"))
                            {
                                DFCategory category = DFHelper
                                        .getCategory(jsonChangedModel
                                                .getJSONObject(i).getInt("id"));
                                if (category != null)
                                {
                                    question.setCategory(category);
                                    ht.update(question);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private void addCategories(JSONArray jsonChangedModel, DFModel currentModel,
            HttpServletRequest request) throws JSONException
    {
        List<DFCategory> categories = ht.find("from DFCategory c where c.model=?",
                currentModel);
        for (int i = 0; i < jsonChangedModel.length(); i++)
        {
            if (jsonChangedModel.getJSONObject(i).isNull("id"))
            {
                DFCategory newCategory = new DFCategory();
                newCategory.setModel(currentModel);
                newCategory.setName(
                        jsonChangedModel.getJSONObject(i).get("name").toString());
                /*
                 * newCategory.setNameL2(jsonChangedModel.getJSONObject(i).get(
                 * "name") .toString());
                 */
                newCategory.setPosition(jsonChangedModel.length() - 1);
                newCategory.setAdminOnly(false);
                ht.save(newCategory);
            }
            else
            {
                for (DFCategory category : categories)
                {
                    if (jsonChangedModel.getJSONObject(i).getInt("id") == category
                            .getId()
                            && !jsonChangedModel.getJSONObject(i).getString("name")
                                    .equalsIgnoreCase(category.getName()))
                    {
                        if (LocaleUtils.isL1(request))
                        {
                            category.setName(jsonChangedModel.getJSONObject(i)
                                    .getString("name"));
                        }
                        else
                        {
                            category.setNameL2(jsonChangedModel.getJSONObject(i)
                                    .getString("name"));
                        }

                        ht.update(category);
                    }
                }
            }
        }
    }

    /**
     * A "short circuit view" will return the HTML of the specified JSP without
     * wrapping it in the site's HTML template.
     */
    private ModelAndView getShortCircuitView(String view)
    {
        ModelAndView mv = new ModelAndView(view);
        mv.addObject(SiteController.SHORT_CIRCUIT_ATTRIBUTE_NAME, Boolean.TRUE);
        return mv;
    }

    private ModelAndView displayError(String errorMsg)
    {
        ModelAndView mv = new ModelAndView("search/search_error");
        mv.addObject("errorMsg", errorMsg);
        return mv;
    }
}