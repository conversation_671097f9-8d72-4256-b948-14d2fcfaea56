package com.orbis.jqgrid;

import static java.util.Collections.emptyList;
import static java.util.stream.Collectors.toList;
import static java.util.stream.Collectors.toMap;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.context.MessageSource;
import org.springframework.web.servlet.ModelAndView;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.orbis.acegi.providers.dao.hibernate.PersonGroup;
import com.orbis.acegi.providers.dao.hibernate.PersonGroupHelper;
import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.CommandQueryTemplate;
import com.orbis.portal.HibernateUtils;
import com.orbis.portal.OrbisJdbcTemplate;
import com.orbis.portal.PortalDeletionNode;
import com.orbis.portal.PortalUtils;
import com.orbis.search.SearchHelper;
import com.orbis.search.SearchModel;
import com.orbis.search.entity.EmailingEmailer;
import com.orbis.search.entity.Entity;
import com.orbis.search.entity.Relationship;
import com.orbis.utils.ArrayUtils;
import com.orbis.utils.ClassUtils;
import com.orbis.utils.CollectionUtils;
import com.orbis.utils.DBUtils;
import com.orbis.utils.DBUtils.LeftJoinInformation;
import com.orbis.utils.FileUtils;
import com.orbis.utils.JSONUtils;
import com.orbis.utils.PaginationUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.content.ContentItem;
import com.orbis.web.content.np.NHelper;
import com.orbis.web.content.user.DynamicUserFilter;
import com.orbis.web.site.SiteElement;

import jakarta.persistence.Query;
import jakarta.servlet.http.HttpServletRequest;


/**
 * This is a helper-class that supports the grid-search framework.
 *
 * <AUTHOR>
 */
public final class JQGridHelper
{
    private static final Pattern STATIC_FROM_PATTERN = Pattern
            .compile(",\\s*([^\\s,]+)\\s+(?:(?:as)\\s+)?([^\\s,]+)");

    public static final String JQGRID_MODEL_KEY = "JQGRID_MODEL_KEY";

    private static final Log logger = LogFactory.getLog(JQGridHelper.class);

    /**
     * A cache for JQGridDataSausage's. key = "data.sausage.class.name"
     */
    private static ConcurrentMap<String, JQGridDataSausage> SAUSAGE_CACHE = new ConcurrentHashMap<>();

    private JQGridHelper()
    {
    }

    /**
     * This helper-method supports the "generate email campaign" use case.
     */
    static DynamicUserFilter saveEmailCampaignFilter(SearchModel searchModel,
            String filterName, SiteElement emailSiteElement, int emailerIndex,
            JSONArray selectedIds, String sortIndex, String sortOrder,
            boolean includeAdditionalUsers)
    {
        DynamicUserFilter uf = null;

        if (searchModel == null || !searchModel.canEmail())
        {
            throw new RuntimeException(
                    "SearchModel is not properly configured for email campaigning.");
        }

        uf = new DynamicUserFilter();
        uf.setParentSiteElementId(emailSiteElement.getId());
        uf.setName(filterName + " " + new Date().toString());
        uf.setReady(true);
        uf.setHql(SearchHelper.getEmailCampaignHql(searchModel.getMasterEntity(),
                emailerIndex, selectedIds, sortIndex, sortOrder));
        EmailingEmailer ee = searchModel.getMasterEntity().getEmailingModel()
                .getEmailers().get(emailerIndex);
        if (includeAdditionalUsers)
        {
            String inClause;
            if (selectedIds != null)
            {
                inClause = DBUtils.buildInClause(selectedIds);
            }
            else
            {
                String ufQuery = uf.getHql();
                String idQuery = "select p.id "
                        + ufQuery.substring(ufQuery.indexOf(" from "));
                List<Integer> jobIds = PortalUtils.getHt().find(idQuery);
                inClause = DBUtils.buildInClause(jobIds);
            }
            uf.setAdditionalHql(ee.getAdditionalHql().replace("?", inClause));
        }
        PortalUtils.getHt().save(uf);

        return uf;
    }

    /**
     * Use this method for saving an instance of JQGridSearch to the database along
     * with the additional necessary properties.
     *
     * @param gridModel
     *            - a JQGridModel instance
     * @param moduleClassName
     *            - a fully qualified class-name of the Orbis module that owns the
     *            search.
     * @param moduleId
     *            - the ID of the Orbis module that owns the search
     * @param createdBy
     *            - the user who created this search
     * @param searchName
     *            - the "save name" of the search
     * @param searchDescr
     *            - the "description" of the search
     * @param isDataPersona
     *            whether the search is a data persona to load stats for in the
     *            insights module
     */
    static JQGridSearch saveSearch(JQGridModel gridModel, String moduleClassName,
            String moduleId, UserDetailsImpl createdBy, String searchName,
            String searchDescr, boolean save, boolean isDataPersona)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        Date now = new Date();

        // SAVE MASTER SEARCH...
        JQGridSearch masterSearch = gridModel.getMaster();
        masterSearch.setMasterSearchId(null);
        masterSearch.setModuleClassName(moduleClassName);
        masterSearch.setDataPersona(isDataPersona);
        masterSearch.setModuleId(moduleId);
        masterSearch.setSearchName(searchName);
        masterSearch.setSearchDescr(searchDescr);

        if (masterSearch.getId() == null)
        {
            masterSearch.setCreatedBy(createdBy);
            masterSearch.setCreateDate(now);
            masterSearch.setUpdateDate(now);
        }
        else
        {
            masterSearch.setUpdateDate(now);
        }

        if (save)
        {
            ht.saveOrUpdate(masterSearch);
        }
        Map<String, JQGridSearch> detailSearches = new LinkedHashMap<>();

        // SAVE DETAIL SEARCHES...
        for (String detailKey : gridModel.getDetails().keySet())
        {
            JQGridSearch detailSearch = gridModel.getDetails().get(detailKey);
            // set the reference to the master
            detailSearch.setMasterSearchId(masterSearch.getId());

            detailSearch.setModuleClassName(moduleClassName);
            detailSearch.setModuleId(moduleId);
            detailSearch.setSearchName(searchName);
            detailSearch.setSearchDescr(searchDescr);

            if (detailSearch.getId() == null)
            {
                detailSearch.setCreatedBy(createdBy);
                detailSearch.setCreateDate(now);
                detailSearch.setUpdateDate(now);
            }
            else
            {
                detailSearch.setUpdateDate(now);
            }

            if (save)
            {
                ht.saveOrUpdate(detailSearch);
            }

            detailSearches.put(detailKey, detailSearch);

        }

        masterSearch.setDetailSearches(detailSearches);
        return masterSearch;
    }

    public static Set<Integer> getIdsFromJtQuery(
            List<? extends Map<String, Integer>> resultSet)
    {
        Set<Integer> rtn = new HashSet<>();

        for (Map<String, Integer> hashmap : resultSet)
        {
            for (Entry<String, Integer> entry : hashmap.entrySet())
            {
                rtn.add(entry.getValue());
            }
        }

        return rtn;
    }

    public static void deleteSearchById(Integer searchId)
    {
        deleteSearches("s.id = " + searchId);
    }

    public static void deleteSearches(String whereClauseFragment)
    {
        if (StringUtils.isEmpty(whereClauseFragment))
        {
            return;
        }

        OrbisJdbcTemplate jt = PortalUtils.getJt();

        String whereClauseFragmentTrimmed = whereClauseFragment.trim();

        String countSearchModel = "select count(*) from search s where "
                + whereClauseFragmentTrimmed;

        if (jt.queryForInt(countSearchModel) > 0)
        {
            PortalUtils.getTransactionTemplate().execute(status -> {
                try
                {
                    jt.execute(
                            "update ss set ss.searchModel = s.searchModel from search ss join search s on ("
                                    + whereClauseFragmentTrimmed
                                    + ") and s.id = ss.masterSearchId");

                    // ClauseSearchIdToSearchEntityEmailingModel
                    StringBuilder idsEM = new StringBuilder();
                    idsEM.append(" select seem.id from search s ");
                    idsEM.append(
                            " inner join search_model sm on s.searchModel = sm.id ");
                    idsEM.append(
                            " inner join search_entity se on sm.masterEntity = se.id ");
                    idsEM.append(
                            " inner join search_entity_emailing_model seem on se.emailingModel = seem.id ");
                    idsEM.append(" where ").append(whereClauseFragmentTrimmed);
                    List idsForEmailModel = jt.queryForList(idsEM.toString());

                    StringBuilder idsSCM = new StringBuilder();
                    idsSCM.append(" select scm.id ");
                    idsSCM.append(" from search s ");
                    idsSCM.append(
                            " inner join search_model sm on s.searchModel = sm.id ");
                    idsSCM.append(
                            " inner join search_entity se on sm.masterEntity = se.id ");
                    idsSCM.append(
                            " inner join search_entity_relationship r on se.id = r.masterEntity ");
                    idsSCM.append(
                            " inner join search_entity se2 on r.relatedEntity = se2.id ");
                    idsSCM.append(
                            " inner join search_criteria_model scm on se2.criteriaModel = scm.id ");
                    idsSCM.append(" where ").append(whereClauseFragmentTrimmed);
                    List idsForRelatedCriteriaModel = jt
                            .queryForList(idsSCM.toString());

                    StringBuilder idsSCM2 = new StringBuilder();
                    idsSCM2.append(" select scm.id ");
                    idsSCM2.append(" from search s ");
                    idsSCM2.append(
                            " inner join search_model sm on s.searchModel = sm.id ");
                    idsSCM2.append(
                            " inner join search_entity se on sm.masterEntity = se.id ");
                    idsSCM2.append(
                            " inner join search_criteria_model scm on se.criteriaModel = scm.id ");
                    idsSCM2.append(" where ").append(whereClauseFragmentTrimmed);
                    List idsForMasterCriteriaModel = jt
                            .queryForList(idsSCM2.toString());

                    Set<Integer> criteriaModelIdsForAnswers = getIdsFromJtQuery(
                            idsForMasterCriteriaModel);
                    criteriaModelIdsForAnswers
                            .addAll(getIdsFromJtQuery(idsForRelatedCriteriaModel));
                    StringBuilder idsSCA = new StringBuilder();
                    idsSCA.append(" select  sca.id ");
                    idsSCA.append(" from search_criteria_question scq ");
                    idsSCA.append(
                            " inner join search_criteria_answer sca on scq.answer = sca.id ");
                    idsSCA.append(
                            " inner join search_criteria_group scg on scq.criteriaGroup = scg.id ");
                    idsSCA.append(
                            " inner join search_criteria_model scm on scg.model = scm.id ");
                    idsSCA.append(" where scm.id in ").append(
                            DBUtils.buildInClause(criteriaModelIdsForAnswers));
                    List idsForCriteriaAnswer = jt.queryForList(idsSCA.toString());

                    runDelete(idsForEmailModel, "search_entity_emailing_model");
                    runDelete(idsForRelatedCriteriaModel, "search_criteria_model");
                    runDelete(idsForMasterCriteriaModel, "search_criteria_model");
                    runDelete(idsForCriteriaAnswer, "search_criteria_answer");
                }
                catch (Exception e)
                {
                    status.setRollbackOnly();
                    e.printStackTrace();
                }

                return null;
            });

        }
    }

    public static void runDelete(List queryForList, String genesisTableName)
    {
        if (queryForList != null && queryForList.size() > 0)
        {
            StringBuilder idsEMInClause = new StringBuilder();
            idsEMInClause.append(" id in  ");
            idsEMInClause
                    .append(DBUtils.buildInClause(getIdsFromJtQuery(queryForList)));

            PortalDeletionNode genesis = PortalDeletionNode
                    .getDeletionList(genesisTableName);
            genesis.deleteParents(idsEMInClause.toString(), genesis.countNodes(0),
                    0);
        }
    }

    public static void deleteExport(String url, Integer jQGridExportEntityId)
    {
        if (!StringUtils.isEmpty(url))
        {
            deleteFilesForJQExport(url);
            PortalUtils.getJt().update("delete from fs_log where export = ?",
                    new Object[] { jQGridExportEntityId });
        }
    }

    public static void deleteExport(JQGridExportEntity export)
    {
        CommandQueryTemplate ht = PortalUtils.getHt();
        if (export != null && ht != null)
        {
            deleteFilesForJQExport(export);

            PortalUtils.getJt().update("delete from fs_log where export = ?",
                    new Object[] { export.getId() });

            // soft-delete the export record.
            ht.delete(export);
        }
    }

    public static void deleteFilesForJQExport(String url)
    {
        if (!StringUtils.isEmpty(url))
        {
            FileUtils.deleteFileAndFolder(PortalUtils.getRealPath(url));
        }
    }

    public static void deleteFilesForJQExport(JQGridExportEntity export)
    {
        if (export.getUrl() != null)
        {
            // hard-delete the export file from the server's hard-disk
            FileUtils.deleteFileAndFolder(PortalUtils.getRealPath(export.getUrl()));
        }
    }

    /**
     * With the specified ID of the "master search", this method will return a
     * JQGridModel instance that includes both master and detail searches...
     *
     * @param masterSearchId
     *            the ID of the "master search"
     * @return JQGridModel
     */
    public static JQGridModel loadGridModel(Integer masterSearchId)
    {
        CommandQueryTemplate ht = PortalUtils.getHtReadOnly();
        JQGridSearch masterSearch = null;
        try
        {
            masterSearch = (JQGridSearch) ht.load(JQGridSearch.class,
                    masterSearchId);
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return getGridModelFromStoredSearch(masterSearch);
    }

    public static JQGridModel getGridModelFromStoredSearch(
            JQGridSearch masterSearch)
    {
        JQGridModel gridModel = null;
        try
        {
            if (masterSearch != null)
            {
                masterSearch.setMaster(true);

                gridModel = new JQGridModel();
                gridModel.setMaster(masterSearch);

                List<JQGridSearch> detailSearches = PortalUtils.getHt().find(
                        "from JQGridSearch s where s.masterSearchId=?",
                        masterSearch.getId());

                for (JQGridSearch detailSearch : detailSearches)
                {
                    detailSearch.setMaster(false);
                    gridModel.addDetail(detailSearch.getEnityKey(), detailSearch);
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        return gridModel;
    }

    /**
     * Seems like when we grab the "colModel" from the JQGrid widget (for saving
     * or exporting), it can include a "row number" column (that is, if that
     * option is turned on) that will mess up the grid-view the next time the
     * colModel is loaded. To solve this problem we can call this method to
     * remove the unwanted row-number column, if it exists.
     *
     * Also, remove any columns that don't have a "label" property.
     */
    static JSONArray cleanColumns(JSONArray colModel)
    {
        JSONArray newColModel = new JSONArray();

        try
        {
            for (int i = 0; i < colModel.length(); i++)
            {
                JSONObject col = colModel.getJSONObject(i);

                if (col.has("name") && !col.getString("name").equals("rn")
                        && !col.getString("name").equals("cb"))
                {
                    try
                    {
                        newColModel.put(col);
                    }
                    catch (Exception e)
                    {
                        // skip column...
                    }
                }
            }
        }
        catch (Exception e)
        {
            // e.printStackTrace();
            return null;
        }

        return newColModel;
    }

    public static SiteElement getSearchModuleSiteElement(String moduleClassName,
            String moduleId, HttpServletRequest request)
    {
        SiteElement siteElement = null;

        try
        {
            if ("com.orbis.web.content.acrm.AcrmControllerCareer"
                    .equals(moduleClassName))
            {
                siteElement = NHelper.getAcrmCareerSiteElement(
                        PortalUtils.getUserLoggedIn(request));
            }
            else
            {
                ContentItem module = (ContentItem) PortalUtils.getHt().load(
                        ClassUtils.loadClass(moduleClassName),
                        Integer.valueOf(moduleId));

                siteElement = module.getSiteElement();
            }
        }
        catch (Exception e)
        {
        }

        return siteElement;
    }

    static List<JQGridExportEntity> getSearchExports(Integer searchId)
    {
        CommandQueryTemplate ht = PortalUtils.getHtReadOnly();
        return ht.find(
                "from JQGridExportEntity e where e.search.id=? and e.deleted=false order by e.exportDate desc",
                searchId);
    }

    public static void main(String[] args) throws JSONException
    {
        JSONArray a = new JSONArray("[0]");
        System.out.println(a);
    }

    static Map<Integer, List<Object[]>> loadDetailEntitiesByMasterIds(
            JQGridSearch jqSearch, Locale locale, boolean buildCache)
    {
        Map ret = new HashMap<Integer, List<Object[]>>();

        if (Relationship.TYPE.COUNT == jqSearch.getDetailType())
        {
            List<Integer> masterIds = jqSearch.getMasterIds();
            if (masterIds != null)
            {
                try
                {
                    JQGridBuilderInterface gridBuilder;
                    if (jqSearch.getGridBuilder() != null)
                    {
                        gridBuilder = jqSearch.getGridBuilder();
                    }
                    else
                    {
                        gridBuilder = (JQGridBuilderInterface) ClassUtils
                                .loadClass(jqSearch.getGridBuilderClassName())
                                .newInstance();
                    }

                    JSONObject results = gridBuilder.getSearchResponse(jqSearch, 0,
                            0, null, null, buildCache);

                    for (Integer id : masterIds)
                    {
                        JSONArray cellArray;
                        List<Object[]> rows = new ArrayList<>();
                        if (results.has(id.toString()))
                        {
                            cellArray = (JSONArray) results.get(id.toString());
                        }
                        else if (results.has("idNotFound"))
                        {
                            cellArray = (JSONArray) results.get("idNotFound");
                        }
                        else
                        {
                            cellArray = new JSONArray("[0]");
                        }
                        List<Object> valueList = new ArrayList<>();
                        for (String value : JSONUtils.toStringArray(cellArray))
                        {
                            valueList.add(value);
                        }

                        rows.add(valueList.toArray());

                        ret.put(id, rows);
                    }
                }

                catch (Exception e)
                {
                    e.printStackTrace();
                }
            }
        }
        else
        {
            // Now load and return the entities...
            ret = loadDetailEntitiesByMasterIds(jqSearch,
                    JQGridHelper.getDataSausage(jqSearch), locale);
        }

        return ret;
    }

    static int getRowCount(JQGridSearch jqSearch)
    {
        CommandQueryTemplate ht = PortalUtils.getHtReadOnly();
        int rowCount = 0;

        String hql = jqSearch.getCountIdsHql(false);

        if (!jqSearch.isMaster())
        {
            List<Integer> masterIds = jqSearch.getMasterIds();
            if (masterIds != null)
            {
                if (hql.toUpperCase().lastIndexOf("WHERE") < 0)
                {
                    hql += " where ";
                }
                else
                {
                    hql += " and ";
                }

                final Relationship relationship = jqSearch.getRelationship();
                final Entity masterEntity = jqSearch.getMasterEntity();

                if (relationship == null || relationship.isDirect(masterEntity))
                {

                    hql += " ( " + jqSearch.getMasterHqlFkName();
                    if (masterIds.size() == 1)
                    {
                        hql += "=" + masterIds.get(0);
                    }
                    else
                    {
                        hql += " in " + DBUtils.buildInClause(masterIds);
                    }
                    hql += " ) ";
                }
                else
                {
                    if (!relationship.getMasterJoinHql()
                            .startsWith(masterEntity.getEntityHqlAlias()))
                    {
                        int whereIndex = hql.toUpperCase().indexOf(" WHERE");
                        hql = String.format("%s%s%s%s and",
                                hql.substring(0, whereIndex),
                                !StringUtils.isEmpty(masterEntity
                                        .getStaticFromHql()) ? masterEntity
                                                .getStaticFromHql() : "",
                                hql.substring(whereIndex),
                                !StringUtils.isEmpty(masterEntity
                                        .getStaticWhereHql()) ? SearchHelper
                                                .removeWhereClauseLeadingAndOr(
                                                        masterEntity
                                                                .getStaticWhereHql()
                                                                .trim())
                                                : "");
                    }
                    hql = String.format("%s (%s=%s and %s.%s", hql,
                            jqSearch.getMasterHqlFkName(),
                            relationship.getMasterJoinHql(),
                            masterEntity.getEntityHqlAlias(),
                            masterEntity.getEntityHqlId());
                    if (masterIds.size() == 1)
                    {
                        hql += "=" + masterIds.get(0);
                    }
                    else
                    {
                        hql += " in " + DBUtils.buildInClause(masterIds);
                    }
                    hql += ") ";
                }

            }
            else
            {
                return rowCount;
            }
        }

        logger.debug("getRowCount(" + (jqSearch.isMaster() ? "master " : "detail ")
                + rowCount + "): " + hql);

        rowCount = ((Integer) ht.find(hql).get(0)).intValue();

        return rowCount;
    }

    /**
     * Returns a Map of master entity ids to lists of detail entity row data
     *
     * @param jqSearch
     *            - the JQGridSearch instance
     * @param sausage
     *            - a JQGridDataSausage instance used for "massaging" the data
     *            entities (optional - may be null)
     * @param locale
     *            TODO
     * @return a List of fully populated entities
     */
    static Map loadDetailEntitiesByMasterIds(JQGridSearch jqSearch,
            JQGridDataSausage sausage, Locale locale)
    {
        final CommandQueryTemplate ht = PortalUtils.getHtReadOnly();

        boolean orderByVirtualColumn = isOrderByVirtualColumn(jqSearch);
        final String hql = getEntityHql(jqSearch, null, false, true,
                !orderByVirtualColumn);
        logger.debug("loadDetailEntitiesByMasterIds: " + hql);
        List<Object[]> rows = ht.executeFind(session -> {
            Query query = ht.createQuery(session, hql);
            query.setMaxResults(65000);
            return query.getResultList();
        });

        Map dataByMasterId = CollectionUtils.mapify(rows);
        if (sausage != null)
        {
            setVirtualColumnsToNull(jqSearch, rows);
            // Execute any required "data massaging" to the "page" of entities
            // before returning them to the requestor...
            List<Object[]> rows2 = (List<Object[]>) dataByMasterId.values().stream()
                    .collect(Collectors.toList());
            for (Object[] row : rows)
            {
                rows2.add(org.apache.commons.lang.ArrayUtils.remove(row, 0));
            }
            sausage.massageDataRows(dataByMasterId, rows2, jqSearch, locale);
            if (orderByVirtualColumn)
            {
                int orderByIndex = jqSearch
                        .getDataArrayIndex(jqSearch.getSortIndex());
                for (Object entry : dataByMasterId.entrySet())
                {
                    List<Object[]> gridData = ((Map.Entry<Integer, List<Object[]>>) entry)
                            .getValue();
                    ArrayUtils.sortRows(gridData, orderByIndex,
                            "desc".equals(jqSearch.getSortOrder()));
                }
            }
        }
        return dataByMasterId;
    }

    /**
     * Returns a List of database entities based on the HQL in this search
     * instance, page-start index, and page size limit.
     *
     * @param jqSearch
     *            - the JQGridSearch instance
     * @param startIdx
     *            - page start index
     * @param pageSize
     *            - page size
     * @param sausage
     *            - a JQGridDataSausage instance used for "massaging" the data
     *            entities (optional - may be null)
     * @param locale
     *            TODO
     * @return a List of fully populated entities
     */
    static List<Object[]> loadEntities(JQGridSearch jqSearch, final int startIdx,
            final int pageSize, JQGridDataSausage sausage, Locale locale)
    {
        final CommandQueryTemplate ht = PortalUtils.getHtReadOnly();
        boolean orderByVirtualColumn = isOrderByVirtualColumn(jqSearch);
        String hql = getEntityHql(jqSearch, null, false, false,
                !orderByVirtualColumn);
        List<Object[]> rows;
        if (orderByVirtualColumn)
        {
            logger.debug("loadEntities("
                    + (jqSearch.isMaster() ? "master" : "detail") + "): " + hql);
            rows = ht.find(hql);
            mergeAndSortVirtualColumns(jqSearch, sausage, locale, rows);
            rows = PaginationUtils.paginateListByStartIdx(rows, startIdx, pageSize);
        }
        else
        {
            logger.debug("loadEntities("
                    + (jqSearch.isMaster() ? "master" : "detail") + ") (startIdx "
                    + startIdx + ") (pageSize " + pageSize + "): " + hql);
            rows = ht.executeFind(session -> {
                Query query = ht.createQuery(session, hql);
                query.setFirstResult(startIdx);
                query.setMaxResults(pageSize);
                return query.getResultList();
            });
            if (sausage != null)
            {
                setVirtualColumnsToNull(jqSearch, rows);
                // Execute any required "data massaging" to the "page" of entities
                // before returning them to the requestor...
                sausage.massageDataRows(rows, jqSearch, locale);
            }
        }
        return rows;
    }

    /**
     * Returns an HQL query that could be executed to produce a List of database
     * entities
     *
     * @param jqSearch
     *            - the JQGridSearch instance
     * @param selectIds
     *            - whether the select list should only include the master id
     * @param batchJobForDetail
     *            TODO
     * @param includeOrderBy
     *            Whether to include the order by clause in the
     * @param idsInClause
     *            - additional clause for exporting
     * @return an HQL query
     */
    private static String getEntityHql(JQGridSearch jqSearch, List<Integer> ids,
            boolean selectIds, boolean batchJobForDetail, boolean includeOrderBy)
    {
        final Class<?> entityClass = jqSearch.getEntityClass();

        final String rootEntityAlias = jqSearch.getHqlEntityAlias();
        final Map<JQGridColumn, Optional<LeftJoinInformation>> columnJoinInfo = jqSearch
                .getJqGridColumns().stream().collect(
                        // toMap OK -- returns Optional
                        toMap(Function.identity(),
                                col -> DBUtils.getLeftJoinInformation(entityClass,
                                        rootEntityAlias, col.getIndex())));

        final List<JQGridColumn> remainingColumns = columnJoinInfo.entrySet()
                .stream().filter(entry -> !entry.getValue().isPresent())
                .map(Map.Entry::getKey).collect(toList());

        final List<String> uniqueLeftJoins = columnJoinInfo.values().stream()
                .filter(Optional::isPresent).map(Optional::get)
                // If this alias already exists in the query it shouldn't be
                // duplicated
                .filter(joinInfo -> !jqSearch.getHqlForDetail()
                        .contains(" as " + joinInfo.getJoinAlias() + " "))
                .map(LeftJoinInformation::getLeftJoinClause).distinct()
                .collect(toList());

        final Entity masterEntity = jqSearch.getMasterEntity();
        final Relationship relationship = jqSearch.getRelationship();

        final List<Integer> masterIds = jqSearch.getMasterIds();

        String fromAndWhereClause = DBUtils.appendJoinsToHQLQuery(
                jqSearch.getHqlForDetail(), entityClass,
                jqSearch.getHqlEntityAlias(), uniqueLeftJoins);

        if (jqSearch.getEntity() != null
                && !StringUtils.isEmpty(jqSearch.getEntity().getStaticFromHql()))
        {
            final Matcher matcher = STATIC_FROM_PATTERN
                    .matcher(jqSearch.getEntity().getStaticFromHql());

            while (matcher.find())
            {
                final String className = matcher.group(1);
                final Optional<Class<?>> classFound = DBUtils
                        .getEntityClassBySimpleName(
                                PortalUtils.getHt().getSessionFactory(), className);
                if (classFound.isPresent())
                {
                    final String alias = matcher.group(2);
                    final Map<JQGridColumn, Optional<LeftJoinInformation>> joinInfo = remainingColumns
                            .stream().collect(
                                    // toMap OK -- value can't be null
                                    toMap(Function.identity(),
                                            col -> DBUtils.getLeftJoinInformation(
                                                    classFound.get(), alias,
                                                    col.getIndex())));

                    joinInfo.entrySet().stream()
                            .filter(entry -> entry.getValue().isPresent())
                            .forEach(entry -> columnJoinInfo.put(entry.getKey(),
                                    entry.getValue()));

                    final List<String> leftJoins = joinInfo.values().stream()
                            .filter(Optional::isPresent).map(Optional::get)
                            .map(LeftJoinInformation::getLeftJoinClause).distinct()
                            .collect(toList());

                    fromAndWhereClause = DBUtils.appendJoinsToHQLQuery(
                            fromAndWhereClause, classFound.get(), alias, leftJoins);
                }
            }
        }

        final String selectList = selectIds ? jqSearch.getIdQuestionKey()
                : jqSearch.getJqGridColumns().stream().map(col -> {
                    if (col.isVirtual())
                    {
                        return jqSearch.getIdQuestionKey();
                    }
                    return columnJoinInfo.get(col)
                            .map(LeftJoinInformation::getSelectPath)
                            .orElse(col.getIndex());
                }).collect(Collectors.joining(","));

        StringBuilder sb = new StringBuilder();
        sb.append("select").append(" ");
        if (batchJobForDetail)
        {
            if (relationship == null || relationship.isDirect(masterEntity))
            {
                sb.append(jqSearch.getMasterHqlFkName()).append(",");
            }
            else if (masterEntity != null)
            {
                sb.append(masterEntity.getEntityHqlAlias()).append(".")
                        .append(masterEntity.getEntityHqlId()).append(",");
            }
        }
        sb.append(selectList).append(" ");
        sb.append(fromAndWhereClause).append(" ");

        final List<String> whereClauses = new ArrayList<>();

        if (!jqSearch.isMaster() && masterIds != null)
        {
            if (relationship == null || relationship.isDirect(masterEntity))
            {
                if (masterIds.size() == 1)
                {
                    whereClauses.add(String.format(" ( %s=%s ) ",
                            jqSearch.getMasterHqlFkName(), masterIds.get(0)));
                }
                else
                {
                    whereClauses.add(String.format(" ( %s in %s ) ",
                            jqSearch.getMasterHqlFkName(),
                            DBUtils.buildInClause(masterIds)));
                }
            }
            else if (masterEntity != null)
            {
                if (masterIds.size() == 1)
                {
                    whereClauses.add(String.format(" ( %s=%s and %s.%s=%s ) ",
                            jqSearch.getMasterHqlFkName(),
                            relationship.getMasterJoinHql(),
                            masterEntity.getEntityHqlAlias(),
                            masterEntity.getEntityHqlId(), masterIds.get(0)));
                }
                else
                {
                    whereClauses.add(String.format(" ( %s=%s and %s.%s in %s ) ",
                            jqSearch.getMasterHqlFkName(),
                            relationship.getMasterJoinHql(),
                            masterEntity.getEntityHqlAlias(),
                            masterEntity.getEntityHqlId(),
                            DBUtils.buildInClause(masterIds)));
                }
            }
        }

        if (ids != null && !ids.isEmpty())
        {
            if (relationship == null || relationship.isDirect(masterEntity))
            {
                whereClauses.add(String.format(" ( %s in %s ) ",
                        jqSearch.getIdQuestionKey(), DBUtils.buildInClause(ids)));
            }
            else if (masterEntity != null)
            {
                whereClauses.add(String.format(" ( %s=%s and %s.%s in %s ) ",
                        jqSearch.getIdQuestionKey(),
                        relationship.getMasterJoinHql(),
                        masterEntity.getEntityHqlAlias(),
                        masterEntity.getEntityHqlId(), DBUtils.buildInClause(ids)));
            }
        }

        if (!whereClauses.isEmpty())
        {
            final String additionalWhereClauses = Joiner.on(" and ")
                    .join(whereClauses);
            if (!sb.toString().toLowerCase().contains("where"))
            {
                sb.append(" where ").append(additionalWhereClauses);
            }
            else
            {
                sb.append(" and ").append(additionalWhereClauses);
            }
        }

        if (includeOrderBy)
        {
            final String sortIndex = jqSearch.getSortIndex();
            final String sortOrder = jqSearch.getSortOrder();

            if (!StringUtils.isEmpty(sortIndex) && !StringUtils.isEmpty(sortOrder))
            {
                sb.append("order by").append(" ");
                sb.append(sortIndex).append(" ");
                sb.append(sortOrder).append(" ");
            }
        }

        return sb.toString();
    }

    public static String getSearchMasterIdsSQLSubquery(JQGridSearch jqSearch)
    {
        String hql = getEntityHql(jqSearch, null, true, false, false);
        String sql = null;
        try
        {
            sql = HibernateUtils.translate(hql);
        }
        catch (Exception e)
        {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return sql;
    }

    static JQGridDataSausage getDataSausage(JQGridSearch jqSearch)
    {
        JQGridDataSausage sausage = null;

        if (jqSearch != null && jqSearch.getDataSausageClassName() != null)
        {
            sausage = SAUSAGE_CACHE.computeIfAbsent(
                    jqSearch.getDataSausageClassName(),
                    getDataSausageComputeFunction(jqSearch));
        }

        return sausage;
    }

    private static Function<? super String, ? extends JQGridDataSausage> getDataSausageComputeFunction(
            JQGridSearch jqSearch)
    {
        return className -> {
            try
            {
                Class c = ClassUtils.loadClass(jqSearch.getDataSausageClassName());

                if (c != null && JQGridDataSausage.class.isAssignableFrom(c))
                {
                    return (JQGridDataSausage) c.newInstance();
                }
                throw new RuntimeException("Invalid sausage type.");
            }
            catch (Exception e)
            {
                e.printStackTrace();
                throw new RuntimeException("BAD SAUSAGE! : " + e.getMessage());
            }
        };
    }

    /**
     * This populates a JSP with the objects that are necessary to support the
     * search-results UI (jqGridSearch.jsp)
     */
    public static void populateSearchResultsPage(ModelAndView mv,
            JQGridModel jqGridModel, HttpServletRequest request,
            String searchModelSessionKey)
    {
        setJQGridModelToSession(jqGridModel, request);

        try
        {
            Integer count = getRowCount(jqGridModel.getMaster());

            jqGridModel.getMaster().setRowSelections(Lists.newArrayList());
            jqGridModel.getMaster().setRowDeselections(Lists.newArrayList());
            jqGridModel.getMaster().setCheckAllState(0);
            jqGridModel.getMaster().setCount(count);

            mv.addObject("masterCount", count);
        }
        catch (Exception e)
        {
            e.printStackTrace();
            mv.addObject("searchError", true);
            mv.addObject("masterCount", 0);
        }

        mv.addObject("jqGridModel", jqGridModel);
        mv.addObject("searchModel", SearchHelper.getSearchModelFromSession(request,
                searchModelSessionKey));

        mv.addObject("emailCampaignSiteElement",
                NHelper.getEmailCampaignSiteElement());

        populateSaveSearchUI(mv, jqGridModel, request);
    }

    private static List<Integer> getMasterIds(JQGridModel jqGridModel,
            Locale locale)
    {
        List<Integer> masterIds;
        try
        {
            boolean orderByVirtualColumn = isOrderByVirtualColumn(
                    jqGridModel.getMaster());
            JQGridSearch jqSearch = jqGridModel.getMaster();
            String hql = getEntityHql(jqSearch, null, !orderByVirtualColumn, false,
                    !orderByVirtualColumn);
            if (orderByVirtualColumn)
            {
                JQGridDataSausage sausage = getDataSausage(jqSearch);
                List<Object[]> rows = PortalUtils.getHt().find(hql);
                mergeAndSortVirtualColumns(jqSearch, sausage, locale, rows);
                int idIndex = jqSearch
                        .getDataArrayIndex(jqSearch.getIdQuestionKey());
                masterIds = CollectionUtils.pluck(rows, idIndex);
            }
            else
            {
                masterIds = PortalUtils.getHt().find(hql);
            }
            logger.debug("getMasterIds() " + masterIds.size() + ": " + hql);
        }
        catch (Exception e)
        {
            throw new RuntimeException(
                    "JQGridHelper.getMasterIds(): " + e.getMessage());
        }

        return masterIds;
    }

    /**
     * populate stuff into mv that supports the usecases for the "save search
     * modal" in jqGridSearch.jsp
     */
    private static void populateSaveSearchUI(ModelAndView mv,
            JQGridModel jqGridModel, HttpServletRequest request)
    {
        initializeSearchPermissions();

        UserDetailsImpl userLoggedIn = PortalUtils.getUserLoggedIn(request);
        boolean commonSearchManager = isCommonSearchManager(userLoggedIn);
        mv.addObject("commonSearchManager", commonSearchManager);

        if (commonSearchManager)
        {
            List<PersonGroup> groups = PersonGroupHelper.getSecondaryGroups();

            Map<PersonGroup, Boolean> sharePermissions = new LinkedHashMap<>();
            for (PersonGroup pg : groups)
            {
                sharePermissions.put(pg, false);
            }

            if (jqGridModel != null && jqGridModel.getMaster() != null
                    && jqGridModel.getMaster().getId() != null)
            {
                List<PersonGroup> searchGroups = getSearchGroups(
                        jqGridModel.getMaster());

                for (PersonGroup group : searchGroups)
                {
                    sharePermissions.put(group, true);
                }
            }

            mv.addObject("sharePermissions", sharePermissions);
        }
    }

    private static List<PersonGroup> getSearchGroups(JQGridSearch search)
    {
        List<PersonGroup> groups = PortalUtils.getHt().find(
                "select sg.personGroup from JQGridSearchGroup sg where sg.search=?",
                search);
        return groups;
    }

    /**
     * "Common Search Managers" are those who are allowed to
     * create/modify/delete "common searches" (searches that can be shared with
     * a group of users).
     */
    public static boolean isCommonSearchManager(UserDetailsImpl user)
    {
        return user != null
                && (user.isRoot() || user.getAssignedTypes().containsValue(
                        PersonGroupHelper.MANAGE_SEARCHES_COMMON_SEARCH_MANAGER));
    }

    private static void initializeSearchPermissions()
    {
        PersonGroupHelper.initializePermissions("Manage Searches",
                PersonGroupHelper.MANAGE_SEARCHES_COMMON_SEARCH_MANAGER);
    }

    /**
     * Returns a JQGridModel from the user's session.
     */
    public static JQGridModel getJQGridModelFromSession(HttpServletRequest request)
    {
        JQGridModel gridModel = null;

        if (request.getSession().getAttribute(JQGRID_MODEL_KEY) != null)
        {
            gridModel = (JQGridModel) request.getSession()
                    .getAttribute(JQGRID_MODEL_KEY);
        }

        return gridModel;
    }

    /**
     * Puts the JQGridSearch into the user's session.
     */
    public static void setJQGridModelToSession(JQGridModel gridModel,
            HttpServletRequest request)
    {
        if (gridModel != null)
        {
            request.getSession().setAttribute(JQGRID_MODEL_KEY, gridModel);
        }
        else
        {
            request.getSession().removeAttribute(JQGRID_MODEL_KEY);
        }
    }

    /**
     * Removes the JQGridSearch from the user's session.
     */
    public static void removeJQGridModelFromSession(HttpServletRequest request)
    {
        setJQGridModelToSession(null, request);
    }

    /**
     * used by JQGridExcelExportTask.populateSearchResults
     */
    public static List<Object[]> loadEntities(JQGridSearch jqSearch, int startIdx,
            int pageSize, JQGridDataSausage sausage, List<Integer> ids,
            Locale locale)
    {
        CommandQueryTemplate ht = PortalUtils.getHtReadOnly();
        List<Integer> pageIds = emptyList();
        if (!ids.isEmpty())
        {
            pageIds = PaginationUtils.paginateListByStartIdx(ids, startIdx,
                    pageSize);
        }
        boolean orderByVirtualColumn = isOrderByVirtualColumn(jqSearch);
        String hql = getEntityHql(jqSearch, pageIds, false, false,
                !orderByVirtualColumn);
        List<Object[]> rows;
        if (orderByVirtualColumn)
        {
            List<Object[]> rawRows = PortalUtils.getHt().find(hql);
            rows = transformToArraysList(rawRows);
            mergeAndSortVirtualColumns(jqSearch, sausage, locale, rows);
            rows = PaginationUtils.paginateListByStartIdx(rows, 0, pageSize);
        }
        else
        {
            List rawResult = ht.executeFind(session -> {
                Query query = ht.createQuery(session, hql);
                if (ids.isEmpty())
                {
                    query.setMaxResults(pageSize);
                }
                return query.getResultList();
            });
            rows = transformToArraysList(rawResult);
            if (sausage != null)
            {
                setVirtualColumnsToNull(jqSearch, rows);
                sausage.massageDataRows(rows, jqSearch, locale);
            }
        }
        return rows;
    }

    private static void setVirtualColumnsToNull(JQGridSearch jqSearch,
            List<Object[]> rows)
    {
        final List<JQGridColumn> virtualColumns = jqSearch.getJqGridColumns()
                .stream().filter(JQGridColumn::isVirtual)
                .collect(Collectors.toList());
        final int[] virtualColumnIndices = new int[virtualColumns.size()];

        IntStream.range(0, virtualColumnIndices.length)
                .forEach(i -> virtualColumnIndices[i] = jqSearch
                        .getDataArrayIndex(virtualColumns.get(i).getIndex()));

        // virtual columns are by default populated with the entity id, this
        // changes that to null
        rows.forEach(row -> {
            Arrays.stream(virtualColumnIndices).forEach(virtualColumnIndex -> {
                ArrayUtils.setValueAtIndex(row, virtualColumnIndex, null);
            });
        });
    }

    public static List<Integer> getSelectedIdList(JQGridModel gridModel,
            Locale locale)
    {
        HashSet<Integer> selectedIds = new HashSet<>();
        List<Integer> masterIdsList = getMasterIds(gridModel, locale);

        if (gridModel != null && gridModel.getMaster() != null
                && gridModel.getMaster().getRowSelections() != null)
        {
            JQGridSearch masterSearch = gridModel.getMaster();
            if (masterSearch.getCheckAllState().intValue() == 2)
            {
                Set<Integer> allIds = Sets.newHashSet(masterIdsList);
                allIds.removeAll(masterSearch.getRowDeselections());
                selectedIds = Sets.newHashSet(allIds);
            }
            else if (masterSearch.getCheckAllState().intValue() == 1)
            {
                selectedIds = Sets.newHashSet(masterIdsList);
            }
            else
            {
                selectedIds = Sets.newHashSet(masterSearch.getRowSelections());
                selectedIds.removeAll(masterSearch.getRowDeselections());
            }
        }

        LinkedHashSet<Integer> masterIds = new LinkedHashSet(masterIdsList);

        masterIds.retainAll(new HashSet(selectedIds));

        return new ArrayList(masterIds);
    }

    public static JSONArray getSelectedIds(JQGridModel gridModel, Locale locale,
            boolean nullIfAllSelected)
    {
        JSONArray rowIds = new JSONArray();
        final JSONArray tempRowIds = new JSONArray();

        try
        {
            getSelectedIdList(gridModel, locale).forEach(x -> tempRowIds.put(x));
        }
        catch (Exception e)
        {
            logger.error("getSelectedIds() Exception: " + e.getMessage());
            e.printStackTrace();
        }
        rowIds = tempRowIds;

        if (nullIfAllSelected)
        {
            JQGridSearch master = gridModel.getMaster();
            List<Integer> counts = PortalUtils.getHt()
                    .find(master.getCountIdsHql(true));
            if (null != counts && !counts.isEmpty())
            {
                Integer count = counts.get(0);
                if (count.equals(tempRowIds.length()))
                {
                    rowIds = null;
                }
            }
        }
        return rowIds;
    }

    public static String getJQGridBuilderMasterJoinHQL(JQGridSearch jqSearch)
    {
        String ret;
        Relationship relationship = jqSearch.getRelationship();
        Entity masterEntity = relationship.getRelatedEntity().getSearchModel()
                .getMasterEntity();
        List<Integer> masterIds = jqSearch.getMasterIds();
        Integer masterId = masterIds == null ? jqSearch.getMasterId() : null;
        if (StringUtils.isEmpty(relationship.getMasterJoinHql()))
        {
            if (masterIds == null)
            {
                ret = String.format(" = %s ", masterId != null ? masterId : 0);
            }
            else
            {
                ret = String.format(" in %s ", DBUtils.buildInClause(masterIds));
            }
        }
        else
        {
            String joinAlias = relationship.getMasterJoinHql().substring(0,
                    relationship.getMasterJoinHql().lastIndexOf('.'));
            ret = String.format(" in (select %s from %s %s where %s", joinAlias,
                    masterEntity.getEntityClass().getSimpleName(),
                    masterEntity.getEntityHqlAlias(),
                    masterEntity.getEntityHqlId());
            if (masterIds == null)
            {
                ret += " = " + (masterId != null ? masterId : 0);
            }
            else
            {
                ret += " in " + DBUtils.buildInClause(masterIds);
            }
            ret += ") ";
        }

        return ret;
    }

    public static boolean jqSearchContainsColumnWithIndex(JQGridSearch jqSearch,
            String index)
    {
        List<JQGridColumn> columns = jqSearch.getJqGridColumns();
        if (columns != null)
        {
            for (JQGridColumn column : columns)
            {
                if (column.getIndex().equals(index))
                {
                    return true;
                }
            }
        }
        return false;
    }
    
    private static boolean isOrderByVirtualColumn(JQGridSearch jqGridSearch)
    {
        String sortIndex = jqGridSearch.getSortIndex();
        return jqGridSearch.getJqGridColumns().stream()
                .anyMatch(c -> c.getIndex().equals(sortIndex) && c.isVirtual());
    }

    /**
     * Hibernate returns List<Object> instead of List<Object[]> if only one column
     * is specified in the query. In this case method wraps object in array to make
     * the list consistent.
     *
     * @param list
     *            - Hibernate result list
     * @return a list of arrays (List<Object[]>)
     */
    public static List<Object[]> transformToArraysList(List<?> list)
    {
        List<Object[]> ret;
        if (!list.isEmpty() && !(list.get(0) instanceof Object[]))
        {
            ret = list.stream().map(obj -> new Object[] { obj }).collect(toList());
        }
        else
        {
            ret = (List<Object[]>) list;
        }
        return ret;
    }

    private static void mergeAndSortVirtualColumns(JQGridSearch jqSearch,
            JQGridDataSausage sausage, Locale locale, List<Object[]> data)
    {
        if (sausage != null)
        {
            setVirtualColumnsToNull(jqSearch, data);
            sausage.massageDataRows(data, jqSearch, locale);
        }
        int orderByIndex = jqSearch.getDataArrayIndex(jqSearch.getSortIndex());
        ArrayUtils.sortRows(data, orderByIndex,
                "desc".equals(jqSearch.getSortOrder()));
    }

    /**
     * This is the method that can serve an ajax request made by JQGrid and return
     * the appropriate json response. OrbisController methods that receive JQGrid
     * requests should first get a handle to the JQGridSearch object, then call on
     * this method. The returned JSONObject should be returned by the controller
     * method via ajax.
     *
     * @param jqSearch
     *            - the JQGridSearch instance used for this search
     * @param currentPage
     *            - the requested page index
     * @param pageSize
     *            - the requested "number of rows per page"
     * @param sortIndex
     *            - the requested "HQL sort index"
     * @param sortOrder
     *            - the requested "HQL sort order" (e.g.: asc, desc)
     * @param locale
     * @return JSONObject - for the ajax response
     * @throws Exception
     */
    public static JSONObject searchResponse(JQGridSearch jqSearch, int currentPage,
            int pageSize, String sortIndex, String sortOrder, Locale locale)
            throws Exception
    {
        JSONObject response = new JSONObject();

        if (Relationship.TYPE.COUNT.equals(jqSearch.getDetailType()))
        {
            try
            {
                response = ((JQGridBuilderInterface) ClassUtils
                        .loadClass(jqSearch.getGridBuilderClassName())
                        .newInstance()).getSearchResponse(jqSearch, currentPage,
                                pageSize, sortIndex, sortOrder, false);
            }
            catch (Exception e)
            {
                e.printStackTrace();
            }
        }
        else
        {
            response = getSearchResponseDefault(jqSearch, currentPage, pageSize,
                    sortIndex, sortOrder, locale);
        }

        return response;
    }

    private static class PagingInfo
    {
        final int rowCount;

        final int startIdx;

        final int currentPage;

        final int totalPages;

        private PagingInfo(int rowCount, int startIdx, int currentPage,
                           int totalPages)
        {
            this.rowCount = rowCount;
            this.startIdx = startIdx;
            this.currentPage = currentPage;
            this.totalPages = totalPages;
        }
    }

    private static PagingInfo getPagingInfo(final JQGridSearch jqSearch,
                                                         final int userCurrentPage, final int pageSize)
    {
        int rowCount = JQGridHelper.getRowCount(jqSearch);
        int currentPage = userCurrentPage;
        int startIdx;
        int totalPages = 0;

        // calculate the total pages for the query
        if (rowCount > 0)
        {
            totalPages = (int) Math.ceil(((double) rowCount) / ((double) pageSize));
        }

        // if for some reasons the requested page is greater than the total
        // set the requested page to total page
        if (currentPage > totalPages)
        {
            currentPage = totalPages;
        }

        // calculate the starting position of the rows
        startIdx = pageSize * currentPage - pageSize;

        // if for some reasons start position is negative set it to 0.
        // typical case is that the user type 0 for the requested page
        if (startIdx < 0)
        {
            startIdx = 0;
        }

        return new PagingInfo(rowCount, startIdx, currentPage, totalPages);
    }

    private static JSONObject getSearchResponseDefault(JQGridSearch jqSearch,
            int currentPage, int pageSize, String sortIndex, String sortOrder,
            Locale locale) throws Exception
    {
        jqSearch.setSortIndex(sortIndex);
        jqSearch.setSortOrder(sortOrder);

        final PagingInfo pagingInfo = getPagingInfo(jqSearch, currentPage,
                pageSize);

        JSONArray responseRows = new JSONArray();
        JSONObject responseObject = new JSONObject();
        responseObject.put("page", pagingInfo.currentPage);
        responseObject.put("total", pagingInfo.totalPages);
        responseObject.put("records", pagingInfo.rowCount);
        responseObject.put("rows", responseRows);

        if (pagingInfo.rowCount > 0)
        {

            final JQGridDataSausage sausage = JQGridHelper.getDataSausage(jqSearch);
            final List<Object[]> data = JQGridHelper.loadEntities(jqSearch,
                    pagingInfo.startIdx, pageSize, sausage, locale);

            for (final Object[] row : data)
            {
                JSONArray responseCells = new JSONArray();
                JSONObject responseRow = new JSONObject();
                responseRows.put(responseRow);
                responseRow.put("cell", responseCells);

                responseRow.put("id",
                        String.valueOf(jqSearch.getEntityIdFromRow(row).orElse(0)));

                final JSONArray arr = jqSearch.getWorkColModel();

                for (int i = 0; i < arr.length(); i++)
                {
                    final JSONObject col = arr.optJSONObject(i);
                    if (col == null)
                    {
                        continue;
                    }

                    if (col.optBoolean("isNewWindowsCol"))
                    {
                        if (jqSearch.isDataViewer())
                        {
                            continue;
                        }
                        responseCells.put(getNewWindowCell(
                                responseRow.optString("id", ""), locale));
                    }
                    else
                    {
                        final String questionKey = col.optString("index", "");

                        final Object dataValue = jqSearch
                                .getRowProperty(row, questionKey).orElse(null);

                        String cellValue = null;

                        if (sausage != null)
                        {
                            cellValue = sausage.massageCellValue(jqSearch,
                                    questionKey, dataValue, locale);
                        }

                        if (cellValue == null && dataValue != null)
                        {
                            cellValue = String.valueOf(dataValue);
                        }

                        if (cellValue != null)
                        {
                            cellValue = cellValue.replace("&amp;", "&")
                                    .replace("&#39;", "'");
                        }

                        responseCells.put(cellValue);
                    }
                }
            }
        }

        return responseObject;
    }

    private static String getNewWindowCell(Serializable id, Locale locale)
    {
        MessageSource messageSource = PortalUtils.getMessageSource();
        String view = messageSource.getMessage(
                "i18n.coop_termRecordQualifiedJobsTable.view", null, locale);
        String currentTab = messageSource
                .getMessage("i18n.grid_viewButton.currenttab", null, locale);
        String newTab = messageSource.getMessage("i18n.grid_viewButton.newtab",
                null, locale);

        StringBuilder sb = new StringBuilder();
        if (PortalUtils.isSpiralRobot())
        {
            String template = "<a href=\"javascript:void(0);\" id=\"button_current_%s name value title class=\"btn__small--text  \" onclick=\"resultsActions.onNewWindow('%s', %s)\">%s</a>";

            sb.append(String.format(template, id, id, "false",
                    PortalUtils.getI18nMessage(
                            "i18n.JQGridSubController.current3904461608947062",
                            null, locale)));
            sb.append("&nbsp;&vert;&nbsp;");
            sb.append(String.format(template, id, id, "true",
                    PortalUtils.getI18nMessage(
                            "i18n.JQGridSubController.new7131095735337912", null,
                            locale)));

            return sb.toString();
        }
        else
        {

            sb.append(" <div class=\"btn-group viewBtn\"> ");
            sb.append(
                    "    <a href=\"javascript:void(0)\" class=\"btn btn-mini btn-primary dropdown-toggle\" data-toggle=\"dropdown\"> ");
            sb.append("        %s ");
            sb.append("        <span class=\"caret\"></span> ");
            sb.append("    </a> ");
            sb.append("    <ul class=\"dropdown-menu\"> ");
            sb.append("        <li> ");
            sb.append(
                    "            <a href='javascript:void(0);' onclick='resultsActions.onNewWindow(\"%s\", false)'>%s</a> ");
            sb.append("        </li> ");
            sb.append("        <li> ");
            sb.append(
                    "            <a href='javascript:void(0);' onclick='resultsActions.onNewWindow(\"%s\", true)'>%s</a>           ");
            sb.append("        </li>            ");
            sb.append("    </ul> ");
            sb.append(" </div> ");
            return String.format(sb.toString(), view, id, currentTab, id, newTab);
        }

    }

    public static JQGridSearch resolveJQGridSearch(HttpServletRequest request)
    {
        JQGridModel gridModel = JQGridHelper.getJQGridModelFromSession(request);

        JQGridSearch jqSearch = null;
        String gridId = request.getParameter("gridId");

        if (gridId == null || gridId.equalsIgnoreCase("masterGrid"))
        {
            jqSearch = gridModel.getMaster();
        }
        else
        {
            jqSearch = gridModel.getDetail(gridId);

            String masterId = request.getAttribute("masterId") != null
                    ? (String) request.getAttribute("masterId")
                    : request.getParameter("masterId");

            jqSearch.setMasterId(
                    StringUtils.isInteger(masterId) ? Integer.parseInt(masterId)
                            : null);

            final SearchModel searchModel = getSearchModel(request);

            if (searchModel != null)
            {
                jqSearch.setRelationship(SearchHelper
                        .getDetailRelationship(jqSearch, gridModel, searchModel));

                updateGridDetailSearchHQL(jqSearch, searchModel);
            }
        }
        return jqSearch;
    }

    public static SearchModel getSearchModel(HttpServletRequest request)
    {
        final SearchModel searchModel;
        if (request.getAttribute("reportSessionKey") != null)
        {
            searchModel = SearchHelper.getSearchModelFromSession(request,
                    (String) request.getAttribute("reportSessionKey"));
        }
        else
        {
            searchModel = SearchHelper.getSearchModelFromSession(request,
                    SearchHelper.SEARCH_MODEL_SESSION_KEY);
        }
        return searchModel;
    }

    public static void updateGridDetailSearchHQL(JQGridSearch jqSearch,
            SearchModel searchModel)
    {
        Relationship relationship = jqSearch.getRelationship();
        if (searchModel != null && relationship != null)
        {
            if (Objects.requireNonNull(
                    relationship.getType()) == Relationship.TYPE.DETAIL)
            {
                jqSearch.setHql(SearchHelper.getDetailRelationshipHQL(
                        relationship.getRelatedEntity(), relationship,
                        jqSearch.getMasterId()));
            }
        }
    }

}
