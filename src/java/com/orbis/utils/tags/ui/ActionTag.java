package com.orbis.utils.tags.ui;

import jakarta.servlet.jsp.tagext.SimpleTagSupport;

import org.apache.axis.utils.StringUtils;

@UITagName("action")
public class ActionTag extends OnclickUITag
{
    private String id;

    private String i18n_title;

    private String i18nArgs_title;

    private String i18n_description;

    private String i18nArgs_description;

    private ActionAttribute actionAttribute;

    private String icon;

    private String classes;

    private String css;

    private String iconClasses;

    private String iconCss;

    @Override
    protected void tagInit()
    {
        if (StringUtils.isEmpty(id))
        {
            id = "action";
        }
    }

    @Override
    protected void doUITag()
    {
        ActionTagSupport actionTagSupport = (ActionTagSupport) SimpleTagSupport
                .findAncestorWithClass(this, ActionTagSupport.class);

        ActionAttribute action = getActionAttribute("action");

        action.setTitle(getI18NAttribute("title"));
        action.setDescription(getI18NAttribute("description"));

        actionAttribute = action;

        if (actionTagSupport != null)
        {
            actionTagSupport.addActionTag(id, this);
        }
    }

    public String getPassthroughAttributes()
    {
        return getTagAttributes().getPassthroughAttributes();
    }

    public String getId()
    {
        return id;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public String getI18n_title()
    {
        return i18n_title;
    }

    public void setI18n_title(String i18n_title)
    {
        this.i18n_title = i18n_title;
    }

    public String getI18nArgs_title()
    {
        return i18nArgs_title;
    }

    public void setI18nArgs_title(String i18nArgs_title)
    {
        this.i18nArgs_title = i18nArgs_title;
    }

    public String getI18n_description()
    {
        return i18n_description;
    }

    public void setI18n_description(String i18n_description)
    {
        this.i18n_description = i18n_description;
    }

    public String getI18nArgs_description()
    {
        return i18nArgs_description;
    }

    public void setI18nArgs_description(String i18nArgs_description)
    {
        this.i18nArgs_description = i18nArgs_description;
    }

    public ActionAttribute getActionAttribute()
    {
        return actionAttribute;
    }

    public String getIcon()
    {
        return icon;
    }

    public void setIcon(String icon)
    {
        this.icon = icon;
    }

    public String getClasses()
    {
        return classes;
    }

    public void setClasses(String classes)
    {
        this.classes = classes;
    }

    public String getCss()
    {
        return css;
    }

    public void setCss(String css)
    {
        this.css = css;
    }

    public String getIconClasses()
    {
        return iconClasses;
    }

    public void setIconClasses(String iconClasses)
    {
        this.iconClasses = iconClasses;
    }

    public String getIconCss()
    {
        return iconCss;
    }

    public void setIconCss(String iconCss)
    {
        this.iconCss = iconCss;
    }
}
