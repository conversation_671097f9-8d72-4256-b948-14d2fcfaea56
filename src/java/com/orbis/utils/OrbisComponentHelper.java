package com.orbis.utils;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import org.apache.commons.lang.text.StrSubstitutor;

import com.orbis.portal.PortalConfig;
import com.orbis.portal.PortalConfigHelper;

import jakarta.servlet.http.HttpServletRequest;

public class OrbisComponentHelper
{
    private static final String PROTO_PATH = "/core/spiralRobot/scripts/prototype/";

    public static final String ORBIS_COMPONENTS_RESET = "ORBIS_COMPONENTS_RESET";

    private static final String ORBIS_COMPONENTS_LOADED = "ORBIS_COMPONENTS_LOADED";

    private static OrbisComponents components;

    private static Set<String> localDirs;

    static
    {
        initComponents();

        localDirs = new HashSet<>();
        localDirs.add("/core/orbis/");
        localDirs.add("/core/ckfinder/");
        localDirs.add("/core/spiralRobot/scripts");
        localDirs.add("/core/spiralRobot/css");
    }

    private OrbisComponentHelper()
    {

    }

    public static void initComponents()
    {
        components = new OrbisComponents();

        OrbisComponent orbisApp = new OrbisComponent("orbisApp");
        orbisApp.setScript("/core/spiralRobot/scripts/orbisApp/0.46/OrbisApp.js");
        orbisApp.addDependency("sprintf");
        components.addComponent(orbisApp);

        OrbisComponent orbisAppLegacy = new OrbisComponent("orbisAppLegacy");
        orbisAppLegacy.setScript(
                "/core/spiralRobot/scripts/orbisAppLegacy/0.1/legacy.js");
        components.addComponent(orbisAppLegacy);

        OrbisComponent jqueryAjaxInitOptions = new OrbisComponent(
                "jqueryAjaxInitOptions");
        jqueryAjaxInitOptions
                .setScript("/core/orbis/scripts/jqueryAjaxInitOptions.js");
        components.addComponent(jqueryAjaxInitOptions);

        OrbisComponent jquery = new OrbisComponent("jquery");
        jquery.setScript("/core/spiralRobot/lib/jquery/3.1.1/jquery.min.js");
        jquery.setDev("/core/spiralRobot/scripts/prototype/jquery/3.1.1/jquery.js");
        jquery.addExpansion("jqueryAjaxInitOptions");
        components.addComponent(jquery);

        OrbisComponent jqueryExtensions = new OrbisComponent("jqueryExtensions");
        jqueryExtensions.setScript(
                "/core/spiralRobot/scripts/jqueryExtensions/1.2/JQueryExtensions.js");
        components.addComponent(jqueryExtensions);

        OrbisComponent orbisAppBootstrap = new OrbisComponent("orbisApp");
        orbisAppBootstrap.setScript("/core/orbis/scripts/orbis-366.js");
        orbisAppBootstrap.addDependency("json");
        orbisApp.addLegacyComponent("bootstrap", orbisAppBootstrap);

        OrbisComponent spiralRobot = new OrbisComponent("spiralRobot");
        spiralRobot.setScript(
                "/core/spiralRobot/scripts/spiralRobot/0.32/SpiralRobot.js");
        components.addComponent(spiralRobot);

        OrbisComponent prism = new OrbisComponent("prism");
        prism.setScript("/core/spiralRobot/lib/prism/1.13.0/prism.js");
        prism.setStylesheet("/core/spiralRobot/lib/prism/1.13.0/prism.css");
        components.addComponent(prism);

        OrbisComponent superslides = new OrbisComponent("superslides");
        superslides.setScript(
                "/core/spiralRobot/lib/superslides/0.6.2/jquery.superslides.min.js");
        superslides.setDev(
                "/core/spiralRobot/lib/superslides/0.6.2/jquery.superslides.js");
        superslides.setStylesheet(
                "/core/spiralRobot/lib/superslides/0.6.2/superslides.css");
        components.addComponent(superslides);

        OrbisComponent json = new OrbisComponent("json");
        json.setScript("/core/scripts/json/json2-231.js");
        components.addComponent(json);

        OrbisComponent orbisGrid = new OrbisComponent("orbisGrid");
        orbisGrid.setScript("/core/orbis/scripts/orbisGrid/OrbisGrid-236.js");
        components.addComponent(orbisGrid);

        OrbisComponent owlCarousel = new OrbisComponent("owlCarousel");
        owlCarousel.setScript(
                "/core/spiralRobot/lib/owl.carousel/2.2.1/owl.carousel.js");
        owlCarousel.setStylesheet(
                "/core/spiralRobot/lib/owl.carousel/2.2.1/owl.carousel.css");
        owlCarousel.addDependency("owlCarouselThemeDefault");
        components.addComponent(owlCarousel);

        OrbisComponent owlCarouselThemeDefault = new OrbisComponent(
                "owlCarouselThemeDefault");
        owlCarouselThemeDefault.setStylesheet(
                "/core/spiralRobot/lib/owl.carousel/2.2.1/owl.theme.default.css");
        components.addComponent(owlCarouselThemeDefault);

        OrbisComponent ckeditor_jqueryAdapter = new OrbisComponent(
                "ckeditor_jqueryAdapter");
        ckeditor_jqueryAdapter.setScript(
                "/core/spiralRobot/lib/ckeditor/4.6.2/adapters/jquery.js");
        components.addComponent(ckeditor_jqueryAdapter);

        addJqueryValidateComponent();

        OrbisComponent sprintf = new OrbisComponent("sprintf");
        sprintf.setScript("/core/spiralRobot/lib/js/sprintf.min.js");
        components.addComponent(sprintf);

        OrbisComponent jqGrid = new OrbisComponent("jqGrid");
        jqGrid.setScript(
                "/core/scripts/jquery/jquery.jqGrid-4.5.4/js/jquery.jqGrid.min-231.js");
        jqGrid.setStylesheet(
                "/core/scripts/jquery/jquery.jqGrid-4.5.4/css/ui.jqgrid-231.css");
        jqGrid.addDependency("jqGrid_locale");
        jqGrid.addDependency("jqGrid_multiselect");
        jqGrid.addDependency("json");
        jqGrid.addDependency("OrbisGrid");
        jqGrid.setAppendStylesheet(true);
        components.addComponent(jqGrid);

        OrbisComponent jqGrid_locale = new OrbisComponent("jqGrid_locale");
        jqGrid_locale.setScript(
                "/core/scripts/jquery/jquery.jqGrid-4.5.4/js/i18n/grid.locale-${locale}-231.js");
        components.addComponent(jqGrid_locale);

        OrbisComponent jqGrid_multiselect = new OrbisComponent(
                "jqGrid_multiselect");
        jqGrid_multiselect.setScript(
                "/core/scripts/jquery/jquery.jqGrid-4.5.4/plugins/ui.multiselect-231.js");
        jqGrid_multiselect.setStylesheet(
                "/core/scripts/jquery/jquery.jqGrid-4.5.4/plugins/ui.multiselect-231.css");

        components.addComponent(jqGrid_multiselect);

        OrbisComponent OrbisGrid = new OrbisComponent("OrbisGrid");
        OrbisGrid.setScript("/core/orbis/scripts/orbisGrid/OrbisGrid-236.js");
        components.addComponent(OrbisGrid);

        OrbisComponent tableHeadFixer = new OrbisComponent("tableHeadFixer");
        tableHeadFixer
                .setScript("/core/scripts/jquery/tableHeadFixer/tableHeadFixer.js");
        components.addComponent(tableHeadFixer);

        OrbisComponent calendar = new OrbisComponent("calendar");
        calendar.setScript("/core/scripts/calendar/calendar-setup-231.js");
        calendar.setStylesheet("/core/scripts/calendar/calendar-blue-231.css");
        calendar.addDependency("calendar_js");
        calendar.addDependency("calendar_en");
        components.addComponent(calendar);

        OrbisComponent calendar_js = new OrbisComponent("calendar_js");
        calendar_js.setScript("/core/scripts/calendar/calendar-231.js");
        components.addComponent(calendar_js);

        OrbisComponent calendar_en = new OrbisComponent("calendar_en");
        calendar_en.setScript("/core/scripts/calendar/lang/calendar-en-231.js");
        components.addComponent(calendar_en);

        OrbisComponent d3 = new OrbisComponent("d3");
        d3.setScript("/core/scripts/d3-4.7.3/d3.min.js");
        components.addComponent(d3);

        OrbisComponent d3ScaleChromatic = new OrbisComponent("d3ScaleChromatic");
        d3ScaleChromatic.setScript(
                "/core/spiralRobot/scripts/prototype/d3ScaleChromatic/1.5/d3-scale-chromatic.v1.min.js");
        d3ScaleChromatic.addDependency("d3");
        components.addComponent(d3ScaleChromatic);

        OrbisComponent animate = new OrbisComponent("animate");
        animate.setStylesheet("/core/spiralRobot/lib/css/animate.css");
        components.addComponent(animate);

        OrbisComponent md5 = new OrbisComponent("md5");
        md5.setScript("/core/scripts/md5-231.js");
        components.addComponent(md5);

        OrbisComponent keepAlive = new OrbisComponent("keepAlive");
        keepAlive.setScript("/core/orbis/scripts/keepAlive-232.js?");
        keepAlive.addDependency("orbisApp");
        components.addComponent(keepAlive);

        OrbisComponent zrssfeed = new OrbisComponent("zrssfeed");
        zrssfeed.setScript(
                "/core/scripts/jquery/zrssfeed-101/jquery.zrssfeed.min-231.js");
        components.addComponent(zrssfeed);

        OrbisComponent tree = new OrbisComponent("tree");
        tree.setScript(
                "/core/scripts/jquery/jquery-wdTree/src/Plugins/jquery.tree.modified-232.js");
        tree.setStylesheet("/core/scripts/jquery/jquery-wdTree/css/tree-231.css");
        tree.addDependency("treeTools");
        components.addComponent(tree);

        OrbisComponent treeTools = new OrbisComponent("treeTools");
        treeTools.setScript("/core/orbis/scripts/treeTools/treeTools-231.js");
        components.addComponent(treeTools);

        OrbisComponent dragscrollable = new OrbisComponent("dragscrollable");
        dragscrollable.setScript(
                "/core/scripts/jquery/jquery.dragscrollable-1.0/dragscrollable-231.js");
        components.addComponent(dragscrollable);

        OrbisComponent tooltip = new OrbisComponent("tooltip");
        tooltip.setScript("/core/scripts/tooltip/form-field-tooltip-232.js");
        components.addComponent(tooltip);

        OrbisComponent colorPicker = new OrbisComponent("colorPicker");
        colorPicker.setScript(
                "/core/scripts/jquery/jquery-colorpicker-1.4/colorpicker-231.js");
        colorPicker.setStylesheet(
                "/core/scripts/jquery/jquery-colorpicker-1.4/css/colorpicker-231.css");
        colorPicker.addDependency("colorPicker_layoutCss");
        components.addComponent(colorPicker);

        OrbisComponent colorPicker_layoutCss = new OrbisComponent(
                "colorPicker_layoutCss");
        colorPicker_layoutCss.setStylesheet(
                "/core/scripts/jquery/jquery-colorpicker-1.4/css/layout-231.css");
        components.addComponent(colorPicker_layoutCss);

        OrbisComponent jsColor = new OrbisComponent("jsColor");
        jsColor.setScript("/core/scripts/jscolor/jscolor.min.js");
        components.addComponent(jsColor);

        OrbisComponent lightbox = new OrbisComponent("lightbox");
        lightbox.setScript(
                "/core/scripts/jquery/jquery-lightbox-0.5/js/jquery.lightbox-0.5.pack.js");
        lightbox.setStylesheet(
                "/core/scripts/jquery/jquery-lightbox-0.5/css/jquery.lightbox-0.5.css");
        components.addComponent(lightbox);

        OrbisComponent uploadify = new OrbisComponent("uploadify");
        uploadify.setScript(
                "/core/scripts/jquery/jquery.uploadify-2.0.3/jquery.uploadify.v2.0.3.ForOch.min.js");
        uploadify.setStylesheet(
                "/core/scripts/jquery/jquery.uploadify-2.0.3/css/style-231.css");
        uploadify.addDependency("swfobject");
        components.addComponent(uploadify);

        OrbisComponent swfobject = new OrbisComponent("swfobject");
        swfobject.setScript("/core/scripts/swfobject_2_2.js");
        components.addComponent(swfobject);

        OrbisComponent youtubin = new OrbisComponent("youtubin");
        youtubin.setScript(
                "/core/scripts/jquery/jquery.youtubin-1.2/jquery.youtubin-231.js");
        youtubin.addDependency("swfobject");
        components.addComponent(youtubin);

        OrbisComponent simpleviewer = new OrbisComponent("simpleviewer");
        simpleviewer.setScript(
                "/core/scripts/simpleviewer_pro_210/js/simpleviewer-231.js");
        simpleviewer.addDependency("swfobject");
        components.addComponent(simpleviewer);

        OrbisComponent orbisSlideshow = new OrbisComponent("orbisSlideshow");
        orbisSlideshow.setScript(
                "/core/orbis/scripts/orbisSlideshow/orbisSlideshow-2-231.js");
        orbisSlideshow.setStylesheet(
                "/core/orbis/scripts/orbisSlideshow/orbisSlideshow-231.css");
        components.addComponent(orbisSlideshow);

        OrbisComponent orbisRatingWidget = new OrbisComponent("orbisRatingWidget");
        orbisRatingWidget
                .setScript("/core/orbis/scripts/orbisRating/ratingWidget-231.js");
        orbisRatingWidget.setStylesheet(
                "/core/orbis/scripts/orbisRating/ratingWidget-231.css");
        components.addComponent(orbisRatingWidget);

        OrbisComponent scrollTo = new OrbisComponent("scrollTo");
        scrollTo.setScript(
                "/core/scripts/jquery/jquery.scrollTo/jquery.scrollTo-*******-min.js");
        components.addComponent(scrollTo);

        OrbisComponent orbisTools = new OrbisComponent("orbisTools");
        orbisTools.setScript("/core/orbis/scripts/orbisTools/orbisTools.js?v=1");
        components.addComponent(orbisTools);

        OrbisComponent printElement = new OrbisComponent("printElement");
        printElement.setScript(
                "/core/scripts/jquery/jquery.printElement-1.2/jquery.printElement.min-231.js");
        components.addComponent(printElement);

        OrbisComponent printThis = new OrbisComponent("printThis");
        printThis.setScript(
                "/core/scripts/jquery/jquery.printThis/jquery.printThis-231.js");
        components.addComponent(printThis);

        OrbisComponent getStyles = new OrbisComponent("getStyles");
        getStyles.setScript(
                "/core/scripts/jquery/jquery.getStyles/jquery.getStyleObject-231.js");
        components.addComponent(getStyles);

        OrbisComponent orbisDropdown = new OrbisComponent("orbisDropdown");
        orbisDropdown.setScript(
                "/core/orbis/scripts/orbisDropdown/orbisDropdown-231.js");
        orbisDropdown.setStylesheet(
                "/core/orbis/scripts/orbisDropdown/orbisDropdown-231.css");
        components.addComponent(orbisDropdown);

        OrbisComponent highCharts_orbisExtension = new OrbisComponent(
                "highCharts_orbisExtension");
        highCharts_orbisExtension
                .setScript("/core/orbis/scripts/highCharts_orbisExtension-1.js");
        components.addComponent(highCharts_orbisExtension);

        OrbisComponent highCharts_core = new OrbisComponent("highCharts_core");
        highCharts_core.setScript(
                "/core/scripts/jquery/jquery.highcharts-4.1.8/js/highcharts-232.js");
        components.addComponent(highCharts_core);

        OrbisComponent highCharts_more = new OrbisComponent("highCharts_more");
        highCharts_more.setScript(
                "/core/scripts/jquery/jquery.highcharts-4.1.8/js/highcharts-more-232.js");
        components.addComponent(highCharts_more);

        OrbisComponent highCharts_3d = new OrbisComponent("highCharts_3d");
        highCharts_3d.setScript(
                "/core/scripts/jquery/jquery.highcharts-4.1.8/js/highcharts-3d-232.js");
        highCharts_3d.addExpansion("highCharts_orbisExtension");
        components.addComponent(highCharts_3d);

        OrbisComponent highCharts_noData = new OrbisComponent("highCharts_noData");
        highCharts_noData.setScript(
                "/core/scripts/jquery/jquery.highcharts-4.1.8/js/modules/no-data-to-display.js");
        components.addComponent(highCharts_noData);

        OrbisComponent highCharts_data = new OrbisComponent("highCharts_data");
        highCharts_data.setScript(
                "/core/scripts/jquery/jquery.highcharts-4.1.8/js/modules/data.js");
        components.addComponent(highCharts_data);

        OrbisComponent highCharts_offline_exporting = new OrbisComponent(
                "highCharts_offline_exporting");
        highCharts_offline_exporting.setScript(
                "/core/scripts/jquery/jquery.highcharts-4.1.8/js/modules/offline-exporting.js");
        components.addComponent(highCharts_offline_exporting);

        OrbisComponent highCharts_exporting = new OrbisComponent(
                "highCharts_exporting");
        highCharts_exporting.setScript(
                "/core/scripts/jquery/jquery.highcharts-4.1.8/js/modules/exporting-232.js");
        highCharts_exporting.addExpansion("highCharts_offline_exporting");
        components.addComponent(highCharts_exporting);

        OrbisComponent highCharts = new OrbisComponent("highCharts");
        highCharts.setScript(highCharts_core.getScript());
        highCharts.addExpansion("highCharts_data");
        highCharts.addExpansion("highCharts_exporting");
        highCharts.addExpansion("highCharts_more");
        highCharts.addExpansion("highCharts_3d");
        highCharts.addExpansion("highCharts_noData");
        highCharts.addExpansion("highCharts_orbisExtension");
        components.addComponent(highCharts);

        OrbisComponent highCharts_groupedCategories = new OrbisComponent(
                "highCharts_groupedCategories");
        highCharts_groupedCategories.setScript(
                "/core/scripts/jquery/jquery.highcharts-4.1.8/js/modules/grouped-categories.js");
        components.addComponent(highCharts_groupedCategories);

        OrbisComponent highCharts_5_orbisExtension = new OrbisComponent(
                "highCharts_5_orbisExtension");
        highCharts_5_orbisExtension
                .setScript("/core/orbis/scripts/highCharts_5_orbisExtension-2.js");
        components.addComponent(highCharts_5_orbisExtension);

        OrbisComponent highCharts3d_5 = new OrbisComponent("highCharts3d_5");
        highCharts3d_5.setScript(
                "/core/scripts/jquery/jquery.highcharts-5.0.9/code/highcharts-3d.js");
        highCharts3d_5.addExpansion("highCharts_5_orbisExtension");
        components.addComponent(highCharts3d_5);

        OrbisComponent highCharts_5 = new OrbisComponent("highCharts_5");
        highCharts_5.setScript(
                "/core/scripts/jquery/jquery.highcharts-5.0.9/code/modules/no-data-to-display.js");
        highCharts_5.addExpansion("highCharts_5_more");
        highCharts_5.addExpansion("highCharts_5_3d");
        highCharts_5.addExpansion("highCharts_5_funnel");
        highCharts_5.addDependency("highCharts_5_core");

        highCharts_5.addDependency("highCharts_5_orbisExtension");
        components.addComponent(highCharts_5);

        OrbisComponent highCharts_5_funnel = new OrbisComponent(
                "highCharts_5_funnel");
        highCharts_5_funnel.setScript(
                "/core/scripts/jquery/jquery.highcharts-5.0.9/code/modules/funnel.js");
        components.addComponent(highCharts_5_funnel);

        OrbisComponent highCharts_5_core = new OrbisComponent("highCharts_5_core");
        highCharts_5_core.setScript(
                "/core/scripts/jquery/jquery.highcharts-5.0.9/code/highcharts.js");
        highCharts_5_core.setDev(
                "/core/scripts/jquery/jquery.highcharts-5.0.9/code/highcharts.src.js");
        components.addComponent(highCharts_5_core);

        OrbisComponent highCharts_5_more = new OrbisComponent("highCharts_5_more");
        highCharts_5_more.setScript(
                "/core/scripts/jquery/jquery.highcharts-5.0.9/code/highcharts-more.js");
        components.addComponent(highCharts_5_more);

        OrbisComponent highCharts_5_3d = new OrbisComponent("highCharts_5_3d");
        highCharts_5_3d.setScript(
                "/core/scripts/jquery/jquery.highcharts-5.0.9/code/highcharts-3d.js");
        components.addComponent(highCharts_5_3d);

        OrbisComponent highCharts_5_offline_exporting = new OrbisComponent(
                "highCharts_5_offline_exporting");
        highCharts_5_offline_exporting.setScript(
                "/core/scripts/jquery/jquery.highcharts-5.0.9/code/modules/offline-exporting.js");
        components.addComponent(highCharts_5_offline_exporting);

        OrbisComponent highCharts_5_export_csv = new OrbisComponent(
                "highCharts_5_export_csv");
        highCharts_5_export_csv.setScript(
                "/core/scripts/jquery/jquery.highcharts-5.0.9/code/modules/export-csv.js");
        components.addComponent(highCharts_5_export_csv);

        OrbisComponent highCharts_5_exporting = new OrbisComponent(
                "highCharts_5_exporting");
        highCharts_5_exporting.setScript(
                "/core/scripts/jquery/jquery.highcharts-5.0.9/code/modules/exporting.js");
        highCharts_5_exporting.addExpansion("highCharts_5_offline_exporting");
        highCharts_5_exporting.addExpansion("highCharts_5_export_csv");
        components.addComponent(highCharts_5_exporting);

        OrbisComponent highMaps_5 = new OrbisComponent("highMaps_5");
        highMaps_5.setScript(
                "/core/scripts/jquery/jquery.highmaps-5.0.9/code/custom/world.js");
        highMaps_5.addDependency("highCharts_5");
        highMaps_5.addDependency("highMaps_5_map");
        highMaps_5.addDependency("highMaps_5_data");
        components.addComponent(highMaps_5);

        OrbisComponent highMaps_5_map = new OrbisComponent("highMaps_5_map");
        highMaps_5_map.setScript(
                "/core/scripts/jquery/jquery.highmaps-5.0.9/code/modules/map.js");
        highMaps_5.addExpansion("highCharts_5_orbisExtension");
        components.addComponent(highMaps_5_map);

        OrbisComponent highMaps_5_data = new OrbisComponent("highMaps_5_data");
        highMaps_5_data.setScript(
                "/core/scripts/jquery/jquery.highmaps-5.0.9/code/modules/data.js");
        components.addComponent(highMaps_5_data);

        OrbisComponent orbisLimitMaxChar = new OrbisComponent("orbisLimitMaxChar");
        orbisLimitMaxChar.setScript(
                "/core/orbis/scripts/orbisInput/orbisLimitMaxChar-231.js");
        components.addComponent(orbisLimitMaxChar);

        OrbisComponent orbisLimitMaxCharDefault = new OrbisComponent(
                "orbisLimitMaxCharDefault");
        orbisLimitMaxCharDefault.setScript(
                "/core/orbis/scripts/orbisInput/orbisLimitMaxCharDefault-231.js");
        components.addComponent(orbisLimitMaxCharDefault);

        OrbisComponent userDetailPreferences = new OrbisComponent(
                "userDetailPreferences");
        userDetailPreferences.setScript(
                "/core/orbis/scripts/userDetailPreferences/userDetailPreferences-232.js");
        userDetailPreferences.setStylesheet(
                "/core/orbis/css/userDetailPreferences/userDetailPreferences-232.css");
        components.addComponent(userDetailPreferences);

        OrbisComponent jqueryEasingGsgd = new OrbisComponent("jqueryEasingGsgd");
        jqueryEasingGsgd.setScript(
                "/core/scripts/jquery/jquery-easing-gsgd/jquery.easing.1.3.js");
        components.addComponent(jqueryEasingGsgd);

        OrbisComponent tweet = new OrbisComponent("tweet");
        tweet.setScript("/core/scripts/twitter/jquery.tweet-231.js");
        tweet.setStylesheet("/core/scripts/twitter/jquery.tweet-231.css");
        components.addComponent(tweet);

        OrbisComponent agilityjs = new OrbisComponent("agilityjs");
        agilityjs.setScript(
                "/core/scripts/jquery/jquery.agility-0.1.2/agility.min-231.js");
        components.addComponent(agilityjs);

        OrbisComponent fullCalendar_orbisAgendaMod = new OrbisComponent(
                "fullCalendar_orbisAgendaMod");
        fullCalendar_orbisAgendaMod.setScript(
                "/core/orbis/scripts/jquery.fullCalendar-1.6.1-orbisAgendaMod/fullcalendar.min-234.js");
        fullCalendar_orbisAgendaMod.setDev(
                "/core/orbis/scripts/jquery.fullCalendar-1.6.1-orbisAgendaMod/fullcalendar.js");
        fullCalendar_orbisAgendaMod.setStylesheet(
                "/core/orbis/scripts/jquery.fullCalendar-1.6.1-orbisAgendaMod/fullcalendar-231.css");
        fullCalendar_orbisAgendaMod.addDependency("json");
        components.addComponent(fullCalendar_orbisAgendaMod);

        OrbisComponent noDoubleTapZoom = new OrbisComponent("noDoubleTapZoom");
        noDoubleTapZoom.setScript(
                "/core/scripts/jquery/jquery.mobileTouchOptions/mobileOptions-231.js");
        components.addComponent(noDoubleTapZoom);

        OrbisComponent jquery_timePickerAddon = new OrbisComponent(
                "jquery_timePickerAddon");
        jquery_timePickerAddon.setScript(
                "/core/scripts/jquery/jquery-Timepicker-Addon/jquery-ui-timepicker-addon-231.js");
        jquery_timePickerAddon.setStylesheet(
                "/core/scripts/jquery/jquery-Timepicker-Addon/jquery-ui-timepicker-addon-231.css");
        components.addComponent(jquery_timePickerAddon);

        OrbisComponent jquery_timePickerAddon_en = new OrbisComponent(
                "jquery_timePickerAddon_en");
        jquery_timePickerAddon_en.setScript(
                "/core/scripts/jquery/jquery-Timepicker-Addon/localization/jquery-ui-timepicker-en-231.js");
        jquery_timePickerAddon_en.addDependency("jquery_timePickerAddon");
        components.addComponent(jquery_timePickerAddon_en);

        OrbisComponent jquery_timePickerAddon_fr = new OrbisComponent(
                "jquery_timePickerAddon_fr");
        jquery_timePickerAddon_fr.setScript(
                "/core/scripts/jquery/jquery-Timepicker-Addon/localization/jquery-ui-timepicker-fr-1-231.js");
        jquery_timePickerAddon_fr.addDependency("jquery_datePicker_fr");
        jquery_timePickerAddon_fr.addDependency("jquery_timePickerAddon");
        components.addComponent(jquery_timePickerAddon_fr);

        OrbisComponent chosen = new OrbisComponent("chosen");
        chosen.setScript("/core/chosen/chosen.jquery.min-232.js");
        chosen.setStylesheet("/core/chosen/chosen-231.css");
        components.addComponent(chosen);

        OrbisComponent ajax_chosen = new OrbisComponent("ajax_chosen");
        ajax_chosen.setScript("/core/chosen/ajax.chosen-231.js");
        ajax_chosen.addDependency("chosen");
        components.addComponent(ajax_chosen);

        OrbisComponent select2 = new OrbisComponent("select2");
        select2.setScript("/core/scripts/select2/dist/js/select2.full.min-231.js");
        select2.setStylesheet("/core/scripts/select2/dist/css/select2.min-231.css");
        components.addComponent(select2);

        OrbisComponent date_format = new OrbisComponent("date_format");
        date_format.setScript("/core/scripts/dateFormat/date.format-231.js");
        components.addComponent(date_format);

        OrbisComponent markdown = new OrbisComponent("markdown");
        markdown.setScript("/core/scripts/markdown/markdown-231.js");
        components.addComponent(markdown);

        OrbisComponent jquery_datePicker_fr = new OrbisComponent(
                "jquery_datePicker_fr");
        jquery_datePicker_fr.setScript(
                "/core/scripts/jquery/jquery-ui-1.10.2.custom/js/jquery.ui.datepicker-fr-232.js");
        components.addComponent(jquery_datePicker_fr);

        OrbisComponent accounting = new OrbisComponent("accounting");
        accounting.setScript("/core/scripts/accounting/accounting.min-231.js");
        components.addComponent(accounting);

        OrbisComponent blockUI = new OrbisComponent("blockUI");
        blockUI.setScript(
                "/core/scripts/jquery/jquery-blockUI-2.57/jquery.blockUI-231.js");
        components.addComponent(blockUI);

        OrbisComponent tableSorter = new OrbisComponent("tableSorter");
        tableSorter.setScript(
                "/core/scripts/jquery/jquery.tablesorter-2.10.8/js/jquery.tablesorter.min-231.js");
        tableSorter.setStylesheet(
                "/core/scripts/jquery/jquery.tablesorter-2.10.8/css/theme.default-231.css");
        components.addComponent(tableSorter);

        // This is the datepicker used in spiral robot... for now...
        OrbisComponent datepicker2522 = new OrbisComponent("datepicker_2_5_22");
        datepicker2522.setScript(
                "/core/spiralRobot/scripts/prototype/datetimepicker/2.5.22/jquery.datetimepicker.full.min.js");
        datepicker2522.setDev(
                "/core/spiralRobot/scripts/prototype/datetimepicker/2.5.22/jquery.datetimepicker.full.js");
        datepicker2522.setStylesheet(
                "/core/spiralRobot/scripts/prototype/datetimepicker/2.5.22/jquery.datetimepicker.min.css");

        OrbisComponent datepicker = new OrbisComponent("datepicker");
        datepicker.setScript(
                "/core/spiralRobot/lib/datetimepicker/1.3.3/jquery.datetimepicker.full.min.js");
        datepicker.setDev(
                "/core/spiralRobot/lib/datetimepicker/1.3.3/jquery.datetimepicker.full.js");
        datepicker.setStylesheet(
                "/core/spiralRobot/lib/datetimepicker/1.3.3/jquery.datetimepicker.min.css");
        datepicker.addLegacyComponent("2.5.22", datepicker2522);
        components.addComponent(datepicker);

        OrbisComponent dateTimePicker = new OrbisComponent("dateTimePicker");
        dateTimePicker.setScript(
                "/core/bootstrap/plugins/bootstrap-datetimepicker-0.0.11/bootstrap-datetimepicker.min-232-orbisEdits.js");
        dateTimePicker.setStylesheet(
                "/core/bootstrap/plugins/bootstrap-datetimepicker-0.0.11/bootstrap-datetimepicker.min-231.css");
        dateTimePicker.addExpansion("dateTimePicker_orbisTools");
        dateTimePicker.addDependency("dateTimePicker_orbisStyles");
        components.addComponent(dateTimePicker);

        OrbisComponent dateTimePicker_orbisStyles = new OrbisComponent(
                "dateTimePicker_orbisStyles");
        dateTimePicker_orbisStyles.setStylesheet(
                "/core/orbis/css/dateTimePicker_orbisStyles-232.css");
        components.addComponent(dateTimePicker_orbisStyles);

        OrbisComponent dateTimePicker_orbisTools = new OrbisComponent(
                "dateTimePicker_orbisTools");
        dateTimePicker_orbisTools
                .setScript("/core/orbis/scripts/dateTimePicker_orbisTools-244.js");
        components.addComponent(dateTimePicker_orbisTools);

        OrbisComponent orbisDatePicker = new OrbisComponent("orbisDatePicker");
        orbisDatePicker.setScript("/core/orbis/scripts/orbisDatePicker.js");
        orbisDatePicker.addDependency("dateTimePicker");
        components.addComponent(orbisDatePicker);

        OrbisComponent orbisBilingualOptions = new OrbisComponent(
                "orbisBilingualOptions");
        orbisBilingualOptions.setScript(
                "/core/orbis/scripts/orbisBilingualOptions/orbisBilingualOptions-239.js");
        orbisBilingualOptions.setStylesheet(
                "/core/orbis/scripts/orbisBilingualOptions/orbisBilingualOptions-237.css");
        orbisBilingualOptions.addDependency("orbisBilingualOptions_locale");
        components.addComponent(orbisBilingualOptions);

        OrbisComponent orbisBilingualOptions_locale = new OrbisComponent(
                "orbisBilingualOptions_locale");
        orbisBilingualOptions_locale.setScript(
                "/core/orbis/scripts/orbisBilingualOptions/orbisBilingualOptions_${locale}-002.js");
        components.addComponent(orbisBilingualOptions_locale);

        OrbisComponent momentjs = new OrbisComponent("momentjs");
        momentjs.setScript("/core/spiralRobot/lib/momentjs/2.22.1/moment.min.js");

        OrbisComponent momentjsBootstrap = new OrbisComponent("momentjs");
        momentjsBootstrap.setScript("/core/scripts/moment/moment.min-231.js");
        components.addComponent(momentjsBootstrap);

        momentjs.addLegacyComponent("bootstrap", momentjsBootstrap);
        components.addComponent(momentjs);

        OrbisComponent perfectScrollbar = new OrbisComponent("perfectScrollbar");
        perfectScrollbar.setScript(
                "/core/spiralRobot/lib/perfect-scrollbar/1.4.0/perfect-scrollbar.min.js");
        perfectScrollbar.setDev(
                "/core/spiralRobot/lib/perfect-scrollbar/1.4.0/perfect-scrollbar.js");
        perfectScrollbar.setStylesheet(
                "/core/spiralRobot/lib/perfect-scrollbar/1.4.0/perfect-scrollbar.css");
        perfectScrollbar.addExpansion("perfectScrollbarJqueryWrapper");
        components.addComponent(perfectScrollbar);

        OrbisComponent perfectScrollbarJqueryWrapper = new OrbisComponent(
                "perfectScrollbarJqueryWrapper");
        perfectScrollbarJqueryWrapper.setScript(
                "/core/spiralRobot/lib/perfect-scrollbar/1.4.0/perfectScrollbarJqueryWrapper.js");
        components.addComponent(perfectScrollbarJqueryWrapper);

        OrbisComponent vue2animate = new OrbisComponent("vue2animate");
        vue2animate.setStylesheet(
                "/core/spiralRobot/lib/vue2animate/1.0/vue2-animate.min.css");
        components.addComponent(vue2animate);

        OrbisComponent socicon = new OrbisComponent("socicon");
        socicon.setStylesheet("/core/spiralRobot/lib/socicon/3.6.2/style.css");
        components.addComponent(socicon);

        OrbisComponent jsoneditor = new OrbisComponent("jsoneditor");
        jsoneditor.setScript(
                "/core/spiralRobot/lib/jsoneditor/5.19.0/jsoneditor.min.js");
        jsoneditor.setStylesheet(
                "/core/spiralRobot/lib/jsoneditor/5.19.0/jsoneditor.min.css");
        components.addComponent(jsoneditor);

        OrbisComponent rich_marker = new OrbisComponent("rich_marker");
        rich_marker
                .setScript("/core/spiralRobot/lib/richmarker/1.0.0/richmarker.js");
        components.addComponent(rich_marker);

        OrbisComponent pdfjs_textlayerbuilder = new OrbisComponent(
                "pdfjs_textlayerbuilder");
        pdfjs_textlayerbuilder
                .setScript("/core/scripts/pdfjs/build/text.layer.builder.js");
        components.addComponent(pdfjs_textlayerbuilder);

        OrbisComponent nested_sortable = new OrbisComponent("nested_sortable");
        nested_sortable.setScript(
                "/core/scripts/jquery/jquery.mjs.nestedSortable/jquery.mjs.nestedSortable.js");
        components.addComponent(nested_sortable);

        OrbisComponent pdfjs = new OrbisComponent("pdfjs");
        pdfjs.setScript("/core/scripts/pdfjs/build/pdf.js");
        pdfjs.addDependency("pdfjs_textlayerbuilder");
        components.addComponent(pdfjs);

        OrbisComponent tagsinput = new OrbisComponent("tagsinput");
        tagsinput.setScript(
                "/core/scripts/jquery/jquery.tagsinput-1.3.5/jquery.tagsinput.custom.js");
        tagsinput.setStylesheet(
                "/core/scripts/jquery/jquery.tagsinput-1.3.5/jquery.tagsinput.min.css");
        components.addComponent(tagsinput);

        OrbisComponent vuejs_3 = new OrbisComponent("vuejs_3");
        vuejs_3.setScript(
                "/core/spiralRobot/scripts/prototype/vue/3.5.13/vue.global.prod.js");
        vuejs_3.setDev(
                "/core/spiralRobot/scripts/prototype/vue/3.5.13/vue.global.js");

        OrbisComponent vuejs = new OrbisComponent("vuejs");
        vuejs.setScript("/core/spiralRobot/lib/vuejs/2.5.17/vue.min.js");
        vuejs.setDev("/core/spiralRobot/lib/vuejs/2.5.17/vue.js");

        vuejs.addLegacyComponent("3", vuejs_3);

        components.addComponent(vuejs);

        OrbisComponent vuex = new OrbisComponent("vuex");
        vuex.setScript("/core/spiralRobot/scripts/prototype/vuex/3.6.2/vuex.js");
        components.addComponent(vuex);

        OrbisComponent sortable_1_7_0 = new OrbisComponent("sortable_1.7.0");
        sortable_1_7_0.setScript("/core/scripts/Sortable.min.js");

        OrbisComponent sortable = new OrbisComponent("sortable");
        sortable.setScript(
                "/core/spiralRobot/scripts/prototype/sortableJS/1.15.0/Sortable.min.js");
        sortable.setDev(
                "/core/spiralRobot/scripts/prototype/sortableJS/1.15.0/Sortable.js");
        sortable.addLegacyComponent("1.7.0", sortable_1_7_0);
        components.addComponent(sortable);

        OrbisComponent vuedraggable = new OrbisComponent("vuedraggable");
        vuedraggable.setScript(
                "/core/spiralRobot/scripts/prototype/vue3-draggable-next/4.1.4/vuedraggable.umd.min.js");
        vuedraggable.setDev(
                "/core/spiralRobot/scripts/prototype/vue3-draggable-next/4.1.4/vuedraggable.umd.js");
        vuedraggable.addDependency("vuejs", "3");
        vuedraggable.addDependency("sortable");
        components.addComponent(vuedraggable);

        OrbisComponent vuePortal = new OrbisComponent("vuePortal");
        vuePortal.setScript(
                "/core/spiralRobot/scripts/prototype/vuePortal/2.1.7/portal-vue.umd.min.js");
        components.addComponent(vuePortal);

        OrbisComponent veeValidate = new OrbisComponent("veeValidate");
        veeValidate.setScript(
                "/core/spiralRobot/scripts/prototype/veeValidate/4.15.0/vee-validate.iife.prod.js.mjs");
        veeValidate.setDev(
                "/core/spiralRobot/scripts/prototype/veeValidate/4.15.0/vee-validate.iife.js");
        veeValidate.addDependency("vuejs", "3");
        components.addComponent(veeValidate);

        OrbisComponent yup = new OrbisComponent("yup");
        yup.setScript("/core/spiralRobot/scripts/prototype/yup/1.6.1/index.min.js");
        yup.setDev("/core/spiralRobot/scripts/prototype/yup/1.6.1/index.js");
        components.addComponent(yup);

        OrbisComponent veeValidateRules = new OrbisComponent("veeValidateRules");
        veeValidateRules.setScript(
                "/core/spiralRobot/scripts/prototype/veeValidateRules/4.15.0/vee-validate-rules.iife.min.js");
        veeValidateRules.setDev(
                "/core/spiralRobot/scripts/prototype/veeValidateRules/4.15.0/vee-validate-rules.iife.js");
        veeValidateRules.addDependency("veeValidate");
        components.addComponent(veeValidateRules);

        OrbisComponent popperjs = new OrbisComponent("popperjs");
        popperjs.setScript(
                "/core/spiralRobot/scripts/prototype/popperjs/2.9.2/popper.min.js");
        components.addComponent(popperjs);

        OrbisComponent vCalendar = new OrbisComponent("vCalendar");
        vCalendar.setScript(
                "/core/spiralRobot/scripts/prototype/vCalendar/3.0.0-alpha.8/v-calendar.umd.min.js");
        vCalendar.setStylesheet(
                "/core/spiralRobot/scripts/prototype/vCalendar/3.0.0-alpha.8/style.css");
        vCalendar.addDependency("vuejs", "3");
        vCalendar.addDependency("popperjs");
        components.addComponent(vCalendar);

        OrbisComponent vuei18n = new OrbisComponent("vuei18n");
        vuei18n.setScript(
                "/core/spiralRobot/scripts/prototype/vueI18N/8.25.0/vue-i18n.js");
        components.addComponent(vuei18n);

        OrbisComponent lodash = new OrbisComponent("lodash");
        lodash.setScript("/core/scripts/lodash.js");
        components.addComponent(lodash);

        OrbisComponent orbisNotePopups = new OrbisComponent("orbisNotePopups");
        orbisNotePopups.setScript("/core/orbis/scripts/orbisNotePopups-2.js");
        orbisNotePopups.addDependency("printElement");
        components.addComponent(orbisNotePopups);

        OrbisComponent orbisFileUpload = new OrbisComponent("orbisFileUpload");
        orbisFileUpload.setScript(
                "/core/orbis/scripts/orbisFileUpload/orbisFileUpload-3.js");

        OrbisComponent orbisFileUpload_bootstrap = new OrbisComponent(
                "orbisFileUpload_bootstrap");
        orbisFileUpload_bootstrap.setScript(
                "/core/orbis/scripts/orbisFileUpload/orbisFileUpload_bootstrap-3.js");
        orbisFileUpload.addLegacyComponent("bootstrap", orbisFileUpload_bootstrap);
        components.addComponent(orbisFileUpload);

        OrbisComponent html2canvas = new OrbisComponent("html2canvas");
        html2canvas.setScript(
                "/core/spiralRobot/lib/html2canvas/1.0.0-alpha.12/html2canvas.min.js");
        html2canvas.setDev(
                "/core/spiralRobot/lib/html2canvas/1.0.0-alpha.12/html2canvas.js");
        components.addComponent(html2canvas);

        OrbisComponent jspdf = new OrbisComponent("jspdf");
        jspdf.setScript("/core/spiralRobot/lib/jsPDF/1.4.1/jspdf.min.js");
        jspdf.setDev("/core/spiralRobot/lib/jsPDF/1.4.1/jspdf.debug.js");
        components.addComponent(jspdf);

        OrbisComponent html5shiv = new OrbisComponent("html5shiv");
        html5shiv.setScript(
                "/core/spiralRobot/lib/html5shiv/3.7.3/html5shiv.min.js");
        html5shiv.setDev("/core/spiralRobot/lib/html5shiv/3.7.3/html5shiv.js");
        components.addComponent(html5shiv);

        OrbisComponent chartjs = new OrbisComponent("chartjs");
        chartjs.setScript("/core/spiralRobot/lib/js/Chart.min.js");
        chartjs.addDependency("momentjs");
        components.addComponent(chartjs);

        OrbisComponent simpleLineChart = new OrbisComponent("simpleLineChart");
        simpleLineChart.setScript(
                "/core/spiralRobot/scripts/simpleLineChart/0.0/simpleLineChart.js");
        simpleLineChart.addDependency("d3");
        components.addComponent(simpleLineChart);

        OrbisComponent striptags = new OrbisComponent("striptags");
        striptags.setScript(
                "/core/spiralRobot/scripts/prototype/striptags/3.1.1/striptags.js");
        components.addComponent(striptags);

        OrbisComponent resizeSensor = new OrbisComponent("resizeSensor");
        resizeSensor.setScript(
                "/core/spiralRobot/scripts/prototype/resizeSensor/1.0/ResizeSensor.js");
        components.addComponent(resizeSensor);

        OrbisComponent elementQuery = new OrbisComponent("elementQuery");
        elementQuery.setScript(
                "/core/spiralRobot/scripts/prototype/elementQueries/1.0/ElementQueries.js");
        elementQuery.addDependency("resizeSensor");
        components.addComponent(elementQuery);

        OrbisComponent hammerjs = new OrbisComponent("hammerjs");
        hammerjs.setScript(
                "/core/spiralRobot/scripts/prototype/hammerjs/2.0.8/hammer.min.js");
        hammerjs.setDev(
                "/core/spiralRobot/scripts/prototype/hammerjs/2.0.8/hammer.js");
        components.addComponent(hammerjs);

        OrbisComponent webAnimationjs = new OrbisComponent("webAnimationjs");
        webAnimationjs.setScript(
                "/core/spiralRobot/scripts/prototype/webAnimationjs/2.3.1/web-animations.min.js");
        components.addComponent(webAnimationjs);

        OrbisComponent muuri = new OrbisComponent("muuri");

        muuri.setScript(
                "/core/spiralRobot/scripts/prototype/muuri/0.7.1/muuri.min.js");
        muuri.setDev("/core/spiralRobot/scripts/prototype/muuri/0.7.1/muuri.js");
        muuri.addDependency("hammerjs");
        muuri.addDependency("webAnimationjs");
        components.addComponent(muuri);

        OrbisComponent cropperjs = new OrbisComponent("cropperjs");
        cropperjs.setScript(
                "/core/spiralRobot/scripts/prototype/cropperjs/1.4.3/cropper.js");
        cropperjs.setDev(
                "/core/spiralRobot/scripts/prototype/cropperjs/1.4.3/cropper.min.js");
        components.addComponent(cropperjs);

        OrbisComponent cropperjq = new OrbisComponent("cropperjq");
        cropperjq.setScript(
                "/core/spiralRobot/scripts/prototype/jquery-cropper/1.0.0/jquery-cropper.js");
        cropperjq.setDev(
                "/core/spiralRobot/scripts/prototype/jquery-cropper/1.0.0/jquery-cropper.min.js");
        cropperjq.addDependency("cropperjs");
        components.addComponent(cropperjq);

        OrbisComponent aceEditor = new OrbisComponent("aceEditor");
        aceEditor.setScript(
                "/core/spiralRobot/scripts/prototype/ace/1.4.4/src-min-noconflict/ace.js");
        aceEditor.setDev(
                "/core/spiralRobot/scripts/prototype/ace/1.4.4/src-noconflict/ace.js");
        components.addComponent(aceEditor);

        addUiTagScripts();
        addDataViewer();
        addFullCalendar();

        OrbisComponent vue2PerfectScrollbar = new OrbisComponent(
                "vue2PerfectScrollbar");
        vue2PerfectScrollbar.setScript(
                "/core/spiralRobot/scripts/vue2-perfect-scrollbar/vue2-perfect-scrollbar.umd.min.js");
        vue2PerfectScrollbar.setStylesheet(
                "/core/spiralRobot/scripts/vue2-perfect-scrollbar/vue2-perfect-scrollbar.min.css");
        vue2PerfectScrollbar.setDev(
                "/core/spiralRobot/scripts/vue2-perfect-scrollbar/vue2-perfect-scrollbar.umd.js");
        components.addComponent(vue2PerfectScrollbar);

        OrbisComponent vue3PerfectScrollbar = new OrbisComponent(
                "vue3PerfectScrollbar");
        vue3PerfectScrollbar.setScript(
                "/core/spiralRobot/scripts/vue3-perfect-scrollbar/1.6.0/vue3-perfect-scrollbar.umd.min.js");
        vue3PerfectScrollbar.setStylesheet(
                "/core/spiralRobot/scripts/vue3-perfect-scrollbar/1.6.0/vue3-perfect-scrollbar.min.css");
        vue3PerfectScrollbar.setDev(
                "/core/spiralRobot/scripts/vue3-perfect-scrollbar/1.6.0/vue3-perfect-scrollbar.umd.js");
        components.addComponent(vue3PerfectScrollbar);

        OrbisComponent toggleButton = new OrbisComponent("toggleButton");
        toggleButton
                .setScript("/core/orbis/scripts/toggleButton/0.1/toggleButton.js");
        toggleButton.setStylesheet(
                "/core/orbis/scripts/toggleButton/0.1/toggleButton.css");
        components.addComponent(toggleButton);

        OrbisComponent tripleLookup = new OrbisComponent("tripleLookup");
        tripleLookup
                .setScript("/core/orbis/scripts/tripleLookup/0.0/tripleLookup.js");
        components.addComponent(tripleLookup);

        OrbisComponent oms = new OrbisComponent("oms");
        oms.setScript("/core/spiralRobot/scripts/prototype/oms/1.0.0/oms.min.js");
        components.addComponent(oms);

        OrbisComponent expSr = new OrbisComponent("expSr");
        expSr.setScript("/core/orbis/scripts/expSr/0.2/expSr.js");
        expSr.addDependency("oms");
        expSr.addDependency("lodash");
        components.addComponent(expSr);

        OrbisComponent exp = new OrbisComponent("exp");
        exp.setScript("/core/orbis/scripts/exp/0.2/exp.js");
        exp.addDependency("oms");
        exp.addDependency("lodash");
        components.addComponent(exp);

        OrbisComponent dfEditor = new OrbisComponent("dfEditor");
        dfEditor.setScript("/core/spiralRobot/scripts/dfEditor/js/app.js");
        dfEditor.setStylesheet(
                "/core/spiralRobot/scripts/dfEditor/css/question-framework.css");
        dfEditor.addDependency("vuejs");
        dfEditor.addDependency("lodash");
        components.addComponent(dfEditor);

        OrbisComponent fontAwesome = new OrbisComponent("fontAwesome");
        fontAwesome.setStylesheet(
                "/core/bootstrap/font-awesome/css/font-awesome.min-231.css");
        components.addComponent(fontAwesome);

        OrbisComponent dataTables = new OrbisComponent("dataTables");
        dataTables.setScript(
                "/core/scripts/jquery/jquery-datatables-1.10.16/jquery.dataTables.min.js");
        dataTables.setStylesheet(
                "/core/scripts/jquery/jquery-datatables-1.10.16/jquery.dataTables.min.css");
        components.addComponent(dataTables);

        OrbisComponent vue3Draggable = new OrbisComponent("vue3Draggable");
        vue3Draggable.setScript(
                PROTO_PATH + "vue3-draggable/4.0.1/vuedraggable.umd.min.js");
        vue3Draggable
                .setDev(PROTO_PATH + "vue3-draggable/4.0.1/vuedraggable.umd.js");
        vue3Draggable.addDependency("sortable");
        vue3Draggable.addDependency("vuejs", "3");
        components.addComponent(vue3Draggable);

        OrbisComponent vue3EasyDataTable = new OrbisComponent(
                "vue3-easy-data-table");
        vue3EasyDataTable.setScript(PROTO_PATH
                + "vue3-easy-data-table/1.5.47/vue3-easy-data-table.umd.js");
        vue3EasyDataTable.setStylesheet(
                PROTO_PATH + "vue3-easy-data-table/1.5.47/style.css");
        vue3EasyDataTable.addDependency("vuejs", "3");
        components.addComponent(vue3EasyDataTable);

        OrbisComponent virtualSelect = new OrbisComponent("virtualSelect");
        virtualSelect.setScript(
                "/core/spiralRobot/scripts/prototype/virtualSelect/1.0.0/virtual-select.min.js");
        virtualSelect.setStylesheet(
                "/core/spiralRobot/scripts/prototype/virtualSelect/1.0.0/virtual-select.min.css");
        components.addComponent(virtualSelect);

        String filterBarDir = "/core/spiralRobot/scripts/filterBar/0.1.8/";
        OrbisComponent filterBar = new OrbisComponent("filterBar");
        filterBar.setScript(filterBarDir + "filter-bar.iife.js");
        filterBar.setStylesheet(filterBarDir + "style.css");
        filterBar.addDependency("vuejs", "3");
        components.addComponent(filterBar);

        addBadgeCreator();
    }

    public static OrbisComponent getComponent(String componentKey, String version)
    {
        // Uncomment to reset scripts without a restart. Recomment before
        // commit.
        // initComponents();
        OrbisComponent component = null;
        if (!StringUtils.isEmpty(componentKey)
                && components.hasComponent(componentKey))
        {
            component = components.getComponent(componentKey);
            if (!StringUtils.isEmpty(version) && component != null
                    && component.hasLegacy(version))
            {
                component = component.getLegacy(version);
            }
        }

        return component;
    }

    public static String buildComponentUrl(String componentPath)
    {
        PortalConfig resourcesUrlConf = PortalConfigHelper
                .getPortalConfig(PortalConfig.RESOURCES_URL);

        return (isLocalComponent(componentPath) ? ""
                : resourcesUrlConf.getOrbisValue().trim()) + componentPath;
    }

    public static String buildStaticComponentUrl(String componentPath)
    {
        return (isLocalComponent(componentPath) ? ""
                : "https://orbisv4head.blob.core.windows.net") + componentPath;
    }

    public static boolean isLocalComponent(String componentPath)
    {
        return !StringUtils.isEmpty(componentPath) && localDirs.stream()
                .anyMatch(dir -> componentPath.startsWith(dir));
    }

    public static void addLoadedComponent(OrbisComponent component,
            HttpServletRequest request)
    {
        if (request.getSession().getAttribute(ORBIS_COMPONENTS_LOADED) == null)
        {
            request.getSession().setAttribute(ORBIS_COMPONENTS_LOADED,
                    new HashSet<String>());
        }

        if (request.getAttribute(ORBIS_COMPONENTS_LOADED) == null)
        {
            request.setAttribute(ORBIS_COMPONENTS_LOADED, new HashSet<String>());
        }

        ((HashSet<String>) request.getSession()
                .getAttribute(ORBIS_COMPONENTS_LOADED)).add(component.getKey());

        ((HashSet<String>) request.getAttribute(ORBIS_COMPONENTS_LOADED))
                .add(component.getKey());
    }

    public static void resetLoadedComponents(HttpServletRequest request)
    {
        request.getSession().removeAttribute(ORBIS_COMPONENTS_LOADED);
        request.removeAttribute(ORBIS_COMPONENTS_LOADED);
        request.setAttribute(ORBIS_COMPONENTS_RESET, true);
    }

    public static boolean isComponentLoadedInSession(OrbisComponent component,
            HttpServletRequest request)
    {
        return request.getSession().getAttribute(ORBIS_COMPONENTS_LOADED) != null
                && ((HashSet<String>) request.getSession()
                        .getAttribute(ORBIS_COMPONENTS_LOADED))
                        .contains(component.getKey());
    }

    public static boolean isComponentLoadedInRequest(OrbisComponent component,
            HttpServletRequest request)
    {
        return request.getAttribute(ORBIS_COMPONENTS_LOADED) != null
                && ((HashSet<String>) request.getAttribute(ORBIS_COMPONENTS_LOADED))
                        .contains(component.getKey());
    }

    private static void addJqueryValidateComponent()
    {
        OrbisComponent jqueryValidate = new OrbisComponent("jqueryValidate");
        jqueryValidate.setScript(
                "/core/spiralRobot/lib/validate/1.15.1/jquery.validate.min.js");
        jqueryValidate
                .setDev("/core/spiralRobot/lib/validate/1.15.1/jquery.validate.js");
        jqueryValidate.addExpansion("jqueryValidate_localization");
        jqueryValidate.addExpansion("jqueryValidate_additionalMethods");
        jqueryValidate.addExpansion("orbisValidationRules");

        OrbisComponent jqueryValidate_1_11_1 = new OrbisComponent(
                "jqueryValidate_1_11_1");
        jqueryValidate_1_11_1.setScript(
                "/core/scripts/jquery/jquery-validation-1.11.1/jquery.validate.min-231.js");
        jqueryValidate_1_11_1.setDev(
                "/core/scripts/jquery/jquery-validation-1.11.1/jquery.validate-231.js");
        jqueryValidate_1_11_1.addDependency("jqueryValidateOrbisSettings");
        jqueryValidate_1_11_1.addExpansion("jqueryValidate_localization", "1.11.1");
        jqueryValidate_1_11_1.addExpansion("jqueryValidate_additionalMethods",
                "1.11.1");
        jqueryValidate.addLegacyComponent("1.11.1", jqueryValidate_1_11_1);

        components.addComponent(jqueryValidate);

        OrbisComponent jqueryValidate_localization = new OrbisComponent(
                "jqueryValidate_localization");
        jqueryValidate_localization.setScript(
                "/core/spiralRobot/lib/validate/1.15.1/localization/messages_${locale}.min.js");
        jqueryValidate_localization.setDev(
                "/core/spiralRobot/lib/validate/1.15.1/localization/messages_${locale}.js");

        OrbisComponent jqueryValidate_localization_1_11_1 = new OrbisComponent(
                "jqueryValidate_localization_1_11_1");
        jqueryValidate_localization_1_11_1.setScript(
                "/core/scripts/jquery/jquery-validation-1.11.1/localization/messages_${locale}-231.js");
        jqueryValidate_localization.addLegacyComponent("1.11.1",
                jqueryValidate_localization_1_11_1);

        components.addComponent(jqueryValidate_localization);

        OrbisComponent jqueryValidate_additionalMethods = new OrbisComponent(
                "jqueryValidate_additionalMethods");
        jqueryValidate_additionalMethods.setScript(
                "/core/spiralRobot/lib/validate/1.15.1/additional-methods.min.js");
        jqueryValidate_additionalMethods.setDev(
                "/core/spiralRobot/lib/validate/1.15.1/additional-methods.js");
        jqueryValidate_additionalMethods.addDependency("jqueryValidate");

        OrbisComponent jqueryValidate_additionalMethods_1_11_1 = new OrbisComponent(
                "jqueryValidate_additionalMethods_1_11_1");
        jqueryValidate_additionalMethods_1_11_1.setScript(
                "/core/scripts/jquery/jquery-validation-1.11.1/additional-methods.min-231.js");
        jqueryValidate_additionalMethods_1_11_1.setDev(
                "/core/scripts/jquery/jquery-validation-1.11.1/additional-methods-231.js");
        jqueryValidate_additionalMethods_1_11_1.addDependency("jqueryValidate",
                "1.11.1");
        jqueryValidate_additionalMethods.addLegacyComponent("1.11.1",
                jqueryValidate_additionalMethods_1_11_1);

        components.addComponent(jqueryValidate_additionalMethods);

        OrbisComponent jqueryValidateOrbisSettings = new OrbisComponent(
                "jqueryValidateOrbisSettings");
        jqueryValidateOrbisSettings
                .setScript("/core/orbis/scripts/orbisValidateSettings-246.js");
        jqueryValidateOrbisSettings
                .setStylesheet("/core/orbis/scripts/orbisValidateSettings-240.css");
        components.addComponent(jqueryValidateOrbisSettings);

        OrbisComponent orbisValidationRules = new OrbisComponent(
                "orbisValidationRules");
        orbisValidationRules
                .setScript("/core/orbis/scripts/orbisValidationRules-1.js");
        components.addComponent(orbisValidationRules);

    }

    private static void addDataViewer()
    {
        String dataViewerDir = "/core/spiralRobot/scripts/dataViewer/0.10.3/";
        OrbisComponent dataViewerOutcomeStyles = new OrbisComponent(
                "dataViewerOutcomeStyles");
        dataViewerOutcomeStyles.setStylesheet(
                "/core/spiralRobot/scripts/dataViewerOutcome/1.5.3/DataViewerOutcome.css");
        components.addComponent(dataViewerOutcomeStyles);

        OrbisComponent dataViewer = new OrbisComponent("dataViewer");
        dataViewer.setScript(dataViewerDir + "DataViewer.umd.min.js");
        dataViewer.setDev(dataViewerDir + "DataViewer.umd.js");
        dataViewer.setStylesheet(dataViewerDir + "DataViewer.css");
        dataViewer.addDependency("vuejs", "3");
        dataViewer.addExpansion("dataViewerOutcomeStyles");
        components.addComponent(dataViewer);

    }

    private static void addFullCalendar()
    {
        OrbisComponent fullCalendar = new OrbisComponent("fullCalendar");

        fullCalendar.setScript(
                "/core/spiralRobot/lib/fullcalendar/3.9.0/fullcalendar.min.js");
        fullCalendar
                .setDev("/core/spiralRobot/lib/fullcalendar/3.9.0/fullcalendar.js");
        fullCalendar.setStylesheet(
                "/core/spiralRobot/lib/fullcalendar/3.9.0/fullcalendar.css");
        fullCalendar.addDependency("momentjs");
        // fullCalendar.addExpansion("scheduler");

        OrbisComponent fullCal_1_6_1 = new OrbisComponent("fullCal_1_6_1");
        fullCal_1_6_1.setScript(
                "/core/scripts/jquery/jquery.fullCalendar-1.6.1/fullcalendar.min-231.js");
        fullCal_1_6_1.setStylesheet(
                "/core/scripts/jquery/jquery.fullCalendar-1.6.1/fullcalendar-231.css");
        fullCalendar.addLegacyComponent("1.6.1", fullCal_1_6_1);

        OrbisComponent fullCalendarLocales = new OrbisComponent(
                "fullCalendarLocales");
        fullCalendarLocales.setScript(
                "/core/spiralRobot/lib/fullcalendar/3.9.0/locale-all.js");

        OrbisComponent scheduler = new OrbisComponent("scheduler");

        scheduler.setScript(
                "/core/spiralRobot/lib/fullcalendar-scheduler/1.9.4/scheduler.min.js");
        scheduler.setDev(
                "/core/spiralRobot/lib/fullcalendar-scheduler/1.9.4/scheduler.js");
        scheduler.setStylesheet(
                "/core/spiralRobot/lib/fullcalendar-scheduler/1.9.4/scheduler.min.css");
        scheduler.addDependency("fullCalendar");

        components.addComponent(fullCalendar);
        components.addComponent(fullCalendarLocales);
        components.addComponent(scheduler);
    }

    private static void addBadgeCreator()
    {
        String fileNameFormat = "/core/spiralRobot/scripts/badgeCreator/badge-creator.%s.150921.%s";

        OrbisComponent badgeCreator = new OrbisComponent("badgeCreator");
        badgeCreator.setScript(String.format(fileNameFormat, "app", "js"));
        badgeCreator.setStylesheet(String.format(fileNameFormat, "app", "css"));
        badgeCreator.addDependency("vuejs");
        badgeCreator.addDependency("vuex");
        badgeCreator.addDependency("vuei18n");
        badgeCreator.addDependency("badgeCreatorVendorChunks");
        components.addComponent(badgeCreator);

        OrbisComponent badgeCreatorVendorChunk = new OrbisComponent(
                "badgeCreatorVendorChunks");
        badgeCreatorVendorChunk
                .setScript(String.format(fileNameFormat, "chunk-vendors", "js"));
        badgeCreatorVendorChunk.setStylesheet(
                String.format(fileNameFormat, "chunk-vendors", "css"));
        components.addComponent(badgeCreatorVendorChunk);
    }

    private static void addUiTagScripts()
    {
        OrbisComponent uiSidebar = new OrbisComponent("uiSidebar");
        uiSidebar.setScript("/core/spiralRobot/scripts/uiSidebar/0.6/uiSidebar.js");
        uiSidebar.addDependency("perfectScrollbar");
        components.addComponent(uiSidebar);

        OrbisComponent uiBottombar = new OrbisComponent("uiBottombar");
        uiBottombar.setScript(
                "/core/spiralRobot/scripts/uiBottombar/0.0/uiBottombar.js");
        components.addComponent(uiBottombar);

        OrbisComponent uiForm = new OrbisComponent("uiForm");
        uiForm.setScript("/core/spiralRobot/scripts/uiForm/0.3/uiForm.js");
        uiForm.addDependency("jqueryValidate");
        components.addComponent(uiForm);

        OrbisComponent uiTabs = new OrbisComponent("uiTabs");
        uiTabs.setScript("/core/spiralRobot/scripts/uiTabs/0.1/uiTabs.js");
        components.addComponent(uiTabs);

        OrbisComponent srComponents = new OrbisComponent("srComponents");
        srComponents.setScript(
                "/core/spiralRobot/scripts/srComponents/0.11.3/SRComponents.min.js");
        srComponents.setStylesheet(
                "/core/spiralRobot/scripts/srComponents/0.11.3/SRComponents.min.css");
        srComponents.addDependency("vuejs", "3");
        components.addComponent(srComponents);

        OrbisComponent calendarExplorer = new OrbisComponent("calendarExplorer");
        calendarExplorer.setScript(
                "/core/spiralRobot/scripts/calendarExplorer/0.0.6/CalendarExplorer.min.js");
        calendarExplorer.setStylesheet(
                "/core/spiralRobot/scripts/calendarExplorer/0.0.6/CalendarExplorer.min.css");
        calendarExplorer.addDependency("vuejs", "3");
        components.addComponent(calendarExplorer);

        OrbisComponent uiCheckboxGroup = new OrbisComponent("uiCheckboxGroup");
        uiCheckboxGroup.setScript(
                "/core/spiralRobot/scripts/uiCheckboxGroup/0.5/uiCheckboxGroup.js");
        components.addComponent(uiCheckboxGroup);

        OrbisComponent uiDropdown = new OrbisComponent("uiDropdown");
        uiDropdown.setScript(
                "/core/spiralRobot/scripts/uiDropdown/0.2/uiDropdown.js");
        components.addComponent(uiDropdown);

        OrbisComponent uiSelect = new OrbisComponent("uiSelect");
        uiSelect.setScript("/core/spiralRobot/scripts/uiSelect/0.1/uiSelect.js");
        components.addComponent(uiSelect);

        OrbisComponent uiCalendar = new OrbisComponent("uiCalendar");
        uiCalendar.setScript(
                "/core/spiralRobot/scripts/uiCalendar/0.2/uiCalendar.js");
        uiCalendar.addDependency("fullCalendar");
        uiCalendar.addDependency("fullCalendarLocales");
        components.addComponent(uiCalendar);

        OrbisComponent uiAccordion = new OrbisComponent("uiAccordion");
        uiAccordion.setScript(
                "/core/spiralRobot/scripts/uiAccordion/0.1/uiAccordion.js");
        components.addComponent(uiAccordion);

        OrbisComponent uiExpandable = new OrbisComponent("uiExpandable");
        uiExpandable.setScript(
                "/core/spiralRobot/scripts/uiExpandable/0.0/uiExpandable.js");
        components.addComponent(uiExpandable);

        OrbisComponent uiManageItemList = new OrbisComponent("uiManageItemList");
        uiManageItemList.setScript(
                "/core/spiralRobot/scripts/uiManageItemList/0.0/uiManageItemList.js");
        uiManageItemList.addDependency("muuri");
        components.addComponent(uiManageItemList);

        OrbisComponent uiWizard = new OrbisComponent("uiWizard");
        uiWizard.setScript("/core/spiralRobot/scripts/uiWizard/0.4/uiWizard.js");
        uiWizard.addDependency("vuejs");
        uiWizard.addDependency("uiForm");
        uiWizard.addDependency("jqueryExtensions");
        uiWizard.addDependency("elementQuery");
        components.addComponent(uiWizard);

        OrbisComponent uiSortable = new OrbisComponent("uiSortable");
        uiSortable.setScript(
                "/core/spiralRobot/scripts/uiSortable/0.0/uiSortable.js");
        uiSortable.addDependency("jqueryExtensions");
        components.addComponent(uiSortable);

        OrbisComponent configTable = new OrbisComponent("uiConfigTable");
        configTable.setScript(
                "/core/spiralRobot/scripts/uiConfigTable/0.0/uiConfigTable.js");
        components.addComponent(configTable);

        OrbisComponent srSkeleton = new OrbisComponent("srSkeleton");
        srSkeleton.setScript(
                "/core/spiralRobot/scripts/srSkeleton/0.0/srSkeleton.js");
        components.addComponent(srSkeleton);

        OrbisComponent uiTripleLookup = new OrbisComponent("uiTripleLookup");
        uiTripleLookup.setScript(
                "/core/spiralRobot/scripts/uiTripleLookup/0.0/uiTripleLookup.js");
        components.addComponent(uiTripleLookup);

        OrbisComponent uiTour = new OrbisComponent("uiTour");
        uiTour.setScript("/core/spiralRobot/scripts/uiTour/0.0/uiTour.js");
        components.addComponent(uiTour);

        OrbisComponent uiChartCard = new OrbisComponent("uiChartCard");
        uiChartCard.setScript(
                "/core/spiralRobot/scripts/uiChartCard/0.1/uiChartCard.js");
        uiChartCard.addDependency("highCharts_5");
        uiChartCard.addDependency("vuejs");
        uiChartCard.addDependency("d3");
        uiChartCard.addDependency("d3ScaleChromatic");
        components.addComponent(uiChartCard);

        OrbisComponent spiralRobotTagLib = new OrbisComponent("spiralRobotTagLib");
        spiralRobotTagLib.setScript(
                "/core/spiralRobot/scripts/SpiralRobotTagLib-25032025.js");
        components.addComponent(spiralRobotTagLib);

    }

    public static void main(String[] args)
    {
        System.out.println(getComponentIncludes("highCharts_5", null));
    }

    private static HashSet<String> loadedComponents = new HashSet<>();

    private static final String STRUCTURE_SCRIPT = "<script type='text/javascript' src='${script}' charset='UTF-8'></script>\n";

    private static final String STRUCTURE_STYLESHEET = "<link rel='stylesheet' type='text/css' href='${stylesheet}'>\n";

    private static String getComponentIncludes(String componentKey, String version)
    {
        StringBuilder html = new StringBuilder();

        OrbisComponent component = OrbisComponentHelper.getComponent(componentKey,
                version);
        if (component != null)
        {
            Map<String, String> replaceArgs = component.buildStaticMap();
            if (!loadedComponents.contains(componentKey))
            {
                loadedComponents.add(componentKey);
                if (component.hasDependencies())
                {
                    for (Map.Entry<String, String> dependency : component
                            .getDependencies().entrySet())
                    {
                        html.append(getComponentIncludes(dependency.getKey(),
                                dependency.getValue()));
                    }
                }

                if (!StringUtils.isEmpty(component.getScript()))
                {
                    html.append(
                            StrSubstitutor.replace(STRUCTURE_SCRIPT, replaceArgs));
                }

                if (!StringUtils.isEmpty(component.getStylesheet()))
                {
                    html.append(StrSubstitutor.replace(STRUCTURE_STYLESHEET,
                            replaceArgs));
                }

                if (component.hasExpansions())
                {
                    for (Map.Entry<String, String> expansion : component
                            .getExpansions().entrySet())
                    {
                        html.append(getComponentIncludes(expansion.getKey(),
                                expansion.getValue()));
                    }
                }
            }

        }
        else
        {
            System.out.println(
                    String.format("Couldn't find component '%s'", componentKey));
        }

        return html.toString();
    }

}
