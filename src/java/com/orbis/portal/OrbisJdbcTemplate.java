package com.orbis.portal;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.sql.DataSource;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowCallbackHandler;
import org.springframework.jdbc.core.RowMapper;

import com.orbis.utils.BatchUpdateQueryBuilder;
import com.orbis.utils.DBUtils;
import com.orbis.utils.LambdaExceptionUtil;
import com.orbis.utils.LambdaExceptionUtil.TriConsumer_WithExceptions;
import com.orbis.utils.OrbisThread;
import com.orbis.utils.QueryBuilder;
import com.orbis.utils.cachemap.CacheMap;

public class OrbisJdbcTemplate extends JdbcTemplate
{
    private Map<String, Object> m = CacheMap.<String, Object> builder().build();

    public OrbisJdbcTemplate(DataSource ds)
    {
        super(ds);
    }

    @Override
    public void execute(String sql) throws DataAccessException
    {
        this.update(sql);
    }

    @Override
    public Object query(String sql, PreparedStatementSetter pss,
            ResultSetExtractor rse) throws DataAccessException
    {
        logSql(sql);
        Object ret = super.query(sql, pss, rse);

        return ret;
    }

    @Override
    public int update(String sql, PreparedStatementSetter pss)
            throws DataAccessException
    {
        logSql(sql);
        int ret = super.update(sql, pss);

        return ret;
    }

    public <T> T query(QueryBuilder query, GenericResultSetExtractor<T> rse)
    {
        return (T) super.query(query.getStr(), query.getParams(), rse);
    }

    public Integer findInt(String query, Object... args)
    {
        return (Integer) super.query(query, args, rs -> {
            while (rs.next())
                return rs.getInt(1);
            return null;
        });
    }

    public Integer findInt(QueryBuilder query)
    {
        return (Integer) super.query(query.getStr(), query.getParams(), rs -> {
            while (rs.next())
                return rs.getInt(1);
            return null;
        });
    }

    public Double findDouble(QueryBuilder query)
    {
        return (Double) super.query(query.getStr(), query.getParams(), rs -> {
            while (rs.next())
                return rs.getDouble(1);
            return null;
        });
    }

    public String findString(String query, Object... args)
    {
        return (String) super.query(query, args, rs -> {
            while (rs.next())
                return rs.getString(1);
            return null;
        });
    }

    public String findString(QueryBuilder query)
    {
        return (String) super.query(query.getStr(), query.getParams(), rs -> {
            while (rs.next())
                return rs.getString(1);
            return null;
        });
    }

    public void query(QueryBuilder query, RowCallbackHandler rch)
            throws DataAccessException
    {
        super.query(query.getStr(), query.getParams(), rch);
    }

    /**
     * Creates a batch update using the provided list of integers, where each
     * integer is used in its own statement as the first argument.<br>
     * <br>
     * i.e<br>
     * <code>jt.batchUpdate("INSERT INTO sa_slot_coop_program(slot, coopProgram) VALUES (1, ?)", coopProgramIds)</code>
     * 
     * @param sql
     * @param ids
     * @return {@link JdbcTemplate#batchUpdate}
     */
    public int[] orbisBatchUpdate(String sql, List<Integer> ids)
    {
        return batchUpdate(sql, DBUtils.listBatch(ids, LambdaExceptionUtil
                .rethrowBiConsumer((ps, id) -> ps.setInt(1, id))));
    }

    public int queryForIntCached(String sql) throws DataAccessException
    {
        int ret = 0;
        if (m.containsKey(sql))
        {
            ret = (Integer) m.get(sql);
        }
        else
        {
            ret = queryForInt(sql);
            m.put(sql, ret);
        }
        return ret;
    }

    public List queryCached(String sql, RowMapper rowMapper)
            throws DataAccessException
    {
        List ret;
        if (m.containsKey(sql))
        {
            ret = (List) m.get(sql);
        }
        else
        {
            ret = super.query(sql, rowMapper);
            m.put(sql, ret);
        }
        return ret;
    }

    private void logSql(String sql)
    {
        if (PortalUtils.isTestMode())
        {
            logger.info("SQL: " + sql);
        }
    }

    public List query(QueryBuilder query, RowMapper rowMapper)
            throws DataAccessException
    {
        return query(query.getStr(), query.getParams(), rowMapper);
    }

    public <T> List<T> query(QueryBuilder query, GenericRowMapper<T> rowMapper)
            throws DataAccessException
    {
        return super.query(query.getStr(), query.getParams(), rowMapper);
    }

    public <T> List<T> query(String sql, Object[] args,
            GenericRowMapper<T> rowMapper) throws DataAccessException
    {
        return super.query(sql, args, rowMapper);
    }

    public int queryForInt(QueryBuilder query) throws DataAccessException
    {
        return queryForInt(query.getStr(), query.getParams());
    }

    public List queryForList(QueryBuilder query) throws DataAccessException
    {
        return queryForList(query.getStr(), query.getParams());
    }

    /**
     * Syntactic sugar to make using BatchPreparedStatement setter less
     * obnoxious
     * 
     * <p>
     * Unlike {@link DBUtils#listBatch}, this method doesn't require
     * {@code triconsumer} be wrapped in
     * {@link LambdaExceptionUtil#rethrowTriConsumer} to catch the potential
     * {@code SQLException} that is thrown when accessing the
     * {@link PreparedStatement}
     * </p>
     * 
     * @param sql
     *            The update statement
     * @param list
     *            The list of items to iterate over
     * @param triconsumer
     *            Called for each item of in the {@code list}
     * 
     * @see DBUtils#listBatch
     */
    public <T> int[] orbisBatchUpdate(String sql, List<T> list,
            TriConsumer_WithExceptions<PreparedStatement, T, Integer, SQLException> triconsumer)
            throws DataAccessException
    {
        BatchPreparedStatementSetter bpss = new BatchPreparedStatementSetter()
        {
            @Override
            public void setValues(PreparedStatement ps, int i) throws SQLException
            {
                triconsumer.accept(ps, list.get(i), i);
            }

            @Override
            public int getBatchSize()
            {
                return list.size();
            }
        };

        return batchUpdate(sql, bpss);
    }

    /**
     * Creates a batch update using the provided list, where each item is used
     * in its own statement at the provided index.<br>
     * <br>
     * i.e<br>
     * <code>jt.batchUpdate(new QueryBuilder("INSERT INTO sa_slot_coop_program(slot, coopProgram) VALUES (?, ?)", 1), coopProgramIds, 2)</code>
     * 
     * @return {@link JdbcTemplate#batchUpdate}
     */
    public <T> int[] orbisBatchUpdate(QueryBuilder sql, List<T> list,
            int placeholderIndex) throws DataAccessException
    {
        Object[] params = sql.getParams();

        List<Integer> placeholderIndexes = new ArrayList<>();
        placeholderIndexes.add(placeholderIndex);

        return orbisBatchUpdate(sql.getStr(), list,
                new BatchUpdateStatementSetter<T>(params, placeholderIndexes));
    }

    public <T> int[] orbisBatchUpdate(BatchUpdateQueryBuilder<T> sql, List<T> list)
            throws DataAccessException
    {
        Object[] params = sql.getParams();

        List<Integer> placeholderIndexes = sql.getPlaceholders();

        return orbisBatchUpdate(sql.getStr(), list,
                new BatchUpdateStatementSetter<T>(params, placeholderIndexes));
    }

    public <T> int[] orbisBatchUpdate(BatchUpdateQueryBuilder<T> sql, List<T> list,
            OrbisThread thread)
    {
        return orbisBatchUpdate(sql.getStr(), list,
                new InterruptableBatchUpdateStatementSetter<>(thread,
                        sql.getParams(), sql.getPlaceholders()));
    }

    public int update(QueryBuilder query) throws DataAccessException
    {
        return update(query.getStr(), query.getParams());
    }

    public boolean exists(QueryBuilder sql)
    {
        return exists(sql.getStr(), sql.getParams());
    }

    public boolean exists(String sql, Object... params)
    {
        return queryForInt("select iif (exists (" + sql + "), 1, 0)", params) == 1;
    }

    public boolean notExists(QueryBuilder sql)
    {
        return notExists(sql.getStr(), sql.getParams());
    }

    public boolean notExists(String sql, Object... params)
    {
        return queryForInt("select iif (exists (" + sql + "), 1, 0)", params) == 0;
    }

    public int queryForInt(String query, Integer param) throws DataAccessException
    {
        var result = super.queryForObject(query, Integer.class, param);
        return result != null ? result : 0;
    }

    public int queryForInt(String query, Object[] params) throws DataAccessException
    {
        var result = super.queryForObject(query, Integer.class, params);
        return result != null ? result : 0;
    }

    public int queryForInt(String query) throws DataAccessException
    {
        var result = super.queryForObject(query, Integer.class);
        return result != null ? result : 0;
    }

    public long queryForLong(String query, Object[] params) throws DataAccessException
    {
        var result = super.queryForObject(query, Long.class, params);
        return result != null ? result : 0L;
    }
}
