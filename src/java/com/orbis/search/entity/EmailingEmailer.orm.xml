<?xml version="1.0"?>
<entity-mappings
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns="https://jakarta.ee/xml/ns/persistence/orm"
        xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence/orm https://jakarta.ee/xml/ns/persistence/orm/orm_3_1.xsd"
        version="3.1">
    <entity class="com.orbis.search.entity.EmailingEmailer" name="EmailingEmailer">
        <table name="search_entity_emailing_emailer"/>
        <attributes>


            <many-to-one name="model"/>

            <basic name="firstNameBeanPath"/>
            <basic name="lastNameBeanPath"/>
            <basic name="localeBeanPath"/>
            <basic name="noEmailCampaignWhereClause"/>
            <basic name="i18nEmailLabel"/>
            <basic name="i18nCampaignLabel"/>
            <basic name="additionalHql"/>
            <basic name="additionalLabel"/>

        </attributes>
    </entity>
</entity-mappings>