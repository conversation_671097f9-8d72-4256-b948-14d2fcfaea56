package com.orbis.search;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.jdbc.core.JdbcTemplate;
import com.orbis.portal.CommandQueryTemplate;
import org.springframework.web.servlet.ModelAndView;

import com.orbis.portal.PortalUtils;
import com.orbis.search.entity.EntityModel;

public abstract class AbstractSearchInterface implements SearchInterface
{
    @Override
    public ModelAndView displayHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public EntityModel getSearchableEntities()
    {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public SearchModel initSearchModelWithQuestions(HttpServletRequest request)
    {
        SearchModel searchModel = initSearchModel(request);
        SearchHelper.cleanSearchModelQuestions(searchModel);
        SearchHelper.addRelationQuestions(searchModel,
                PortalUtils.getLocale(request));
        return searchModel;
    }

    @Override
    public SearchModel initSearchModel(HttpServletRequest request)
    {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public CommandQueryTemplate getHt()
    {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public final String getActionName()
    {
        return "search";
    }

    @Override
    public ModelAndView onDoubleClickRow(Integer rowId, HttpServletRequest request,
            HttpServletResponse response)
    {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public ModelAndView multiActionHandler(HttpServletRequest request,
            HttpServletResponse response)
    {
        // TODO Auto-generated method stub
        return null;
    }

    @Override
    public ModelAndView renderMassAssignPage(HttpServletRequest request,
            HttpServletResponse response)
    {
        return null;
    }

    @Override
    public JdbcTemplate getJt()
    {
        // TODO Auto-generated method stub
        return null;
    }

}
