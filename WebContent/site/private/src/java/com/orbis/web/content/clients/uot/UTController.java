package com.orbis.web.content.clients.uot;

import java.util.Date;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl;
import com.orbis.portal.PortalUtils;
import com.orbis.security.AuthenticationUtils;
import com.orbis.utils.DateUtils;
import com.orbis.utils.StringUtils;
import com.orbis.web.OrbisController;
import com.orbis.web.content.acrm.UserDetailsHelper;
import com.orbis.web.content.np.NHelper;
import com.orbis.web.content.registrar.RegistrarDAO;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RequestMapping("utController")
public class UTController extends OrbisController
{
    static protected final Log logger = LogFactory.getLog(UTController.class);

    private RegistrarDAO registrarDAO;

    public void setRegistrarDAO(RegistrarDAO registrarDAO)
    {
        this.registrarDAO = registrarDAO;
    }

    @Override
    @RequestMapping("displayHome")
    public ModelAndView displayHome(HttpServletRequest request,
            HttpServletResponse response)
    {
        String username = null;
        String affiliation = null;

        try
        {
            username = request.getHeader("AJP_eppn");
            username = username.replaceAll("@utoronto.ca", "");
            affiliation = request.getHeader("AJP_unscoped-affiliation");
        }
        catch (Exception e)
        {
            e.printStackTrace();
        }

        request.getSession().invalidate();
        ModelAndView mv = null;

        logger.debug("displayHome(): username = " + username);
        logger.debug("displayHome(): affiliation = " + affiliation);

        if (!StringUtils.isEmpty(username) && affiliation != null)
        {
            UserDetailsImpl au = loadExistingUser(username, affiliation);

            if (au == null)
            {
                au = fullSync(request, username, affiliation, au);
            }
            else if (au.getDateUpdated() != null && DateUtils.isAfter(
                    DateUtils.subtractDays(new Date(), 1), au.getDateUpdated()))
            {
                logger.debug("displayHome(): Last updated date > 24 hours");

                Integer overrideRosi = (Integer) PortalUtils.getHt().find(
                        "select count(*) from AcrmUserTag t where t.user.username=? and t.tag.l1Name='Override ROSI Integration'",
                        username).get(0);

                if (overrideRosi.intValue() == 0)
                {
                    logger.debug("displayHome(): No Override ROSI tag");
                    au = fullSync(request, username, affiliation, au);
                }
            }

            if (au != null)
            {
                logger.debug("displayHome(): Logging in " + au.getUsername());

                String pwd = UserDetailsHelper.makePassword(au.getUsername());
                au.setCleartextPassword(pwd);
                au.setPassword(getPasswordEncoder().encode(pwd));

                AuthenticationUtils.setUserLoggedIn(request, au, response);

                mv = getRedirectView("/myAccount");
            }

        }

        return mv;
    }

    private UserDetailsImpl loadExistingUser(String username, String affiliation)
    {
        UserDetailsImpl au = null;

        if (affiliation.contains("student") || affiliation.contains("alumni")
                || affiliation.contains("candidate")
                || affiliation.contains("applicant") || affiliation.isEmpty())
        {
            logger.debug("loadExistingUser(): Loading existing student");
            au = UserDetailsHelper.getUserByUsername(username);
        }

        if (au == null)
        {
            logger.debug(
                    "loadExistingUser(): Student not loaded, attempting to load staff");
            au = loadStaffUser(username, affiliation);
        }
        else if (!isStudentActive(au))
        {
            UserDetailsImpl auStaff = null;

            logger.debug(
                    "loadExistingUser(): Student is inactive, attempting to load staff");
            auStaff = loadStaffUser(username, affiliation);

            if (auStaff != null)
            {
                logger.debug(
                        "loadExistingUser(): Staff loaded, assigning staff user");
                au = auStaff;
            }
        }

        if (au != null)
        {
            logger.debug("loadExistingUser(): Loaded user " + au.getUsername());
        }
        else
        {
            logger.debug("loadExistingUser(): Could not find existing user");
        }

        return au;
    }

    private synchronized UserDetailsImpl fullSync(HttpServletRequest request,
            String username, String affiliation, UserDetailsImpl au)
    {
        UTUser uu = (UTUser) registrarDAO.getUserByUserId(username);

        if (uu != null && (affiliation.contains("student")
                || (affiliation.contains("alumni") && uu.isRecentGrad())
                || affiliation.contains("candidate")
                || affiliation.contains("applicant") || affiliation.isEmpty()))
        {
            logger.debug("fullSync(): Synchronizing student " + uu.getUsername());
            au = registrarDAO.synchronizeUserData(uu, request);
        }

        UserDetailsImpl auStaff = null;

        if (affiliation.contains("staff") || affiliation.contains("faculty"))
        {
            UTUser uuStaff = (UTUser) ((UTDAO) registrarDAO)
                    .getStaffUserByUserId(username);

            if (uuStaff != null)
            {
                logger.debug(
                        "fullSync(): Synchronizing staff " + uuStaff.getUsername());
                auStaff = registrarDAO.synchronizeUserData(uuStaff, request);
            }
        }

        if (au != null)
        {
            logger.debug("fullSync(): au user is " + au.getUsername());

            if (auStaff != null)
            {
                logger.debug(
                        "fullSync(): auStaff user is " + auStaff.getUsername());
                logger.debug("fullSync(): Linking " + au.getUsername() + " and "
                        + auStaff.getUsername());
                NHelper.linkUsers(au, auStaff, request);

                if (!isStudentActive(au))
                {
                    logger.debug(
                            "fullSync(): au is inactive, assigning staff user");
                    au = auStaff;
                }
            }
        }
        else if (auStaff != null)
        {
            logger.debug("fullSync(): au is null, assigning staff user");
            logger.debug("fullSync(): auStaff user is " + auStaff.getUsername());
            au = auStaff;
        }

        return au;
    }

    private UserDetailsImpl loadStaffUser(String username, String affiliation)
    {
        UserDetailsImpl auStaff = null;

        if (affiliation.contains("staff") || affiliation.contains("faculty"))
        {
            logger.debug("loadExistingUser(): Loading existing staff");
            auStaff = UserDetailsHelper
                    .getUserByUsername(username + " - Staff/Faculty");
        }

        return auStaff;
    }

    private boolean isStudentActive(UserDetailsImpl au)
    {
        boolean studentActive = true;

        if (UserDetailsImpl.USER_STATUS_INACTIVE.equals(au.getUserStatus())
                || !au.isEnabled() || !au.getS30().equals("true"))
        {
            studentActive = false;
        }

        return studentActive;
    }
}