export declare type ButtonSizesValues = 'small' | 'medium' | 'large';
export declare enum ButtonSizes {
    small = "btn__small--text",
    medium = "btn__default--text",
    large = "btn__hero--text"
}
export declare type ButtonTypesValues = 'info' | 'default' | 'success' | 'warning' | 'error' | 'white' | 'black' | 'disabled';
export declare enum ButtonTypes {
    info = "btn--info",
    default = "btn--default",
    success = "btn--success",
    warning = "btn--warning",
    error = "btn--error",
    white = "btn--white",
    black = "btn--black",
    disabled = "btn--disabled"
}
export declare type ButtonStylesValues = 'outline' | 'pill' | 'outlinePill' | 'plain';
export declare enum ButtonStyles {
    outline = "outline",
    pill = "pill",
    outlinePill = "outline pill",
    plain = "plain"
}
export declare enum Location {
    Left = "left",
    Right = "right"
}
export declare type LoadingStates = 'loading' | 'success';
export declare type Notification = {
    message: string;
    duration: number | undefined;
};
export declare type LabelValue = {
    label: string;
    value: number;
    string: any;
    boolean: any;
};
export declare type LabelAction = {
    label: string;
    callback: () => void;
};
export declare type EmptyStateSizes = 'small' | 'medium' | 'large';
export declare type NotificationTypes = 'error' | 'success' | 'warning' | 'info';
export declare const NOTIFICATONS_INJECT_KEY = "UINotification";
export interface INotify {
    notify(message: string, type?: NotificationTypes | undefined, duration?: number | undefined): void;
}
