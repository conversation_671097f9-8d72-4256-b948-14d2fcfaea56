import UIConfigurationList, { type Props as ConfigListProps } from './UIConfigurationList.vue';
import UIConfigurationListItem, { type Props as ItemProps } from './UIConfigurationListItem.vue';
import UIConfigurationListItemGroup, { type Props as GroupProps } from './UIConfigurationListGroup.vue';
export interface ConfigurationListState {
    configList: ConfigListProps;
    currentGroup?: GroupProps | null;
    currentItem?: ItemProps | null;
}
export { UIConfigurationList, UIConfigurationListItem, UIConfigurationListItemGroup };
