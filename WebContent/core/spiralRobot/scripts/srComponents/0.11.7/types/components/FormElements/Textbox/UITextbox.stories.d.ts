import type { <PERSON>a, StoryObj } from '@storybook/vue3';
import UITextbox from './UITextbox.vue';
declare const meta: Meta<typeof UITextbox>;
export default meta;
declare type Story = StoryObj<typeof UITextbox>;
export declare const Default: Story;
export declare const WithError: Story;
export declare const WithHelpText: Story;
export declare const NumberInput: Story;
export declare const TextareaExample: Story;
