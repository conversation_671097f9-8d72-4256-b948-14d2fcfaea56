import type { <PERSON><PERSON>, StoryObj } from '@storybook/vue3';
import UIGrid from './UIGrid.vue';
declare const meta: Meta<typeof UIGrid>;
export default meta;
declare type Story = StoryObj<typeof UIGrid>;
export declare const Default: Story;
export declare const Alignment: Story;
export declare const Distribution: Story;
export declare const WithOffset: Story;
export declare const WithOrder: Story;
