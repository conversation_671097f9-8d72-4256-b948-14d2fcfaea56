declare const _default: {
    title: string;
    component: import("vue").DefineComponent<{}, {}, any, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    argTypes: {
        selectLabel: {
            control: string;
        };
        options: {
            control: string;
        };
        emptyOption: {
            control: string;
        };
        emptyOptionLabel: {
            control: string;
        };
        enableOther: {
            control: string;
        };
        otherLabel: {
            control: string;
        };
        otherPlaceholder: {
            control: string;
        };
    };
    args: {
        selectLabel: string;
        options: ({
            value: string;
            label: string;
            disabled?: undefined;
        } | {
            value: string;
            label: string;
            disabled: boolean;
        })[];
        emptyOption: boolean;
        enableOther: boolean;
        otherLabel: string;
        otherPlaceholder: string;
    };
};
export default _default;
export declare const Default: (args: any) => {
    components: {
        UISingleChoice: import("vue").DefineComponent<{}, {}, any, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    };
    setup(): {
        modelValue: import("vue").Ref<null, null>;
        modelText: import("vue").Ref<string, string>;
        args: any;
    };
    template: string;
};
