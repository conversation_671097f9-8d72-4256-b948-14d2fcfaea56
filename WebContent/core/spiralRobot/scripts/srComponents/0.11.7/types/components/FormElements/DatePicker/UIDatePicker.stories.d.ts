import type { Meta, StoryObj } from '@storybook/vue3';
import UIDatePicker from './UIDatePicker.vue';
import UIDateRangePicker from './UIDateRangePicker.vue';
declare const meta: Meta<import("vue").DefineComponent<{}, {}, any, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>>;
export default meta;
declare type Story = StoryObj<typeof UIDatePicker>;
export declare const Default: Story;
declare type DateRangeStory = StoryObj<typeof UIDateRangePicker>;
export declare const DateRange: DateRangeStory;
