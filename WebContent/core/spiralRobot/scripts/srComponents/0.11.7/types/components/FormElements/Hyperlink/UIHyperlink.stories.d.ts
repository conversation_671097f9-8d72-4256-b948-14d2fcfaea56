declare const _default: {
    title: string;
    component: import("vue").DefineComponent<{}, {}, any, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    argTypes: {
        title: {
            control: string;
        };
        urlLabel: {
            control: string;
        };
        textLabel: {
            control: string;
        };
        urlPlaceholder: {
            control: string;
        };
        textPlaceholder: {
            control: string;
        };
        required: {
            control: string;
        };
        helpText: {
            control: string;
        };
        error: {
            control: string;
        };
    };
    args: {
        title: string;
        urlLabel: string;
        textLabel: string;
        urlPlaceholder: string;
        helpText: string;
        textPlaceholder: string;
        required: boolean;
    };
};
export default _default;
export declare const Default: (args: any) => {
    components: {
        UIHyperlink: import("vue").DefineComponent<{}, {}, any, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    };
    setup(): {
        modelValue: import("vue").Ref<{
            url: string;
            text: string;
        }, {
            url: string;
            text: string;
        } | {
            url: string;
            text: string;
        }>;
        args: any;
    };
    template: string;
};
export declare const Filled: (args: any) => {
    components: {
        UIHyperlink: import("vue").DefineComponent<{}, {}, any, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    };
    setup(): {
        modelValue: import("vue").Ref<{
            url: string;
            text: string;
        }, {
            url: string;
            text: string;
        } | {
            url: string;
            text: string;
        }>;
        args: any;
    };
    template: string;
};
export declare const WithError: (args: any) => {
    components: {
        UIHyperlink: import("vue").DefineComponent<{}, {}, any, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    };
    setup(): {
        modelValue: import("vue").Ref<{
            url: string;
            text: string;
        }, {
            url: string;
            text: string;
        } | {
            url: string;
            text: string;
        }>;
        args: any;
    };
    template: string;
};
