import { FileDetails, FileValidationRules } from './types';
/**
 * Normalize the incoming accept prop to always be an array of mime types. Any types not found will be left as is.
 *
 * @param {string | string[] | undefined} accept - The accept prop value
 * @returns {string[]} - An array of normalized mime types.
 */
export declare function normalizeIncomingAccept(accept: string | string[] | undefined): string[];
/**
 * Generates a new FileDetails object based off of the newly added file
 *
 * @param {File} file - The newly added file.
 * @returns {FileDetails} - The generated file details.
 */
export declare function generateNewFileDetails(file: File): FileDetails;
/**
 * File validation check
 *
 * @param {FileDetails} fileDetails - The file to be validated.
 * @param {object} validationRules - The common file tag properties.
 * @returns {string | undefined} - The error message if validation fails, otherwise undefined.
 */
export declare function checkFileForErrors(fileDetails: FileDetails, validationRules: FileValidationRules): string | undefined;
/**
 * Formats the size of a file in bytes to a human-readable string.
 * KB if size is greater than or equal to 1KB, MB if size is greater than or equal to 1MB, and B otherwise.
 *
 * @param {number} sizeInBytes - The size of the file in bytes.
 * @returns {string} The formatted size string.
 */
export declare function formatSize(sizeInBytes: number): string;
export declare function generateReadableFileExtensions(acceptedFileTypes: string[]): string;
