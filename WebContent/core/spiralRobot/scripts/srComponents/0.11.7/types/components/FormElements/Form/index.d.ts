import UIForm from './UIForm.vue';
import { FieldValidator } from 'vee-validate';
export declare type FormElements = {
    [key: string]: {
        validate: FieldValidator<unknown> | undefined;
        element: HTMLElement | null;
    };
};
export declare type FormResult = {
    isValid: boolean;
    errors: {
        [key: string]: string[];
    };
    values: Record<string, unknown>;
};
export { UIForm };
