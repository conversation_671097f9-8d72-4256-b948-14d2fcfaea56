declare const _default: {
    title: string;
    component: import("vue").DefineComponent<{}, {}, any, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    parameters: {
        actions: {
            handles: string[];
        };
    };
    argTypes: {
        title: {
            control: string;
        };
        options: {
            control: string;
        };
        maxSelected: {
            control: string;
        };
        enableOther: {
            control: string;
        };
        otherLabel: {
            control: string;
        };
        otherPlaceholder: {
            control: string;
        };
    };
};
export default _default;
export declare const Default: (args: any) => {
    components: {
        UIMultiChoice: import("vue").DefineComponent<{}, {}, any, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        UINotifications: import("vue").DefineComponent<{}, {}, any, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    };
    setup(): {
        args: any;
        modelValue: import("vue").Ref<any, any>;
        modelText: import("vue").Ref<any, any>;
        notificationRef: import("vue").Ref<any, any>;
        handleLimitReached: () => void;
    };
    template: string;
};
export declare const WithoutOther: (args: any) => {
    components: {
        UIMultiChoice: import("vue").DefineComponent<{}, {}, any, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
        UINotifications: import("vue").DefineComponent<{}, {}, any, import("vue").ComputedOptions, import("vue").MethodOptions, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
    };
    setup(): {
        args: any;
        modelValue: import("vue").Ref<any, any>;
        modelText: import("vue").Ref<any, any>;
        notificationRef: import("vue").Ref<any, any>;
        handleLimitReached: () => void;
    };
    template: string;
};
