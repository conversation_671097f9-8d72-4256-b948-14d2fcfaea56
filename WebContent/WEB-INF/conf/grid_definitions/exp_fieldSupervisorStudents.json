{"categories": [{"categoryID": "main", "categoryName": "MAIN", "ordinal": 1}], "columns": [{"category": "main", "colID": "rId", "columnName": "rId", "columnType": "com.orbis.web.content.grid.GridColumnDefault", "l2Property": "r.id", "ordinal": -1, "property": "r.id", "queryIndex": 0, "valueClass": "java.lang.Integer", "visible": 0, "sortOrdinal": 1, "sortDirection": 2}, {"category": "main", "colID": "student", "columnName": "i18n.exp_facultyMemberRecordsToApprove.Student7089148501962481", "columnType": "com.orbis.web.content.grid.GridColumnDefault", "l2Property": "u.firstAndLastName", "ordinal": 1, "property": "u.firstAndLastName", "queryIndex": 1, "valueClass": "java.lang.String", "visible": 1}, {"category": "main", "colID": "termName", "columnName": "i18n.exp_facultyMemberRecordsToApprove.Term2883040947061485", "columnType": "com.orbis.web.content.grid.GridColumnDefault", "l2Property": "t.l2Name", "ordinal": 2, "property": "t.name", "queryIndex": 2, "valueClass": "java.lang.String", "visible": 1}, {"category": "main", "colID": "courseCode", "columnName": "i18n.exp_facultyMemberRecordsToApprove.CourseCode1050897610514251", "columnType": "com.orbis.web.content.grid.GridColumnDefault", "l2Property": "c.l2Code", "ordinal": 3, "property": "c.code", "queryIndex": 3, "valueClass": "java.lang.String", "visible": 1}, {"category": "main", "colID": "courseSection", "columnName": "i18n.exp_facultyMemberRecordsToApprove.CourseSect3861322036742230", "columnType": "com.orbis.web.content.grid.GridColumnDefault", "l2Property": "c.l2Section", "ordinal": 4, "property": "c.section", "queryIndex": 4, "valueClass": "java.lang.String", "visible": 1}, {"category": "main", "colID": "courseName", "columnName": "i18n.exp_facultyMemberRecordsToApprove.CourseName6805227851573034", "columnType": "com.orbis.web.content.grid.GridColumnDefault", "l2Property": "c.l2Name", "ordinal": 5, "property": "c.name", "queryIndex": 5, "valueClass": "java.lang.String", "visible": 1}, {"category": "main", "colID": "tctName", "columnName": "i18n.exp_adminPostingInterestedStudents.Experience7169198263828061", "columnType": "com.orbis.web.content.grid.GridColumnDefault", "emptyValueText": "", "interactionRoleColumn": 0, "l2Property": "tct.l2Name", "ordinal": -1, "property": "tct.name", "queryIndex": 6, "valueClass": "java.lang.String", "visible": 0}, {"category": "main", "colID": "expTypeName", "columnName": "i18n.exp_adminPostingInterestedStudents.Experience7169198263828061", "columnType": "com.orbis.web.content.grid.GridColumnDefault", "emptyValueText": "", "interactionRoleColumn": 0, "l2Property": "expType.l2Name", "ordinal": -1, "property": "expType.name", "queryIndex": 7, "valueClass": "java.lang.String", "visible": 0}, {"category": "main", "colID": "experienceName", "columnName": "i18n.exp_adminPostingInterestedStudents.Experience7169198263828061", "columnType": "com.orbis.web.content.grid.GridColumnDefault", "emptyValueText": "", "interactionRoleColumn": 0, "property": "concat_ws(' - ', expType.name, tct.name)", "l2Property": "concat_ws(' - ', expType.l2Name, tct.l2Name)", "ordinal": 6, "queryIndex": 8, "valueClass": "java.lang.String", "visible": 1}], "gridID": "exp_fieldSupervisorStudents", "i18nCode": "", "permissions": {"Portal Staff": {"CAN_EXPORT": true, "CAN_FILTER": true, "CAN_SAVE_FILTERS": true, "CAN_APPLY_DEFAULT_FILTER": true}, "Experiential Education Field Supervisor": {"CAN_EXPORT": false, "CAN_FILTER": true, "CAN_SAVE_FILTERS": false, "CAN_APPLY_DEFAULT_FILTER": false}}}