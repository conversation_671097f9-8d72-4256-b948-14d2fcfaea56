{"rec": {"sampleData": {"appendSample": "expSubRecordFields"}}, "record": {"sampleData": {"appendSample": "expRecord"}}, "experienceType": {"sampleData": {"appendSample": "experienceType"}}, "course": {"sampleData": {"appendSample": "expCourse"}}, "user": {"sampleData": {"appendSample": "userDetails"}}, "erec": {"sampleData": {"isCollection": true, "appendSample": "dfAnswerEntity"}}, "etrac": {"sampleData": {"isCollection": true, "appendSample": "dfAnswerEntity"}}, "ejrnl": {"sampleData": {"isCollection": true, "appendSample": "dfAnswerEntity"}}, "stev": {"sampleData": {"isCollection": true, "appendSample": "dfAnswerEntity"}}, "eeval": {"sampleData": {"isCollection": true, "appendSample": "dfAnswerEntity"}}, "facev": {"sampleData": {"isCollection": true, "appendSample": "dfAnswerEntity"}}, "etcf1": {"sampleData": {"isCollection": true, "appendSample": "dfAnswerEntity"}}, "etcf2": {"sampleData": {"isCollection": true, "appendSample": "dfAnswerEntity"}}, "etcf3": {"sampleData": {"isCollection": true, "appendSample": "dfAnswerEntity"}}, "etcf4": {"sampleData": {"isCollection": true, "appendSample": "dfAnswerEntity"}}, "etcf5": {"sampleData": {"isCollection": true, "appendSample": "dfAnswerEntity"}}, "etcf6": {"sampleData": {"isCollection": true, "appendSample": "dfAnswerEntity"}}, "fieldSupervisors": {"sampleData": {"isCollection": true, "appendSample": "userDetails"}}, "facultyAdvisors": {"sampleData": {"isCollection": true, "appendSample": "userDetails"}}}