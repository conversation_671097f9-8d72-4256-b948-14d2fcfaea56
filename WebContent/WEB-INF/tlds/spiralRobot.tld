<?xml version="1.0" encoding="UTF-8"?>
<taglib version="2.1" xmlns="http://java.sun.com/xml/ns/javaee"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-jsptaglibrary_2_1.xsd">
	<tlib-version>1.0</tlib-version>
	<short-name>ui</short-name>
	<uri>/WEB-INF/tlds/spiralRobot.tld</uri>
	
	<tag-file>
		<name>ajax</name>
		<path>/WEB-INF/tags/ui/ajax.tag</path>
	</tag-file>
	<tag-file>
		<name>flashMessages</name>
		<path>/WEB-INF/tags/ui/flashMessages.tag</path>
	</tag-file>
	<tag-file>
		<name>truncatedValue</name>
		<path>/WEB-INF/tags/ui/truncatedValue.tag</path>
	</tag-file>
	
	<!-- 
	
	Action Aware Attributes
	com.orbis.utils.tags.ui.ActionAwareUITag
	
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Unencrypted action to be submitted when the button is clicked.</description>
		</attribute>
		
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
	
	 -->
	
	<!-- 
	
	Onclick UI tag attributes (extends Action Aware UI tag)
	com.orbis.utils.tags.ui.OnclickUITag
	
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Javascript attribute to fire whenever the button is clicked.
			</description>
		</attribute>
	
		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Text to be shown in a confirm modal that will execute the button's click behaviour if the confirm modal is accepted.<br />
				The click behaviour in the modal will not run if the confirm modal is declined or closed.
			</description>
		</attribute>
	
		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The args attribute for i18n_confirmOnclick</description>
		</attribute>
		
		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
		
		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
	
	 -->
	
	<tag>
		<name>calendar</name>
		<tag-class>com.orbis.utils.tags.ui.CalendarTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>action_loadEvents</name>
			<required>false</required>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>subAction_loadEvents</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>siteElement_loadEvents</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>

		<attribute>
			<name>action_saveEvent</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>subAction_saveEvent</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>siteElement_saveEvent</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>action_loadResources</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>subAction_loadResources</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>siteElement_loadResources</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>js_onNewEvent</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>js_onEventClick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>weekends</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		
		<attribute>
			<name>eventClickToDay</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>showDay</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Date</type>
		</attribute>
	
		<attribute>
			<name>minTime</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>maxTime</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>resourceSelectorId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>filterSidebarId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>labelMap</name>
		<tag-class>com.orbis.utils.tags.ui.LabelMapTag</tag-class>
		<body-content>scriptless</body-content>
	</tag>
	
	<tag>
		<name>label</name>
		<tag-class>com.orbis.utils.tags.ui.LabelTag</tag-class>
		<body-content>empty</body-content>
		
		<attribute>
			<name>key</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>label</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>labelArgs</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>section</name>
		<tag-class>com.orbis.utils.tags.ui.SectionTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		
		<attribute>
			<name>key</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>evaluate</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
		</attribute>
	</tag>

	<tag>
		<name>largeHeader</name>
		<tag-class>com.orbis.utils.tags.ui.LargeHeaderTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>false</dynamic-attributes>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_superTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_superTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>action_config</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>subAction_config</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>siteElement_config</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		<attribute>
			<name>additionalParams_config</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		<attribute>
			<name>action_showConfigSidebar</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_headerGroup</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_headerGroup</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>participants</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>subtitleCount</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>background</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>resourcesCount</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.int</type>
		</attribute>
		<attribute>
			<name>pendingEnrollmentsCount</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.int</type>
		</attribute>
		<attribute>
			<name>pendingActivitiesCount</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.int</type>
		</attribute>
	</tag>
	<tag>
		<name>badgesList</name>
		<tag-class>com.orbis.utils.tags.ui.BadgesListTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>false</dynamic-attributes>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>name</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>btnAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>btnSize</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.BUTTON_SIZES</type>
		</attribute>
		<attribute>
			<name>btnType</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.BUTTON_TYPES</type>
		</attribute>		
	</tag>
	<tag>
		<name>badge</name>
		<tag-class>com.orbis.utils.tags.ui.BadgeTag</tag-class>
		<body-content>scriptless</body-content>
		<attribute>
			<name>bgColor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>iconName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>headerCardsList</name>
		<tag-class>com.orbis.utils.tags.ui.HeaderCardsListTag</tag-class>
		<body-content>scriptless</body-content>
	</tag>
	<tag>
		<name>headerCardsListItem</name>
		<tag-class>com.orbis.utils.tags.ui.HeaderCardsListItemTag</tag-class>
		<body-content>scriptless</body-content>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>headerCardsListItemFooter</name>
		<tag-class>com.orbis.utils.tags.ui.HeaderCardsListItemFooterTag</tag-class>
		<body-content>scriptless</body-content>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>pagination</name>
		<tag-class>com.orbis.utils.tags.ui.PaginationTag</tag-class>
		<body-content>scriptless</body-content>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>currentPage</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
		<attribute>
			<name>numberOfPages</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
		<attribute>
			<name>callback</name>
			<description>
				The name of a JavaScript function that will accept a page number
			</description>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>accordion</name>
		<tag-class>com.orbis.utils.tags.ui.AccordionTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>collapseNonActive</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>style</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.ACCORDION_STYLE</type>
		</attribute>
	</tag>
	<tag>
		<name>accordionItem</name>
		<tag-class>com.orbis.utils.tags.ui.AccordionItemTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>contentClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>contentCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>expanded</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>
				Have this accordion item open when first getting to the page
			</description>
		</attribute>
		<attribute>
			<name>extraControl</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				If set, creates a second button adjacent to the close button with an id based on the accordion's ID. 				
				The given string is used to pick which material icon to use.
			</description>
		</attribute>
		<attribute>
			<name>extraOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Attaches onclick to button created by extraControl.
				If no extraControl set, this does nothing.
			</description>
		</attribute>
		<attribute>
			<name>containsControls</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>
				When true, applies expand button's ID to accordion__content to ensure aria-controls works
			</description>
		</attribute>
		<attribute>
			<name>style</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.ACCORDION_STYLE</type>
		</attribute>
		<attribute>
			<name>headerClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>actionsGroup</name>
		<tag-class>com.orbis.utils.tags.ui.ActionsGroupTag</tag-class>
		<body-content>scriptless</body-content>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>icon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>actionsGroupItem</name>
		<tag-class>com.orbis.utils.tags.ui.ActionsGroupItemTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>buttonClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>i18n_toolTip</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>i18nArgs_toolTip</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>submit</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
	
		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>additionalParams</name> 
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
	</tag>

	<tag>
		<name>button</name>
		<tag-class>com.orbis.utils.tags.ui.ButtonTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<description>
			This is a generic button that can be used to call javascript, submit a self built form or to submit its parent form.
		</description>
		<tag-extension namespace="info">
			{
				examples : ["test_uiButtonExample1.jsp", "test_uiButtonExample2.jsp"]
			}
		</tag-extension>
		
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Javascript attribute to fire whenever the button is clicked.
			</description>
		</attribute>
	
		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Text to be shown in a confirm modal that will execute the button's click behaviour if the confirm modal is accepted.<br />
				The click behaviour in the modal will not run if the confirm modal is declined or closed.
			</description>
		</attribute>

		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The args attribute for i18n_confirmOnclick</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Element level css for the button element.</description>
		</attribute>
	
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Unique identifyer for the button element</description>
		</attribute>
	
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Classes to be placed in the button element</description>
		</attribute>
		
		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
		
		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Unencrypted action to be submitted when the button is clicked.</description>
		</attribute>
		
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		
		<attribute>
			<name>buttonType</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.BUTTON_BEHAVIOUR_TYPE</type>
		</attribute>
		
		<attribute>
			<name>size</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.BUTTON_SIZES</type>
		</attribute>
		
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.BUTTON_TYPES</type>
		</attribute>
		
		<attribute>
			<name>style</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.BUTTON_STYLES</type>
		</attribute>
		
		<attribute>
			<name>i18n_iconButtonTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18nArgs_iconButtonTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18n_iconButtonTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18nArgs_iconButtonTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>name</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>buttonRow</name>
		<tag-class>com.orbis.utils.tags.ui.ButtonRowTag</tag-class>
		<body-content>scriptless</body-content>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>catalogueHome</name>
		<tag-class>com.orbis.utils.tags.ui.CatalogueHomeTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>title</name>			
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	
	<tag>
		<name>card</name>
		<tag-class>com.orbis.utils.tags.ui.CardTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>cardContentClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>size</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.CARD_SIZES</type>
		</attribute>
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.CARD_TYPES</type>
		</attribute>
		<attribute>
			<name>icon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>buttonIcon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>backgroundColor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.BACKGROUND_COLOR</type>
		</attribute>
		<attribute>
			<name>iconColor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.FONT_COLOR</type>
		</attribute>
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>enableButton</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	
	<tag>
		<name>cardBody</name>
		<tag-class>com.orbis.utils.tags.ui.CardBodyTag</tag-class>
		<body-content>scriptless</body-content>
	</tag>
	
	<tag>
		<name>cardFooter</name>
		<tag-class>com.orbis.utils.tags.ui.CardFooterTag</tag-class>
		<body-content>scriptless</body-content>
	</tag>
	
	<tag>
		<name>cardHeader</name>
		<tag-class>com.orbis.utils.tags.ui.CardHeaderTag</tag-class>
		<body-content>scriptless</body-content>
	</tag>
	
	<tag>
		<name>cardStat</name>
		<tag-class>com.orbis.utils.tags.ui.CardStatTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Object</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>cardStatus</name>
		<tag-class>com.orbis.utils.tags.ui.CardStatusTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.utils.tags.ui.CardStatusTag.STATUS_TYPE</type>
		</attribute>
	</tag>
	
	<tag>
		<name>checkbox</name>
		<tag-class>com.orbis.utils.tags.ui.CheckboxTag</tag-class>
		<dynamic-attributes>true</dynamic-attributes>
		<body-content>scriptless</body-content>
				
		<attribute>
			<name>name</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.utils.tags.ui.CheckboxTag.CHECKBOX_TYPE</type>
		</attribute>
		<attribute>
			<name>onChange</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>groupCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>groupClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>labelCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>labelClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>checked</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>readOnly</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>inlineBlockLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>hideLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>checkboxGroup</name>
		<tag-class>com.orbis.utils.tags.ui.CheckboxGroupTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>name</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>readOnly</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>required</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>forceSidebarToBody</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>noSidebar</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				If this attribute is set to true and the number of checkbox group items
				is more than 10 it will render the vanilla checkbox group.
			</description>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>sidebarMode</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<description>
				If this attribute is set to true it will render the sidebar.
			</description>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	
	<tag>
		<name>checkboxGroupItem</name>
		<tag-class>com.orbis.utils.tags.ui.CheckboxGroupItemTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		
		<attribute>
			<name>name</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>groupClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>groupCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>checked</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>required</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>checkboxGroupOther</name>
		<tag-class>com.orbis.utils.tags.ui.CheckboxGroupOtherTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>name</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>otherBoxName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>checkboxClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>checked</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	<tag>
		<name>dashboardStats</name>
		<tag-class>com.orbis.utils.tags.ui.DashboardStatsTag</tag-class>
		<body-content>scriptless</body-content>
	</tag>
	<tag>
		<name>dashboardStatsItem</name>
		<tag-class>com.orbis.utils.tags.ui.DashboardStatsItemTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		
		<attribute>
			<name>backgroundColor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.BACKGROUND_COLOR</type>
		</attribute>

		<attribute>
			<name>labelFontColor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.FONT_COLOR</type>
		</attribute>
	
		<attribute>
			<name>statFontColor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.FONT_COLOR</type>
		</attribute>
	
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
	
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
	
		<attribute>
			<name>valueClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>dateCard</name>
		<tag-class>com.orbis.utils.tags.ui.DateCardTag</tag-class>
		<body-content>scriptless</body-content>
			
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>		
		</attribute>
		
		<attribute>
			<name>date</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Date</type>		
		</attribute>
	</tag>
	
	<tag>
		<name>datepicker</name>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<tag-class>com.orbis.utils.tags.ui.DatepickerTag</tag-class>
		
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Date</type>
		</attribute>
		<attribute>
			<name>groupId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>inputClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>labelClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>placeholder</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>groupCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>readOnly</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>i18n_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>startDate</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>endDate</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>showDate</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>noValidationOnChange</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>showTime</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>onSelect</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>onClose</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>required</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>linkTo</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>overrideParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>secondaryActionsGroup</name>
		<tag-class>com.orbis.utils.tags.ui.SecondaryActionsGroupTag</tag-class>
		<dynamic-attributes>true</dynamic-attributes>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>icon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>size</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.spiralRobot.SpiralRobot.BUTTON_SIZES</type>
		</attribute>

		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.spiralRobot.SpiralRobot.BUTTON_TYPES</type>
		</attribute>

		<attribute>
			<name>style</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.spiralRobot.SpiralRobot.BUTTON_STYLES</type>
		</attribute>
	</tag>
	
	<tag>
		<name>dropdown</name>
		<tag-class>com.orbis.utils.tags.ui.DropdownTag</tag-class>
		<dynamic-attributes>true</dynamic-attributes>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>forceDropdownArrow</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>

		<attribute>
			<name>isIconDropdown</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>noSelect</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>outerContainerClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>outerContainerCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>containerClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>listClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>isFloating</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>size</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.spiralRobot.SpiralRobot.BUTTON_SIZES</type>
		</attribute>

		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.spiralRobot.SpiralRobot.BUTTON_TYPES</type>
		</attribute>

		<attribute>
			<name>style</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.spiralRobot.SpiralRobot.BUTTON_STYLES</type>
		</attribute>		
	</tag>

	<tag>
		<name>dropdownItem</name>
		<tag-class>com.orbis.utils.tags.ui.DropdownItemTag</tag-class>
		<dynamic-attributes>true</dynamic-attributes>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
	
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
		</attribute>
	
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>anchorClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>anchorCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>selected</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>

	<tag>
		<name>fileCard</name>
		<tag-class>com.orbis.utils.tags.ui.FileCardTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		
		<attribute>
			<name>fileName</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
						
		<attribute>
			<name>fileType</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>date</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Date</type>
		</attribute>
		
		<attribute>
			<name>icon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>

	<tag>
		<name>fileUpload</name>
		<tag-class>com.orbis.utils.tags.ui.FileUploadTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>uploadDirectory</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>existingFileName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>existingFileUUID</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>formats</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>maxSize</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Long</type>
		</attribute>
	
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>multiple</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>required</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>autoSubmit</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>buttonOnly</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>i18n_buttonTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>i18nArgs_buttonTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18n_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18nArgs_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>inputClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>form</name>
		<tag-class>com.orbis.utils.tags.ui.FormTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		<attribute>
			<name>get</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>autocomplete</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>ajaxForm</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>url</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>name</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>onBeforeSubmit</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>removeEnctype</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>callback</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>grid</name>
		<tag-class>com.orbis.utils.tags.ui.GridTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>reverse</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>align</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.LAYOUT_GRID_HORIZONTAL_ALIGN</type>
		</attribute>
	
		<attribute>
			<name>verticalAlign</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.LAYOUT_GRID_VERTICAL_ALIGN</type>
		</attribute>

		<attribute>
			<name>distribution</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.LAYOUT_GRID_DISTRIBUTION</type>
		</attribute>
	</tag>
	<tag>
		<name>gridAction</name>
		<tag-class>com.orbis.utils.tags.ui.GridActionTag</tag-class>
		<body-content>scriptless</body-content>
		<attribute>
			<name>i18n_name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_name</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>params</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>modalToggle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>gridActionsContainer</name>
		<tag-class>com.orbis.utils.tags.ui.GridActionsContainerTag</tag-class>
		<body-content>scriptless</body-content>
		<attribute>
			<name>gridID</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>gridCol</name>
		<tag-class>com.orbis.utils.tags.ui.GridColTag</tag-class>
		<body-content>scriptless</body-content>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>width</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>offset</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>order</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.LAYOUT_GRID_COL_ORDER</type>
		</attribute>
	</tag>
	<tag>
		<name>gridColumnFilter</name>
		<tag-class>com.orbis.utils.tags.ui.GridColumnFilterTag</tag-class>
		<body-content>scriptless</body-content>
		<attribute>
			<name>colID</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>height</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>select</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>isBoolean</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>multipleValuesInField</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>showTime</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>showDate</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>interactionLoad</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>gridColumnFilterInteractionCategory</name>
		<tag-class>com.orbis.utils.tags.ui.GridColumnFilterInteractionCategoryTag</tag-class>
		<body-content>scriptless</body-content>
		<attribute>
			<name>interactionCategoryColID</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>interactionSubCategoryColID</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>gridColumnHeader</name>
		<body-content>scriptless</body-content>
		<dynamic-attributes>false</dynamic-attributes>
		<tag-class>com.orbis.utils.tags.ui.GridColumnHeaderTag</tag-class>
		<attribute>
			<name>ColID</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>	
	<tag>
		<name>gridOption</name>
		<tag-class>com.orbis.utils.tags.ui.GridOptionTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>optionClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>displayOption</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>selected</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>gridServiceTeamColumnFilter</name>
		<tag-class>com.orbis.utils.tags.ui.GridServiceTeamColumnFilterTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
	</tag>
	<tag>
		<name>gridServiceTeamColumnHeader</name>
		<tag-class>com.orbis.utils.tags.ui.GridServiceTeamColumnHeaderTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
	</tag>
	<tag>
		<name>gridServiceTeamTableCell</name>
		<tag-class>com.orbis.utils.tags.ui.GridServiceTeamTableCellTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<attribute>
			<name>rowData</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>[Ljava.lang.Object;</type>
		</attribute>
	</tag>
	<tag>
		<name>gridTh</name>
		<tag-class>com.orbis.utils.tags.ui.GridThTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>sorting</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>sortType</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>alphabetic</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>realProperty</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>gridThDateFilter</name>
		<tag-class>com.orbis.utils.tags.ui.GridThDateFilterTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>showFromOnly</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>showToOnly</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>linkToGridColumnThProperty</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_customLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>gridThNoFilter</name>
		<tag-class>com.orbis.utils.tags.ui.GridThNoFilterTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
	</tag>
	<tag>
		<name>gridThNumberFilter</name>
		<tag-class>com.orbis.utils.tags.ui.GridThNumberFilterTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>linkToGridColumnThProperty</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_customLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>gridThSelectFilter</name>
		<tag-class>com.orbis.utils.tags.ui.GridThSelectFilterTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>inputClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>linkedFilter</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>preOnChange</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>	
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>width</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>useHtmlForDisplay</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>	
		<attribute>
			<name>multipleValuesInField</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>realProperty</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>linkToGridColumnThProperty</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_customLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>gridThTextFilter</name>
		<tag-class>com.orbis.utils.tags.ui.GridThTextFilterTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
			<attribute>
			<name>property</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>inputClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>linkToGridColumnThProperty</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>customLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>

	<tag>
		<name>simpleHeader</name>
		<tag-class>com.orbis.utils.tags.ui.SimpleHeaderTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			This should be the new common dark header. Meant to replace ui:header
		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>i18n_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>sub-title which appears underneath the progress bar</description>
		</attribute>

		<attribute>
			<name>i18nArgs_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>theme</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.HEADER_THEMES</type>
		</attribute>

		<attribute>
			<name>titleHeaderLevel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.H_LEVELS</type>
		</attribute>

		<attribute>
			<name>subtitleHeaderLevel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.H_LEVELS</type>
		</attribute>
	</tag>

	<tag>
		<name>header</name>
		<tag-class>com.orbis.utils.tags.ui.HeaderTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Deprecated in favor of ui:simpleHeader
		</description>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>autoNavBack</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	
	<tag>
		<name>helpTip</name>
		<body-content>scriptless</body-content>
		<tag-class>com.orbis.utils.tags.ui.HelpTipTag</tag-class>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18n_text</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>i18nArgs_text</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>iconPicker</name>
		<tag-class>com.orbis.utils.tags.ui.IconPickerTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>onSelect</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>isConverted</name>
		<tag-class>com.orbis.utils.tags.ui.IsConvertedTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.spiralRobot.SpiralRobot.IS_CONVERTED_PROGRESS</type>
		</attribute>
		<attribute>
			<name>toolConversion</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>	
	</tag>
	
	<tag>
		<name>documentOverlay</name>
		<tag-class>com.orbis.utils.tags.ui.DocumentOverlayTag</tag-class>
		<dynamic-attributes>true</dynamic-attributes>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>modalClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>modalCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>appendToBody</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		
		<attribute>
			<name>fullscreen</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		
		<attribute>
			<name>showCornerClose</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	
	<tag>
		<name>modal</name>
		<tag-class>com.orbis.utils.tags.ui.ModalTag</tag-class>
		<dynamic-attributes>true</dynamic-attributes>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>titleClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>load</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>includeClose</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>

		<attribute>
			<name>onClose</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>backdrop</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		
		<attribute>
			<name>appendToBody</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	
	<tag>
		<name>fullscreen</name>
		<tag-class>com.orbis.utils.tags.ui.FullscreenTag</tag-class> 
		<body-content>scriptless</body-content>
	 
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>openId</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	 
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute> 
		
		
	</tag>

	<tag>
		<name>navBackItem</name>
		<tag-class>com.orbis.utils.tags.ui.NavBackItemTag</tag-class>
		<dynamic-attributes>true</dynamic-attributes>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
	
		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
	
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>navBack</name>
		<tag-class>com.orbis.utils.tags.ui.NavBackTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>customTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		
		<attribute>
			<name>size</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.BUTTON_SIZES</type>
		</attribute>
		
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.BUTTON_TYPES</type>
		</attribute>
		
		<attribute>
			<name>style</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.BUTTON_STYLES</type>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
	</tag>
	
	<tag>
		<name>note</name>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<tag-class>com.orbis.utils.tags.ui.NoteTag</tag-class>
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>		
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.spiralRobot.SpiralRobot.NOTE_TYPES</type>
		</attribute>
		<attribute>
			<name>block</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>showClose</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>i18n_titleStatus</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_titleStatus</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>statusType</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.utils.tags.ui.TagLabelTag.TAGLABEL_TYPES</type>
		</attribute>		
	</tag>
	<tag>
		<name>bottomBar</name>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<tag-class>com.orbis.utils.tags.ui.BottomBarTag</tag-class>
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>		
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.spiralRobot.SpiralRobot.BOTTOM_BAR_TYPES</type>
		</attribute>
	</tag>
	<tag>
		<name>noteCard</name>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<tag-class>com.orbis.utils.tags.ui.NoteCardTag</tag-class>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>		
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.spiralRobot.SpiralRobot.NOTE_TYPES</type>
		</attribute>
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>pulse</name>
		<body-content>scriptless</body-content>
		<dynamic-attributes>false</dynamic-attributes>
		<tag-class>com.orbis.utils.tags.ui.PulseTag</tag-class>
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.spiralRobot.SpiralRobot.PULSE_TYPES</type>
		</attribute>
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>notification</name>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<tag-class>com.orbis.utils.tags.ui.NotificationTag</tag-class>
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.spiralRobot.SpiralRobot.NOTIFICATION_TYPES</type>
		</attribute>
		<attribute>
			<name>duration</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
	</tag>
	<tag>
		<name>panel</name>
		<dynamic-attributes>false</dynamic-attributes>
		<body-content>scriptless</body-content>
		<tag-class>com.orbis.utils.tags.ui.PanelTag</tag-class>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>hasTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	<tag>
		<name>panelTitle</name>
		<dynamic-attributes>false</dynamic-attributes>
		<body-content>scriptless</body-content>
		<tag-class>com.orbis.utils.tags.ui.PanelTitleTag</tag-class>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>level</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
	</tag>
	<tag>
		<name>progressBar</name>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<tag-class>com.orbis.utils.tags.ui.ProgressBarTag</tag-class>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The percentage value of the progress bar</description>
		</attribute>
		<attribute>
			<name>max</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>The maximum value of the progress</description>
		</attribute>
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The main title that appears on top of the progress bar</description>
		</attribute>
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>showEntityName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>boolean value indicates if the title is displayed or not</description>
		</attribute>
		<attribute>
			<name>action_primary</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>action executed when the main title is clicked</description>
		</attribute>
		<attribute>
			<name>subAction_primary</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>additionalParams_primary</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		<attribute>
			<name>i18n_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>sub-title which appears underneath the progress bar</description>
		</attribute>
		<attribute>
			<name>i18nArgs_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>additionalParams_secondary</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		<attribute>
			<name>action_secondary</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>action executed when the subtitle is clicked</description>
		</attribute>
		<attribute>
			<name>subAction_secondary</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_statusLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_statusLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>labelClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>childContainerCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
	</tag>
	<tag>
		<name>radioButton</name>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<tag-class>com.orbis.utils.tags.ui.RadioButtonTag</tag-class>
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>ondblclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>name</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>checked</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>hideLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>inlineBlockLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>containerClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>radioGroup</name>
		<body-content>scriptless</body-content>
		<tag-class>com.orbis.utils.tags.ui.RadioGroupTag</tag-class>
		
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>readOnly</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>required</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>i18n_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>wizardMode</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	<tag>
		<name>radioGroupOther</name>
		<body-content>scriptless</body-content>
		<tag-class>com.orbis.utils.tags.ui.RadioGroupOtherTag</tag-class>
		
		<attribute>
			<name>name</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>otherBoxName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>checked</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>rangeSlider</name>
		<body-content>scriptless</body-content>
		<dynamic-attributes>false</dynamic-attributes>
		<tag-class>com.orbis.utils.tags.ui.RangeSliderTag</tag-class>
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>min</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
		<attribute>
			<name>max</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>multiple</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
		<attribute>
			<name>readOnly</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	<tag>
		<name>select</name>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<tag-class>com.orbis.utils.tags.ui.SelectTag</tag-class>
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>containerClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>labelClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>arrowClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>helpTextClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>labelCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>helpTextCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>onchange</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>required</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>multiple</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>size</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>inTable</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>i18n_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		<attribute>
			<name>includeEmptyOption</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>i18n_emptyOptionLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_emptyOptionLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>sort</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	<tag>
		<name>selectItem</name>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<tag-class>com.orbis.utils.tags.ui.SelectItemTag</tag-class>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>selected</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>hidden</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>	
		<attribute>
			<name>ariaLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>selectItemGroup</name>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<tag-class>com.orbis.utils.tags.ui.SelectItemGroupTag</tag-class>
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>sidebar</name>
		<tag-class>com.orbis.utils.tags.ui.SidebarTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>location</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.SIDEBAR_LOCATIONS</type>
		</attribute>

		<attribute>
			<name>openButtonSelector</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>removeContentPadding</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>showOnLoad</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>

		<attribute>
			<name>lockBodyScroll</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Applies an overflow--hidden class to the body when the sidebar is open. Defaults to true.</description>
		</attribute>
		
		<attribute>
			<name>overrideLayout</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>

		<attribute>
			<name>appendToBody</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>

		<attribute>
			<name>alwaysAppendToBody</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>
				This needs to be true when the parent element has position: absolute or position: relative, otherwise
				the sidebar will be rendered within the parent element, instead of attaching to the side of the window
			</description>
		</attribute>
		<attribute>
			<name>hideCloseButton</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>
				set to true to hide the close button in the sidebar header.
			</description>
		</attribute>
	</tag>
	
	<tag>
		<name>sidebarSection</name>
		<tag-class>com.orbis.utils.tags.ui.SidebarSectionTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>sidebarHeader</name>
		<tag-class>com.orbis.utils.tags.ui.SidebarHeaderTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>sidebarHeaderStat</name>
		<tag-class>com.orbis.utils.tags.ui.SidebarHeaderStatTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>skipLink</name>
		<body-content>scriptless</body-content>
		<tag-class>com.orbis.utils.tags.ui.SkipLinkTag</tag-class>
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>staticForm</name>
		<body-content>scriptless</body-content>
		<tag-class>com.orbis.utils.tags.ui.StaticFormTag</tag-class>
	</tag>
	<tag>
		<name>staticFormItem</name>
		<tag-class>com.orbis.utils.tags.ui.StaticFormItemTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>

		<attribute>
			<name>date</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	
		<attribute>
			<name>time</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	
	<tag>
		<name>switch</name>
		<tag-class>com.orbis.utils.tags.ui.SwitchTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>	
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>required</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>readOnly</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>onchange</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>switchItem</name>
		<tag-class>com.orbis.utils.tags.ui.SwitchItemTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>checked</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	<tag>
		<name>tabs</name>
		<tag-class>com.orbis.utils.tags.ui.TabsTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			<![CDATA[
			
				{
					content : 'ui:tab' 
				}
			
			]]>
		</description>
		
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>tabSelected</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>tabListClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>tabListCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
	</tag>
	<tag>
		<name>tab</name>
		<tag-class>com.orbis.utils.tags.ui.TabTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>image</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>

		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>

		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>active</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>itemsClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>itemsCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>showSection</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>tabDropdown</name>
		<tag-class>com.orbis.utils.tags.ui.tabDropdown</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>

		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>

		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>active</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>itemsClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>itemsCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>showSection</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	<tag>
		<name>tabSection</name>
		<tag-class>com.orbis.utils.tags.ui.TabSectionTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>forceDisplay</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	<tag>
		<name>textarea</name>
		<tag-class>com.orbis.utils.tags.ui.TextareaTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>containerId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>readOnly</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>required</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.TEXTAREA_TYPE</type>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
		<attribute>
			<name>fieldCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>fieldClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>labelClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>tagLabel</name>
		<tag-class>com.orbis.utils.tags.ui.TagLabelTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.utils.tags.ui.TagLabelTag.TAGLABEL_TYPES</type>		
		</attribute>
		
		<attribute>
			<name>size</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.utils.tags.ui.TagLabelTag.TAGLABEL_SIZES</type>		
		</attribute>
		
		<attribute>
			<name>pill</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>		
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>actionCard</name>
		<tag-class>com.orbis.utils.tags.ui.ActionCardTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>actionClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>leftIcon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>rightIcon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
	</tag>
	<tag>
		<name>textbox</name>
		<tag-class>com.orbis.utils.tags.ui.TextboxTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>containerId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>fieldClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>labelClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_placeholder</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_placeholder</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>readOnly</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>style</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.TEXTBOX_STYLE</type>
		</attribute>
		<attribute>
			<name>fieldCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>labelCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>required</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>disabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>maxLength</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.TEXTBOX_TYPE</type>
		</attribute>
		<attribute>
			<name>layout</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.LAYOUT</type>
		</attribute>
		<attribute>
			<name>onkeydown</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>ondblclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>onfocus</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>min</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
		<attribute>
			<name>max</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
		<attribute>
			<name>step</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
		<attribute>
			<name>leftIcon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>rightIcon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>marginClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>toDo</name>
		<tag-class>com.orbis.utils.tags.ui.ToDoTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.TODO_TYPE</type>
		</attribute>
	</tag>
	
	<tag>
		<name>catalogueCard</name>
		<tag-class>com.orbis.utils.tags.ui.CatalogueCardTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>false</dynamic-attributes>
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18n_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>category</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>description</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>link</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		<attribute>
			<name>i18n_schedule</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>i18nArgs_schedule</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>exp_date</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>htColor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>bgColor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>bgImage</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>htColorEnabled</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	
	<tag>
		<name>catalogueCardSidebar</name>
		<tag-class>com.orbis.utils.tags.ui.CatalogueCardSidebarTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>false</dynamic-attributes>
	</tag>
	
	<tag>
		<name>catalogueCardSidebarHeader</name>
		<tag-class>com.orbis.utils.tags.ui.CatalogueCardSidebarHeaderTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>false</dynamic-attributes>
		<attribute>
			<name>logo</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>exp_date</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>siteElementPath</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>catalogueCardSidebarBody</name>
		<tag-class>com.orbis.utils.tags.ui.CatalogueCardSidebarBodyTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>false</dynamic-attributes>
		<attribute>
			<name>tagAssignPropertyName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>anticipatedCompetencyAssignClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>configId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>recordId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>outlookLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>l2OutlookLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>outlookDescription</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>l2OutlookDescription</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>tripleLookup</name>
		<body-content>scriptless</body-content>
		<tag-class>com.orbis.utils.tags.ui.TripleLookupTag</tag-class>
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>showOrg</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>showDiv</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>showUser</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
	</tag>
	
	<tag>
		<name>keyValueList</name>
		<tag-class>com.orbis.utils.tags.ui.KeyValueListTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>keyValueListItem</name>
		<tag-class>com.orbis.utils.tags.ui.KeyValueListItemTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>key</name>
		<tag-class>com.orbis.utils.tags.ui.KeyTag</tag-class>
		<body-content>scriptless</body-content>
	</tag>
	
	<tag>
		<name>value</name>
		<tag-class>com.orbis.utils.tags.ui.ValueTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>wizard</name>
		<tag-class>com.orbis.utils.tags.ui.WizardTag</tag-class>
		<dynamic-attributes>true</dynamic-attributes>
		<body-content>scriptless</body-content>
		<description>
			This is the main container tag for building a wizard.<br />
			It will instantiate the jquery uiWizard.js plugin and set up all the functionality for building a multi-step wizard.<br />
			The wizard created will need at least 1 ui:wizardGroup tag, and at least 2 ui:wizardStep tags.<br />
			It will output the majority of the UI from the sidebar to the form surrounding the entire wizard.
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Unique identifyer for the wizard. This is the main element used by the javascript plugin $("#id").uiWizard();
			</description>
		</attribute>
		
		<attribute>
			<name>formId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				ID for the ui:form inside wiz; if null, will output as ID plus --form.
			</description>
		</attribute>
		
		<attribute>
			<name>wizardStateId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>
				The primary key of the ui_wizard_state that keeps a state of the wizard that needs to be reloaded.<br />
				If empty, the wizard will create a state of its own.<br />
				Once the state is loaded, it will be updated on every step.
			</description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				A title describing the wizard
			</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				i18n code parameters for i18n_title
			</description>
		</attribute>
		
		<attribute>
			<name>i18n_instructions</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Instructions to help the user through the wizard.<br />
				This can be changed by using $("#id").uiWizard("instructions" [, instructions])
			</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_instructions</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				i18n code parameters for i18n_instructions
			</description>
		</attribute>
		
		<attribute>
			<name>i18n_instructionsTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				A title for i18n_instructions<br />
				This can be changed by using $("#id").uiWizard("instructionsTitle" [, instructionsTitle])
			</description>
		</attribute>

		<attribute>
			<name>jsSkipBtnCallBack</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				JS Call back function that will be called after Skip Step Btn is pressed
			</description>
		</attribute>

		<attribute>
			<name>jsPrevBtnCallBack</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				JS Call back function that will be called after Prev Step Btn is pressed
			</description>
		</attribute>

		<attribute>
			<name>jsNextBtnCallBack</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				JS Call back function that will be called after Next Step Btn is pressed
			</description>
		</attribute>

		<attribute>
			<name>i18nArgs_instructionsTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				i18n code parameters for i18n_instructionsTitle
			</description>
		</attribute>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Action used when finishing the wizard.<br />
				This is where the entire form gets sent out to the server, happens when the finish button is clicked.
			</description>
		</attribute>
		
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Action aware parameter used with action
			</description>
		</attribute>
		
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>
				Action aware parameter used with action
			</description>
		</attribute>
		
		<attribute>
			<name>action_synchForm</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Action used when changes happen to the form. <br />
				The entire form will be sent to this action along with the parameters "wizardId", "wizardStateId", "wizardCompleted", "currentStep" and "lastStep".<br />
				Use UITagHelper#getWizard() to gather these attributes.
			</description>
		</attribute>
		
		<attribute>
			<name>subAction_synchForm</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Action aware parameter used with action_synchForm
			</description>
		</attribute>
		
		<attribute>
			<name>siteElement_synchForm</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>
				Action aware parameter used with action_synchForm
			</description>
		</attribute>
		
		<attribute>
			<name>callback_synchForm</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Action aware parameter used with action_synchForm
			</description>
		</attribute>
		
		<attribute>
			<name>dynamicVueClass</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Class name for dynamically loaded vue sections within each step.
				This can be used if steps have their dynamicVue attribute set to false and you still want dynamic data being added to the step content, or if you have a more complex vue component set up within the step.
			</description>
		</attribute>
	</tag>
	<tag>
		<name>wizardStepGroup</name>
		<tag-class>com.orbis.utils.tags.ui.WizardStepGroupTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Tag used to give each set of steps a title. Each wizard will need at least one visible group and each group will need at least one visible step.
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Unique identifyer for the group. This will be used to reference the group in the uiWizard JS lib.
			</description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Title describing the group of steps. This will also show up in the wizard progress bar.
			</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				i18n args parameter for i18n_title
			</description>
		</attribute>
		
		<attribute>
			<name>displayCondition</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				A javascript condition that will dictate if this group should be accessible.<br />
				A js object named 'form' will be accessible in this attribute, containing all the up to date parameters from the current wizard form.<br />
				This condition will fire whenever the form is stored.
			</description>
		</attribute>
		
		<attribute>
			<name>hideStepNumber</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>
				Hides step number that is usually show in the header of each step of the wizard
			</description>
		</attribute>
	</tag>
	<tag>
		<name>wizardStep</name>
		<tag-class>com.orbis.utils.tags.ui.WizardStepTag</tag-class>
		<dynamic-attributes>true</dynamic-attributes>
		<body-content>scriptless</body-content>
		
		<description>
			This is where any form elements related to the wizard should be.<br />
			Every wizard should have at least 2 visible steps.
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				The unique identifier for each step. This will be used to reference the step in the uiWizard JS lib, and in the wizard state table.
			</description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Title describing the form elements under the step. This will also show up in the wizard progress bar.
			</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				i18n args parameter for i18n_title
			</description>
		</attribute>
		
		<attribute>
			<name>displayCondition</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				A javascript condition that will dictate if this step should be accessible.<br />
				A js object named 'form' will be accessible in this attribute, containing all the up to date parameters from the current wizard form.<br />
				This condition will fire whenever the form is stored.
			</description>
		</attribute>
		
		<attribute>
			<name>additionalStepValidation</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				A javascript condition that will dictate if this step should be able to progress.<br />
				A js object named 'form' will be accessible in this attribute, containing all the up to date parameters from the current wizard form.<br />
				This condition will fire whenever the form is validated.
				It will be up to the logic in this attribute to display an error message explaining why the user cannot advance in the form.
			</description>
		</attribute>
		
		<attribute>
			<name>allowSkip</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>
				Display the "skip" button on this step, allowing the user filling out the form, to ignore the validation and proceed to the next step.
			</description>
		</attribute>
		
		<attribute>
			<name>status</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.utils.tags.ui.WizardStepTag.STEP_STATUS</type>
			<description>
				Manual way to assign a step's initial status. This will automatically be set when the wizard is given a wizardStateId.<br />
				This attribute will accept the following values: incomplete, complete, skipped.
			</description>
		</attribute>
		
		<attribute>
			<name>dynamicVue</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>
				Allows the wizard to search for {{*}} notation without needing to surround it with a container with the wizard's [^wizard dynamicVueClass] class.<br />
				Should be disabled if the step contains vue components of its own.
			</description>
		</attribute>

		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				If specified the step will be ajax loaded using this action method.
			</description>
		</attribute>
	</tag>
	
	<tag>
		<name>wizardVueSection</name>
		<tag-class>com.orbis.utils.tags.ui.WizardVueSectionTag</tag-class>
		<dynamic-attributes>true</dynamic-attributes>
		<body-content>scriptless</body-content>
		<description>
			Subtag to wizard, will render the content as a vue instance with formData as a data object containing the up to date wizard form
		</description>
	</tag>
	
	
	<tag>
		<name>codeSample</name>
		<tag-class>com.orbis.utils.tags.ui.CodeSampleTag</tag-class>
		<body-content>JSP</body-content>
		<description>
			Tag that will output it's contents in a formated div along with it's evaluated html
		</description>
		
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				The unique identifier for each step. This will be used to reference the tag when searching through the jsp using an xml parser
			</description>
		</attribute>
		
		<attribute>
			<name>title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				A title describing the code being demonstrated in the sample
			</description>
		</attribute>
		
		<attribute>
			<name>showOutput</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Show the evaluated body content. Default: true</description>
		</attribute>
		
		<attribute>
			<name>showOutput</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Show the evaluated body render. Default: true</description>
		</attribute>
		
		<attribute>
			<name>language</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.utils.tags.ui.LANGUAGE</type>
			<description>The language used in the sample. Default: markup, Available values: sql, markup</description>
		</attribute>
	</tag>
	
	<tag>
		<name>manageItemList</name>
		<tag-class>com.orbis.utils.tags.ui.ManageItemListTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
				 
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>hasInstructions</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>When true, creates a note beside widget with width--4 using other instruction attributes</description>
		</attribute> 
		 
		<attribute>
			<name>i18n_instructionsTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute> 
		
		<attribute>
			<name>i18n_instructionsContent</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute> 
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Java method to call when submit is pressed.</description>
		</attribute>
		
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>useDefaultActions</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Use the default actions when it makes sense to allow a number of changes and then call a single Java method when 'submit'. The default
			actions build an 'added' and 'removed' list based on items dropped in two manageItemListCols. These are passed as JSON arrays in the request
			to the method specified as the manageItemList's 'action'</description>
		</attribute>
	</tag>
	
	<tag>
		<name>manageItemListCol</name>
		<tag-class>com.orbis.utils.tags.ui.ManageItemListColTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Column of items for manageItemsList. If using parent tag's default functions, only use two of these. 
		</description>
		
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute> 
		
		<attribute>
			<name>releaseAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Override Javascript function to call when extra functions are required. To function, the parent's 'useDefaultActions' must be
			false. The string passed to this attribute will be used to call the function on the JSP and will be passed the 'board-item' element
			that was just released. If the board item should pass a value when dropped, you'll have to set the attribute 'data-value' on the board-item element</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute> 
		
		<attribute>
			<name>headerColor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>BG color for header block. Value will be appended to class of 'color--bg--' </description>
		</attribute> 
		
		
	</tag>

	<tag>
		<name>manageItemsSidebar</name>
		<tag-class>com.orbis.utils.tags.ui.ManageItemsSidebarTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Tag that will manage users in a module config or items in a checkbox group.
		</description>
		
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>callback</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		
		<attribute>
			<name>formData</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>formDataValue</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>items</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.List</type>
		</attribute>
		
		<attribute>
			<name>checkboxName</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The name of the checkboxes inside the sidebar</description>
		</attribute>
		
		<attribute>
			<name>checklist</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
			<description>A checklist of all the checked items</description>
		</attribute>
		
		<attribute>
			<name>isManageUsers</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>specifies if the tag is used for managing users or just simply checkbox group</description>
		</attribute>
	</tag>
	
	<tag>
		<name>listCard</name>
		<tag-class>com.orbis.utils.tags.ui.ListCardTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
		</description>
			Card that can display in a list
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>icon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The name of the material icon to be displayed. Can be used at the same time as the value (ex:face)</description>
		</attribute>
				
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
				
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>A string that will show up in with or without the icon</description>
		</attribute>
		
		<attribute>
			<name>size</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.LIST_CARD_SIZES</type>
		</attribute>
		
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.LIST_CARD_TYPES</type>
		</attribute>
						
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
				
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18n_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18nArgs_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>listCardList</name>
		<tag-class>com.orbis.utils.tags.ui.ListCardListTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Container for listCard.
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>isEdit</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Allows for bulk edit of listCard checkboxes</description>		
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
				
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
				
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>formPage</name>
		<tag-class>com.orbis.utils.tags.ui.FormPageTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<description>
			Tag that contains a whole form page with form and form description
		</description>
		<dynamic-attributes>true</dynamic-attributes>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The id of the form page</description>
		</attribute>
		<attribute>
			<name>formId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The id of the form inside the form page</description>
		</attribute>
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The action of the form inside the form page</description>
		</attribute>
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The sub action of the form inside the form page</description>
		</attribute>
		<attribute>
			<name>hideActionButtons</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Hide 'Save' and 'Cancel' form buttons.</description>
		</attribute>
		<attribute>
			<name>cancelAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The action called when the cancel button is hit.</description>
		</attribute>
		<attribute>
			<name>cancelOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The javascript called when the cancel button is hit.</description>
		</attribute>
		<attribute>
			<name>saveOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The javascript called when the save button is hit. Replaces the form submit if populated</description>
		</attribute>
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>The site element of the form inside the form page</description>
		</attribute>
		<attribute>
			<name>scrollOffset</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>Sets the offset of the scroll to behaviour of the form page navigation. Defaults to 140 or 20 if tag is in configurationListPage tag</description>
		</attribute>
		<attribute>
			<name>get</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>boolean value indicates if the method of the form is get or post</description>
		</attribute>
		<attribute>
			<name>autocomplete</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Boolean value indicates if the form is autocomplete</description>
		</attribute>
		<attribute>
			<name>ajaxForm</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Boolean value indicates if the form is ajax form</description>
		</attribute>
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The target of the form inside the form page</description>
		</attribute>
		<attribute>
			<name>url</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The url of the form inside the form page</description>
		</attribute>
		<attribute>
			<name>formName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The name of the form inside the form page</description>
		</attribute>
		<attribute>
			<name>onBeforeSubmit</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The onsubmit value of the form inside the form page</description>
		</attribute>
		<attribute>
			<name>removeEnctype</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>The id of the form inside the form page</description>
		</attribute>
		<attribute>
			<name>formClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The classes of the form inside the form page</description>
		</attribute>
		<attribute>
			<name>formPageClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The classes of the gridcol 8</description>
		</attribute>
		<attribute>
			<name>formDescriptionClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The classes of the gridcol 4</description>
		</attribute>
		<attribute>
			<name>formCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The css of the form inside the form page</description>
		</attribute>
		<attribute>
			<name>callback</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The callback of the form inside the form page</description>
		</attribute>

		<attribute>
			<name>i18n_saveLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>i18n value of the title of the fieldset</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_saveLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
		
		<attribute>
			<name>i18n_cancelLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>i18n value of the title of the fieldset</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_cancelLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
	</tag>
	
	<tag>
		<name>formGroup</name>
		<tag-class>com.orbis.utils.tags.ui.FormGroupTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Tag that contains a single fiedset group of input inside the form
		</description>
		<dynamic-attributes>false</dynamic-attributes>
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>id of the input group</description>
		</attribute>
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>i18n value of the title of the fieldset</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
	</tag>
	<tag>
		<name>sortable</name>
		<tag-class>com.orbis.utils.tags.ui.SortableTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Tag that contains other tags to allow drag and drop between them.
				The children tags will also need to be is--draggable
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>id of the sortable div</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>additional classes for the sortable div</description>
		</attribute>
		
		<attribute>
			<name>action_synchOrder</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Action used when changes happen to the form. <br />
				The new order of the list will be sent to this action along with the parameters <br />
			</description>
		</attribute>
		
		<attribute>
			<name>subAction_synchOrder</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Action aware parameter used with action_synchForm
			</description>
		</attribute>
		
		<attribute>
			<name>siteElement_synchOrder</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>
				Action aware parameter used with action_synchForm
			</description>
		</attribute>
		
	</tag>
	
	<tag>
		<name>sortableTable</name>
		<tag-class>com.orbis.utils.tags.ui.SortableTableTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Replaces the table tag and converts its tbody into a sortable one <br />
				Be sure to add a .js--draggable on the sortable table rows <br /> 
				Be sure to add a .drag--handle on a span that has a material-icon drag_handle
		</description>
		<dynamic-attributes>true</dynamic-attributes>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>id of the sortable table</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>additional classes for the sortable table</description>
		</attribute>
		
		<attribute>
			<name>action_synchOrder</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Action used when changes happen to the form. <br />
				The new order of the list will be sent to this action along with the parameters <br />
			</description>
		</attribute>
		
		<attribute>
			<name>subAction_synchOrder</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Action aware parameter used with action_synchForm
			</description>
		</attribute>
		
		<attribute>
			<name>siteElement_synchOrder</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>
				Action aware parameter used with action_synchForm
			</description>
		</attribute>
		
	</tag>
	
	<tag>
		<name>userProfileHeader</name>
		<tag-class>com.orbis.utils.tags.ui.UserProfileHeaderTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Header tag for common user profiles
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>id of expandable container</description>
		</attribute>
		
		<attribute>
			<name>user</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl</type>
			<description>User details object that will populate this header</description>
		</attribute>
		
		<attribute>
			<name>i18n_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Main header sub title</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Main header sub title args</description>
		</attribute>
		
		<attribute>
			<name>userStatus</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.HEADER_STATUS</type>
			<description>Handles the status icon set in the upper part of the profile picture</description>
		</attribute>

		<attribute>
			<name>excludeImageSection</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>shows or hide image section</description>
		</attribute>

		<attribute>
			<name>overrideTitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>allows override title using ui:section</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>additional classes for the outer most container</description>
		</attribute>
	</tag>
	
	<tag>
		<name>divisionProfileHeader</name>
		<tag-class>com.orbis.utils.tags.ui.DivisionProfileHeaderTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Header tag for common user profiles
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>id of expandable container</description>
		</attribute>
		
		<attribute>
			<name>division</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.content.crm.Company</type>
			<description>User details object that will populate this header</description>
		</attribute>
		
		<attribute>
			<name>i18n_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Main header sub title</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Main header sub title args</description>
		</attribute>
		
		<attribute>
			<name>divisionStatus</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.HEADER_STATUS</type>
			<description>Handles the status icon set in the upper part of the profile picture</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>additional classes for the outer most container</description>
		</attribute>
		
		<attribute>
			<name>version2</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Use version 2 of header. Once version 1 is completely removed or we decide against version 2, please remove this variable.</description>
		</attribute>
	</tag>
	
	<tag>
		<name>organizationProfileHeader</name>
		<tag-class>com.orbis.utils.tags.ui.OrganizationProfileHeaderTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Header tag for common user profiles
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>id of expandable container</description>
		</attribute>
		
		<attribute>
			<name>organization</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.content.crm.Organization</type>
			<description>User details object that will populate this header</description>
		</attribute>
		
		<attribute>
			<name>i18n_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Main header sub title</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Main header sub title args</description>
		</attribute>
		
		<attribute>
			<name>organizationStatus</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.HEADER_STATUS</type>
			<description>Handles the status icon set in the upper part of the profile picture</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>additional classes for the outer most container</description>
		</attribute>
		
		<attribute>
			<name>version2</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Use version 2 of header. Once version 1 is completely removed or we decide against version 2, please remove this variable.</description>
		</attribute>
	</tag>
	
	<tag>
		<name>jobPostingProfileHeader</name>
		<tag-class>com.orbis.utils.tags.ui.JobPostingProfileHeaderTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Header tag for common job posting profiles
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>id of expandable container</description>
		</attribute>
		
		<attribute>
			<name>posting</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.content.np.NPosting</type>
			<description>Job Posting object that will populate this header</description>
		</attribute>
		
		<attribute>
			<name>i18n_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Main header sub title</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Main header sub title args</description>
		</attribute>
		
		<attribute>
			<name>postingStatus</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.HEADER_STATUS</type>
			<description>Handles the status icon set in the upper part of the profile picture</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>additional classes for the outer most container</description>
		</attribute>
		
		<attribute>
			<name>previewMode</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>put the header into preview mode if the job posting currently being viewed is in preview mode</description>
		</attribute>
	</tag>
	
	<tag>
		<name>headerTags</name>
		<tag-class>com.orbis.utils.tags.ui.HeaderTagsTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Tag container for the tags ui to be placed inside of a large header
		</description>
		
		<attribute>
			<name>displayLimit</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>
				Number of tags to display in the header, if there are more tags, a button will pop up to show a modal with all of the tags.
				Set to 0 for no limit
				Default: 8
			</description>
		</attribute>
	</tag>
	
	<tag>
		<name>headerTagsItem</name>
		<tag-class>com.orbis.utils.tags.ui.HeaderTagsItemTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Tags to configure the HeaderTagsTag
		</description>
		
		<attribute>
			<name>priority</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.HEADER_TAG_PRIORITY</type>
			<description>Affects the color and display order of the tag. Default: low</description>
		</attribute>
	</tag>
	
	<tag>
		<name>expandable</name>
		<tag-class>com.orbis.utils.tags.ui.ExpandableTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Apply to lists or containers to hide contents
		</description>
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>id of expandable container</description>
		</attribute>
		<attribute>
			<name>classes</name>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>classes applied to expandable container</description>
		</attribute>
		<attribute>
			<name>type</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				type of expandable to render out<br />
				Can be 'list', 'table', 'block'
			</description>
		</attribute>
		<attribute>
			<name>canHide</name>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>
				if true, show button will become hide button when expanded; else, button will disappear on open
			</description>
		</attribute>
		<attribute>
			<name>buttonType</name>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				accepts icon (will show + or -) or text (will show 'Show More' and 'Hide')
			</description>
		</attribute>
		<attribute>
			<name>displayItems</name>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>
				Number of items to show before hiding and adding button -- only relevant with list type
			</description>
		</attribute>
	</tag>
	<tag>
		<name>spiralRobotTemplate</name>
		<tag-class>com.orbis.utils.tags.ui.SpiralRobotTemplateTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Spiral Robot Template
		</description>
	</tag>
	<tag>
		<name>action</name>
		<tag-class>com.orbis.utils.tags.ui.ActionTag</tag-class>
		<body-content>empty</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<description>
			Used to add one or many actions to tags.
			These processed action can be found in the respective tag's ActionAwareUITag#actions map.
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID of the data grid. This will be the key to collect the ActionAttribute in the ActionAwareUITag#actions map. These can be duplicated as the action map will store multiple actions per ID.</description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The title of the action that will allow the tag to label elements that will perform the action</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Internationalization arguments for i18n_title</description>
		</attribute>
		
		<attribute>
			<name>icon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Icon used to represent the action. This should be populated with a title describing the action as well.</description>
		</attribute>
		
		<attribute>
			<name>i18n_description</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The description of the action that will allow the tag to display what the current action is going to do when executing the action</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_description</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Internationalization arguments for i18n_description</description>
		</attribute>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Unencrypted action to be submitted when the button is clicked.</description>
		</attribute>
		
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Javascript attribute to fire whenever the button is clicked.
			</description>
		</attribute>
	
		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Text to be shown in a confirm modal that will execute the button's click behaviour if the confirm modal is accepted.<br />
				The click behaviour in the modal will not run if the confirm modal is declined or closed.
			</description>
		</attribute>
	
		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The args attribute for i18n_confirmOnclick</description>
		</attribute>
		
		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
		
		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the action element if applicable</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the action element if applicable</description>
		</attribute>
		
		<attribute>
			<name>iconClasses</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the icon element if applicable</description>
		</attribute>
		
		<attribute>
			<name>iconCss</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the action icon if applicable</description>
		</attribute>
	</tag>
	
	<tag>
		<name>tour</name>
		<tag-class>com.orbis.utils.tags.ui.TourTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			A way of creating a tour in a webpage. It will take in several tourStep tags and init the $.uiTour js library.
			To start the tour, run $("#tourTagId").uiTour("startTour")
		</description>
		
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The unique ID of the tour.</description>
		</attribute>
		
		<attribute>
			<name>runOnLoad</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Start the tour as soon as the page is ready</description>
		</attribute>
	</tag>
	
	<tag>
		<name>tourStep</name>
		<tag-class>com.orbis.utils.tags.ui.TourStepTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			A way of creating a tour in a webpage. It will take in several tourStep tags and init the $.uiTour js library.
			To start the tour, run $("#tourTagId").uiTour("startTour").
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The unique ID of the step.</description>
		</attribute>
		
		<attribute>
			<name>focus</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>A css selector of the items being described in the step</description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The title of the step</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Internationalization arguments for i18n_title</description>
		</attribute>
		
		<attribute>
			<name>onShow</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Javascript callback that will handle what happens with the step is displayed</description>
		</attribute>
		
		<attribute>
			<name>onHide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Javascript callback that will handle what happens with the step is displayed</description>
		</attribute>
	</tag>
	
	<tag>
		<name>checkboxTable</name>
		<tag-class>com.orbis.utils.tags.ui.CheckboxTableTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			The ui:checkboxTable can be used to set up a range of items that have the same set of boolean properties within them.
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the table that can be used to select the outter most element in the html structure and to hook into the javascript library</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Title describing the content within the table. Can be one or more i18n codes and / or plain text.</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_title</description>
		</attribute>
	</tag>
	
	<tag>
		<name>checkboxTableFlag</name>
		<tag-class>com.orbis.utils.tags.ui.CheckboxTableFlagTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Defines the boolean flags that are common through all defined ui:checkboxTableRow tags.
			Can only be used within the ui:checkboxTable tag.
		</description>
		
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the flag that will be used as an attribute in the ui:checkboxTableRow to assign it's value to the specified row. It will also be used to build the name of the checkboxes in the column</description>
		</attribute>
		
		<attribute>
			<name>type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.utils.tags.ui.CheckboxTableFlagTag.FLAG_TYPE</type>
			<description>Sets the display type of the flag</description>
		</attribute>
	</tag>
	
	<tag>
		<name>checkboxTableRow</name>
		<tag-class>com.orbis.utils.tags.ui.CheckboxTableRowTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<description>
			Defines the rows, their labels and their data.
			Can only be used within the ui:checkboxTable tag.
		</description>
		
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the row that will be used to build names of checkboxes in the row</description>
		</attribute>
	</tag>
	
	<tag>
		<name>tagCloud</name>
		<tag-class>com.orbis.utils.tags.ui.TagCloudTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<description>
			The container for one or many tagGroup tags. This will provide the common UI container for tag groups.
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the tag cloud</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The style classes assigned to the tag cloud</description>
		</attribute>
		
		<attribute>
			<name>i18n_cloudName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Name of the cloud. Will be put in the panel header before 'Tag Cloud'
			</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_cloudName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Name of the cloud. Will be put in the panel header before 'Tag Cloud'
			</description>
		</attribute>
		
		<attribute>
			<name>showAddLookup</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>
				Show the Add Tag Lookup in the cloud
			</description>
		</attribute>
		
		<attribute>
			<name>manageButtonId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				The ID of the manage button in the tag cloud body that will be moved up into the panel heading
			</description>
		</attribute>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Unencrypted action to be submitted when the button is clicked.</description>
		</attribute>
		
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
	</tag>
	
	<tag>
		<name>tagGroup</name>
		<tag-class>com.orbis.utils.tags.ui.TagGroupTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<description>
			Used inside of tagCloud tag, this is a common view for classes extending Tag, TagCategory and TagAssigned. This can be populated via AcrmHelper#populateAjaxTagCloud (for an ajax loaded tag cloud) and AcrmHelper#populateTagCloud (for a static tag cloud)
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the tag group</description>
		</attribute>
		
		<attribute>
			<name>cloudKey</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Required. This is the key passed in to AcrmHelper#populateAjaxTagCloud and AcrmHelper#populateTagCloud so that we can uniquely identify the tag data being passed around.
			</description>
		</attribute>
		
		<attribute>
			<name>groupName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				String displayed in the group title section.
			</description>
		</attribute>
		
		<attribute>
			<name>selectedTags</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
			<description>
				The set of tags currently selected.
			</description>
		</attribute>
		
		<attribute>
			<name>expiryExtendAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				The action for extending an expired tag.
			</description>
		</attribute>
		
		<attribute>
			<name>autoLoad</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>
				Default true. This is only used when an action is provided.
			</description>
		</attribute>
		
		<attribute>
			<name>allowRemove</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>
				Show remove button beside tags.
			</description>
		</attribute>
		
		<attribute>
			<name>unassignableFromLookup</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>
				Default false. This tag group is not to be included in this tag clouds lookup as an assignable tag. 
				Useful when you are showing mutliple tag assignables in the same tag cloud. Should be set to true for all tag 
				groups that aren't for tag assigns of the main tag assignable of this page. Ex: Set to true for the wtr tags in the nposting tag cloud 
				or set to true for the student tags in the exprecord tag cloud
			</description>
		</attribute>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Unencrypted action to be submitted when the button is clicked.</description>
		</attribute>
		
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
	</tag>
	
	<tag>
		<name>optionCatalog</name>
		<tag-class>com.orbis.utils.tags.ui.OptionCatalogTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>
		
		<attribute>
			<name>allOptionsVisible</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Show all options on the page and clicking on the group names will just scroll to them instead of hide/show them</description>
		</attribute>
		
		<attribute>
			<name>enableGroupCopy</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Adds functionality that will allow the user to copy the option states from one group into another. This tool works based off of the values of the options, it will relate two options if they have the same value. Should only be enabled if the options in most or all the groups are the same.</description>
		</attribute>
		
		<attribute>
			<name>disableCategoryHeader</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Removed the category button group from the top of the option catalog when there are categories assigned to the catalog</description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Title describing the content within the group. Can be one or more i18n codes and / or plain text</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_title</description>
		</attribute>
	</tag>
	
	<tag>
		<name>optionCatalogGroup</name>
		<tag-class>com.orbis.utils.tags.ui.OptionCatalogGroupTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>
		
		<attribute>
			<name>name</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Name that will be assigned to the checkbox and used to collect the data on the server side. If empty, no checkbox will be added to the group.</description>
		</attribute>
		
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Value that will be assigned to the checkbox and used to collect the data on the server side</description>
		</attribute>
		
		<attribute>
			<name>checked</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Checked state of the checkbox</description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Title describing the content within the group. Can be one or more i18n codes and / or plain text</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_title</description>
		</attribute>
		
		<attribute>
			<name>i18n_description</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Description text describing the content within the group. Can be one or more i18n codes and / or plain text. If this is populated, a button will show up beside the group to open a modal up with the description.</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_description</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_description</description>
		</attribute>
		
		<attribute>
			<name>attr1</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Utility property for this tag</description>
		</attribute>
	</tag>
	<tag>
		<name>optionCatalogCategory</name>
		<tag-class>com.orbis.utils.tags.ui.OptionCatalogCategoryTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			These will need to be declared before the groups and the options. If categories are used, every option is expected to have a category tied to it.
		</description>
		
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>
		
		<attribute>
			<name>i18n_description</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Description text describing the content within the group. Can be one or more i18n codes and / or plain text. If this is populated, a button will show up beside the group to open a modal up with the description.</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_description</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_description</description>
		</attribute>
		
		<attribute>
			<name>hideInHeader</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Hides this category from the header</description>
		</attribute>
		
		<attribute>
			<name>rootCompetencyName</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Top competency name of this child</description>
		</attribute>
	</tag>
	
	<tag>
		<name>optionCatalogOption</name>
		<tag-class>com.orbis.utils.tags.ui.OptionCatalogOptionTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>
		
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Name that will be assigned to the checkbox and used to collect the data on the server side</description>
		</attribute>
		
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Value that will be assigned to the checkbox and used to collect the data on the server side</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>CSS classes applied to the checkbox</description>
		</attribute>
		
		<attribute>
			<name>categoryId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>ID of the category that this option relates to</description>
		</attribute>
		
		<attribute>
			<name>checked</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Checked state of the checkbox</description>
		</attribute>
		
		<attribute>
			<name>i18n_description</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Description text describing the content within the sub group. Can be one or more i18n codes and / or plain text.  If this is populated, a button will show up beside the option to open a modal up with the description.</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_description</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_title</description>
		</attribute>
		
		<attribute>
			<name>excludeHeader</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>If the section 'pillHeader' is set on the ui:optionCatalog tag, this flag can be used to exclude that header from a specific option</description>
		</attribute>
		
		<attribute>
			<name>i18n_toolTip</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>
	
	<tag>
		<name>insightsCard</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsCardTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>
		
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The value of the stat being displayed</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
	
		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Javascript attribute to fire whenever the button is clicked.
			</description>
		</attribute>

		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Text to be shown in a confirm modal that will execute the button's click behaviour if the confirm modal is accepted.<br />
				The click behaviour in the modal will not run if the confirm modal is declined or closed.
			</description>
		</attribute>

		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The args attribute for i18n_confirmOnclick</description>
		</attribute>

		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>

		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsChartCard</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsChartCard</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>statCard</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>StatCard</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsStatCard</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsStatCard</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>cardIndex</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CardIndex</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsDiagramChart</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsDiagramChart</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>statCard</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>StatCard</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsPieChart</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsPieChart</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>statCard</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>StatCard</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsVerticalRectangleChart</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsVerticalRectangleChart</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>statCard</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>StatCard</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsMediumSquareChart</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsMediumSquareChart</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>statCard</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>StatCard</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsCardMediumSquare</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsCardMediumSquare</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>initialCount</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>cardIndex</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>StatCard</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsCardWideBanner</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsCardWideBanner</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>initialCount</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>cardIndex</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>StatCard</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>
	</tag>

	<tag>
		<name>insightButtonTemplate</name>
		<tag-class>com.orbis.utils.tags.ui.InsightButtonTemplate</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>
	</tag>


	<tag>
		<name>insightsChartCard</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsChartCard</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>statCard</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>StatCard</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsStatCard</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsStatCard</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>cardIndex</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CardIndex</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsDiagramChart</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsDiagramChart</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>statCard</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>StatCard</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsPieChart</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsPieChart</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>statCard</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>StatCard</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsVerticalRectangleChart</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsVerticalRectangleChart</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>statCard</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>StatCard</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsMediumSquareChart</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsMediumSquareChart</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>statCard</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>StatCard</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsCardMediumSquare</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsCardMediumSquare</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>initialCount</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>cardIndex</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>StatCard</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsCardWideBanner</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsCardWideBanner</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>initialCount</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Data</description>
		</attribute>

		<attribute>
			<name>cardIndex</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>StatCard</description>
		</attribute>

		<attribute>
			<name>data</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Data</description>
		</attribute>
	</tag>

	<tag>
		<name>insightButtonTemplate</name>
		<tag-class>com.orbis.utils.tags.ui.InsightButtonTemplate</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the option catalog</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>
	</tag>

	<tag>
		<name>insightsCardWide</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsCardWideTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the card</description>
		</attribute>
		
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The value of the stat being displayed</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Javascript attribute to fire whenever the button is clicked.
			</description>
		</attribute>
	
		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Text to be shown in a confirm modal that will execute the button's click behaviour if the confirm modal is accepted.<br />
				The click behaviour in the modal will not run if the confirm modal is declined or closed.
			</description>
		</attribute>

		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The args attribute for i18n_confirmOnclick</description>
		</attribute>

		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>

		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>

		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Javascript attribute to fire whenever the button is clicked.
			</description>
		</attribute>

		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Text to be shown in a confirm modal that will execute the button's click behaviour if the confirm modal is accepted.<br />
				The click behaviour in the modal will not run if the confirm modal is declined or closed.
			</description>
		</attribute>

		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The args attribute for i18n_confirmOnclick</description>
		</attribute>

		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>

		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
	</tag>
	
	<tag>
		<name>insightsCardWideChart</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsCardWideChartTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the card</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>showValuesAsPercent</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Show values in the legend as a percentage. Default: true</description>
		</attribute>
	</tag>
	
	<tag>
		<name>insightsCardTallChart</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsCardTallChartTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the card</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>
	</tag>
	
	<tag>
		<name>experienceCardChart</name>
		<tag-class>com.orbis.utils.tags.ui.ExperienceCardChartTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the card</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Unencrypted action to be submitted when the button is clicked.</description>
		</attribute>

		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>

		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>

		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Action aware parameter used with action</description>
		</attribute>

		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
			<description>Action aware parameter used with action</description>
		</attribute>

		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>

		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Javascript attribute to fire whenever the button is clicked.
			</description>
		</attribute>

		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Text to be shown in a confirm modal that will execute the button's click behaviour if the confirm modal is accepted.<br />
				The click behaviour in the modal will not run if the confirm modal is declined or closed.
			</description>
		</attribute>

		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The args attribute for i18n_confirmOnclick</description>
		</attribute>

		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>

		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>

		<dynamic-attributes>true</dynamic-attributes>
	</tag>
	
	<tag>
		<name>insightsCardSmallWide</name>
		<tag-class>com.orbis.utils.tags.ui.InsightsCardSmallWideTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the card</description>
		</attribute>
		
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>The value of the stat being displayed</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>

		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Javascript attribute to fire whenever the button is clicked.
			</description>
		</attribute>

		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Text to be shown in a confirm modal that will execute the button's click behaviour if the confirm modal is accepted.<br />
				The click behaviour in the modal will not run if the confirm modal is declined or closed.
			</description>
		</attribute>

		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The args attribute for i18n_confirmOnclick</description>
		</attribute>

		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>

		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>

	</tag>

	<tag>
		<name>chartSeries</name>
		<tag-class>com.orbis.utils.tags.ui.ChartSeriesTag</tag-class>
		<body-content>scriptless</body-content>
		<description>

		</description>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the data</description>
		</attribute>

		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Description text describing the series. Can be one or more i18n codes and / or plain text.</description>
		</attribute>

		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_title</description>
		</attribute>
	</tag>
	
	<tag>
		<name>expTranscriptCards</name>
		<tag-class>com.orbis.utils.tags.ui.ExpTranscriptCardsTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the data</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>
	</tag>
	
	<tag>
		<name>expTranscriptCard</name>
		<tag-class>com.orbis.utils.tags.ui.ExpTranscriptCardTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS to be applied to the outer most element</description>
		</attribute>

		<attribute>
			<name>headerTextColor</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Accepts any type of color string supported by css to apply to text in the header</description>
		</attribute>

		<attribute>
			<name>headerBackground</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Accepts a URL, an image data: string or any type of color string supported by css to apply to the header</description>
		</attribute>
		
		<attribute>
			<name>preview</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Render the card into preview mode.</description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_title</description>
		</attribute>
		
		<attribute>
			<name>i18n_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_subtitle</description>
		</attribute>
		
		<attribute>
			<name>i18n_description</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_description</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_description</description>
		</attribute>

		<attribute>
			<name>i18n_type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_type</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_type</description>
		</attribute>
		
		<attribute>
			<name>i18n_term</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_term</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_term</description>
		</attribute>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Unencrypted action to be submitted when the button is clicked.</description>
		</attribute>

		<attribute>
			<name>modalTitleLinkParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
			<description>Title link params map of a modal card window with a set of parameters to go to the underlying real activity.</description>
		</attribute>
		
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Javascript attribute to fire whenever the button is clicked.
			</description>
		</attribute>
	
		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Text to be shown in a confirm modal that will execute the button's click behaviour if the confirm modal is accepted.<br />
				The click behaviour in the modal will not run if the confirm modal is declined or closed.
			</description>
		</attribute>
	
		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The args attribute for i18n_confirmOnclick</description>
		</attribute>
		
		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
		
		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>

		<attribute>
			<name>altTextDescription</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes a string to display the alt-text of images in cards</description>
		</attribute>
		
	</tag>

	<tag>
		<name>iconListCards</name>
		<tag-class>com.orbis.utils.tags.ui.IconListCardsTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the data</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>
		
	</tag>
	
	<tag>
		<name>experienceCards</name>
		<tag-class>com.orbis.utils.tags.ui.ExperienceCardsTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the data</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>
		
	</tag>
	
	<tag>
		<name>iconListCard</name>
		<tag-class>com.orbis.utils.tags.ui.IconListCardTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the data</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Top element level style attribute.</description>
		</attribute>
		
		<attribute>
			<name>darkMode</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Apply dark colors to the card</description>
		</attribute>
		
		<attribute>
			<name>icon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Material icon to be used.</description>
		</attribute>

		<attribute>
			<name>iconText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Text to be used instead of material icon.</description>
		</attribute>

		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Description text describing the series. Can be one or more i18n codes and / or plain text.</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_title</description>
		</attribute>
		
		<attribute>
			<name>i18n_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18nArgs_subtitle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>subtitleIcon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Icon to be displayed with subtitle text</description>
		</attribute>

		<attribute>
			<name>subtitleStyle</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Styles of subtitle span</description>
		</attribute>

		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Unencrypted action to be submitted when the button is clicked.</description>
		</attribute>
		
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Javascript attribute to fire whenever the button is clicked.
			</description>
		</attribute>
	
		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Text to be shown in a confirm modal that will execute the button's click behaviour if the confirm modal is accepted.<br />
				The click behaviour in the modal will not run if the confirm modal is declined or closed.
			</description>
		</attribute>
	
		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The args attribute for i18n_confirmOnclick</description>
		</attribute>
		
		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
		
		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
	</tag>
	
	<tag>
		<name>chartPlot</name>
		<tag-class>com.orbis.utils.tags.ui.ChartPlotTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>x</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Double</type>
			<description>X value of the plot</description>
		</attribute>
		
		<attribute>
			<name>y</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Double</type>
			<description>Y value of the plot</description>
		</attribute>
		
		<attribute>
			<name>i18n_label</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Description text describing the plot. Can be one or more i18n codes and / or plain text.</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_label</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_label</description>
		</attribute>
		
		<attribute>
			<name>icon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Icon used to represent the data</description>
		</attribute>
		
		<attribute>
			<name>color</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Color used to represent the data</description>
		</attribute>
	</tag>
	
	<tag>
		<name>configurationList</name>
		<tag-class>com.orbis.utils.tags.ui.ConfigurationListTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the main container and used to interact with the object</description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Description text describing the series. Can be one or more i18n codes and / or plain text.</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_title</description>
		</attribute>
		
		<attribute>
			<name>showConfig</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Child configurationListItem ID to load as soon as the page loads</description>
		</attribute>

		<attribute>
			<name>i18n_dropdownLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Overrides the sections dropdown label. Defaults to 'sections'.</description>
		</attribute>

		<attribute>
			<name>i18nArgs_dropdownLabel</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_dropdownLabel</description>
		</attribute>
	</tag>
	
	<tag>
		<name>configurationListPage</name>
		<tag-class>com.orbis.utils.tags.ui.ConfigurationListPageTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the main container and used to interact with the object</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>CSS classes assigned to the container div</description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Description text describing the series. Can be one or more i18n codes and / or plain text.</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_title</description>
		</attribute>
	</tag>
	
	<tag>
		<name>configurationListGroup</name>
		<tag-class>com.orbis.utils.tags.ui.ConfigurationListGroupTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the main container and used to interact with the object</description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Description text describing the series. Can be one or more i18n codes and / or plain text.</description>
		</attribute>
		
		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Comma delimited arguments to be passed on to the i18n codes within i18n_title</description>
		</attribute>
	</tag>
	
	<tag>
		<name>configurationListItem</name>
		<tag-class>com.orbis.utils.tags.ui.ConfigurationListItemTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the main container and used to interact with the object</description>
		</attribute>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		
		<attribute>
			<name>additionalParams</name> 
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
		</attribute>
		
		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>customAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
		<!-- 
		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute> -->
	</tag>
	
	<tag>
		<name>actionsBar</name>
		<tag-class>com.orbis.utils.tags.ui.ActionsBarTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Bar of buttons to be placed at the bottom of the screen
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the main container and used to interact with the object</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Element level css for the button element.</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Classes to be placed in the button element</description>
		</attribute>

		<attribute>
			<name>forceLabelHide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Force all children to only show their icons</description>
		</attribute>
	</tag>
	
	<tag>
		<name>actionsBarItem</name>
		<tag-class>com.orbis.utils.tags.ui.ActionsBarItemTag</tag-class>
		<body-content>scriptless</body-content>
		<dynamic-attributes>true</dynamic-attributes>
		<description>
			Button used inside the actionsBar tag.
		</description>
		
		<attribute>
			<name>icon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Material icon name displayed in the button</description>
		</attribute>
		
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Javascript attribute to fire whenever the button is clicked.
			</description>
		</attribute>
	
		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Text to be shown in a confirm modal that will execute the button's click behaviour if the confirm modal is accepted.<br />
				The click behaviour in the modal will not run if the confirm modal is declined or closed.
			</description>
		</attribute>

		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The args attribute for i18n_confirmOnclick</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Element level css for the button element.</description>
		</attribute>
	
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Unique identifyer for the button element</description>
		</attribute>
	
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Classes to be placed in the button element</description>
		</attribute>
		
		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
		
		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Unencrypted action to be submitted when the button is clicked.</description>
		</attribute>
		
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>

	</tag>
	
<tag>
		<name>actionTextbox</name>
		<tag-class>com.orbis.utils.tags.ui.ActionTextboxTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Textbox merged with a button
		</description>
		
		<attribute>
			<name>useLeftButton</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>Display the left textbox button</description>
		</attribute>
		
		<attribute>
			<name>value</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18n_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>i18nArgs_helpText</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>name</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the main container and used to interact with the object</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Element level css for the button element.</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Classes to be placed in the button element</description>
		</attribute>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Unencrypted action to be submitted when the button is clicked.</description>
		</attribute>
		
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Javascript attribute to fire whenever the button is clicked.
			</description>
		</attribute>
	
		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Text to be shown in a confirm modal that will execute the button's click behaviour if the confirm modal is accepted.<br />
				The click behaviour in the modal will not run if the confirm modal is declined or closed.
			</description>
		</attribute>
	
		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The args attribute for i18n_confirmOnclick</description>
		</attribute>
		
		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
		
		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
	
	</tag>
		
	<tag>
		<name>creditsRail</name>
		<tag-class>com.orbis.utils.tags.ui.CreditsRailTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the data</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS rules to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				
			</description>
		</attribute>

		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
		
		<attribute>
			<name>icon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>

		<attribute>
			<name>sidebarId</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>used for when you want to use a custom sidebar instead of the baked in one</description>
		</attribute>
		
	</tag>
	
	<tag>
		<name>creditsRailItem</name>
		<tag-class>com.orbis.utils.tags.ui.CreditsRailItemTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the data</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS rules to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>icon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description></description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				
			</description>
		</attribute>

		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
		
		<attribute>
			<name>i18n_name</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				
			</description>
		</attribute>

		<attribute>
			<name>i18nArgs_name</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
		
	</tag>
	
	<tag>
		<name>surveyCards</name>
		<tag-class>com.orbis.utils.tags.ui.SurveyCardsTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the main container and used to interact with the object</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Element level css for the button element.</description>
		</attribute>
		
	</tag>
	
	<tag>
		<name>surveyCard</name>
		<tag-class>com.orbis.utils.tags.ui.SurveyCardTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the data</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>CSS classes to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>CSS rules to be applied to the outer most element</description>
		</attribute>
		
		<attribute>
			<name>icon</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				
			</description>
		</attribute>
		
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				
			</description>
		</attribute>

		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description></description>
		</attribute>
		
		<attribute>
			<name>action</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Unencrypted action to be submitted when the button is clicked.</description>
		</attribute>
		
		<attribute>
			<name>subAction</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>target</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>siteElement</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Object</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>additionalParams</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.util.Map</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>href</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Action aware parameter used with action</description>
		</attribute>
		
		<attribute>
			<name>onclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Javascript attribute to fire whenever the button is clicked.
			</description>
		</attribute>
	
		<attribute>
			<name>i18n_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>
				Text to be shown in a confirm modal that will execute the button's click behaviour if the confirm modal is accepted.<br />
				The click behaviour in the modal will not run if the confirm modal is declined or closed.
			</description>
		</attribute>
	
		<attribute>
			<name>i18nArgs_confirmOnclick</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The args attribute for i18n_confirmOnclick</description>
		</attribute>
		
		<attribute>
			<name>show</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>
		
		<attribute>
			<name>hide</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Takes an ID to be used with the spiral robot show/hide framework</description>
		</attribute>

		<dynamic-attributes>true</dynamic-attributes>
	</tag>

	<tag>
		<name>emptyState</name>
		<tag-class>com.orbis.utils.tags.ui.EmptyStateTag</tag-class>
		<body-content>scriptless</body-content>

		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the main container and used to interact with the object</description>
		</attribute>

		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Element level css for the button element.</description>
		</attribute>

		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Classes to be placed in the button element</description>
		</attribute>

		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>size</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>com.orbis.web.spiralRobot.SpiralRobot.EMPTY_STATE_SIZES</type>
		</attribute>
	</tag>

	<tag>
		<name>productivity</name>
		<tag-class>com.orbis.utils.tags.ui.ProductivityTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Container for productivity sidebar and more tags coming soon (TM)
		</description>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>The ID assigned to the main container and used to interact with the object</description>
		</attribute>
		
		<attribute>
			<name>css</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Element level css for the button element.</description>
		</attribute>
		
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
			<description>Classes to be placed in the button element</description>
		</attribute>
	</tag>
	
	<tag>
		<name>productivitySidebar</name>
		<tag-class>com.orbis.utils.tags.ui.ProductivitySidebarTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>i18n_title</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>i18nArgs_title</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>classes</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>location</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>SpiralRobot.SIDEBAR_LOCATIONS</type>
		</attribute>

		<attribute>
			<name>openButtonSelector</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	
		<attribute>
			<name>showOnLoad</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		
		<attribute>
			<name>overrideLayout</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>

		<attribute>
			<name>appendToBody</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
		</attribute>
		<attribute>
			<name>hideCloseButton</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Boolean</type>
			<description>
				set to true to hide the close button in the sidebar header.
			</description>
		</attribute>
	</tag>
	
	<tag>
		<name>pageMaterial</name>
		<tag-class>com.orbis.utils.tags.ui.PageMaterialTag</tag-class>
		<body-content>scriptless</body-content>
		
		<attribute>
			<name>id</name>
			<required>false</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
		
		<attribute>
			<name>figma</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>
	</tag>

	<tag>
		<name>abnCampaign</name>
		<tag-class>com.orbis.utils.tags.ui.ABnCampaignTag</tag-class>
		<body-content>scriptless</body-content>
		<description>
			Renders the body based off of the currentUser's ABnCampaign subscription and the submitted caseNumber. If those number match, the body will render. If they do not match, nothing will render.
			If the currentUser is not registered to the campaign, they will be automatically registered.
		</description>

		<attribute>
			<name>campaignKey</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.String</type>
		</attribute>

		<attribute>
			<name>caseNumber</name>
			<required>true</required>
			<rtexprvalue>true</rtexprvalue>
			<type>java.lang.Integer</type>
		</attribute>
	</tag>
	
	<function>
		<name>isHelpTextLimitReached</name>
		<function-class>com.orbis.web.spiralRobot.SpiralRobot</function-class>
		<function-signature>boolean isHelpTextLimitReached(java.lang.String)</function-signature>
	</function>
</taglib>