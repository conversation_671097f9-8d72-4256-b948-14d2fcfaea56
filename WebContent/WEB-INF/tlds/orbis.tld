<?xml version="1.0" encoding="UTF-8"?>
<taglib version="2.1" xmlns="http://java.sun.com/xml/ns/javaee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-jsptaglibrary_2_1.xsd">
    <tlib-version>1.0</tlib-version>
    <short-name>o</short-name>
    <uri>/WEB-INF/tlds</uri>
    <function>
        <name>encodeBase64</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String encodeBase64(java.lang.String)</function-signature>
    </function>
    <function>
        <name>isVideoConferencingEnabled</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean isVideoConferencingEnabled()</function-signature>
    </function>
    <function>
        <name>timeAgo</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String timeAgo(java.lang.String, java.util.Date)</function-signature>
    </function>
    <function>
        <name>canEmployerEditPosting</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean canEmployerEditPosting(int, java.lang.String, java.lang.String, boolean, boolean, boolean)</function-signature>
    </function>
	<function>
        <name>toLocale</name>
        <function-class>com.orbis.utils.LocaleUtils</function-class>
        <function-signature>java.util.Locale toLocale(java.lang.String)</function-signature>
    </function>
    <function>
    	<name>getInteractionMessageNoRecipientUserId</name>
        <function-class>com.orbis.expressions.Functions</function-class>
    	<function-signature>java.lang.Integer getInteractionMessageNoRecipientUserId()</function-signature>
    </function>
    <function>
        <name>removeDeleted</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String removeDeleted(java.lang.String, java.lang.String)</function-signature>
    </function>
    <function>
        <name>cleanUserForDisplay</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String cleanUserForDisplay(com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl, java.lang.String)</function-signature>
    </function>
    <function>
        <name>contains</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean contains(java.util.Collection, java.lang.Object)</function-signature>
    </function>
    <function>
        <name>size</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>int size(java.util.Collection)</function-signature>
    </function>
    <function>
        <name>containsDate</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean containsDate(java.util.List, java.util.Date, boolean)</function-signature>
    </function>
    <function>
        <name>parseDate</name>
        <function-class>com.orbis.utils.DateUtils</function-class>
        <function-signature>Date parseDate(java.lang.String, java.lang.String)</function-signature>
    </function>
    <function>
        <name>round</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>double round(double, java.lang.String, int)</function-signature>
    </function>
    <function>
        <name>pluralize</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String pluralize(java.lang.String, int)</function-signature>
    </function>
    <function>
        <name>addMinutes</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>double addMinutes(java.util.Date, java.lang.Object)</function-signature>
    </function>
    <function>
        <name>now</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.util.Date now()</function-signature>
    </function>
    <function>
        <name>min</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>Double min(double, double)</function-signature>
    </function>
    <function>
        <name>ceil</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>Double ceil(double)</function-signature>
    </function>
    <function>
        <name>partition</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>List partition(java.util.List, int)</function-signature>
    </function>
    <function>
        <name>truncateSet</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.util.Set truncateSet(java.util.Set, int)</function-signature>
    </function>
    <function>
        <name>truncateList</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.util.List truncateList(java.util.List, int)</function-signature>
    </function>
    <function>
        <name>dateDiff</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>Integer dateDiff(java.util.Date, java.util.Date)</function-signature>
    </function>
    <function>
        <name>toCamelCase</name>
        <function-class>com.orbis.utils.StringUtils</function-class>
        <function-signature>String toCamelCase(java.lang.String)</function-signature>
    </function>
    <function>
        <name>getTextColor</name>
        <function-class>com.orbis.utils.StringUtils</function-class>
        <function-signature>String getTextColor(java.lang.String)</function-signature>
    </function>
    <function>
        <name>keySet</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>Set keySet(java.util.Map)</function-signature>
    </function>
    <function>
        <name>values</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>List values(java.util.Map)</function-signature>
    </function>
    <function>
        <name>isNull</name>
        <function-class>java.util.Objects</function-class>
        <function-signature>boolean isNull(java.lang.Object)</function-signature>
    </function>
    <function>
        <name>localize</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String localize(java.lang.String, java.lang.String, java.lang.String)</function-signature>
    </function>
    <function>
        <name>getDateYear</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>Integer getDateYear(java.util.Date)</function-signature>
    </function>
    <function>
        <name>getL1Value</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String getL1Value(java.lang.String, java.util.Map)</function-signature>
    </function>
    <function>
        <name>getL2Value</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String getL2Value(java.lang.String, java.util.Map)</function-signature>
    </function>
    <function>
        <name>getDFQuestionMultiChoicesJson</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String getDFQuestionMultiChoicesJson(com.orbis.df.DFAnswerEntity, com.orbis.df.DFQuestion, java.lang.String)</function-signature>
    </function>
    <function>
        <name>getDFQuestionMultiChoicesOtherVal</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String getDFQuestionMultiChoicesOtherVal(com.orbis.df.DFAnswerEntity, com.orbis.df.DFQuestion, java.lang.String)</function-signature>
    </function>
    <function>
    	<name>isInstance</name>
    	<function-class>com.orbis.expressions.Functions</function-class>
    	<function-signature>boolean isInstance(java.lang.Object, java.lang.String)</function-signature>
    </function>
    <function>
        <name>getBilingualListFromString</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>List getBilingualListFromString(java.lang.String, java.lang.String)</function-signature>
    </function>
    <function>
        <name>listify</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>List listify(java.lang.String)</function-signature>
    </function>
    <function>
        <name>getBilingualListFromStringOrdering</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>List getBilingualListFromStringOrdering(java.lang.String, java.lang.String, boolean)</function-signature>
    </function>
    <function>
        <name>getLocalizedList</name>
        <function-class>com.orbis.utils.LocaleUtils</function-class>
        <function-signature>List getLocalizedList(java.lang.String, java.lang.String)</function-signature>
    </function>
    <function>
        <name>isBilingual</name>
        <function-class>com.orbis.utils.LocaleUtils</function-class>
        <function-signature>boolean isBilingual(java.lang.String)</function-signature>
    </function>
    <function>
        <name>getBilingualMapFromString</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>Map getBilingualMapFromString(java.lang.String, java.lang.String)</function-signature>
    </function>
    <function>
        <name>getLValue</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String getLValue(java.lang.String, java.lang.String, java.lang.String)</function-signature>
    </function>
    <function>
        <name>getStatusBilingual</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String getStatusBilingual(java.lang.String, java.lang.String)</function-signature>
    </function>
    <function>
        <name>listIntersection</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String listIntersection(java.util.List, java.util.List)</function-signature>
    </function>
    <function>
        <name>gridColumnMapByJspProperty</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.util.Map gridColumnMapByJspProperty(java.lang.String, jakarta.servlet.http.HttpServletRequest)</function-signature>
    </function>
    <function>
        <name>generateGridInstanceId</name>
        <function-class>com.orbis.web.content.grid.GridHelper</function-class>
        <function-signature>String generateGridInstanceId(java.lang.String, jakarta.servlet.http.HttpServletRequest)</function-signature>
    </function>
    <function>
        <name>getRequestParamMapAsString</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String getRequestParamMapAsString(java.util.Map)</function-signature>
    </function>
    <function>
        <name>distinctList</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.util.List distinctList(java.util.List)</function-signature>
    </function>
    <function>
        <name>permissionGroupsMappedById</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.util.Map permissionGroupsMappedById()</function-signature>
    </function>
    <function>
        <name>truncate</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String truncate(java.lang.String, int, boolean)</function-signature>
    </function>
    <function>
        <name>stringIsNumericValue</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean stringIsNumericValue(java.lang.String)</function-signature>
    </function>
     <function>
        <name>getConfig</name>
        <description>returns the value of the requested PortalConfig as a string, or an empty string if not found</description>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String getConfig(java.lang.String)</function-signature>
    </function>
    <function>
        <name>isAzureSite</name>
        <description>returns true if siteControllerSettings bean in siteController.xml has an azure entry</description>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean isAzureSite()</function-signature>
    </function>
     <function>
        <name>capitalize</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String capitalize(java.lang.String)</function-signature>
    </function>
    <function>
        <name>parseInt</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>Object parseInt(java.lang.Object)</function-signature>
    </function>
    <function>
        <name>minutesToHumanReadable</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String minutesToHumanReadable(int)</function-signature>
    </function>
    <function>
        <name>getReadableInteractionType</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String getReadableInteractionType(java.lang.String)</function-signature>
    </function>
    <function>
        <name>isEngagement</name>
        <function-class>com.orbis.web.content.interaction.InteractionHelper</function-class>
        <function-signature>boolean isEngagement(java.lang.String)</function-signature>
    </function>
    <function>
        <name>appStatusMap</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.util.Map appStatusMap(com.orbis.web.content.np.NPostingModule, java.lang.String, jakarta.servlet.http.HttpServletRequest)</function-signature>
    </function>
    <function>
        <name>random</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String random()</function-signature>
    </function>
    <function>
        <name>getFullName</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String getFullName(java.lang.String, java.lang.String, java.lang.String)</function-signature>
    </function>
    <function>
        <name>formatDate</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String formatDate(java.util.Date, java.lang.String)</function-signature>
    </function>
    <function>
    	<name>dwsc</name>
    	<function-class>com.orbis.web.content.dashboard.DashboardHelper</function-class>
    	<function-signature>java.util.Map getDashboardSubCategoriesGroupedByCategory(boolean)</function-signature>
    </function>
    <function>
    	<name>stripHtml</name>
    	<function-class>com.orbis.utils.HtmlUtils</function-class>
    	<function-signature>java.lang.String stripHtml(java.lang.String)</function-signature>
    </function>
    <function>
        <name>getStatusForDisplay</name>
        <function-class>com.orbis.web.content.na.NAHelper</function-class>
        <function-signature>String getStatusForDisplay(com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl, com.orbis.web.content.np.NApplication, java.lang.String)</function-signature>
    </function>
    <function>
        <name>getProfileImage</name>
        <function-class>com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl</function-class>
        <function-signature>String getProfileImageData(jakarta.servlet.http.HttpServletRequest, com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl)</function-signature>
    </function>
    <function>
        <name>getProfileImageUrl</name>
        <function-class>com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl</function-class>
        <function-signature>String getProfileImage(jakarta.servlet.http.HttpServletRequest, com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl)</function-signature>
    </function>
    <function>
        <name>getProfileImageMIMEType</name>
        <function-class>com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl</function-class>
        <function-signature>String getProfileImageMIMEType(jakarta.servlet.http.HttpServletRequest, com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl)</function-signature>
    </function>
    <function>
        <name>getProperty</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>void getProperty(java.lang.Object, java.lang.String)</function-signature>
    </function>
    <function>
        <name>serializeObject</name>
        <function-class>com.orbis.utils.StringUtils</function-class>
        <function-signature>java.lang.String serializeObject(java.lang.Object)</function-signature>
    </function>
    <function>
        <name>getAndRecordBreadcrumbs</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.util.List getBreadcrumbs(jakarta.servlet.http.HttpServletRequest, java.lang.String)</function-signature>
    </function>
    <function>
        <name>getMatrixMap</name>
        <function-class>com.orbis.df.DFHelper</function-class>
        <function-signature>java.util.Map getMatrixMap(com.orbis.df.DFAnswerEntity, com.orbis.df.DFQuestion, java.lang.String)</function-signature>
    </function>
    <function>
        <name>canAccessSiteElement</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean canAccessSiteElement(com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl, com.orbis.web.site.SiteElement)</function-signature>
    </function>
    <function>
        <name>escapeSingleQuote</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String escapeSingleQuote(java.lang.String)</function-signature>
    </function>
    <function>
        <name>isUnderMyAccountTemplate</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean isUnderMyAccountTemplate(com.orbis.web.site.SiteElement)</function-signature>
    </function>
    <function>
        <name>isSpiralRobotCompatible</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean isSpiralRobotCompatible(com.orbis.web.site.SiteElement, jakarta.servlet.jsp.PageContext)</function-signature>
    </function>
    <function>
        <name>getLandingPageModule</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>com.orbis.web.content.lp.LandingPageModule getLandingPageModule()</function-signature>
    </function>
    <function>
        <name>getDashboardNavType</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.Integer getDashboardNavType(jakarta.servlet.jsp.PageContext)</function-signature>
    </function>
    <function>
        <name>canViewDashboardItem</name>
        <function-class>com.orbis.web.content.dashboard.DashboardHelper</function-class>
        <function-signature>boolean canViewDashboardItem(java.lang.String, com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl, com.orbis.web.content.dashboard.DashboardModule)</function-signature>
    </function>
    <function>
        <name>getDashboardItemLabel</name>
        <function-class>com.orbis.web.content.dashboard.DashboardHelper</function-class>
        <function-signature>java.lang.String getDashboardItemLabel(java.lang.String, com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl, com.orbis.web.content.dashboard.DashboardModule, java.lang.String)</function-signature>
    </function>
    <function>
        <name>hasExperientialCatalogue</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean hasExperientialCatalogue()</function-signature>
    </function>
    <function>
        <name>getFooterLinkmap</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.util.Map getFooterLinkmap()</function-signature>
    </function>
    <function>
        <name>getFooterLinkmapLocalized</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.util.Map getFooterLinkmapLocalized(java.lang.String)</function-signature>
    </function>
    <function>
        <name>getLocalizedString</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String getLocalizedString(java.lang.String, java.lang.String)</function-signature>
    </function>
    <function>
        <name>getMilliseconds</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>long getMilliseconds(java.util.Date)</function-signature>
    </function>
    <function>
        <name>getInterviewMethodLabel</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String getInterviewMethodLabel(int, com.orbis.web.content.interview.InterviewModule, java.lang.String)</function-signature>
    </function>
    <function>
        <name>getInterviewTypeLabel</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String getInterviewTypeLabel(int, com.orbis.web.content.interview.InterviewModule, java.lang.String)</function-signature>
    </function>
    <function>
        <name>removeMapEntry</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.Object removeMapEntry(java.util.Map, java.lang.Object)</function-signature>
    </function>
    <function>
        <name>isAccessible</name>
        <function-class>com.orbis.web.content.dashboard.DashboardUpcomingScheduleEntryWrapper</function-class>
        <function-signature>boolean isAccessible(jakarta.servlet.http.HttpServletRequest, com.orbis.web.content.dashboard.DashboardUpcomingScheduleEntryWrapper)</function-signature>
    </function>
    <function>
        <name>isBefore</name>
        <function-class>com.orbis.utils.DateUtils</function-class>
        <function-signature>boolean isBefore(java.util.Date, java.util.Date)</function-signature>
    </function>
    <function>
        <name>isAfter</name>
        <function-class>com.orbis.utils.DateUtils</function-class>
        <function-signature>boolean isAfter(java.util.Date, java.util.Date)</function-signature>
    </function>
    <function>
        <name>isServiceTeamEngaged</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean isServiceTeamEngaged()</function-signature>
    </function>
    <function>
        <name>padString</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String padString(java.lang.Object, java.lang.String, int)</function-signature>
    </function>
    <function>
        <name>concat</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String concat(java.lang.String, java.lang.String)</function-signature>
    </function>
    <function>
        <name>isValidDocPdf</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean isValidDocPdf(java.lang.Object, int)</function-signature>
    </function>
    <function>
        <name>join</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean join(java.util.List, java.lang.String)</function-signature>
    </function>
    <function>
        <name>pluck</name>
        <function-class>com.orbis.utils.CollectionUtils</function-class>
        <function-signature>java.util.List pluck(java.util.List, int)</function-signature>
    </function>
    <function>
        <name>locale</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.util.Locale getLocale(jakarta.servlet.jsp.PageContext)</function-signature>
    </function>
    <function>
        <name>getFileServiceUrlQuick</name>
        <function-class>com.orbis.web.content.np.NHelper</function-class>
        <function-signature>java.util.String getFileServiceUrlQuick(java.lang.String, java.lang.String)</function-signature>
    </function>
   	<function>
        <name>canFacultyOrPortalStaffView</name>
        <function-class>com.orbis.web.content.pc.PCHelper</function-class>
        <function-signature>java.lang.Boolean canFacultyOrPortalStaffView(com.orbis.web.content.pc.PCProjectType)</function-signature>
    </function>
    <function>
        <name>getBodyClass</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.util.String getBodyClass(com.orbis.web.site.SiteElement)</function-signature>
    </function>
    <function>
        <name>getFirstSiteElementByType</name>
        <function-class>com.orbis.web.content.np.NHelper</function-class>
        <function-signature>java.util.String getFirstSiteElementByType(java.lang.String)</function-signature>
    </function>
    <function>
        <name>persisted</name>
        <description>checks if a ContentItem is persisted in the database</description>
        <function-class>com.orbis.web.content.ContentItemHelper</function-class>
        <function-signature>boolean isPersisted(com.orbis.web.content.ContentItem)</function-signature>
    </function>
    <function>
        <name>jsonStringToFormInputs</name>
        <description>Converts a json string to hidden form input in jsps</description>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String jsonStringToFormInputs(java.lang.String)</function-signature>
    </function>
    <function>
        <name>profileImageEnabled</name>
        <description>Checks if profile images can be displayed based on the specified primary group</description>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean isUserProfileImageEnabled(com.orbis.acegi.providers.dao.hibernate.PersonGroup)</function-signature>
    </function>
    <function>
        <name>reflectionsEnabled</name>
        <description>Returns true if reflections are enabled site-wide</description>
        <function-class>com.orbis.web.content.ec.reflection.ECReflectionHelper</function-class>
        <function-signature>boolean isReflectionsEnabled()</function-signature>
    </function>
    <function>
        <name>masterReflectionConfig</name>
        <description>Returns the master reflection configuration</description>
        <function-class>com.orbis.web.content.ec.reflection.ECReflectionHelper</function-class>
        <function-signature>com.orbis.web.content.ec.reflection.ECReflectionConfig getMasterConfig()</function-signature>
    </function>
    <function>
        <name>equals</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.Boolean equals(java.lang.String, java.lang.String)</function-signature>
    </function>
    <function>
        <name>fixUrl</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String fixUrl(java.lang.String)</function-signature>
    </function>
    <function>
        <name>hasInteractionTemplates</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean hasInteractionTemplates(java.lang.String, java.lang.String, com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl, java.lang.String)</function-signature>
    </function>
    <function>
        <name>encrypt</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String encrypt(java.lang.String, com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl)</function-signature>
    </function>
    <function>
        <name>encryptWithSub</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String encrypt(java.lang.String, java.lang.String, com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl)</function-signature>
    </function>
    <function>
        <name>modelHasQuestions</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>boolean modelHasQuestions(com.orbis.df.DFModel)</function-signature>
    </function>
    <function>
        <name>getImageData</name>
        <function-class>com.orbis.utils.FileUtils</function-class>
        <function-signature>java.lang.String getImageData(java.lang.String)</function-signature>
    </function>
    <function>
        <name>getImageDataForUUID</name>
        <function-class>com.orbis.utils.FilePathUtils</function-class>
        <function-signature>java.lang.String getImageDataForUUID(java.lang.String)</function-signature>
    </function>
    <function>
        <name>getReadableFileName</name>
        <function-class>com.orbis.utils.FilePathUtils</function-class>
        <function-signature>java.lang.String getReadableFileNameForFilePath(java.lang.String)</function-signature>
    </function>
    <function>
        <name>getReadableFileNameForUUID</name>
        <function-class>com.orbis.utils.FilePathUtils</function-class>
        <function-signature>java.lang.String getReadableFileNameForUUID(java.lang.String, boolean)</function-signature>
    </function>
    <function>
        <name>getFilePath</name>
        <function-class>com.orbis.utils.FilePathUtils</function-class>
        <function-signature>java.lang.String getFilePathUrlForUUID(java.lang.String, boolean)</function-signature>
    </function>
    <function>
        <name>getFullBaseUrl</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String getFullBaseUrl(com.orbis.web.site.SiteElement)</function-signature>
    </function>
    <function>
    	<name>addDays</name>
    	<function-class>com.orbis.expressions.Functions</function-class>
    	<function-signature>java.util.Date addDays(java.util.Date, java.lang.Object)</function-signature>
    </function>
    <function>
    	<name>getStartDate</name>
    	<function-class>com.orbis.expressions.Functions</function-class>
    	<function-signature>java.util.Date getStartDate(java.util.Date)</function-signature>
    </function>
    <function>
    	<name>getEndDate</name>
    	<function-class>com.orbis.expressions.Functions</function-class>
    	<function-signature>java.util.Date getEndDate(java.util.Date)</function-signature>
    </function>
    <function>
    	<name>getDifferenceInDays</name>
    	<function-class>com.orbis.expressions.Functions</function-class>
    	<function-signature>java.lang.Long getDifferenceInDays(java.util.Date, java.util.Date)</function-signature>
    </function>
    <function>
    	<name>getDifferenceInMinutesDB</name>
    	<function-class>com.orbis.expressions.Functions</function-class>
    	<function-signature>java.lang.Long getDifferenceInMinutesDB(java.lang.String, java.lang.String)</function-signature>
    </function>
    <function>
    	<name>convertLocalDateToDate</name>
    	<function-class>com.orbis.expressions.Functions</function-class>
    	<function-signature>java.util.Date convertLocalDateToDate(java.time.LocalDate)</function-signature>
    </function>
    <function>
        <name>findAny</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String findAnyWithMatchingField(java.util.List, java.lang.String, java.lang.Object)</function-signature>
    </function>
    <function>
        <name>isPostingQualifiersConfigured</name>
        <function-class>com.orbis.web.content.np.NPostingHelper</function-class>
        <function-signature>java.lang.Boolean isPostingQualifiersConfigured(com.orbis.web.content.np.NPosting, com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl)</function-signature>
    </function>
    <function>
        <name>generateId</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.lang.String generateRandomId()</function-signature>
    </function>
    <function>
        <name>canUserCreateProject</name>
        <function-class>com.orbis.web.content.pc.PCProjectHelper</function-class>
        <function-signature>boolean canUserCreateProject(com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl, com.orbis.web.content.pc.PCModule)</function-signature>
    </function>
    <function>
        <name>isCampusLinkCentralServer</name>
        <function-class>com.orbis.web.content.ep.EPHelper</function-class>
        <function-signature>boolean isCentralServer()</function-signature>
    </function>
     <function>
        <name>formatNumber</name>
        <function-class>com.orbis.utils.NumberUtils</function-class>
        <function-signature>String formatNumber(java.lang.Number, java.lang.String)</function-signature>
    </function>
    <function>
        <name>abbreviateNumber</name>
        <function-class>com.orbis.utils.NumberUtils</function-class>
        <function-signature>String abbreviateNumber(java.lang.Number)</function-signature>
    </function>
    <function>
        <name>absInt</name>
        <function-class>java.lang.Math</function-class>
        <function-signature>int abs(int)</function-signature>
    </function>
    <function>
    	<name>isNA</name>
    	<function-class>com.orbis.utils.StringUtils</function-class>
    	<function-signature>boolean isNA(java.lang.String)</function-signature>
   	</function>
   	<function>
    	<name>isEmptyOrNA</name>
    	<function-class>com.orbis.utils.StringUtils</function-class>
    	<function-signature>boolean isEmptyOrNA(java.lang.String)</function-signature>
   	</function>
   	<function>
    	<name>isAllEmpty</name>
    	<function-class>com.orbis.expressions.Functions</function-class>
    	<function-signature>boolean isAllEmpty(java.lang.Object, java.lang.Object)</function-signature>
   	</function>
   	<function>
        <name>getUserFriendlyDate</name>
        <function-class>com.orbis.utils.DateUtils</function-class>
        <function-signature>String getUserFriendlyDate(java.util.Date, java.lang.String)</function-signature>
    </function>
    <function>
        <name>escape</name>
        <function-class>com.orbis.utils.HtmlUtils</function-class>
        <function-signature>String escape(java.lang.String, boolean, boolean)</function-signature>
    </function>
    <function>
        <name>convertJobPostingOptionsToDataviewerOptions</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>String convertJobPostingOptionsToDataviewerOptions(java.util.List, java.lang.String)</function-signature>
    </function>
    <function>
        <name>unescape</name>
        <function-class>com.orbis.utils.HtmlUtils</function-class>
        <function-signature>String htmlUnescape(java.lang.String)</function-signature>
    </function>
    <function>
        <name>getMessage</name>
        <function-class>com.orbis.portal.PortalUtils</function-class>
        <function-signature>String getI18nMessage(java.lang.String, java.lang.String)</function-signature>
    </function>
    <function>
        <name>containsAny</name>
        <function-class>com.orbis.utils.CollectionUtils</function-class>
        <function-signature>boolean containsAny(java.util.Collection, java.util.Collection)</function-signature>
    </function>
    <function>
        <name>getTimeZoneAbbreviation</name>
        <function-class>com.orbis.utils.DateUtils</function-class>
        <function-signature>String getTimeZoneAbbreviation(java.lang.String)</function-signature>
    </function>
    <function>
        <name>getTimeZoneOffsetSeconds</name>
        <function-class>com.orbis.utils.DateUtils</function-class>
        <function-signature>int getTimeZoneOffsetSeconds()</function-signature>
    </function>
    <function>
        <name>getHeaderTabs</name>
        <function-class>com.orbis.expressions.Functions</function-class>
        <function-signature>java.utils.List getHeaderTabs(com.orbis.acegi.providers.dao.hibernate.UserDetailsImpl, jakarta.servlet.http.HttpServletRequest)</function-signature>
    </function>


    <tag>
        <name>encrypt</name>
        <description>encrypts the given parameters, outputs a URL GET safe string.</description>
        <tag-class>com.orbis.utils.OrbisEncryptActionTag</tag-class>
        <body-content>scriptless</body-content>
        <attribute>
        	<name>action</name>
            <description>the action to encrypt</description>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
        	<name>subAction</name>
            <description>optionally include the subAction</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
        	<name>input</name>
            <description>if true, write a type="hidden" name="action" input tag instead.</description>
            <required>false</required>
            <rtexprvalue>false</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
    </tag>
    <tag>
        <name>nav</name>
        <description>creates an orbisApp.buildForm 'onclick' attribute. dynamic attributes will be converted into parameters. use the 'js:' prefix to prevent the value from being quoted at rendertime.</description>
        <tag-class>com.orbis.utils.OrbisNavigateTag</tag-class>
        <body-content>scriptless</body-content>
        <dynamic-attributes>true</dynamic-attributes>
        <attribute>
            <name>anchor</name>
            <description>render the result as an HTML 'A' (anchor) tag</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <name>anchorClass</name>
            <description>class attribute for the rendered anchor tag</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>anchorId</name>
            <description>id attribute for the rendered anchor tag</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>action</name>
            <description>the name of the internal action (java method)</description>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>siteElement</name>
            <description>the siteElement to navigate to. defaults to current element. sets the form's 'action' attribute.</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>com.orbis.web.site.SiteElement</type>
        </attribute>
        <attribute>
            <name>siteElementPath</name>
            <description>the URL of a siteElement to navigate to. no default. sets the form's 'action' attribute.</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>siteElementPathJS</name>
            <description>a javascript expression that evaluates to a string URL of the siteElement to navigate to. sets the form's 'action' attribute.</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>target</name>
            <description>the form target</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
        	<name>additionalParams</name>
            <description>additional parameters</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.util.Map</type>
        </attribute>
        <attribute>
        	<name>showNewTab</name>
            <description>If set to true, the tag will display as a dropdown to include a new tab option</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <name>confirmOnClick</name>
            <description>Wrap the orbisApp.buildForm(...).submit(); in a confirm prompt. Will accept i18n messages.</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>confirmOnClickArgs</name>
            <description>The arguments used in the confirmOnClick message</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>clickable</name>
            <description>The arguments used to indicate if element is clickable</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
    </tag>
    <tag>
        <name>nav-attr</name>
        <description>this tag must be nested within an OrbisNavigateTag. allows you to specify attributes where the name of the attribute has EL script elements.</description>
        <tag-class>com.orbis.utils.OrbisNavigateAttributeTag</tag-class>
        <body-content>empty</body-content>
        <dynamic-attributes>false</dynamic-attributes>
        <attribute>
            <name>name</name>
            <description>the attribute's name</description>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>value</name>
            <description>the attribute's value</description>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>quoteName</name>
            <description>if true, the name will be quoted when rendered. default true</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
        <attribute>
            <name>quoteValue</name>
            <description>if true, the value will be quoted when rendered. default true</description>
            <required>false</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Boolean</type>
        </attribute>
	</tag>
    <tag>
        <name>map</name>
        <description>declares a java.util.Map in the current scope. dynamic attributes on this tag will be added to the map. use the map-entry tag for entries with dynamic names.</description>
        <tag-class>com.orbis.utils.MapTag</tag-class>
        <body-content>scriptless</body-content>
        <dynamic-attributes>true</dynamic-attributes>
        <attribute>
            <name>var</name>
            <description>the name of the variable</description>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <variable>
			<name-from-attribute>var</name-from-attribute>
			<variable-class>java.util.Map</variable-class>
			<scope>AT_END</scope>
        </variable>
	</tag>
	<tag>
        <name>map-nested</name>
        <description>declares a java.util.Map in the scope of another map. dynamic attributes on this tag will be added to the map. use the map-entry tag for entries with dynamic names.</description>
        <tag-class>com.orbis.utils.MapNestedTag</tag-class>
        <body-content>scriptless</body-content>
         <attribute>
            <name>key</name>
            <description>the key of the entry</description>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Object</type>
        </attribute>
	</tag>
    <tag>
        <name>map-entry</name>
        <description>should be nested within the map tag. adds an entry to the parent map.</description>
        <tag-class>com.orbis.utils.MapEntryTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <name>key</name>
            <description>the key of the entry</description>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Object</type>
        </attribute>
        <attribute>
            <name>value</name>
            <description>the value of the entry</description>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Object</type>
        </attribute>
	</tag>
	<tag>
        <name>map-append</name>
        <description>add an entry to a map in the current scope. if the map does not already exist it will be created.</description>
        <tag-class>com.orbis.utils.MapAppendTag</tag-class>
        <body-content>empty</body-content>
        <attribute>
            <name>var</name>
            <description>the name of the variable</description>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.String</type>
        </attribute>
        <attribute>
            <name>key</name>
            <description>the key of the entry</description>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Object</type>
        </attribute>
        <attribute>
            <name>value</name>
            <description>the value of the entry</description>
            <required>true</required>
            <rtexprvalue>true</rtexprvalue>
            <type>java.lang.Object</type>
        </attribute>
	</tag>
</taglib>
