<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="partial" />   --%>
<ui:navBack>
	<ui:navBackItem action="displayHome"><orbis:message code="i18n.df_modelMatcher.Home3385867767050050" /></ui:navBackItem>
	<ui:navBackItem action="question" subAction="displayDFQuestionEditor" dfModelEntityType="${dfModelEntityType}" dfModelEntityId="${dfModelEntityId}"><orbis:message code="i18n.df_modelMatcher.Editor4021118624777827" /></ui:navBackItem>
</ui:navBack>

<ui:header><orbis:message code="i18n.df_modelMatcher.FormMatche5772294965590676" /></ui:header>

<%@ include file="/WEB-INF/spiralRobot/jsp/df/df_modelMatcher.vue.jsp"%>

<div id="mcanvas">
	<div class="panel" style="padding-top:48px;">
		<h${level} class="heading--banner" >
			Form Matcher
			<span class="pull-right" v-if="network">
				<small><orbis:message code="i18n.df_modelMatcher.Pleasewait5754006568442048" /></small>
			</span>
		</h${level}>
		<form enctype="multipart/form-data" class="margin--b--xxl js--ui-form form-horizontal" method="post" onsubmit="return false;">
			<div id="mcanvas">
				<div class="control-group">
					<label class="control-label" for=""> <orbis:message code="i18n.df_modelMatcher.Form4767874073333055" /> </label>
				</div>
				<div class="controls">
				<div class="select margin--b--xxl">
					<select name="secondary" class="js--ui-select required" v-model="matcher.secondary" @change="loadSecondaryQuestions">
						<option :value="null"><orbis:message code="i18n.df_modelMatcher.select1372198269999413" /></option>
						<option v-for="m in secondaryModels" :value="m">{{m.name}}</option>
					</select>
					<div class="select__arrow"></div>
				</div>
				</div>
			</div>
			<div class=" matchQuestionsList" v-if="matcher.secondary">
				<a href="javascript:;" @click="addQuestionMatch">
					<small><orbis:message code="i18n.df_modelMatcher.Addaquesti4076717384126402" /></small>
				</a>
				<hr>
				<question-match v-for="qm in matcher.questions" v-bind:match="qm"> </question-match>
				
				<button type="submit" class="btn__default--text btn--default btn " @click="saveMatcher" ><orbis:message code="i18n.df_editor.Save"/></button>
			</div>
		</form>
	</div>
</div>