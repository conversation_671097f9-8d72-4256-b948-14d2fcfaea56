<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>
<c:set var="questionClone" value="${dfQuestion}" scope="request" />
<c:set var="questionAnswersClone" value="${DFAnswerrEntity}" scope="request" />
<c:set var="matrixMap" value="${o:getMatrixMap(questionAnswersClone, questionClone, orbisLocale)}" /> 

<c:if test="${!isHidden}">
	<div style="position: relative;">
		<div class="js--error-placement">
			<div class="container--table">
				<table id="question_${dfQuestion.id}_${DFAnswerrEntity.id}" class="table small zebra margin--b--xxl">
					<caption class="table__caption">${dfQuestionLabel}${dfQuestion.required ? '*' : ''}</caption>
					<thead class="table__header">
						<tr class="table__row--header">
							<th class="table__heading">&nbsp;</th>
							<c:forEach var="colLabel" items="${isL1 ? dfQuestion.matrixColumnLabels : dfQuestion.l2MatrixColumnLabels}">
								<th class="table__heading">${colLabel}</th>
							</c:forEach>
						</tr>
					</thead>
					<tbody>
						<c:forEach var="row" items="${matrixMap}" varStatus="rowStatus">
							<tr class="table__row--body">
								<td class="table__heading">${row.key}</td>
								<c:forEach var="column" items="${row.value}" varStatus="colStatus">
									<td class="table__value">
										<label class="inline-label">
											<input type="radio" aria-label="${column.key} - ${row.key}" id="question_${dfQuestion.id}_${DFAnswerrEntity.id}_${rowStatus.index}" name="question_${dfQuestion.id}_${rowStatus.index}" class="${empty massAssignWtr && dfQuestion.required ? 'required' : ''} ${leaderClass}" value="${colStatus.index}" ${column.value ? 'checked' : ''}>
										</label>	
									</td>
								</c:forEach>
							</tr>
						</c:forEach>
					</tbody>
				</table>
			</div>
		</div>
	</div>
	
	<c:set var="questionTooltip" value="${isL1 ? dfQuestion.toolTip : dfQuestion.toolTipL2}" />
	<c:if test="${not empty questionTooltip}">
		<p class="text--help">${questionTooltip}</p>
	</c:if>
</c:if>
<c:if test="${isHidden}">
	<c:forEach var="row" items="${matrixMap}" varStatus="rowStatus">
		<c:forEach var="column" items="${row.value}" varStatus="colStatus">
			<c:if test="${column.value}">
				<input type="hidden" name="dfh_question_${dfQuestion.id}_${rowStatus.index}" value="${colStatus.index}" />
			</c:if>
		</c:forEach>
	</c:forEach>
</c:if>