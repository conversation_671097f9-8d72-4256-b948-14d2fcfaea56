<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>
<orbis:addComponent component="vuejs" />
<orbis:addComponent component="lodash" />
<script type="text/javascript">
	
	var vm;

	$(function() {
		
		var controllerPath = '${siteElement.fullPath}.htm';
		var modelId = ${model.id};
		
		var QuestionTypes = Object.freeze({
			TEXT: 0,
			NUMBER: 1,
			MULTI: 2,
			BOOLEAN: 3,
			DATE: 4,
			SINGLE: 8,
			FLOAT: 12
		});
		
		var OpTypes = Object.freeze({
			EQUALS: {
				key: 0, 
				name: 'Equals', 
				q: [
					QuestionTypes.TEXT, 
					QuestionTypes.NUMBER, 
					QuestionTypes.BOOLEAN,
					QuestionTypes.DATE, 
					QuestionTypes.SINGLE, 
					QuestionTypes.FLOAT
				]
			},
			NOT_EQUALS: {
				key: 1, 
				name: 'Not Equals', 
				q: [
					QuestionTypes.TEXT, 
					QuestionTypes.NUMBER, 
					QuestionTypes.BOOLEAN,
					QuestionTypes.DATE, 
					QuestionTypes.SINGLE, 
					QuestionTypes.FLOAT
				]
			},
			GREATER_THAN: {
				key: 2, 
				name: 'Greater Than', 
				q: [
					QuestionTypes.NUMBER, 
					QuestionTypes.DATE, 
					QuestionTypes.FLOAT
				]
			},
			GREATER_THAN_EQUAL: {
				key: 3, 
				name: 'Greater Than or Equals', 
				q: [
					QuestionTypes.NUMBER, 
					QuestionTypes.DATE, 
					QuestionTypes.FLOAT
				]
		   	},
			LESS_THAN: {
				key: 4, 
				name: 'Less Than', 
				q: [
					QuestionTypes.NUMBER, 
					QuestionTypes.DATE, 
					QuestionTypes.FLOAT
				]
		   	},
			LESS_THAN_EQUAL: {
				key: 0, 
				name: 'Less Than or Equals', 
				q: [
					QuestionTypes.NUMBER, 
					QuestionTypes.DATE, 
					QuestionTypes.FLOAT
				]
		   	},
			ANY: {
				key: 6, 
				name: 'Any', 
				q: [
					QuestionTypes.MULTI
				]
		   	},
			ALL: {
				key: 7, 
				name: 'All', 
				q: [
					QuestionTypes.MULTI
				]
		   	}
		});
		
		Vue.component("question-match", {
			template: '#question-match',
			props: ['match'],
			computed: {
				secondaryQuestions: function() {
					return _.filter(this.$root.secondaryQuestions, function(o){
						return this.match.q1 && o.type == this.match.q1.type;
					}.bind(this));
				},
				validOperations: function() {
					return _.filter(OpTypes, function(o){
						return this.match.q1 && _.includes(o.q, this.match.q1.type)
					}.bind(this));
				},
			},
			methods: {
				remove: function() {
					this.$root.matcher.questions = _.without(this.$root.matcher.questions, this.match);
				}
			}
		});

		vm = new Vue({
			el : '#mcanvas',
			created: function() {
				var self = this;
				self.network=true;
				$.post(controllerPath, {
					action:'<o:encrypt action="question" subAction="ajaxLoadInitModelMatcherData" />',
					modelId: ${model.id}	
				}, function(data) {
					$.extend(true, self, data);
					self.network=false;
				}, 'json');
			},
			data : {
				modelId: modelId,
				matcher: {secondary:null, questions: []},
				secondaryModels:[],
				network: false
			},
			methods: {
				addQuestionMatch: function() {
					this.matcher.questions.push(this.createQuestionMatcher());
				},
				createQuestionMatcher: function() {
					return { q1: null, q2: null, op: 0 }
				},
				loadSecondaryQuestions: function() {
					if(this.matcher.secondary) {
						var self = this;
						self.network=true;
						$.post(controllerPath, {
							action:'<o:encrypt action="question" subAction="ajaxLoadModelMatchSecondaryQuestions" />',
							modelId: self.matcher.secondary.id
						}, function(data) {
							self.secondaryQuestions = data;
							self.network=false;
						}, 'json');
					}
				},
				saveMatcher: function() {
					var self = this;
					self.network=true;
					$.post(controllerPath, {
						action: '<o:encrypt action="question" subAction="saveModelMatcher" />',
						modelId: '${model.id}',
						payload: JSON.stringify(this.matcher)
					}, function(data){
						self.network=false;
						if(data.success) {
							orbisAppSr.showNotification('Matcher saved successfully', "success");
						} else {
							orbisAppSr.showNotification('Failed to save matcher.', "error");
						}
					}, 'json')
				}
			}
		});

	});
</script>

<template id="question-match">
<div class="panel panel-default">
	<div class="panel-body">
		<div class="control-group">
			<label class="control-label" for=""> <orbis:message code="i18n.df_modelMatcher.Question16632227376337113" /> </label>
			<div class="controls">
				<select name="secondary" class="js--ui-select required" v-model="match.q1">
					<option :value="null"><orbis:message code="i18n.df_modelMatcher.select7654469836634797" /></option>
					<option v-for="q in this.$root.primaryQuestions" :value="q">{{q.text}}</option>
				</select>
			</div>
		</div>
		<div class="control-group">
			<label class="control-label" for=""> <orbis:message code="i18n.df_modelMatcher.Question22811123487374544" /> </label>
			<div class="controls">
				<select name="secondary" class="js--ui-select required" v-model="match.q2">
					<option :value="null"><orbis:message code="i18n.df_modelMatcher.select7654469836634797" /></option>
					<option v-for="q in secondaryQuestions" :value="q">{{q.text}}</option>
				</select>
			</div>
		</div>
		<div class="control-group">
			<label class="control-label" for=""> <orbis:message code="i18n.df_modelMatcher.Operation2243781668297196" /> </label>
			<div class="controls">
				<select name="secondary" class="js--ui-select required" v-model="match.op">
					<option :value="null"><orbis:message code="i18n.df_modelMatcher.select7654469836634797" /></option>
					<option v-for="op in validOperations" :value="op.key">{{op.name}}</option>
				</select>
			</div>
		</div>

		<br>
		<a href="javascript:;" @click="remove">
			<small><orbis:message code="i18n.df_modelMatcher.remove3119947564664912" /></small>
		</a>
	</div>
</div>
</template>