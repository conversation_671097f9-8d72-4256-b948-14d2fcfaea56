<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>
<c:set var="questionClone" value="${dfQuestion}" scope="request" />
<c:set var="questionAnswersClone" value="${DFAnswerrEntity}" scope="request" />
<c:set var="matrixMap" value="${o:getMatrixMap(questionAnswersClone, questionClone, orbisLocale)}" />
<c:if test="${not empty leaderClass}">
	<c:set var="reloadAction" value="reload_form${DFModel.id}()"/>
</c:if>
<c:if test="${!isHidden}">
	<c:set var="uniqueId" value="dfMultiMatrix${o:generateId()}" />
	
	<div class="container--table">
		<table id="question_${dfQuestion.id}_${DFAnswerrEntity.id}" class="table small zebra margin--b--xxl">
			<caption class="table__caption">${dfQuestionLabel}${dfQuestion.required ? '*' : ''}</caption>
			<thead class="table__header">
				<tr class="table__row--header">
					<th class="table__heading">&nbsp;</th>
					<c:forEach var="colLabel" items="${isL1 ? dfQuestion.matrixColumnLabels : dfQuestion.l2MatrixColumnLabels}">
						<th class="table__heading">${colLabel}</th>
					</c:forEach>
				</tr>
			</thead>
			<tbody class="input__group js--error-placement-container">
				<c:forEach var="row" items="${matrixMap}" varStatus="rowStatus">
					<tr class="table__row--body">
						<td class="table__heading">${row.key}</td>
						<c:forEach var="column" items="${row.value}" varStatus="colStatus">
							<c:if test = "${colStatus.index == 0}">
								<td class="table__value">
									<label class="inline-label">
										<input type="checkBox" 
										aria-label="${column.key} - ${row.key}" 
										name="question_${dfQuestion.id}_${rowStatus.index}_${colStatus.index}" 
										onchange="checkRequirement_${uniqueId}('js--checkbox-identifier-${uniqueId}_${rowStatus.index}' , '${rowStatus.index}'); ${reloadActions}"
										class="js--${uniqueId} ${empty massAssignWtr && dfQuestion.required ? 'required' : ''} js--checkbox-identifier-${uniqueId}_${rowStatus.index} ${leaderClass}" 
										value="${rowStatus.index}_${colStatus.index}" ${column.value ? "checked" : ""}>
									</label>	
								</td>
							</c:if>
							<c:if test = "${colStatus.index != 0}">
								<td class="table__value">
									<label class="inline-label">
										<input type="checkBox" 
										aria-label="${column.key} - ${row.key}" 
										name="question_${dfQuestion.id}_${rowStatus.index}_${colStatus.index}" 
										onchange="checkRequirement_${uniqueId}('js--checkbox-identifier-${uniqueId}_${rowStatus.index}' , '${rowStatus.index}'); ${reloadAction}"
										class="js--${uniqueId} js--checkbox-identifier-${uniqueId}_${rowStatus.index} ${leaderClass}" 
										value="${rowStatus.index}_${colStatus.index}" ${column.value ? "checked" : ""}>
									</label>	
								</td>
							</c:if>
						</c:forEach>
					</tr>
				</c:forEach>
			</tbody>
		</table>
	</div>
	<input type="hidden" name="question_${dfQuestion.id}" id="question_${dfQuestion.id}"/>
	<c:set var="questionTooltip" value="${isL1 ? dfQuestion.toolTip : dfQuestion.toolTipL2}" />
	<c:if test="${not empty questionTooltip}">
		<p class="text--help">${questionTooltip}</p>
	</c:if>
	<script type="text/javascript">
	$(document).ready(function(){
		 <c:forEach var="row" items="${matrixMap}" varStatus="rowStatus">
			checkRequirement_${uniqueId}('js--checkbox-identifier-${uniqueId}_${rowStatus.index}', '${rowStatus.index}')
		</c:forEach> 
	});
	
	
	function checkRequirement_${uniqueId}(checkBoxClass, rowKey){
		
		var values = [];
		 $.each($("input[type='checkbox'].js--${uniqueId}:checked"), function(){
			values.push($(this).val());
         });
		 
		$('#question_${dfQuestion.id}').val(values.toString());
		
		
		if(${empty massAssignWtr && dfQuestion.required})
		{
			
			var matrixChecked = $("input[type='checkbox']."+checkBoxClass+":checked")
			if(matrixChecked.length > 0){
				$("input[name=" + 'question_${dfQuestion.id}_'+ rowKey +'_0' + "]").removeClass('required');
			}
			
			else{
				$("input[name=" + 'question_${dfQuestion.id}_'+ rowKey +'_0' + "]").addClass('required');	
			}
		}
	}
			
	</script>
</c:if>
<c:if test="${isHidden}">
	<c:forEach var="row" items="${matrixMap}" varStatus="rowStatus">
		<c:forEach var="column" items="${row.value}" varStatus="colStatus">
			<c:if test="${column.value}">
				<input type="hidden" name="dfh_question_${dfQuestion.id}_${rowStatus.index}" value="${rowStatus.index}_${colStatus.index}" />
			</c:if>
		</c:forEach>
	</c:forEach>
</c:if>