<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>
<c:if test="${not empty isShortCircuitView}">
	<!DOCTYPE html>
	<html lang="en">
	<head>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<%@ include file="/WEB-INF/spiralRobot/jsp/linkedResources.jsp" %>
	</head>
	<body>
</c:if>
<c:if test="${empty searchModel || empty searchModel.masterEntity}">
	<ui:note type="error" i18n_title="Undefined" classes="width--40">
		<orbis:message code="i18n.search_config.SearchMode5167136247998168" />
	</ui:note>
</c:if>
<c:if test="${not empty searchModel.errorMessages}">
	<c:set var="msgs" value="${searchModel.errorMessages}" />		
	<%@ include file="/WEB-INF/spiralRobot/jsp/i18nMsgs.jsp" %>			
</c:if>
<c:if test="${not empty searchModel && not empty searchModel.masterEntity}">
	<script type="text/javascript">
	var controllerPath = "${siteElement.fullPath}.htm";
		$(document).ready(function(){
			$("#mainPanel").removeClass('display--none');
			
			var relation ="${relation}";	
			var relationArray = relation.split(',');
			
			for (var i = 0; i < relationArray.length; i++)
			{
				var rel = relationArray[i];
				var request = {
					action : '<o:encrypt action="${actionValue}"/>',
					subAction : "loadConfirRelationship",
					reportKey : "${reportKey}",
					rel :  $("#"+ rel+"_tab").parent().parent().parent().data("rel"),
					rand : Math.floor(Math.random() * 100000)
				};
				  $("#"+ rel+"_tab").load(controllerPath, request, function(responseText, textStatus, xhr){
				});  
			}
		});
	</script>
	<div id="mainPanel" class="display--none padding--l--m padding--r--m padding--t--m">
		<%@ include file="/WEB-INF/spiralRobot/jsp/search/search_configHeader.jsp"%>
		<c:if test="${empty searchModel.attributes['SUPPRESS_CONFIG_TITLE']}">
			<ui:form action="displayHome" id="displayHomeFromSearchForm" classes="display--none">
				<c:if test="${not empty searchModel.attributes['extraHiddenFieldForTermTab']}">
					${searchModel.attributes['extraHiddenFieldForTermTab']}
				</c:if>
			</ui:form>
			<ui:navBack>
					<c:if test="${empty searchModel.attributes['termCourseId']}">
						<ui:navBackItem action="displayHome">
							<orbis:message code="i18n.search_config.Backtosite5467892580977284" arguments="${isL1 ? siteElement.elementTitle : (not empty siteElement.elementTitle2 ? siteElement.elementTitle2 : siteElement.elementTitle)}" />
						</ui:navBackItem>
					</c:if>
				<c:if test="${not empty searchModel.attributes['programId']}">
					<ui:navBackItem action="displayProgram" programId="${searchModel.attributes['programId']}"><orbis:message code="i18n.search_results.BacktoProg152539731546804" /></ui:navBackItem>
				</c:if>
				<c:if test="${not empty searchModel.attributes['termCourseId']}">
					<ui:navBackItem action="displayTermCourse" tcId="${searchModel.attributes['termCourseId']}"><orbis:message code="i18n.exp_termCourseStudentInteraction.BacktoTermCourse" /></ui:navBackItem>
				</c:if>
			</ui:navBack>
			<ui:header classes="display--flex align--middle">
				<orbis:message code="i18n.search_config.filters" />:
				<c:choose>
					<c:when test="${fn:startsWith(searchModel.masterEntity.entityLabel, 'i18n')}">
						<orbis:message code="${searchModel.masterEntity.entityLabel}" />
					</c:when>
					<c:otherwise>
						${searchModel.masterEntity.entityLabel}
					</c:otherwise>
				</c:choose>
				<ui:button classes="margin--l--s" type="info" action="${actionValue}" subAction="viewCriteria" clearCriteria="true"><orbis:message code="i18n.search_config.ClearCrite1385293436867241" /></ui:button>
			</ui:header>	
		</c:if>
		<ui:form id="questionForm" action="${actionValue}" subAction="search" siteElement="${siteElement}" classes="">
			<input type="hidden" name="state" value="${searchModel.state}" />
			<c:if test="${not empty filters}">
				<orbis:message code="i18n.report_main.BCurrentFi40441036814194387" />
				<ul class="list--plain">
					${filters}
				</ul>
			</c:if>
			<ul class="list--plain display--flex margin--b--m">
				<li class="margin--r--xs">
					<ui:button type="success" classes="margin--l--s" size="large" buttonType="submit"><orbis:message code="i18n.search_config.Next" /></ui:button>
				</li>
			</ul>
			
			
			<%@ include file="/WEB-INF/spiralRobot/jsp/search/search_configMaster.jsp"%>
			<%@ include file="/WEB-INF/spiralRobot/jsp/search/search_configDetails.jsp"%>
			<%@ include file="/WEB-INF/spiralRobot/jsp/search/search_configFooter.jsp"%>
		
			<ul class="list--plain display--flex margin--t--m">
				<li><ui:button type="success" classes="margin--l--s" size="large" onclick="$('#questionForm').submit();"><orbis:message code="i18n.search_config.Next" /></ui:button></li>
			</ul>
		</ui:form>
	</div>
</c:if>