<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>

<c:if test="${not empty isShortCircuitView}">
	<!DOCTYPE html>
	<html lang="en">
	<head>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<%@ include file="/WEB-INF/spiralRobot/jsp/linkedResources.jsp" %>
	</head>
	<body>
</c:if>

<script type="text/javascript">

	var controllerPath = "${siteElement.fullPath}.htm";

	var resultsActions = {
		ondblClickRow : function(rowId){
			<c:if test="${searchModel.canViewDetails}">
				orbisAppSr.openTempMessageDialog('<spring:message code="i18n.search_results.PleaseWait" javaScriptEscape="true"/>');
				var params = {"action": "${o:encrypt(actionValue, currentUser)}", "subAction":"onDoubleClickRow", "rowId": rowId, "state": "${searchModel.state}", "searchType":"${searchModel.masterEntity.entityLabel}", "entity":"${searchModel.masterEntity.entityHqlName}" };
				<c:if test="${not empty searchModel.attributes['extraHiddenFields']}" >
					$.extend(params, ${searchModel.attributes['extraHiddenFields']});
				</c:if>
				orbisAppSr.buildForm(params, "${siteElement.fullPath}.htm", false).submit();
			</c:if>
		},
		onNewWindow : function(rowId, newTab){
			<c:if test="${searchModel.canViewDetails}">
				var params = {"action": "${o:encrypt(actionValue, currentUser)}", "subAction":"onDoubleClickRow", "rowId": rowId, "state": "${searchModel.state}", "searchType":"${searchModel.masterEntity.entityLabel}", "entity":"${searchModel.masterEntity.entityHqlName}" };
				<c:if test="${not empty searchModel.attributes['extraHiddenFields']}" >
					$.extend(params, ${searchModel.attributes['extraHiddenFields']});
				</c:if>
				orbisAppSr.buildForm(params, "${siteElement.fullPath}.htm", newTab).submit();
			</c:if>
		}
	}
	
	$(window).bind('beforeunload', function(){
		//saveLastViewed();
	});
	
	$(document).ready(function(){
		//saveLastViewed();
	});
	
	function saveLastViewed()
	{
		saveSearch(true, {
			name : "lastSearchViewed",
			description : "Last Viewed Search",
			shareLevel : 0
		});
	}
</script>

<c:if test="${not empty searchModel.attributes['customJavascript']}">
	<script type="text/javascript" src="${searchModel.attributes['customJavascript']}"></script>
</c:if>


<c:if test="${not empty searchModel.plugins}">
	<c:forEach var="p" items="${searchModel.plugins}">
		<script type="text/javascript" src="${p}"></script>
	</c:forEach>
</c:if>


<ui:form id="displayHomeForm" classes="display--none" action="displayHome">
	<input type="hidden" name="currentTabSelected" value="dashboard">
	<c:if test="${not empty searchModel.attributes['extraHiddenFieldForTermTab']}">
		${searchModel.attributes['extraHiddenFieldForTermTab']}
	</c:if>										
</ui:form>

<ui:form id="displayTimePeriodTabForm" classes="display--none">
	<c:if test="${not empty searchModel.attributes['timePeriod']}">
		${searchModel.attributes['timePeriod']}
	</c:if>
</ui:form>
<ui:form id="displayHomeTabForm" classes="hide">
	<c:if test="${not empty searchModel.attributes['extraHiddenFieldForTab2']}">
		${searchModel.attributes['extraHiddenFieldForTab2']}
	</c:if>
</ui:form>
<c:if test="${ empty showBackToHomeNav}">
	<ui:navBack>
		<c:if test="${empty searchModel.attributes['extraHiddenFieldForTab2']}">
			<ui:navBackItem action="displayHome">${isL1 ? siteElement.elementTitle : (not empty siteElement.elementTitle2 ? siteElement.elementTitle2 : siteElement.elementTitle)}</ui:navBackItem>
		</c:if>
		<c:if test="${not empty searchModel.attributes['extraHiddenFieldForTab2']}">
			
			<ui:navBackItem onclick="$('#displayHomeTabForm').submit();"><orbis:message code="i18n.common.backToDashboard" /></ui:navBackItem>
		</c:if>
		<c:if test="${not empty searchModel.attributes['programId']}">
			<ui:navBackItem action="displayProgram" programId="${searchModel.attributes['programId']}"><orbis:message code="i18n.search_results.BacktoProg152539731546804" /></ui:navBackItem>
		</c:if>
		<c:if test="${not empty searchModel.attributes['timePeriod']}">
			
			<ui:navBackItem onclick="$('#displayTimePeriodTabForm').submit();"><orbis:message code="i18n.search_results.BacktoTimePeriods" /></ui:navBackItem>
		</c:if>
		<c:if test="${not empty searchModel.attributes['termCourseId']}">
			<ui:navBackItem action="displayTermCourse" tcId="${searchModel.attributes['termCourseId']}">Back to Term Course</ui:navBackItem>
		</c:if>
	</ui:navBack>

	<ui:header>
		<orbis:message code="i18n.search_results.SearchResults" />
	</ui:header>
</c:if>

<c:if test="${not empty searchModel.attributes['returnForm']}">
	${searchModel.attributes['returnForm']}
</c:if>
		
<c:if test="${not empty filters}">
	<ui:note type="info" i18n_title="i18n.report_main.BCurrentFi40441036814194387">
		<ul>
			${filters}
		</ul>
	</ui:note>
</c:if>
		
<%@ include file="/WEB-INF/spiralRobot/jsp/search/search_resultsHeader.jsp"%>
<%@ include file="/WEB-INF/spiralRobot/jsp/search/search_gridSetup.jsp"%>
<%@ include file="/WEB-INF/spiralRobot/jsp/search/search_resultsFooter.jsp"%>



