<%@ include file="/WEB-INF/jsp/include.jsp" %>
<jsp:useBean id="currentTag" scope="request" type="com.orbis.utils.tags.ui.SimpleHeaderTag"/>

<style>
    .simple--header {
        display: flex;
        width: calc(100% + 32px);
        transform: translateX(-16px);
        justify-content: space-between;
        align-items: center;
        align-content: center;
        row-gap: 8px;
    }

    .simple--header__dark {
        background-color: #232323;
    }


    .simple--header__main {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .simple--header__controls {
        display: flex;
        align-items: flex-start;
        gap: 8px;
    }

    .color--bg--dark-grey {
        background-color: #232323 !important
    }

    .frosted--button {
        background: rgba(255, 255, 255, 0.10);
    }

    @media (max-width: 768px) {
        .mobile--align--left {
            justify-content: flex-start;
            width: 100%;
            max-width: 100%;
            min-width: 100%;
        }
    }

</style>

<ui:grid id="${id}" css="${css}" classes="ui__simple--header simple--header padding--t--l ${theme} ${classes}">
    <ui:gridCol classes="simple--header__col" width="${currentTag.rightColContent ? 6 : 12}">
        <div class="simple--header__main padding--l--xs margin--l--m">
            <c:if test="${currentTag.actionMenuPopulated}">
                <div class="simple--header__controls">
                        <%-- Handling back action, can be populated by using the ui:action tag with the ID of back --%>
                    <c:if test="${not empty backActions}">
                        <c:choose>
                            <c:when test="${fn:length(backActions) == 1}">
                                
                                <c:forEach items="${backActions}" var="actionButton">
                                    <%-- If you're populating the customButtons section, this is the style you want to copy from --%>
                                    <ui:button i18n_iconButtonTitle="${backAction.tagAttributes.title}"
                                               data-pt-position="top" onclick="${actionButton.processedOnclick}"
                                               size="small" style="pill"
                                               type="${currentTag.lightTheme ? 'info' : 'defaultt'}"
                                               classes="height--24 display--flex align--middle ${currentTag.darkTheme ? 'color--font--white frosted--button' : ''} ${backAction.classes}">
                                        <i class="material-icons" class="${backAction.iconClasses}">arrow_back</i>
                                    </ui:button>
                                </c:forEach>
                            
                            </c:when>
                            <c:otherwise>
                                
                                <ui:dropdown i18n_title="arrow_back" isIconDropdown="true" size="small" style="pill"
                                             type="${currentTag.lightTheme ? 'info' : 'defaultt'}"
                                             classes="height--24 display--flex align--middle ${currentTag.darkTheme ? 'color--font--white frosted--button' : ''}">
                                    <c:forEach items="${backActions}" var="actionButton">
                                        <ui:dropdownItem onclick="${actionButton.processedOnclick}"
                                                         classes="${actionButton.classes}">
                                            ${actionButton.tagAttributes.title}
                                        </ui:dropdownItem>
                                    </c:forEach>
                                </ui:dropdown>
                            
                            </c:otherwise>
                        </c:choose>
                    </c:if>
                        
                        <%-- Handling custom actions, can be populated by using the ui:action tag with no ID --%>
                    <c:if test="${not empty actions}">
                        
                        <c:choose>
                            <c:when test="${fn:length(actions) <= 2}">
                                
                                <c:forEach items="${actions}" var="actionButton">
                                    <%-- If you're populating the customButtons section, this is the style you want to copy from --%>
                                    <ui:button i18n_iconButtonTitle="${actionButton.tagAttributes.title}"
                                               onclick="${actionButton.processedOnclick}" data-pt-position="top"
                                               size="small" style="pill"
                                               type="${currentTag.lightTheme ? 'info' : 'defaultt'}"
                                               classes="height--24 display--flex align--middle ${currentTag.darkTheme ? 'color--font--white frosted--button' : ''} ${actionButton.classes}">
                                        <i class="material-icons"
                                           class="${actionButton.iconClasses}">${actionButton.icon}</i>
                                    </ui:button>
                                </c:forEach>
                            
                            </c:when>
                            <c:otherwise>
                                
                                <ui:dropdown i18n_title="more_vert" isIconDropdown="true" size="small" style="pill"
                                             type="${currentTag.lightTheme ? 'info' : 'defaultt'}"
                                             classes="height--24 display--flex align--middle ${currentTag.darkTheme ? 'color--font--white frosted--button' : ''}">
                                    <c:forEach items="${actions}" var="actionButton">
                                        <ui:dropdownItem onclick="${actionButton.processedOnclick}"
                                                         classes="${actionButton.classes}">
                                            ${actionButton.tagAttributes.title}
                                        </ui:dropdownItem>
                                    </c:forEach>
                                </ui:dropdown>
                            
                            </c:otherwise>
                        </c:choose>
                    
                    </c:if>
                    
                    <ui:section key="customButtons" evaluate="${sectionsMap}"/>
                </div>
            </c:if>
            
            <h1 class="mobile--small-font margin--b--none ${titleHeaderLevel} ${currentTag.darkTheme ? 'color--font--white' : ''}">
                    ${title}
            </h1>
            
            <c:if test="${subtitle.populated || not empty sectionsMap.subtitle}">
                <ui:section key="subtitle" evaluate="${sectionsMap}">
                    <h2 class="mobile--small-font align--start width--100 text--truncate margin--b--none ${subtitleHeaderLevel} ${currentTag.darkTheme ? 'color--font--white' : ''}">
                            ${subtitle}
                    </h2>
                </ui:section>
            </c:if>
        </div>
    
    </ui:gridCol>
    
    <c:if test="${currentTag.rightColContent}">
        <ui:gridCol width="6" classes="display--flex align--end mobile--align--lelf wrap simple--header__col">
            
            <ui:section key="rightSideContent" evaluate="${sectionsMap}"/>
        
        </ui:gridCol>
    </c:if>
</ui:grid>
