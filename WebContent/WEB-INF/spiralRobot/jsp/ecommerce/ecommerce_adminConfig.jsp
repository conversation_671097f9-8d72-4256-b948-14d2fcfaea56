<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>
<c:set var="tabSelected" value="config" />
<%@ include file="ecommerce_adminTabHeader.jsp"%>
	
<ui:grid>
	<ui:gridCol width="4">
		<ui:panel>
			<ui:panelTitle>General Configuration</ui:panelTitle>
			<ul class="nav nav-tabs nav-stacked">
				<li><o:nav anchor="true" action="displayVendorConfig"><i class="icon-cog icon-large"></i> <orbis:message code="i18n.ecommerce_adminConfig.ConfigureP9003947786678741" /></o:nav></li>
				<li><o:nav anchor="true" action="displayTaxes"><i class="icon-cog icon-large"></i> <orbis:message code="i18n.ecommerce_adminConfig.SetupTaxCodes" /></o:nav></li>
				<li><o:nav anchor="true" action="displayReceiptConfig"><i class="icon-cog icon-large"></i> <orbis:message code="i18n.ecommerce_adminConfig.Setreceipt9158774779524952" /></o:nav></li>
			</ul>
		</ui:panel>
	</ui:gridCol>
</ui:grid>
<%@ include file="ecommerce_adminTabFooter.jsp"%>
