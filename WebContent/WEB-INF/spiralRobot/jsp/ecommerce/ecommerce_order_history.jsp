<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<%-- <ui:isConverted type="complete"></ui:isConverted>   --%>

<%-- JSP C:SET ARGUMENTS --%>

<%-- orderEntityClass (required): The fully qualified class-name of the order's EcommerceEntity (eg: "com.orbis.web.content.np.NPosting"). --%>
<c:if test="${empty orderEntityClass}"><c:set var="orderEntityClass" value="UNDEFINED"/></c:if>

<%-- orderEntityId (required): The database ID of the order's EcommerceEntity. --%>
<c:if test="${empty orderEntityId}"><c:set var="orderEntityId" value="UNDEFINED"/></c:if>

<%-- customerId (required): The UserDetailsImpl ID of the customer. --%>
<c:if test="${empty customerId}"><c:set var="customerId" value="UNDEFINED"/></c:if>

<%-- orderNumberPrefix (required): The "order number prefix" to be used for new orders. --%>
<c:if test="${empty orderNumberPrefix}"><c:set var="orderNumberPrefix" value="UNDEFINED"/></c:if>

<%-- defaultProductCode (required): The "default product code" to be used for new order-items ("^[a-zA-Z0-9]*$"). --%>
<c:if test="${empty defaultProductCode}"><c:set var="defaultProductCode" value="UNDEFINED"/></c:if>

<%-- adminMode (optional): set to "true" to allow user to create adjustment orders.  Set to "false" to hide this feature. --%>
<c:if test="${empty adminMode}"><c:set var="adminMode" value="true"/></c:if>

<%-- showButtons (optional): set to "true" to display the 'print' and 'email' buttons.  Set to "false" to hide these features. --%>
<c:if test="${empty showButtons}"><c:set var="showButtons" value="true"/></c:if>

<%-- onOrderClick (optional): set with the name of the javascript function to be fired when the user clicks on an order in the history-list --%>
<c:if test="${empty onOrderClick}"><c:set var="onOrderClick" value="showOrderDetailsModal"/></c:if>

<%-- onOrderEmailSent (optional): set with the name of the javascript function to be fired when the user sends an orderHistory/orderDetail email. --%>
<c:if test="${empty onOrderEmailSent}"><c:set var="onOrderEmailSent" value="undefined"/></c:if>

<%-- onOrderStatusUpdate (optional): set with the name of the javascript function to be fired when the user updates the status of an order. --%>
<c:if test="${empty onOrderStatusUpdate}"><c:set var="onOrderStatusUpdate" value="undefined"/></c:if>

<%-- loadCustomerHistory (optional): set to "true" to show ALL orders belonging to the "customerId".  Set to "false" to show only the order history pertaining to the "orderId" --%>
<c:if test="${empty loadCustomerHistory}"><c:set var="loadCustomerHistory" value="false"/></c:if>

<%-- showManageOrderBtn (optional): set to "false" to hide the red "Ecommerce Order" button. --%>
<c:if test="${empty showManageOrderBtn}"><c:set var="showManageOrderBtn" value="true"/></c:if>

<%-- additionalParams (optional): a json-object string of "additional request parameters" that will be appended to the various requests. --%>
<c:if test="${empty additionalParams}"><c:set var="additionalParams" value=""/></c:if>

<style>
	@media screen and (min-width: 768px) {
		.order-history {
			padding: 0 80px;
		}
	}

	table.orderHistoryTable tbody tr:hover {
		cursor:pointer;
	}
</style>

<div id="orderHistory" class="order-history">
	<spring:message code="i18n.ecommerce_order_history.loadingorderhistory" />
</div>

<script type="text/javascript">
	// Initial orders list load
	$(window).on("load", () => {
		loadHistory();
	});

	// Order row click show modal
	$(document).on("click", "table.orderHistoryTable tbody tr", function() {
		const orderId = $(this).data("orderId");
		$("tr", "table.orderHistoryTable tbody").removeClass("selectedOrder");
		$("tr[data-order-id=" + orderId + "]", "table.orderHistoryTable tbody").addClass("selectedOrder");
		showOrderDetailsModal(orderId);
	});

	function loadHistory() {
		let request = {
			action: '<o:encrypt action="ajaxLoadOrderHistory" />',
			orderId: '${orderId}',
			adminMode: ${adminMode},
			rnd: Math.floor(Math.random() * 1000000)
		};

		// Modify request if we need to show ALL orders belonging to the "customerId"
		<c:if test="${loadCustomerHistory}">
			request = {
				...request,
				action: '<o:encrypt action="ajaxLoadCustomerOrders" />',
				moduleId: '${ecommerceModuleId}',
				customerId: '${customerId}'
			}
		</c:if>

		const orderHistoryElem = $("#orderHistory");

		orderHistoryElem.load("${siteElement.fullPath}.htm", request, (data, status, xhr) => {
			if (!orbisAppSr.checkAjaxResponse(xhr)) {
				// try again...
				orderHistoryElem.empty();
				orderHistoryElem.text('<spring:message code="i18n.ecommerce_order_history.loadingorderhistory" javaScriptEscape="true"/>');
				loadHistory();
			}
		});
	}

	function showOrderDetailsModal(orderId) {
		const request = {
				action: '<o:encrypt action="ajaxLoadOrderDetails" />',
				orderId: orderId,
				adminMode: ${adminMode},
				rnd: Math.floor(Math.random() * 1000000)
			};
		$(".modal__body", "#orderDetailsModal").load("${siteElement.fullPath}.htm", request, (data, status, xhr) => {
			if(!orbisAppSr.checkAjaxResponse(xhr)) {
				return;
			}

			if (orderDetails.orderStatus === 1 && orderDetails.paymentVendorConfigured) {
				$("#payButton", "#orderDetailsModal").removeClass("display--none");
			}
			else {
				$("#payButton", "#orderDetailsModal").addClass("display--none");
			}

			if (orderDetails.isReconciliationOrder) {
				$("#backToReconcileTargetButton", "#orderDetailsModal").removeClass("display--none");
			}
			else {
				$("#backToReconcileTargetButton", "#orderDetailsModal").addClass("display--none");
			}

			if(orderDetails.adminModuleUrl) {
				$("#manageOrderBtn", "#orderDetailsModal").removeClass("display--none");
			}
			else {
				$("#manageOrderBtn", "#orderDetailsModal").addClass("display--none");
			}

			$("#orderDetailsModal").data({
				orderId: orderId,
				adminModuleUrl: orderDetails.adminModuleUrl
			});
			orbisAppSr.showModal('orderDetailsModal');
		});
	}

	function printOrderHistory() {
		if (!$("tr", "table.orderHistoryTable tbody").length) {
			orbisAppSr.showAlertModal('<spring:message code="i18n.ecommerce_order_history.Thereisnot6633462601234983" javaScriptEscape="true"/>');
			return;
		}

		const form = orbisAppSr.buildForm({
			action : '<o:encrypt action="printOrderHistory" />',
			orderId : parentOrderId,
			customerId: '${customerId}',
			moduleId: '${ecommerceModuleId}',
			rnd: Math.floor(Math.random() * 1000000)
		}, "${siteElement.fullPath}.htm");
		form.get(0).target = "_blank";
		form.submit();
	}
	
	function printOrder() {
		const form = orbisAppSr.buildForm({
			action : '<o:encrypt action="printOrder" />',
			orderId : $("#orderDetailsModal").data("orderId"),
			rnd: Math.floor(Math.random() * 1000000)
		}, "${siteElement.fullPath}.htm");
		form.get(0).target = "_blank";
		form.submit();
	}
	
	function emailOrderHistory() {
		if (!$("tr", "table.orderHistoryTable tbody").length) {
			orbisAppSr.showAlertModal('<spring:message code="i18n.ecommerce_order_history.Thereisnot0914019325042904" javaScriptEscape="true"/>');
			return;
		}

		const request = {
			action: '<o:encrypt action="ajaxLoadEmailOrderHistory" />',
			orderId: parentOrderId,
			customerId: '${customerId}',
			moduleId: '${ecommerceModuleId}',
			adminMode: ${adminMode},
			rnd: Math.floor(Math.random() * 1000000)
		};

		$(".modal__body", "#emailOrderModal").load("${siteElement.fullPath}.htm", request, (data, status, xhr) => {
			if(!orbisAppSr.checkAjaxResponse(xhr)) {
				return;
			}

			$("#emailOrderModalTitle", "#emailOrderModal").text('<spring:message code="i18n.ecommerce_order_history.emailOrderHistory" javaScriptEscape="true"/>');
			$("#emailOrderModal").data("emailType", "orderHistory");
			orbisAppSr.showModal('emailOrderModal');
		});
	}

	function emailOrder() {
		orbisAppSr.hideModal('orderDetailsModal');

		const request = {
			action: '<o:encrypt action="ajaxLoadEmailOrder" />',
			orderId: $("#orderDetailsModal").data("orderId"),
			adminMode: ${adminMode},
			rnd: Math.floor(Math.random() * 1000000)
		};

		$(".modal__body", "#emailOrderModal").load("${siteElement.fullPath}.htm", request, (data, status, xhr) => {
			if(!orbisAppSr.checkAjaxResponse(xhr)) {
				return;
			}

			$("#emailOrderModalTitle", "#emailOrderModal").text('<spring:message code="i18n.ecommerce_order_history.EmailOrderInfo" javaScriptEscape="true"/>');
			$("#emailOrderModal").data("emailType", "orderDetail");
			orbisAppSr.showModal('emailOrderModal');
		});
	}
	
	function sendOrderEmail() {
		if(!$("#ecommerceEmailerForm").valid()) {
			document.getElementById("ecommerceEmailerForm").scrollIntoView();
		}

		const orderEmailedFunc = ${onOrderEmailSent};
		const request = {
			action: '<o:encrypt action="ajaxSendOrderEmail" />',
			emailTo: $("input[name=emailTo]", "#emailOrderModal").val(),
			emailFrom: $("input[name=emailFrom]", "#emailOrderModal").val(),
			emailSubject: $("input[name=emailSubject]", "#emailOrderModal").val(),
			emailMessage: $("textarea[name=emailMessage]", "#emailOrderModal").val(),
			emailOrderInfo: $("#emailOrderInfo", "#emailOrderModal").html(),
			emailType: $("#emailOrderModal").data("emailType"),
			orderEntityClass: '${orderEntityClass}',
			orderEntityId: '${orderEntityId}',
			rnd: Math.floor(Math.random() * 1000000)
		};

		$.post("${siteElement.fullPath}.htm", request, function(data, status, xhr){
			if(!orbisAppSr.checkAjaxResponse(xhr)) {
				return;
			}

			if (!data.successful) {
				const errMsg = $(document.createElement("div")).html('<spring:message code="i18n.email_commonEmailerDone.EmailFailed" javaScriptEscape="true"/>').addClass("tag-label error");
				$(".msg", "#emailOrderModal").prepend(errMsg);
				return;
			}

			orbisAppSr.hideModal('emailOrderModal');
			orbisAppSr.timedMessageDialog('<spring:message code="i18n.email_commonEmailerDone.EmailSent" javaScriptEscape="true"/>', 2000);

			if (typeof orderEmailedFunc === 'function') {
				orderEmailedFunc();
			}
		}, "json");
	}
	
	function manageOrder() {
		const orderDetailsModalElem = $("#orderDetailsModal");
		orbisAppSr.buildForm({
			action : '<o:encrypt action="displayOrderDetails" />',
			orderId : orderDetailsModalElem.data("orderId"),
			rnd: Math.floor(Math.random() * 1000000)
		}, orderDetailsModalElem.data("adminModuleUrl")).submit();
	}
	
	function reconcileOrder() {
		const request = {
			action : '<o:encrypt action="reconcileOrderCheckout" />',
			orderId : $("#orderDetailsModal").data("orderId"),
			rnd : Math.floor(Math.random() * 1000000)
		};
		<c:if test="${not empty additionalParams}">
			$.extend(request, ${additionalParams});
		</c:if>
		orbisAppSr.buildForm(request, "${siteElement.fullPath}.htm").submit();
	}
	
	function backToReconcileTarget() {
		showOrderDetailsModal(orderDetails.reconcileTargetId);
	}
</script>

<%-- *** ADMIN MODE JAVASCRIPT *** --%>
<c:if test="${adminMode}">
	<orbis:addComponent component="agilityjs" />
	<orbis:addComponent component="json" />
	<orbis:addComponent component="accounting" />
	<orbis:addComponent component="jqueryValidate" version="1.11.1" />
	<script type="text/javascript">
		var orderItemEditor;
		var parentOrderId;
		var taxOptions = {};
		var order = {};
		var orderItems = {};
		var defaultProductCode = "${defaultProductCode}";
		
		/*** AGILITY PROTOTYPE: ORDER-ITEM VIEW  ***/ 
		var orderItemViewProto = $$({
			view: {
				format: '<tr class="orderItem">\
					       <td style="vertical-align:middle;">\
								<a class="deleteItem" href="javascript:void(0)" title="<spring:message htmlEscape="true" javaScriptEscape="true" code="i18n.ecommerce_order_history.deleteitem" />"><img src="${RESOURCES_URL}/core/images/icons/icon-delete.png"></a>\
						   </td>\
					       <td class="itemClick">\
			  					<span data-bind="productCode" class="boldCaps"></span> - <span data-bind="productName"></span>\
			  					<div data-bind="productDesc" style="font-style:italic;"></div>\
			  				</td>\
						   <td>\
						   		<table class="orderItemTotals">\
						   		</table>\
						   </td>\
				       </tr>',
				style: '& .boldCaps {font-weight:bold;text-transform:uppercase;}\
						& .itemClick:hover {cursor:pointer;}\
						& table.orderItemTotals {float:right;padding:0px;margin:0px;}\
						& table.orderItemTotals td {border:none;text-align:right;padding:0px 0px 0px 5px;}'
			},
			controller: {
				'click .deleteItem': function() {deleteOrderItem(this);},
				'click .itemClick': function() {editOrderItem(this);},
				'create': function() {this.renderTotals();},
				'change': function() {this.renderTotals();}
			}, 
			renderTotals: function() {
				var subTotal = parseFloat(this.model.get("subTotal"));
				var runningTotal = subTotal;
				this.view.$(".orderItemTotals").empty();
				this.view.$(".orderItemTotals").append(this.renderTotal("", subTotal));
				var taxOptions = this.model.get("taxOptions");
				if (!$.isEmptyObject(taxOptions))
				{
					for (var taxId in taxOptions)
					{
						var taxOption = taxOptions[taxId];
						if (taxOption.checked)
						{
							var taxLabel = taxOption.taxCode + ' (' + taxOption.taxValue + '%)';
							taxOption.taxTotal = accounting.toFixed((subTotal * (taxOption.taxValue / 100)), 2);
							runningTotal += parseFloat(taxOption.taxTotal);
							this.view.$(".orderItemTotals").append(this.renderTotal(taxLabel, taxOption.taxTotal));
						}
						else
						{
							delete taxOption['taxTotal'];
						}
					}
				}
				
				var thisModel = this.model.get();
				thisModel.total = runningTotal;
				this.model.set(thisModel, {silent:true});
				
				this.formatTotals();
			},
			renderTotal: function(label, total) {
				return '<tr><td>' + label + '</td><td class="amt">' + total + '</td></tr>';
			},
			formatTotals: function() {
				this.view.$(".amt").each(function() {
					$(this).text(accounting.formatMoney($(this).text()));
				});
			}
		});	
		
		/*** AGILITY PROTOTYPE: ORDER-ITEM EDITOR  ***/ 
		var orderItemEditorProto = $$({
			view: {
			  format: '<form enctype="multipart/form-data" >\
			  			<table class="table table-condensed">\
	  				       <tr>\
						       <td><spring:message code="i18n.ecommerce_order_history.ProductCode" /></td>\
							   <td><input type="text" data-bind="productCode" name="productCode"></td>\
					       </tr>\
	  				       <tr>\
						       <td><spring:message code="i18n.ecommerce_order_history.ProductName" /></td>\
							   <td><input type="text" data-bind="productName" name="productName"></td>\
					       </tr>\
	  				       <tr>\
						       <td><spring:message code="i18n.ecommerce_order_history.ProductDescription" /></td>\
							   <td><input type="text" data-bind="productDesc" name="productDesc"></td>\
					       </tr>\
	  				       <tr>\
						       <td><spring:message code="i18n.ecommerce_order_history.Charge" /></td>\
							   <td><input type="text" data-bind="subTotal" placeholder="0.00" style="text-align:right;" name="subTotal"></td>\
					       </tr>\
	  				       <tr class="taxRow">\
						       <td><spring:message code="i18n.ecommerce_order_history.ApplicableTaxes" /></td>\
							   <td class="taxCell"></td>\
					       </tr>\
				       	</table>\
				      </form>',
				style: '& .taxRow {display:none;}'
			},
			controller: {
				'create': function(){
					var taxOptions = this.model.get("taxOptions");
					if (!$.isEmptyObject(taxOptions))
					{
						for (var taxId in taxOptions)
						{
							this.view.$(".taxCell").append(this.renderTaxOption(taxOptions[taxId]));
						}
						this.view.$(".taxRow").removeClass("display--none");
					}
				},
				'click .taxChk': function(evt){
					var taxId = $(evt.target).data("taxId");
					var taxOptions = this.model.get("taxOptions");
					taxOptions[taxId].checked = evt.target.checked;
				}
			},
			renderTaxOption: function(taxOption) {
				return '<input type="checkbox" class="taxChk" data-tax-id="' + taxOption.taxId + '" ' + (taxOption.checked ? 'checked' : '') + '> ' + taxOption.taxCode + ' (' + taxOption.taxValue + '%)<BR>';
			}
		});
		$(window).on("load", function(){
		     jQuery.validator.addMethod("money", function(value, element) {
		         return this.optional(element) || /^[+-]?[0-9]{1,3}(?:,?[0-9]{3})*(?:\.[0-9]{2})?$/.test(value);
		     }, '<spring:message code="i18n.ecommerce_order_history.MustBeInCu25142900998939477" javaScriptEscape="true" />');

			 <%-- fixes z-index problems --%>
		     $("#editOrderModal").appendTo("body");
		     $("#editOrderItemModal").appendTo("body");
		     $("#orderStatusModal").appendTo("body");
		     $("#orderNoteModal").appendTo("body");
		});
		function editOrderItem(orderItem) {
			var editModel = {};

			if (orderItem)
			{
				// edit existing order-item...
				editModel = orderItem.model.get();
				editModel.orderItem = orderItem;
			}
			else
			{
				// add/edit NEW order-item...
				editModel.taxOptions = jQuery.extend(true, {}, taxOptions);
				editModel.id = "NEW_" + Math.floor(Math.random() * 10000000);
				editModel.productCode = defaultProductCode;
			}

			orderItemEditor = $$(orderItemEditorProto, editModel);
			
			$(".modal-body", "#editOrderItemModal").empty();
			$$.document.append(orderItemEditor, $(".modal-body", "#editOrderItemModal"));
			var validateCfg = {
				rules: {
					productCode: {
						required: true,
						minlength: 4
					},
					productName: {
						required: true
					},
					productDesc: {
						required: true
					},
					subTotal: {
						required: true,
						money: true
					}
				}
			};
			$("form", "#editOrderItemModal").validate(validateCfg);
			orbisAppSr.hideModal('editOrderModal');
			orbisAppSr.showModal('editOrderItemModal');
		}
		function saveOrderItem() {
			if ($("form", "#editOrderItemModal").valid())
			{
				var orderItem = orderItemEditor.model.get("orderItem"); 
				if (orderItem)
				{
					// update existing order-item...
					var viewModel = orderItemEditor.model.get();
					delete viewModel['orderItem'];
					orderItem.model.set(viewModel);
				}
				else
				{
					// add new order-item...
					orderItem = $$(orderItemViewProto, orderItemEditor.model.get());
					$$.document.append(orderItem, $(".orderItemsList", "#editOrderModal"));
				}
		
				orderItems[orderItem.model.get("id")] = orderItem;
				refreshOrder();
				
				orbisAppSr.showModal('editOrderModal');
				orbisAppSr.hideModal('editOrderItemModal');
			}
		}
		function deleteOrderItem(orderItem) {
			delete orderItems[orderItem.model.get("id")];
			orderItem.destroy();
			refreshOrder();
		}
		function cancelOrderItem() {
			orbisAppSr.showModal('editOrderModal');
			orbisAppSr.hideModal('editOrderItemModal');
		}
		function refreshOrder() {
			// *** CALC TOTALS...
			var totalsModel = {};
			totalsModel.runningSubTotal = 0;
			totalsModel.runningTotal = 0;
			totalsModel.taxOptions = jQuery.extend(true, {}, taxOptions);

			for (var k in orderItems)
			{
				var orderItem = orderItems[k];
				if (orderItem.model.get("subTotal"))
				{
					totalsModel.runningSubTotal += parseFloat(orderItem.model.get("subTotal"));
					totalsModel.runningTotal += parseFloat(orderItem.model.get("subTotal"));
				}

				for (var taxId in orderItem.model.get("taxOptions"))
				{
					var taxOption = orderItem.model.get("taxOptions")[taxId];
					
					if (taxOption.checked)
					{
						totalsModel.taxOptions[taxId].checked = true;
						if (!totalsModel.taxOptions[taxId].taxTotal)
						{
							totalsModel.taxOptions[taxId].taxTotal = 0;
						}
						var taxTotal = parseFloat(taxOption.taxTotal);
						totalsModel.taxOptions[taxId].taxTotal += taxTotal;
						totalsModel.runningTotal += taxTotal;
					}
				}
			}
			
			// *** RENDER TOTALS...
			$(".footerTotals").empty();
			$(".footerTotals").append(renderTotal("Sub Total", totalsModel.runningSubTotal));
			for (var taxId in totalsModel.taxOptions)
			{
				var taxOption = totalsModel.taxOptions[taxId];
				if (taxOption.checked)
				{
					$(".footerTotals").append(renderTotal(taxOption.taxCode, taxOption.taxTotal));
				}
			}
			$(".footerTotals").append(renderTotal("TOTAL", totalsModel.runningTotal));
			$(".amt", ".footerTotals").each(function(){$(this).text(accounting.formatMoney($(this).text()))});
			
			// *** REFRESH 'ORDER' OBJECT (gets sent at checkout-time)...
			order = {};
			order.subTotal = totalsModel.runningSubTotal;
			order.total = totalsModel.runningTotal;
			order.orderItems = {};
			for (var k in orderItems)
			{
				order.orderItems[k] = orderItems[k].model.get();
			}
		}
		function renderTotal(label, total) {
			return '<tr><td>' + label + '</td><td class="amt">' + total + '</td></tr>';
		}
		function checkout() {
			if (!order.orderItems || order.orderItems.length == 0)
			{
				orbisAppSr.showAlertModal('<spring:message code="i18n.ecommerce_order_history.Youneedtoc25142900998939477" javaScriptEscape="true"/>');
			}
			else
			{
				order.customerId = "${customerId}";
				order.orderNumberPrefix = "${orderNumberPrefix}";
				order.parentOrderId = parentOrderId;
				order.paymentType = $("select[name=paymentType]", "#editOrderModal").val();
				order.note = $("input[name=note]", "#editOrderModal").val();
				order.orderEntityClass = "${orderEntityClass}";
				order.orderEntityId = "${orderEntityId}";

				orbisAppSr.buildForm({
					action : '<o:encrypt action="checkoutAdjustmentOrder" />',
					jsonOrder : JSON.stringify(order)
				}, "${siteElement.fullPath}.htm").submit();
			}
		}
		function openStatusModal() {
			orbisAppSr.showModal('orderStatusModal');
		}
		function closeStatusModal() {
			orbisAppSr.hideModal('orderStatusModal');
			orbisAppSr.showModal('orderDetailsModal');
		}
		function saveStatus() {
			var status = $("#statusSelect", "#orderStatusModal").val();
			
			if (!$.isNumeric(status))
			{
				orbisAppSr.showAlertModal('<spring:message code="i18n.ecommerce_order_history.Astatushas9023851526486635" javaScriptEscape="true"/>');
			}
			else
			{
				closeStatusModal();

				var request = {
					action: '<o:encrypt action="ajaxSaveOrderStatus" />',
					orderId: $("#orderDetailsModal").data("orderId"),
					status: $("#statusSelect", "#orderStatusModal").val()
				};

				var orderStatusUpdateFunc = ${onOrderStatusUpdate};

				$.post("${siteElement.fullPath}.htm", request, function(data, status, xhr){
					if(orbisAppSr.checkAjaxResponse(xhr)) {
						showOrderDetailsModal($("#orderDetailsModal").data("orderId"));
						loadHistory();
						
						if (typeof orderStatusUpdateFunc === 'function')
						{
							orderStatusUpdateFunc();
						}
					}
				}, "json");
			}
		}
		function openNoteModal() {
			$("#noteInput", "#orderNoteModal").val("");
			orbisAppSr.showModal('orderNoteModal');
		}
		function saveEcommerceOrderNote() {
			orbisAppSr.hideModal('orderNoteModal');

			var request = {
				action: '<o:encrypt action="ajaxSaveOrderNote" />',
				orderId: $("#orderDetailsModal").data("orderId"),
				note: $("#noteInput", "#orderNoteModal").val()
			};

			$.post("${siteElement.fullPath}.htm", request, function(data, status, xhr){
				if(orbisAppSr.checkAjaxResponse(xhr)) {
					showOrderDetailsModal($("#orderDetailsModal").data("orderId"));
				}
			}, "json");
		}
	</script>
</c:if>

<%-- ORDER DETAILS DIALOG --%>
<ui:modal id="orderDetailsModal" i18n_title="i18n.ecommerce_order_history.OrderDetails">
	<div class="modal__body margin--b--m">
	</div>
	<c:if test="${adminMode}">
		<ui:button onclick="openStatusModal()"><orbis:message code="i18n.ecommerce_order_history.UpdateStatus" /></ui:button>
		<ui:button onclick="openNoteModal()"><orbis:message code="i18n.ecommerce_order_history.AddNote" /></ui:button>
	</c:if>
	<ui:button id="payButton" type="error" onclick="reconcileOrder()"><orbis:message code="i18n.ecommerce_order_history.reconcileOrder" /></ui:button>
	<ui:button id="backToReconcileTargetButton" onclick="backToReconcileTarget()" aria-label="Back to Reconcile Target"><B class="icon-hand-left"></B></ui:button>

	<c:if test="${showManageOrderBtn}">
		<ui:button id="manageOrderBtn" onclick="manageOrder()" classes="display--none"><orbis:message code="i18n.ecommerce_order_history.EcommerceOrder" /></ui:button>
	</c:if>
	<ui:button onclick="printOrder()"><orbis:message code="i18n.ecommerce_order_history.Print" /></ui:button>
	<ui:button onclick="emailOrder()"><orbis:message code="i18n.ecommerce_order_history.Email" /></ui:button>
</ui:modal>

<%-- EMAIL ORDER DIALOG --%>
<ui:modal id="emailOrderModal" i18n_title="i18n.ecommerce_order_history.EmailOrderInfo">
	<div class="modal__body"></div>
	<div class="msg"></div>
	<ui:button type="info" onclick="sendOrderEmail()"><spring:message code="i18n.ecommerce_order_history.Send" /></ui:button>
</ui:modal>

<%-- *** ADMIN MODE DIALOGS *** --%>
<c:if test="${adminMode}">

	<%-- EDIT ORDER DIALOG --%>
	<ui:modal i18n_title="i18n.ecommerce_order_history.BORDERCONFIGB" id="editOrderModal">
		
		<ui:select name="paymentType" i18n_title="i18n.ecommerce_order_history.Type">
			<ui:selectItem value="0"><spring:message code="i18n.ecommerce_order_history.CreditCard" /></ui:selectItem>
			<ui:selectItem value="1"><spring:message code="i18n.ecommerce_order_history.Cash" /></ui:selectItem>
			<ui:selectItem value="2"><spring:message code="i18n.ecommerce_order_history.Cheque" /></ui:selectItem>
			<ui:selectItem value="3"><spring:message code="i18n.ecommerce_order_history.FreeOverride" /></ui:selectItem>
			<ui:selectItem value="4"><spring:message code="i18n.ecommerce_order_history.Invoice" /></ui:selectItem>
			<ui:selectItem value="5"><spring:message code="i18n.ecommerce_order_history.REFUND" /></ui:selectItem>
		</ui:select>
		
		<ui:textbox name="note" maxLength="255"><spring:message code="i18n.ecommerce_order_history.Noteoptional" /></ui:textbox>		

		<div class="label heading--underline">			
			<spring:message code="i18n.ecommerce_order_history.BORDERITEMSB" />
		</div>
		
		<table class="table">
			<tbody class="orderItemsList"></tbody>
			
			<tfoot class="orderItemsFooter">
				<tr>
					<td>
						<ui:button type="info" size="small" onclick="editOrderItem()"><i class="material-icons small">add</i><spring:message code="i18n.ecommerce_order_history.AddOrderItem" /></ui:button>
					</td>
					<td style="text-align:right;">
				   		<table class="footerTotals">
				   		</table>
					</td>
				</tr>
			</tfoot>
		</table>
		
		<ui:button onclick="checkout()"><spring:message code="i18n.ecommerce_order_history.Checkout" /></ui:button>
	</ui:modal>
	    
	<%-- ORDER-ITEM DIALOG --%>
	<ui:modal i18n_title="i18n.ecommerce_order_history.OrderItem" id="editOrderItemModal" includeClose="false">
		<ui:button type="info" onclick="cancelOrderItem()"><spring:message code="i18n.ecommerce_order_history.Cancel" /></ui:button>
		<ui:button onclick="saveOrderItem()"><spring:message code="i18n.ecommerce_order_history.Save" /></ui:button>
	</ui:modal>

	<%-- ORDER-STATUS DIALOG --%>
	<ui:modal i18n_title="i18n.ecommerce_order_history.ChangePaymentStatus" id="orderStatusModal">
		<ui:select name="statusSelect" id="statusSelect" i18n_title="i18n.ecommerce_order_history.BSelectStatusB">
			<ui:selectItem value="1"><spring:message code="i18n.ecommerce_order_history.PENDING" /></ui:selectItem>
			<ui:selectItem value="2"><spring:message code="i18n.ecommerce_order_history.PAID" /></ui:selectItem>
			<ui:selectItem value="3"><spring:message code="i18n.ecommerce_order_history.FAILED" /></ui:selectItem>
			<ui:selectItem value="4"><spring:message code="i18n.ecommerce_order_history.INCOMPLETE" /></ui:selectItem>
			<ui:selectItem value="5"><spring:message code="i18n.ecommerce_order_history.CANCELLED" /></ui:selectItem>
		</ui:select>
		
		<ui:button onclick="saveStatus()"><spring:message code="i18n.ecommerce_order_history.SaveStatus" /></ui:button>
	</ui:modal>
	
	<%-- ORDER-NOTE DIALOG --%>
	<ui:modal i18n_title="i18n.ecommerce_order_history.CreateNewNote" id="orderNoteModal">
		<ui:textbox id="noteInput" name="noteInput"><spring:message code="i18n.ecommerce_order_history.BEnterNoteB" /></ui:textbox>
		
		<ui:button onclick="saveEcommerceOrderNote()"><spring:message code="i18n.ecommerce_order_history.SaveNote" /></ui:button>
	</ui:modal>
</c:if>
