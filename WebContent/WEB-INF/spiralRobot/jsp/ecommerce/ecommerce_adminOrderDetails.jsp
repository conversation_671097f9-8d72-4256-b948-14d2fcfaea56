<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<%-- <ui:isConverted type="complete" />   --%>

<ui:navBack>
	<ui:navBackItem action="displayHome">
		<orbis:message code="i18n.common.tool.overview" />
	</ui:navBackItem>
</ui:navBack>

<ui:header>
	<orbis:message code="i18n.ecommerce_adminOrderDetails.EcommerceOrder" />
</ui:header>

<c:set var="activeTab" value="orderDetail" />
<%@ include file="ecommerce_order_nav.jsp"%>

<div id="orderDetails">
	<ui:grid>
		<ui:gridCol width="8">
			<c:if test="${not empty orderEntity}">
				<ui:panel classes="margin--b--m">
					<ui:panelTitle><spring:message code="i18n.ecommerce_adminOrderDetails.BOrderInformationB" /></ui:panelTitle>
					<ui:keyValueList>
						<ui:keyValueListItem>
							<ui:key><spring:message code="i18n.ecommerce_adminOrderDetails.OrderType" /></ui:key>
							<ui:value>${orderType}</ui:value>
						</ui:keyValueListItem>
						
						<ui:keyValueListItem>
							<ui:key><spring:message code="i18n.ecommerce_adminOrderDetails.OrderDescription" /></ui:key>
							<ui:value><ui:button style="plain" type="info" action="${orderEntity.detailsPageAction}" orderId="${order.id}" quickLookup="true" siteElement="${orderEntity.detailsPageSiteElement}">${typeDetail}</ui:button></ui:value>
						</ui:keyValueListItem>
					</ui:keyValueList>
				</ui:panel>
			</c:if>
			
			<c:if test="${empty orderEntity}">
				<ui:note type="warning">
					<orbis:message code="i18n.ecommerce_adminOrderDetails.strongThee8117498453013108" />
				</ui:note>
			</c:if>

			<ui:panel classes="margin--b--m">
				<ui:panelTitle><spring:message code="i18n.ecommerce_adminOrderDetails.BCustomerI1885346670702398" /></ui:panelTitle>
				<c:if test="${not empty order.customer.id}">
					<ui:button action="displayCustomerOrders" type="info" size="small" userId="${order.customer.id}" orderId="${order.id}" quickLookup="true"><spring:message code="i18n.ecommerce_adminOrderDetails.ViewAllCus2569089194739287" /></ui:button>
				</c:if>
				<c:if test="${empty order.customer}">
					<span class="tag-label info"><spring:message code="i18n.ecommerce_adminOrderDetails.AnonymousCustomer" /></span>
					<c:if test="${not empty order.overrideEmail}">
						<ui:keyValueList>
							<ui:keyValueListItem>
								<ui:key><orbis:message code="i18n.ecommerce_adminOrderDetails.Email3118488049596305" /></ui:key>
								<ui:value>${order.overrideEmail}</ui:value>
							</ui:keyValueListItem>
						</ui:keyValueList>
					</c:if>
				</c:if>
				<c:if test="${not empty order.customer}">
					<ui:keyValueList>
						<ui:keyValueListItem>
							<ui:key><spring:message code="i18n.ecommerce_adminOrderDetails.NameUserType" /></ui:key>
							<ui:value>
								<c:choose>
									<c:when test="${not empty acrmSE}">
										<ui:button type="info" action="displayContactInfoEditForUser" classes="plain" userId="${order.customer.id}" quickLookup="true" siteElement="${acrmSE}">${order.customer.fullName} (${order.customer.primaryGroup.name})</ui:button>
									</c:when>
									<c:otherwise>
										${order.customer.fullName} (${order.customer.primaryGroup.name})
									</c:otherwise>
								</c:choose>
							</ui:value>
						</ui:keyValueListItem>
						<ui:keyValueListItem>
							<ui:key><spring:message code="i18n.ecommerce_adminOrderDetails.Username" /></ui:key>
							<ui:value>
								<c:choose>
									<c:when test="${not empty acrmSE}">
										<ui:button type="info" action="displayContactInfoEditForUser" classes="plain" userId="${order.customer.id}" quickLookup="true" siteElement="${acrmSE}">${order.customer.username}</ui:button>
									</c:when>
									<c:otherwise>
										${order.customer.username}
									</c:otherwise>
								</c:choose>
							</ui:value>
						</ui:keyValueListItem>
						<ui:keyValueListItem>
							<ui:key><spring:message code="i18n.ecommerce_adminOrderDetails.OrganizationDivision" /></ui:key>
							<ui:value>${order.customer.organization} ${(not empty order.customer.organization && not empty order.customer.company) ? "/" : ""} ${order.customer.company}</ui:value>
						</ui:keyValueListItem>
						<ui:keyValueListItem>
							<ui:key><spring:message code="i18n.ecommerce_adminOrderDetails.eMail" /></ui:key>
							<ui:value>
								<c:if test="${not empty order.overrideEmail}">
									${order.overrideEmail}
								</c:if>
								<c:if test="${empty order.overrideEmail}">
									${order.customer.email}
								</c:if>
							</ui:value>
						</ui:keyValueListItem>
						<ui:keyValueListItem>
							<ui:key><spring:message code="i18n.ecommerce_adminOrderDetails.Telephone" /></ui:key>
							<ui:value>${order.customer.phoneNumber}</ui:value>
						</ui:keyValueListItem>
					</ui:keyValueList>
				</c:if>
			</ui:panel>

			<ui:panel classes="margin--b--m">
				<ui:panelTitle>
					<spring:message code="i18n.ecommerce_adminOrderDetails.OrderHistory" />
				</ui:panelTitle>
				
				<c:set var="orderId" value="${order.id}" />
				<c:set var="customerId" value="${order.customer.id}" />
				<c:set var="orderNumberPrefix" value="ADJUSTMENT" />
				<c:set var="orderEntityClass" value="${order.orderEntityClass}" />
				<c:set var="orderEntityId" value="${order.orderEntityId}" />
				<c:set var="showManageOrderBtn" value="false" />
				<%@ include file="/WEB-INF/spiralRobot/jsp/ecommerce/ecommerce_order_history.jsp"%>
			</ui:panel>

			<ui:panel>
				<ui:panelTitle>
					<spring:message code="i18n.ecommerce_adminOrderDetails.BOrderEmailHistoryB" />
				</ui:panelTitle>

				<%@ include file="/WEB-INF/spiralRobot/jsp/ecommerce/ecommerce_adminOrderDetails_emailLogs.jsp"%>
			</ui:panel>
		</ui:gridCol>
	</ui:grid>
</div>
