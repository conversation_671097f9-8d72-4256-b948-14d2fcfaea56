<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>

<div class="grd lp-theme__content-wrapper">
	<main class="grd__col--12 lp-theme__main">
		<div class="grd dist--around">
			<div class="grd__col--12" style="padding-top: 0px;">

				<script type="text/javascript">
					function reloadPage()
					{
						<o:nav action="displayViewAdvancedRegister" regId="${reg.id}" />
					}
				</script>
				
				<c:if test="${reg.regType.status == 1}">
					<ui:grid>
						<ui:gridCol width="12">
							<div class="orbis-posting-actions">
								<div style="text-align: center">
									<c:if test="${currentUser.portalStaff}">
										<ui:button size="small" action="displayAdvancedRegister" regId="${reg.id}">
											<orbis:message code="i18n.event_viewAdvancedRegister.EditRegist2374637729629114" />
										</ui:button>
										<c:if test="${reg.attended && not empty reg.dateCreated && empty reg.dateUpdated}">
											<ui:button size="small" action="displaySatisfactionSurvey" eventId="${reg.globalEvent.id}" userId="${reg.user.id}">
												<orbis:message code="i18n.global_registrants.CompleteSurvey" />
											</ui:button>
										</c:if>
										<c:if test="${!competenciesDisabled}">
											<c:choose>
												<c:when test="${reg.attended}">
													<ui:button size="small" action="displayAchievedCompetenciesEdit" regId="${reg.id}">
														<orbis:message code="i18n.event_viewAdvancedRegister.Competenci2725972782330998" />
													</ui:button>
											
												</c:when>
												<c:when test="${not empty reg.user}">
												 	<ui:button classes="protip" data-pt-position="top" data-pt-title="i18n.event_viewAdvancedRegister.Availableo5966269457802801">
														<orbis:message code="i18n.event_viewAdvancedRegister.Competenci2725972782330998" />
													</ui:button>
												</c:when>
											</c:choose>
										</c:if>	
									</c:if>
									<c:if test="${evAdmin}">
										<ui:button size="small" action="displayAdvRegEmailer" regId="${reg.id }" comingFrom="displayViewAdvancedRegister" emailTo="advRegistrant">
											<orbis:message code="i18n.event_viewAdvancedRegister.Email" />
										</ui:button>
									</c:if>
									<c:set var="printMessage">
										<c:choose>
											<c:when test="${ empty reg.regType.ecommerceOrder || isPaid}">
												<orbis:message code="i18n.event_viewAdvancedRegister.PrintRecei5244938359547295" />
											</c:when>
											<c:otherwise>
												<orbis:message code="i18n.event_viewAdvancedRegister.PrintInvoi2233416177316556" />
											</c:otherwise>
										</c:choose>
									</c:set>
									<ui:button size="small" action="printDetailedAdvReg" regId="${reg.id}" challenge="${challenge}">
										${printMessage}
									</ui:button>
								</div>
							</div>
						</ui:gridCol>
					</ui:grid>
				</c:if>
				
				<c:set var="detailsView" value="view"/>
				<%@ include file="event_advancedRegisterDetails.jsp"%>
				
				<c:if test="${not empty reg.regType.ecommerceOrder && comingFrom != 'publicSide'}">
					<ui:panel>
						<ui:panelTitle>
							<orbis:message code="i18n.os_orderDetails.EcommerceDetails" />
						</ui:panelTitle>
						
						<c:set var="orderEntityClass" value="${reg.regType['class'].name}" />
						<c:set var="orderEntityId" value="${reg.regType.id}" />
						<c:set var="orderId" value="${reg.regType.ecommerceOrder.id}" />
						<c:set var="orderNumberPrefix" value="${reg.globalEvent.orderNumberPrefix}"/>
						<c:set var="onOrderStatusUpdate" value="reloadPage"/>
						<c:if test="${!evAdmin}">
							<c:set var="adminMode" value="false" />
						</c:if>
						<%@ include file="/WEB-INF/spiralRobot/jsp/ecommerce/ecommerce_order_history.jsp" %>
						
					</ui:panel>
				</c:if>
			</div>
		</div>
	</main>
</div>