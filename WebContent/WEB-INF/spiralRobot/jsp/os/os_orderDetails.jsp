<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>
<c:if test="${not empty successMessage}"><ui:notification type="success">${successMessage}</ui:notification></c:if>
<c:if test="${not empty errorMessage}"><ui:notification type="error">${errorMessage}</ui:notification></c:if>

<c:if test="${not empty emailSent}">
	<ui:notification type="success">
		<orbis:message code="i18n.os_orderDetails.Emailhasbeensent" />
	</ui:notification>
</c:if>

<ui:userProfileHeader user="${currentUser}" excludeImageSection="true" overrideTitle="true">
	<ui:section key="topControls">
		<ui:navBack style="outline" type="white" customTitle="true">
			<c:if test="${empty studentStep}">
				<ui:navBackItem action="displayHome">
					<orbis:message code="i18n.os_orderDetails.BacktoHome" />
				</ui:navBackItem>
				<c:if test="${comingFrom == 'list'}">
					<ui:navBackItem action="displayOrdersList" listType="${listType}">
						<orbis:message code="i18n.os_orderDetails.BacktoOrdersList" />
					</ui:navBackItem>
				</c:if>
				<c:if test="${comingFrom == 'customer'}">
					<ui:navBackItem customerId="${customerId}" action="displayCustomer">
						<orbis:message code="i18n.os_orderDetails.BacktoCust41657158412692974" />
					</ui:navBackItem>
				</c:if>
				<c:if test="${comingFrom == 'customerAsCustomer'}">
					<ui:navBackItem action="displayCustomersOrdersList">
						<orbis:message code="i18n.os_orderDetails.BacktoMyOrders" />
					</ui:navBackItem>
				</c:if>
				<c:if test="${comingFrom == 'org'}">
					<ui:navBackItem action="displayCustomerOrg" orgId="${orgId}">
						<orbis:message code="i18n.os_orderDetails.BacktoOrganization" />
					</ui:navBackItem>
				</c:if>
			</c:if>
			<c:if test="${not empty studentStep}">
				<ui:navBackItem siteElement="${studentStep.student.termCourse.term.module.siteElementPath}" action="displayTermCourseStudent" tcsId="${studentStep.student.id}">
					<orbis:message code="i18n.os_orderDetails.Backtoorde0827295368022529" arguments="${order.customer.id==currentUser.id ? 'Your' : 'Student'}" />
				</ui:navBackItem>
			</c:if>
		</ui:navBack>
	</ui:section>
	
	<ui:section key="title">
		${module.moduleName}
	</ui:section>
</ui:userProfileHeader>

<ui:grid align="center" classes="margin--t--l">
	<ui:gridCol width="8">
		<ui:panel>
			<ui:panelTitle>
				<orbis:message code="i18n.os_orderDetails.Orderorder2822051410110803" arguments="${order.orderNumber}" />
			</ui:panelTitle>
		
			<table class="table is--list width--100">
				<col class="width--30">
				<caption class="table__caption"><orbis:message code="i18n.os_orderDetails.OrderDetai3041984109431071" /></caption>
				<tbody>
					<tr class="table__row--body">
						<td class="table__value"><orbis:message code="i18n.os_orderDetails.OrderStatus" /></td>
						<td class="table__value">
							<span class="label ${order.status==2 ? 'label-success' : 'label-important'}"><orbis:message code="${order.statusI18n}" /></span>
						</td>
					</tr>
					<tr class="table__row--body">
						<td class="table__value"><orbis:message code="i18n.os_orderDetails.DateCreated" /></td>
						<td class="table__value">
							<B><fmt:formatDate value="${order.orderDate}" pattern="${orbisDateMedium2}" /></B>
							<orbis:message code="i18n.os_orderDetails.at" /> 
							<B><fmt:formatDate value="${order.orderDate}" pattern="${orbisTimeShort2}" /></B> 
						</td>
					</tr>
					<c:if test="${not empty order.createdBy}">
						<tr class="table__row--body">
							<td class="table__value"><orbis:message code="i18n.os_orderDetails.CreatedBy" /></td>
							<td class="table__value"><B>${order.createdBy.fullNameWithUsername}</B></td>
						</tr>
					</c:if>
					<c:if test="${order.status == 3 || order.status == 4 && not empty order.cancelledDate}">
						<tr class="table__row--body">
							<td class="table__value"><orbis:message code="i18n.os_orderDetails.DateCancelled" /></td>
							<td class="table__value">
								<B><fmt:formatDate value="${order.cancelledDate}" pattern="${orbisDateMedium2}" /></B>
								<orbis:message code="i18n.os_orderDetails.at8751893523741235" /> 
								<B><fmt:formatDate value="${order.cancelledDate}" pattern="${orbisTimeShort2}" /></B> 
							</td>
						</tr>
					</c:if>
					<c:if test="${order.status == 4 && not empty order.deletedDate}">
						<tr class="table__row--body">
							<td class="table__value"><orbis:message code="i18n.os_orderDetails.DateDeleted" /></td>
							<td class="table__value">
								<B><fmt:formatDate value="${order.deletedDate}" pattern="${orbisDateMedium2}" /></B>
								<orbis:message code="i18n.os_orderDetails.at8751893523741235" /> 
								<B><fmt:formatDate value="${order.deletedDate}" pattern="${orbisTimeShort2}" /></B> 
							</td>
						</tr>
					</c:if>
					<tfoot><tr class="table__row--footer"><td class="table__value--footer" colspan="2"><orbis:message code="i18n.os_orderDetails.OrderDetai3041984109431071" /></td></tr></tfoot>
				</tbody>
			</table>			
			<c:if test="${order.status == 0 || order.status == 1}">
				<ui:note type="error">
					<orbis:message code="i18n.os_orderDetails.Thisorderh272325086618844" />
				</ui:note>
				<ui:button action="displayPlaceOrder" osOrderId="${order.id}"><orbis:message code="i18n.os_orderDetails.FinishOrder" /></ui:button>
				<ui:button type="error" action="cancelOrder" osOrderId="${order.id}"><orbis:message code="i18n.os_orderDetails.CancelOrder" /></ui:button>
			</c:if>
			<c:if test="${order.status == 3}">
				<ui:note type="error">
					<orbis:message code="i18n.os_orderDetails.Thisorderh8596645909355966" />
				</ui:note>
				<ui:button type="error" action="deleteOrder" osOrderId="${order.id}"><orbis:message code="i18n.os_orderDetails.DeleteOrder" /></ui:button>
			</c:if>
		</ui:panel>
		
		<c:if test="${order.status == 2}">
			<ui:grid>
				<ui:gridCol width="12">
					<c:if test="${!osAdmin}">
						<ui:button type="error" action="cancelOrder" osOrderId="${order.id}"><orbis:message code="i18n.os_orderDetails.CancelOrder" /></ui:button>
					</c:if>
					<c:if test="${osAdmin}">
						<ui:button type="error" show="adminCancelModal"><orbis:message code="i18n.os_orderDetails.CancelOrder" /></ui:button>
						<c:set var="itsANew2"><spring:message code="i18n.os_orderDetails.Invoice" /></c:set>
						<c:if test="${empty order.ecommerceOrder || order.ecommerceOrder.status == 2}">
							<c:set var="itsANew2"><spring:message code="i18n.os_orderDetails.Receipt" /></c:set>
						</c:if>
						<ui:button action="displayOSOrderEmailer" osOrderId="${order.id}" comingFrom="displayOrder"><orbis:message code="i18n.os_orderDetails.Emailorder49009150969764836" arguments="${itsANew2}" /></ui:button>
					</c:if>
				</ui:gridCol>
			</ui:grid>
		</c:if>
		
		<ui:panel classes="margin--t--m">
			<ui:panelTitle>
				<orbis:message code="i18n.os_orderDetails.OrderDetails" />
			</ui:panelTitle>
			
				<c:if test="${module.useDefaultReceiptHeaderFooter}">
					<c:if test="${order.ecommerceOrder.status == 2}">
						<p>${ecModule.receiptHeaderAsHtml}</p>
						<%@ include file="os_orderReviewAndReceipt.jsp" %>
						<p>${module.receiptAdditionalMessage}</p>
						<p>${ecModule.receiptFooterAsHtml}</p>
					</c:if>
					<c:if test="${order.ecommerceOrder.status != 2}">
						<p>${ecModule.invoiceHeaderAsHtml}</p>
						<%@ include file="os_orderReviewAndReceipt.jsp" %>
						<p>${module.invoiceAdditionalMessage}</p>
						<p>${ecModule.invoiceFooterAsHtml}</p>
					</c:if>
				</c:if>
				<c:if test="${!module.useDefaultReceiptHeaderFooter}">					
					<c:if test="${order.ecommerceOrder.status == 2}">
						<p>${module.moduleReceiptHeader}</p>
						<%@ include file="os_orderReviewAndReceipt.jsp" %>
						<p>${module.receiptAdditionalMessage}</p>
						<p>${module.moduleReceiptFooter}</p>
					</c:if>
					<c:if test="${order.ecommerceOrder.status != 2}">
						<p>${module.moduleInvoiceHeader}</p>
						<%@ include file="os_orderReviewAndReceipt.jsp" %>
						<p>${module.invoiceAdditionalMessage}</p>
						<p>${module.moduleInvoiceFooter}</p>
					</c:if>
				</c:if>
		</ui:panel>
		
		<script type="text/javascript">
			function reloadPage()
			{
				<o:nav action="displayOrder" osOrderId="${order.id}" />
			}
		</script>
		
		<c:if test="${not empty order.ecommerceOrder}">
			<ui:panel classes="margin--t--l">
				<ui:panelTitle>
					<orbis:message code="i18n.os_orderDetails.EcommerceDetails" />
				</ui:panelTitle>
				
					<c:set var="orderEntityClass" value="${order['class'].name}" />
					<c:set var="orderEntityId" value="${order.id}" />
					<c:set var="orderId" value="${order.ecommerceOrder.id}" />
					<c:set var="orderNumberPrefix" value="${module.orderPrefix}"/>
					<c:set var="onOrderStatusUpdate" value="reloadPage"/>
					<c:if test="${!osAdmin}">
						<c:set var="adminMode" value="false" />
					</c:if>
					<%@ include file="/WEB-INF/spiralRobot/jsp/ecommerce/ecommerce_order_history.jsp" %>
				
			</ui:panel>
		</c:if>
	</ui:gridCol>
</ui:grid>

<ui:modal id="adminCancelModal" i18n_title="Cancel Order" includeClose="true" aria-labelledby="Cancel Order">
	<ui:note i18n_title="i18n.os_orderDetails.EmailCustomer">
		<orbis:message code="i18n.os_orderDetails.Doyouwisht5493943258526356" />
	</ui:note>
	<ui:button data-dismiss="modal" action="cancelOrder" osOrderId="${order.id}" sendCancelEmail="true"  ><orbis:message code="i18n.os_orderDetails.SendEmail" /></ui:button> 
	<ui:button data-dismiss="modal" action="cancelOrder" osOrderId="${order.id}" sendCancelEmail="false" ><orbis:message code="i18n.os_orderDetails.DoNotSendEmail"/></ui:button>
</ui:modal>
