<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp" %>
<%-- <ui:isConverted type="complete" />   --%>
<ui:header>${module.moduleName}</ui:header>
<ui:actionsGroup i18n_title="i18n.common.actions" id="os_interactions" >
	<ui:actionsGroupItem action="displayPlaceOrder"><orbis:message code="i18n.os_adminHomeOverviewTab.PlaceOrderOnBehalf" /></ui:actionsGroupItem>
	<ui:actionsGroupItem action="search" subAction="search" searchType="orders"><orbis:message code="i18n.os_adminHomeOverviewTab.SearchOrders" /></ui:actionsGroupItem>
</ui:actionsGroup>


<%-- 
<div class="row-fluid">
	<div class="span12">
		<div class="orbis-posting-actions">
			<div style="text-align: center">
				<o:nav anchor="true" anchorClass="btn btn-primary btn-small" action="displayPlaceOrder"><orbis:message code="i18n.os_adminHomeConfigTab.PlaceOrderOnBehalf" /></o:nav>
				<o:nav anchor="true" anchorClass="btn btn-primary btn-small" action="search" subAction="search" searchType="orders"><orbis:message code="i18n.os_adminHomeConfigTab.SearchOrders" /></o:nav>
			</div>
		</div>
	</div>
</div> --%>

<c:set var="currentTabSelected" value="configTab" />
<%@ include file="os_adminHomeTabs.jsp"%>
<ui:grid>
	<ui:gridCol width="4">
		<ui:panel>
			<ui:panelTitle><orbis:message code="i18n.os_adminHomeConfigTab.bModuleOptionsb" /></ui:panelTitle>
			<div id="dashboard">
				<ul class="nav nav-tabs nav-stacked">
					<li>
						<o:nav anchor="true" action="displayConfigureModuleGeneral"><orbis:message code="i18n.os_adminHomeConfigTab.GeneralMod7545288338885628" /></o:nav>
					</li>
					<li>
						<o:nav anchor="true" action="displayManageProductCatalogue"><orbis:message code="i18n.os_adminHomeConfigTab.ManageProd8241370692394232" /></o:nav>
					</li>
					<li>
						<o:nav anchor="true" action="displayManageProductCategories"><orbis:message code="i18n.os_adminHomeConfigTab.ManageProd6068702237937227" /></o:nav>
					</li>
					<li>
						<o:nav anchor="true" action="displayManageEmailsAndContent"><orbis:message code="i18n.os_adminHomeConfigTab.ManageStoc6722094613467734" /></o:nav>
					</li>
					<li>
						<o:nav anchor="true" action="displayTaxOverrideTags"><orbis:message code="i18n.os_adminHomeConfigTab.ManageTaxO7059693119248658" /></o:nav>
					</li>
					<li>
						<o:nav anchor="true" action="displayAdmins"><orbis:message code="i18n.os_adminHomeConfigTab.ManageModuleAdmins" /></o:nav>
					</li>
				</ul>
			</div>
		</ui:panel>
	</ui:gridCol>
</ui:grid>