<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>

<div class="is--spiral--robot">

	<ui:userProfileHeader user="${record.owner}" classes="margin--b--l">
	
		<c:if test="${not empty headerNavigation}">
			<ui:section key="topControls">
				${headerNavigation}
			</ui:section>
		</c:if>
		
		<ui:section key="subtitle">
			<div class="width--100 text--truncate">
				${isL1 ? record.studentStep.student.termCourse.termCourseName : record.studentStep.student.termCourse.l2TermCourseName}
			</div>
			<div class="width--100 text--truncate">
				${isL1 ? record.studentStep.step.tct.type.name : record.studentStep.step.tct.type.l2Name}
			</div>
		</ui:section>

		<ui:headerTags>
			<%@ include file="/WEB-INF/spiralRobot/jsp/ccrm/ccrm_headerQuestionsItems.jsp"%>
			<%@ include file="/WEB-INF/spiralRobot/jsp/exp/exp_recordIndustryPartnerList.jsp" %>
		</ui:headerTags>
		
	</ui:userProfileHeader>
</div>

<ui:grid>
	<ui:gridCol width="8">
		<ui:panel>
			<ui:keyValueList>
				<c:if test="${!currentUser.assignedTypes['Student']}">
					<ui:keyValueListItem>
						<ui:key><orbis:message code="i18n.exp_recordHeader.StudentNam7407568910593551" /></ui:key>
						<ui:value>
							<c:if test="${isAdmin}">
								<ui:button style="plain" action="displayTermCourseStudent" tcsId="${record.studentStep.student.id}">
									<strong>${record.owner.fullName}</strong>
								</ui:button>
							</c:if>
							<c:if test = "${!isAdmin}">
								<strong>${record.owner.fullName}</strong>
							</c:if>
						</ui:value>
					</ui:keyValueListItem>
				</c:if>
				<ui:keyValueListItem>
					<ui:key><orbis:message code="i18n.exp_recordHeader.Course7128503692888966" /></ui:key>
					<ui:value>
						<c:if test="${isAdmin}">
							<ui:button style="plain" action="displayTermCourse" tcId="${record.studentStep.student.termCourse.id}">
								<strong>${isL1 ? record.studentStep.student.termCourse.termCourseName : record.studentStep.student.termCourse.l2TermCourseName}</strong>
							</ui:button>
						</c:if>
						<c:if test="${!isAdmin}">
							<strong>${isL1 ? record.studentStep.student.termCourse.termCourseName : record.studentStep.student.termCourse.l2TermCourseName}</strong>
						</c:if>
					</ui:value>
				</ui:keyValueListItem>
				<ui:keyValueListItem>
					<ui:key><orbis:message code="i18n.exp_recordHeader.Experience4973105116817224" /></ui:key>
					<ui:value>
						<c:if test="${isAdmin}">
							<ui:button style="plain" action="displayExpType" expTypeId="${record.studentStep.step.tct.type.id}">
								<strong>${isL1 ? record.studentStep.step.tct.type.name : record.studentStep.step.tct.type.l2Name}</strong>
							</ui:button>
							<strong>${(isL1 && not empty record.studentStep.step.tct.name) || (isL2 && not empty record.studentStep.step.tct.l2Name) ? ' - ' :''}${isL1 ? record.studentStep.step.tct.name : record.studentStep.step.tct.l2Name}</strong>
						</c:if>
						<c:if test="${!isAdmin}">
							<strong>${isL1 ? record.studentStep.step.tct.type.name : record.studentStep.step.tct.type.l2Name}${(isL1 && not empty record.studentStep.step.tct.name) || (isL2 && not empty record.studentStep.step.tct.l2Name) ? ' - ' :''}${isL1 ? record.studentStep.step.tct.name : record.studentStep.step.tct.l2Name}</strong>	
						</c:if>
					</ui:value>
				</ui:keyValueListItem>
				<ui:keyValueListItem>
					<ui:key><orbis:message code="i18n.exp_recordHeader.DateCreate1069822059406299" /></ui:key>
					<ui:value>
						<orbis:formatDate value="${record.dateCreated}" pattern="${orbisDateLongTimeShort}" />
						<c:if test="${ not empty record.createdBy.fullName }">
							(<orbis:message code="i18n.exp_recordHeader.Byrecordcr0957431834854569" arguments="\${record.createdBy.fullName}" />)
						</c:if>
					</ui:value>
				</ui:keyValueListItem>
				<ui:keyValueListItem>
					<ui:key><orbis:message code="i18n.exp_recordHeader.LastUpdate4121638205271635" /></ui:key>
					<ui:value>
						<c:if test="${empty record.dateUpdated}">N/A</c:if>
						<orbis:formatDate value="${record.dateUpdated}" pattern="${orbisDateLongTimeShort}" />
						<c:if test="${ not empty record.updatedBy.fullName }">
							(<orbis:message code="i18n.exp_recordHeader.Byrecordcr0957431834854569" arguments="\${record.updatedBy.fullName}" />)
						</c:if>
					</ui:value>
				</ui:keyValueListItem>
				<c:if test="${not empty record.posting}">
					<ui:keyValueListItem>
						<ui:key><orbis:message code="i18n.exp_recordHeader.Posting1994942652121448" /></ui:key>
						<ui:value>
							<ui:button style="plain" action="${isAdmin ? 'displayPosting' : 'displayRedesignPosting'}" postingId="${record.posting.id}">
								(${record.posting.id}) ${isL1 ? record.posting.postingName : record.posting.l2PostingName}
							</ui:button>
						</ui:value>
					</ui:keyValueListItem>
					<c:if test="${record.posting.type.enableAttendanceSlots && not empty attendanceSlots}">
						<ui:keyValueListItem>
							<ui:key>
								<c:if test="${record.posting.type.studentsSubmitAttendanceSlotPreferences && currentUser.id==record.owner.id}">
									<c:if test="${empty recordAttSlot}">
										<%@ include file="exp_recordAttendanceSlotPreferencesForm.jsp" %>
									</c:if>
									<c:if test="${not empty recordAttSlot}">
										<%@ include file="exp_postingAttendanceSlotVars.jsp" %>
										<orbis:message code="i18n.exp_recordHeader.YourSlot6843140420199360" /> <orbis:message code="i18n.exp_posting.attSlotatt1564831564894566" arguments="${dayOfWeek},${startTime},${endTime}" />
									</c:if>
								</c:if>
								<c:if test="${!(record.posting.type.studentsSubmitAttendanceSlotPreferences && currentUser.id==record.owner.id)}">
									<ui:select i18n_title="i18n.exp_recordHeader.Attendance2410397617680179" id="attendanceSlotSelect" name="attendanceSlotSelect">
										<ui:selectItem value=""><orbis:message code="i18n.exp_recordHeader.NoneSelect6728723111150301" /></ui:selectItem>
										<c:forEach var="attSlot" items="${attendanceSlots}">
											<%@ include file="exp_postingAttendanceSlotVars.jsp" %>
											<c:if test="${attSlot.attSlot.numStudents > attSlot.attSlot.takenSlots  || recordAttSlotId == attSlot.attSlot.id}">
												<ui:selectItem value="${attSlot.attSlot.id}" selected="${recordAttSlotId == attSlot.attSlot.id}">
													<orbis:message code="i18n.exp_recordHeader.dayOfWeekf1733544707258239" arguments="${dayOfWeek},${startTime},${endTime}" />
													<c:if test="${currentUser.id!=record.owner.id}">
														<orbis:message code="i18n.exp_recordSelectAttendanceSlot.attSlotatt0732311704535796" arguments="${attSlot.attSlot.numStudents-attSlot.attSlot.takenSlots }" />
													</c:if>
													<c:if test="${not empty attSlot.studentChoice.choiceNumber}">
														<orbis:message code="i18n.exp_recordHeader.Student9008056217667185" /><orbis:message code="i18n.exp_postingAttendanceSlots.choice.${attSlot.studentChoice.choiceNumber}" />
													</c:if>
												</ui:selectItem>
											</c:if> 
										</c:forEach>
									</ui:select>
									<ui:button size="small" id="changeSlotButton" classes="display--none">Change Attendance Slot</ui:button>
									<c:if test="${not empty recordAttSlotId}">
										<ui:button size="small" show="cancelAttendSlot"><orbis:message code="i18n.exp_recordHeader.CancelAtte2029439083245817"/></ui:button>
									</c:if>
									<ui:modal i18n_title="i18n.exp_recordHeader.CancelAtte2029439083245817" id="cancelAttendSlot" includeClose="false">
										<div>
											<orbis:message code="i18n.exp_recordHeader.ClickbCanc1222428614983849" />
											</br>
											<orbis:message code="i18n.exp_recordHeader.ClickbCanc9272307166880403" />
										</div>
										</br>
										<div>
											<ui:button hide="cancelButton" type="info" onclick="cancelModalButton();">
												<orbis:message code="i18n.exp_recordHeader.Cancel0776873042848485" />
											</ui:button>
											<c:if test="${'msu' != siteCode}">
												<ui:button hide="cancelAttendSlot" type="info" onclick="cancelSlotOnly();">
													<orbis:message code="i18n.exp_recordHeader.CancelSlot1952711339978355" />
												</ui:button>
											</c:if>
											<ui:button hide="cancelDeleteButton" type="info" onclick="cancelSlotDeletePost();">
												<orbis:message code="i18n.exp_recordHeader.CancelSlot0127113857307781" />
											</ui:button>
										</div>
									</ui:modal>
									<script type="text/javascript">
										function cancelSlotDeletePost() {
											var request = {
												action: '<o:encrypt action="cancelAttendSlotDelFromPosting"/>',
												recordId: '${record.id}',
												cancelSlot: 'on',
												attendanceSlotId: $("#attendanceSlotSelect").val()
											};
											orbisApp.buildForm(request).submit();
											$("#cancelAttendSlot").uiHide();
										};
										function cancelSlotOnly(){
											var request = {
												action: '<o:encrypt action="updateRecordAttendanceSlot"/>',
												recordId: '${record.id}',
												cancelSlot: 'on',
												attendanceSlotId: $("#attendanceSlotSelect").val()
											};
											orbisApp.buildForm(request).submit();
										};
										function cancelModalButton() {
											$("#cancelAttendSlot").uiHide();
										};

										$(document).ready(function(){
											$("#attendanceSlotSelect").change(function(){
												$("#changeSlotButton").removeClass("display--none");
											});
											
											$("#changeSlotButton").click(function(){
												var request = {
													action: '<o:encrypt action="updateRecordAttendanceSlot"/>',
													recordId: '${record.id}',
													attendanceSlotId: $("#attendanceSlotSelect").val()
												};
												if (${recordAttSlotId != 0})
												{
													var confirmMessage = ""
													if (${isAdmin})
													{
														confirmMessage = "<orbis:message code="i18n.exp_recordHeader.Areyousure5174825374866761" />";
													}
													else
													{
														confirmMessage = "<orbis:message code="i18n.exp_recordHeader.Areyousure0648183231640615" />";
													}
													orbisAppSr.confirmDialog(confirmMessage, function(){orbisAppSr.buildForm(request).submit()});
												}
												else
												{
													orbisAppSr.buildForm(request).submit();
												}
											});
										});
									</script>
								</c:if>
							</ui:key>
						</ui:keyValueListItem>
					</c:if>
				</c:if>
				<c:if test="${hourTrackingEnabled}">
					<ui:keyValueListItem>
						<ui:key><orbis:message code="i18n.exp_recordHeader.HourTracki5779699998617408" /></ui:key>
						<ui:value>
							<c:set var="remainingHours" value="${ requiredHours - totalHoursTracked >= 0 ? requiredHours - totalHoursTracked : 0 }" />
							<c:choose>
								<c:when test="${ remainingHours <= 0 && requiredHours > 0 }">
									<strong style="display:flex;align-items:center;color:green;">
										<i class="material-icons" aria-hidden="true" style="font-size:24px;line-height:1em;margin-right:4px;">done</i>
										<orbis:message code="i18n.exp_recordHeader.requiredHo2242357343360454" arguments="${ requiredHours },${ requiredHours },${ totalHoursTracked }" />
									</strong>
								</c:when>
								<c:when test="${requiredHours <= 0}">
									<strong>
										<orbis:message code="i18n.exp_recordHeader.0totalhour9413718930149217" arguments="${ totalHoursTracked }" />
									</strong>
								</c:when>
								<c:otherwise>
									<strong>
										<orbis:message code="i18n.exp_recordHeader.totalHours1920488676318085" arguments="${totalHoursTracked},${requiredHours},${ remainingHours }" />
									</strong>
								</c:otherwise>
							</c:choose>
						</ui:value>
					</ui:keyValueListItem>
				</c:if>
				<c:if test="${journalEnabled}">
					<ui:keyValueListItem>
						<ui:key><orbis:message code="i18n.exp_recordHeader.RequiredJo0623701105897409" /></ui:key>
						<ui:value><strong>${requiredJournalEntries}</strong></ui:value>
					</ui:keyValueListItem>
					<ui:keyValueListItem>
						<ui:key><orbis:message code="i18n.exp_recordHeader.TotalJourn2283399523451160" /></ui:key>
						<ui:value><strong>${totalJournalEntries}</strong></ui:value>
					</ui:keyValueListItem>
				</c:if>
				<c:if test="${record.studentStep.step.tct.type.enableFieldSupervisors && not empty fieldSupervisors}">
					<ui:keyValueListItem>
						<ui:key><orbis:message code="i18n.exp_recordHeader.FieldSuper2275385682111130" /></ui:key>
						<ui:value>
							<c:forEach var="rfs" items="${fieldSupervisors}" varStatus="status">
								${rfs.fieldSupervisor.preferredFirstName} ${rfs.fieldSupervisor.lastName}
								<c:set var="fullName">${rfs.fieldSupervisor.preferredFirstName} ${rfs.fieldSupervisor.lastName} </c:set>
								<c:if test="${moduleUser.expAdminOrExpCoordinatorOrCourseCoordinator || not empty currentUser.assignedTypes['Faculty Course Management']}">
									<o:nav confirmOnClick="i18n.exp_recordHeader.Areyousure1819350389540367" confirmOnClickArgs="${fullName }" anchor="true" anchorClass="btn btn-mini btn-danger" action="deleteFieldSupervisor" recordFieldSupervisorId="${rfs.id}">x</o:nav>
								</c:if>
								<br>
							</c:forEach>
						</ui:value>
					</ui:keyValueListItem>
				</c:if>
			</ui:keyValueList>
		</ui:panel>
	</ui:gridCol>
</ui:grid>
