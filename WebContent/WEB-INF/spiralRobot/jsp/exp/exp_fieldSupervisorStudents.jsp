<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>

<ui:navBack>
	<ui:navBackItem action="displayHome"><orbis:message code="i18n.exp_fieldSupervisorStudents.Overview6028218266906249" /></ui:navBackItem>
</ui:navBack>
<ui:header i18n_subtitle="${ siteElement.contentItem.getName(orbisLocale) }">
	<orbis:message code="i18n.exp_fieldSupervisorStudents.Students9341439311925838" />
</ui:header>
<ui:panel>
	<c:set var="gridFilters">
		<g:filterCollection queryFactory="true" />
	</c:set>
	
	<%@ include file="/WEB-INF/spiralRobotjsp/grid/grid_placeHolder.jsp"%>
</ui:panel>
