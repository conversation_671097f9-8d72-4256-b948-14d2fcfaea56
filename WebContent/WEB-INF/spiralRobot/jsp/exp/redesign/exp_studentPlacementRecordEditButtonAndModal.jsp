<%@ include file="/WEB-INF/jsp/include.jsp"%>

<c:choose>
	<c:when test="${subRecordsExist}">
		<ui:modal i18n_title="i18n.exp_recordActionBar.EditRecord8474023128419759" id="editRecordModal">

			<div class="well">
				<p>
					<i class="icon-info-sign icon-large"></i>
					<orbis:message code="i18n.exp_recordActionBar.SelectWhic4490244518288292" />
				</p>
				<ui:select id="selectEditKeys" name="selectEditKeys" >
					<c:set var="atLeastOneEditable" value="${false}"/>
					<c:if test="${canEditMainRecord}">
						<c:set var="atLeastOneEditable" value="${true}"/>
						<ui:selectItem id="editMainRecord" name="editMainRecord" value="erec"><orbis:message code="i18n.exp_recordActionBar.Experienti2427188728605722" /></ui:selectItem>
					</c:if>
					<c:forEach var="m" items="${activeRecordForms}">
						<c:if test="${!(m.key == 'facev' || m.key == 'eeval' || m.key == 'stev')}">
							<c:forEach var="subRecord" items="${editSubrecordsForms}">
								<c:if test="${m.key == subRecord.key}">
									<c:forEach var="subForm" items="${subRecord.value}">
										<c:choose>
											<c:when test ="${ m.key == 'ejrnl' }">
												<c:set var="editJournalRecordsCount" value="${(not empty editJournalRecordsCount ? editJournalRecordsCount : 0) + 1 }" />
											</c:when>
											<c:when test="${ m.key == 'etrac' }">
												<c:set var="editHourTrackingRecordsCount" value="${(not empty editHourTrackingRecordsCount ? editHourTrackingRecordsCount : 0) + 1 }" />
											</c:when>
										</c:choose>
										<c:if test="${!currentUser.student || subForm.status==3 || subForm.status==0 || (subForm.status==5 && subForm.createdBy.id == currentUser.id) || (subForm.status==1 && m.key=='etrac' && allowStudentToEditApprovedHours)}">
											<c:set var="atLeastOneEditable" value="${true}"/>
											<ui:selectItem id="editKeys" name="editKeys" value="${subRecord.key}" data-sub-record-id="${subForm.id}">
												${m.value[0]}
												<c:choose>
													<c:when test ="${ m.key == 'ejrnl' }">
														- ${ editJournalRecordsCount }
													</c:when>
													<c:when test="${ m.key == 'etrac' }">
														- ${ editHourTrackingRecordsCount }
													</c:when>
												</c:choose>
											</ui:selectItem>
										</c:if>
									</c:forEach>
								</c:if>
							</c:forEach>
						</c:if>
					</c:forEach>
				</ui:select>
			</div>

			<ui:button hide="editRecordModal" onclick="editRecord();">
				<orbis:message code="i18n.exp_recordActionBar.Select3172852702861647" />
			</ui:button>
		</ui:modal>

		<c:if test="${atLeastOneEditable}">
			<ui:actionsGroupItem show="editRecordModal">
				<orbis:message code="i18n.exp_record.Edit4882279243699492"  />
			</ui:actionsGroupItem>
		</c:if>

		<script type="text/javascript">
			function editRecord()
			{
				if ($("#editMainRecord").prop("selected"))
				{
					var request = {
						action: '<o:encrypt action="displayRecordDetailEdit"/>',
						recordId: "${record.id}",
						expModelKey:  $("#selectEditKeys :selected").val(),
					};
					orbisApp.buildForm(request).submit();
				}
				else
				{
					var request = {
						action: '<o:encrypt action="displayRecordEdit"/>',
						recordId: "${record.id}",
						expModelKey:  $("#selectEditKeys :selected").val(),
						subRecordId: $("#selectEditKeys :selected").data("subRecordId")
						<c:if test="${not empty fromProgress}">,
						fromProgress: ${fromProgress}
						</c:if>
					};
					orbisApp.buildForm(request).submit();
				}
			}
		</script>
	</c:when>
	<c:otherwise>
		<c:if test="${canEditMainRecord}">
			<ui:actionsGroupItem action="displayRecordDetailEdit" recordId="${record.id}" expModelKey="erec">
				<orbis:message code="i18n.exp_record.Edit4882279243699492"/>
			</ui:actionsGroupItem>
		</c:if>
	</c:otherwise>
</c:choose>