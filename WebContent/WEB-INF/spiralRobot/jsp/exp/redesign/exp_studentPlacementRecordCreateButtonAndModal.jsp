<%@ include file="/WEB-INF/jsp/include.jsp"%>

<c:if test="${not empty createSubRecords}">
	<ui:actionsGroupItem show="createRecordModal">
		<orbis:message code="i18n.exp_record.CreateReco2495296709706926" />
	</ui:actionsGroupItem>

	<ui:modal i18n_title="i18n.exp_recordActionBar.CreateSubr8401076222458058" id="createRecordModal">
		<div class="well">
			<p>
				<i class="icon-info-sign icon-large"></i>
				<orbis:message code="i18n.exp_recordActionBar_createRecordButton.Selectwhic6563182312454970" />
			</p>
			<ui:select id="selectCreateKeys" name="selectCreateKeys" onchange="disableSelectButtonIfRestricted()" style="width: 300px">
				<c:forEach var="m" items="${createSubRecords}">
					<c:if test="${!(m.key == 'facev' || m.key == 'eeval' || m.key == 'stev')}">
						<ui:selectItem value="${m.key}">${m.value[0]}</ui:selectItem>
					</c:if>
				</c:forEach>
			</ui:select>
		</div>

		<span id="create-record-modal-select-button-wrapper" class="d-inline-block">
			<ui:button type="defaultt" id="createRecordModalSelectButton" onclick="createRecord();" hide="createRecordModal">
				<orbis:message code="i18n.exp_termCourseStudentDetails.Submit7265760985037813"/>
			</ui:button>
		</span>
	</ui:modal>

	<script type="text/javascript">
		$(document).ready(function () {
			disableSelectButtonIfRestricted();
		});

		function disableSelectButtonIfRestricted() {
			const subRecordsCreationRestrictions = {};
			<c:forEach var="restriction" items="${subRecordsCreationRestrictionMap}">
			subRecordsCreationRestrictions['${restriction.key}'] = '<orbis:message code="${restriction.value}"/>';
			</c:forEach>
			const selectedOption = $('#selectCreateKeys').children('option:selected');
			const restrictionMessage = subRecordsCreationRestrictions[selectedOption.val()];
			if (restrictionMessage){
				$('#create-record-modal-select-button-wrapper').attr('data-pt-position', 'top');
				$('#create-record-modal-select-button-wrapper').attr('data-pt-title', restrictionMessage);
				$('#create-record-modal-select-button-wrapper').addClass('protip');
				$('#createRecordModalSelectButton').attr('disable', 'true')
						.css('pointer-events','none');
			} else {
				$('#create-record-modal-select-button-wrapper').removeClass('protip');
				$('#create-record-modal-select-button-wrapper').removeAttr('aria-describedby');
				$('#createRecordModalSelectButton').removeAttr('disable');
				$('#createRecordModalSelectButton').css('pointer-events','auto');
			}
		}

		function createRecord()
		{
			var request = {
				action: '<o:encrypt action="displayRecordEdit"/>',
				recordId: "${record.id}",
				expModelKey:  $("#selectCreateKeys :selected").val() <c:if test="${not empty fromProgress}">,
				fromProgress: ${fromProgress}</c:if>
			};

			orbisApp.buildForm(request).submit();
		}
	</script>
</c:if>