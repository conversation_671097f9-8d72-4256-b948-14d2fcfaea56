<%@ include file="/WEB-INF/jsp/include.jsp"%>
<c:set var="isAdmin" value="${moduleUser.expAdminOrExpCoordinatorOrCourseCoordinator}"/>
<c:set var="isStudent" value="${not empty currentUser.student}" />
<c:set var="isAdminOrManageFaculty" value="${moduleUser.expAdminOrCoordinator || not empty currentUser.assignedTypes['Faculty Course Management']}"/>

<style>
	.doc-viewer--page-header {
		padding: 16px 8px;
		background: #232323;
	}
	
	.items.active {
		border-width: 6px;
	}

	.posting-link {
		color: white;
		text-decoration: underline;
	}
</style>

<script type="text/javascript">
	function loadMapTab() {
		var request = {
			action: '<o:encrypt action="displayRecordMap"/>',
			recordId: ${record.id},
			r: Math.random()*100000
		};
		$("#mapTab").load("${siteElement.fullPath}.htm .tab-content", request);
	}
</script>

<%@ include file="exp_studentPlacementRecordActionBar.jsp" %>

<ui:userProfileHeader user="${record.owner}" >
	<ui:section key="topControls">
		<ui:navBack style="outline" customTitle="true" type="white" classes="border-radius--4">
			<ui:navBackItem action="displayStudentHomeCurrentExperiencesDataViewer" searchType="active"><orbis:message
					code="i18n.exp_studentPlacementRecord.Back4890163985659061"/></ui:navBackItem>
		</ui:navBack>
	</ui:section>
	
	<ui:section key="subtitle">
		<h2 class="h6 margin--t--s">${isL1 ? record.studentStep.student.termCourse.termCourseName : record.studentStep.student.termCourse.l2TermCourseName}</h2>
		<h2 class="h6 margin--t--s">${isL1 ? record.studentStep.step.tct.type.name : record.studentStep.step.tct.type.l2Name}
			<c:if test="${not empty record.posting}">
				|
				<o:nav anchorClass="posting-link" anchor="true" action="displayRedesignPosting" target="_blank" postingId="${record.posting.id}">
					${isL1 ? record.posting.postingName : record.posting.l2PostingName}
					<c:if test="${isAdmin}">
						(${record.posting.id})
					</c:if>
				</o:nav>
			</c:if>
		</h2>
		<div class="display--flex margin--t--s">
			<span class='display--flex align--middle margin--r--s'>
				<i class="material-icons font--18 margin--r--xs">event</i>
				<span class="h6 color--font--white margin--b--none">
					<orbis:formatDate value="${record.dateCreated}" pattern="${orbisDateLong}" />
				</span>
			</span>
			<span class='display--flex align--middle'>
				<i class="material-icons font--18 margin--r--xs">update</i>
				<span class="h6 color--font--white margin--b--none">
					<c:if test="${empty record.dateUpdated}">N/A</c:if>
					<orbis:formatDate value="${record.dateUpdated}" pattern="${orbisDateLong}" />
				</span>
			</span>
		</div>
		<c:if test="${isAdmin || isStudent}">
			<ui:button type="white" style="outlinePill" show="industryPartnersSideBar" classes="margin--t--m"><orbis:message code="i18n.exp_studentPlacementRecord.ViewIndust0584246869902307" /></ui:button>
		</c:if>
	</ui:section>

	<ui:headerTags>
		<%@ include file="/WEB-INF/spiralRobot/jsp/ccrm/ccrm_headerQuestionsItems.jsp"%>
		<%@ include file="/WEB-INF/spiralRobot/jsp/exp/exp_recordIndustryPartnerList.jsp"%>
	</ui:headerTags>
	
</ui:userProfileHeader>
	
<ui:tabs css="width: calc(100% + 32px);transform: translateX(-16px);background: rgba(68, 68, 68, 0.9);">
	<c:forEach var="m" items="${activeRecordForms}">
		<c:if test="${m.value[1] != 'i18n.exp_posting.NoRecord' && !(m.key == 'facev' || m.key == 'eeval' || m.key == 'stev')}">
			<ui:tab showSection="overviewTab_${m.key}" active="${expModelKey == m.key}"
					action="displayStudentPlacementRecord" recordId="${record.id}"
					expModelKey="${m.key}">
				${m.value[0]}
			</ui:tab>
		</c:if>
	</c:forEach>
	<c:if test="${siteElement.contentItem.expRecordMapEnabled}">
		<ui:tab showSection="mapTab" onclick="loadMapTab();">
			<orbis:message code="i18n.exp_studentPlacementRecord.Map9320901126973361"/>
		</ui:tab>
	</c:if>
</ui:tabs>
<c:forEach var="m" items="${activeRecordForms}">
	<c:if test="${!(m.key == 'facev' || m.key == 'eeval' || m.key == 'stev')}">
		<ui:tabSection id="overviewTab_${m.key}">
			<div class="margin--a--l">
				<div class="margin--t--m">

					<ui:grid classes='margin--t--none'>

						<c:if test="${not empty expModel}">
							<c:if test="${isAdmin}">
								<ui:gridCol width="4">
									<orbis:tagCloud cloudName="Record And Student">
										<c:if test="${o:getConfig('ACADEMIC_TAGS_ENABLED') == '1'}">
											<orbis:tagGroup cloudKey="recordAcademicCloud" groupName="Record Academic Tags"/>
										</c:if>
										<c:if test="${o:getConfig('CAMPUS_TAGS_ENABLED') == '1'}">
											<orbis:tagGroup cloudKey="recordCampusCloud" groupName="Record Campus Tags"/>
										</c:if>
										<c:if test="${o:getConfig('COMPLIANCE_TAGS_ENABLED') == '1'}">
											<orbis:tagGroup cloudKey="recordComplianceCloud" groupName="Record Compliance Tags"/>
										</c:if>
										<c:if test="${o:getConfig('DEMOGRAPHIC_TAGS_ENABLED') == '1'}">
											<orbis:tagGroup cloudKey="recordDemographicCloud" groupName="Record Demographic Tags"/>
										</c:if>
										<c:if test="${o:getConfig('INDUSTRY_TAGS_ENABLED') == '1'}">
											<orbis:tagGroup cloudKey="recordIndustryCloud" groupName="Record Industry Tags"/>
										</c:if>
										<orbis:tagGroup cloudKey="userCloud" groupName="Student Tags"/>
										<c:if test="${o:getConfig('ACADEMIC_TAGS_ENABLED') == '1'}">
											<orbis:tagGroup cloudKey="userAcademicCloud" groupName="Student Academic Tags"/>
										</c:if>
										<c:if test="${o:getConfig('CAMPUS_TAGS_ENABLED') == '1'}">
											<orbis:tagGroup cloudKey="userCampusCloud" groupName="Student Campus Tags"/>
										</c:if>
										<c:if test="${o:getConfig('COMPLIANCE_TAGS_ENABLED') == '1'}">
											<orbis:tagGroup cloudKey="userComplianceCloud" groupName="Student Compliance Tags"/>
										</c:if>
										<c:if test="${o:getConfig('DEMOGRAPHIC_TAGS_ENABLED') == '1'}">
											<orbis:tagGroup cloudKey="userDemographicCloud" groupName="Student Demographic Tags"/>
										</c:if>
										<c:if test="${o:getConfig('INDUSTRY_TAGS_ENABLED') == '1'}">
											<orbis:tagGroup cloudKey="userIndustryCloud" groupName="Student Industry Tags"/>
										</c:if>
									</orbis:tagCloud>
								</ui:gridCol>
							</c:if>

							<ui:gridCol width="8" classes=" padding--a--none">
								<c:if test="${not empty subRecordList}">
									<c:set var="subRecordListHourTrackingRecordsCount" value="0"/>
									<c:set var="subRecordListJournalRecordsCount" value="0"/>
									<c:forEach var="subRecord" items="${subRecordList}" varStatus="m">
										<c:if test="${subRecord.status!=4 || (subRecord.status==4 && isAdmin)}">
											<ui:buttonRow classes="margin--l--s" i18n_title="Actions">
												<c:if test="${'etrac'== expModelKey||('ejrnl'== expModelKey)}">
													<c:choose>
														<c:when test="${ 'ejrnl' == expModelKey }">
															<c:set var="subRecordListJournalRecordsCount"
																   value="${ subRecordListJournalRecordsCount + 1 }"/>
															<h4>${ subRecordLabelsByModelKeyMap[expModelKey] }
																- ${ subRecordListJournalRecordsCount }</h4>
														</c:when>
														<c:when test="${ 'etrac' == expModelKey }">
															<c:set var="subRecordListHourTrackingRecordsCount"
																   value="${ subRecordListHourTrackingRecordsCount + 1 }"/>
															<h4>${ subRecordLabelsByModelKeyMap[expModelKey] }
																- ${ subRecordListHourTrackingRecordsCount }</h4>
														</c:when>
													</c:choose>
													<c:if test="${subRecord.status==0}">
														<ui:tagLabel classes="info"><orbis:message code="i18n.exp.common.expRecord.status.0"/></ui:tagLabel>
													</c:if>
													<c:if test="${subRecord.status==1}">
														<ui:tagLabel classes="success"><orbis:message code="i18n.exp.common.expRecord.status.1"/></ui:tagLabel>
													</c:if>
													<c:if test="${subRecord.status==2}">
														<ui:tagLabel classes="warning"><orbis:message code="i18n.exp.common.expRecord.status.2"/></ui:tagLabel>
													</c:if>
													<c:if test="${subRecord.status==5}">
														<ui:tagLabel classes="info"><orbis:message code="i18n.exp.common.expRecord.status.5"/></ui:tagLabel>
													</c:if>
													<c:if test="${'ejrnl'== expModelKey}">
														<ui:key><orbis:message code="i18n.exp_record.DateSubmit8997993008743096"/></ui:key><ui:value><orbis:formatDate
															value="${subRecord.dateCreated}" pattern="${orbisDateShort}"/></ui:value>
													</c:if>
												</c:if>
											</ui:buttonRow>
											<ui:form>
												<c:set var="DFModel" value="${dfModelList[m.index]}"/>
												<c:set var="DFAnswerrEntity" value="${subRecord}"/>
												<%@ include file="/WEB-INF/spiralRobot/jsp/df/df_form1.jsp" %>
												<c:if test="${not empty workflowForm}">
													<c:set var="DFModel" value="${workflowForm.DFModel}"/>
													<c:set var="DFAnswerrEntity" value="${workflowAnswers}"/>
													<%@ include file="/WEB-INF/spiralRobot/jsp/df/df_form1.jsp" %>
												</c:if>
											</ui:form>
										</c:if>
									</c:forEach>
								</c:if>
								<c:if test="${empty subRecordList && !canEditForm}">
									<ui:note type="info"><orbis:message code="i18n.exp_record.Noinformat1008683683477236" /></ui:note>
								</c:if>
							</ui:gridCol>
						</c:if>

						<c:if test="${empty expModel}">
							<ui:note type="info"><orbis:message code="i18n.exp_record.Youcannotv4301584010484310" /></ui:note>
						</c:if>
					</ui:grid>
				</div>
			</div>
		</ui:tabSection>
	</c:if>
</c:forEach>

<ui:tabSection id="mapTab">
<%--	Load by ajax, see js loadMapTab()--%>
	Loading...
</ui:tabSection>

<ui:sidebar id="industryPartnersSideBar" i18n_title="Industry Partners">
	<c:choose>
		<c:when test="${not empty industryPartners}">
			<ui:surveyCards>
				<c:forEach var="industryPartner" items="${industryPartners}">
					<c:if test="${isAdmin}">
						<ui:surveyCard icon="domain" i18n_title="${industryPartner.org.name}" siteElement="${acrmAdminSe}" action="displayOrganization" organizationId="${industryPartner.org.id}">
							<div class="display--flex flex--column">
								<o:nav anchor="true" siteElement="${acrmAdminSe}" action="displayDivision" divisionId="${industryPartner['div'].id}">
									${industryPartner['div'].name}
								</o:nav>
								<o:nav anchor="true" siteElement="${acrmAdminSe}" action="displayContactOverview" acrmUserId="${industryPartner.user.id}">
									${industryPartner.user.username}
								</o:nav>
							</div>
						</ui:surveyCard>
					</c:if>
					<c:if test="${isStudent}">
						<ui:surveyCard icon="domain" i18n_title="${industryPartner.org.name}">
							<div class="display--flex flex--column">
								<span>${industryPartner['div'].name}</span>
								<span>${industryPartner.user.username}</span>
							</div>
						</ui:surveyCard>
					</c:if>
				</c:forEach>
			</ui:surveyCards>
		</c:when>
		<c:otherwise>
			<h3 class="text-align--center">
				<orbis:message code="i18n.exp_recordIndustryPartners.Thisrecord5956625382863269"/>
			</h3>
		</c:otherwise>
	</c:choose>
</ui:sidebar>

<%@ include file="/WEB-INF/spiralRobot/jsp/exp/exp_studentStepInstructionsModal.jsp" %>
