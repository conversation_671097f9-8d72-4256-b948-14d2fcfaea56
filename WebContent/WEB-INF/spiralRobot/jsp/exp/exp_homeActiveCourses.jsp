<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted type="complete" />   --%>

<div class="dashboard-header position--relative padding--a--m display--flex align--middle ui--user-profile-header" style="flex-wrap: wrap !important; min-height: auto;">
	<div class="padding--l--m">
		<div class="padding--b--l display--flex width--100 z-index--1">
			<ui:navBack type="white" style="outline">
				<ui:navBackItem action="displayHome">
					<orbis:message code="i18n.common.backToHome" />
				</ui:navBackItem>
			</ui:navBack>
		</div>

		<h1 class="margin--b--s color--font--white">
			${isL1 ? siteElement.contentItem.name : siteElement.contentItem.l2Name} - <orbis:message code="i18n.exp_homeActiveCourses.ActiveCour2334125183776262" />
		</h1>
	</div>
</div>

<%@ include file="exp_homeActiveCourses_docViewerCard.vue.jsp"%>
<%@ include file="exp_homeActiveCourses_docViewerDocument.vue.jsp"%>

<script type="text/javascript">
    var dataViewerActions = [

    ];

    var dataViewerPropsOverrides = {
        enableDocViewer: true,
        enableTable: true,
        docViewerDocument,
        docViewerCard,
        displayMode: 'docViewer'
    };
</script>

<%@ include file="/WEB-INF/jsp/dataViewer/dataViewer_placeholder.jsp"%>
