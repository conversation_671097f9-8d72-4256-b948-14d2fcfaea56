<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<orbis:addComponent component="vuedraggable"/>
<orbis:addComponent component="lodash" />

<%@ include file="exp_homeActiveCourses_resourceLibraryItem.vue.jsp"%>
<%@ include file="exp_homeActiveCourses_coordinatorCard.vue.jsp"%>
<%@ include file="exp_homeActiveCourses_facultyAssignedSidebar.vue.jsp"%>
<%@ include file="exp_homeActiveCourses_termCoursePlacementMapReport.jsp"%>
<%@ include file="exp_homeActiveCourses_stepCard.vue.jsp"%>
<%@ include file="/WEB-INF/jsp/ckeditor.vue.jsp"%>

<template id="docViewerDocument">
	<div class="dashboard-header--mini color--bg--default padding--a--l">
		<div class="dashboard-header--mini__content wrap">
			<div class="display--flex dashboard-header--mini__content--left flex--column">
				<div class="display--flex align--middle margin--b--m">
					<ui-button
						class="width--32 height--32 align--center border-radius--16 margin--r--s padding--a--none"
						size="small"
						btn-style="outlinePill"
						type="white"
						@click="showResourceLibrarySidebar = !showResourceLibrarySidebar"
					>
						<i class="material-icons">insert_drive_file</i>
					</ui-button>
					<h1 class="color--font--white margin--b--none">{{courseData.courseLabel}}</h1>
				</div>
				<div class="display--flex wrap gap--8">
					<ui:tagLabel type="defaultt" size="small" pill="true" classes="display--flex align--middle margin--b--none padd border-radius--12 height--24 font--11">
						<i class="material-icons font--11 margin--r--xs color--font--success">circle</i>
						{{termCourseStatusLabel}}
					</ui:tagLabel>
					<ui:tagLabel type="defaultt" size="small" pill="true" classes="display--flex align--middle margin--b--none border-radius--12 height--24 font--11">
						<i class="material-icons font--11 margin--r--xs color--font--default">circle</i>
						{{courseData.termName}}
					</ui:tagLabel>
					<ui-button
						class="border-radius--12 height--24"
						size="small"
						btn-style="outlinePill"
						type="warning"
						@click="openPendingStudents(row.id)"
					>
						<i class="material-icons font--12 margin--r--xs">pending</i>
						{{courseData.pendingStudentsCount}} <orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.PendingStu2731242970778886" />
					</ui-button>
					<ui-button
						class="border-radius--12 height--24"
						size="small"
						btn-style="outlinePill"
						type="white"
						@click="openStudents(row.id)"
					>
						<i class="material-icons font--12 margin--r--xs">face</i>
						{{courseData.studentsCount}} <orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Students2282652955107464" />
					</ui-button>
					<ui-button
						class="border-radius--12 height--24"
						size="small"
						btn-style="outlinePill"
						type="white"
						@click="showCoordinatorsSidebar = !showCoordinatorsSidebar"
					>
						<i class="material-icons font--12 margin--r--xs">group</i>
						{{courseData.coordinatorsCount}} <orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.coordinato6938400881254832" />
					</ui-button>
					<ui-button
						class="border-radius--12 height--24"
						size="small"
						btn-style="outlinePill"
						type="white"
						@click="showFacultiesSidebar = !showFacultiesSidebar"
					>
						<i class="material-icons font--12 margin--r--xs">face</i>
						{{courseData.facultyCount}} <orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.FacultyAss2314939249020602" />
					</ui-button>
				</div>
			</div>
		</div>
	</div>

	<ui-credits-rail dark chip>
		<template #inner-item>
			<ui-button btn-style="pill" class="height--32 align--center border-radius--16" @click="displayTermCourseTags">
				<i class="material-icons">settings</i>
			</ui-button>
		</template>
		<ui-credits-rail-item
				v-for="tag in tagsAssigned"
				:key="tag"
				:title="tag"
		></ui-credits-rail-item>
	</ui-credits-rail>

	<ui-tabs dark>
		<template #tabs>
			<ui-tab id="overview">
				<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Overview9492679647133164" />
			</ui-tab>
			<ui-tab id="reports" @click="initReports">
				<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Reports4083488189829284" />
			</ui-tab>
		</template>
		<div class="padding--a--l color--bg--grey--lightest">
			<ui-tab-section tab-id="overview">
				<h2 class="display--block">
					<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Experience3206304926103716" />
				</h2>

				<div
					v-for="(type, index) in termCourseTypes"
					:key="type.tct.type.name"
					class="padding--b--xxxl"
					:class="{'padding--t--xxxl': index !== 0}"
				>
					<div class="display--flex space--between align--top wrap margin--b--m">
						<span class="margin--r--l">{{type.tct.type.name}}</span>
						<div class="display--flex wrap gap--8">
							<ui-button class="height--24" size="small" btn-style="outlinePill">
								<i class="material-icons font--12 margin--r--xs">face</i>
								{{experienceStatusCounts[type.tct.id].inProgress}} <orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.inprogress1861272047480866" />
							</ui-button>
							<ui-button class="height--24" size="small" btn-style="outlinePill">
								<i class="material-icons font--12 margin--r--xs">group</i>
								{{experienceStatusCounts[type.tct.id].inactive}} <orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.inactive5931204567418357" />
							</ui-button>
							<ui-button class="margin--r--s height--24" size="small" btn-style="outlinePill">
								<i class="material-icons font--12 margin--r--xs">face</i>
								{{experienceStatusCounts[type.tct.id].completed}}% <orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.completed4995986221416777" />
							</ui-button>
							<ui-button class="height--24" size="small" btn-style="pill" type="info" @click="loadWorkflowSidebar(type.tct.id, type.tct.type.name)">
								<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Editworkfl4649424828489036" />
							</ui-button>
							<ui-dropdown :class-map="{ 'button': 'height--24'}" title="Place Students" size="small" type="info" pill>
								<ui-dropdown-item @click="createRecords(false, type.tct.id)">
									<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.CreatePlac8977959499161887" />
								</ui-dropdown-item>
								<ui-dropdown-item @click="createRecords(true, type.tct.id)">
									<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.PlaceStude3314066381533966" />
								</ui-dropdown-item>
								<ui-dropdown-item @click="placeStudentInOpportunity(type.tct.id)">
									<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.PlaceStude2750830477020339" />
								</ui-dropdown-item>
							</ui-dropdown>
						</div>
					</div>
					<div class="margin--b--m">
						<div class="display--flex wrap margin--b--l active-courses-insights">
							<ui-insights-card-small
								:value="courseData.statCards[type.tct.id].recordsCount"
								outlined
								@click="openPlacementRecords(row.id, type.tct.id)"
							>
								<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.PlacementR8855725834925518" />
							</ui-insights-card-small>
							<ui-insights-card-small
								:value="courseData.statCards[type.tct.id].notPlacedCount"
								outlined
								@click="openUnplacedStudents(type.tct.id)"
							>
								<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Unplaced1024983177892313" />
							</ui-insights-card-small>
							<ui-insights-card-small
								:value="courseData.statCards[type.tct.id].postingsCount"
								outlined
								@click="openPostings(row.id, type.tct.id)"
							>
								<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Postings3174428430441683" />
							</ui-insights-card-small>
							<ui-insights-card-small
								:value="courseData.statCards[type.tct.id].notFilledCount"
								outlined
								@click="openPostings(row.id, type.tct.id, 'notFilled')"
							>
								<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Unfilled5153963524261339" />
							</ui-insights-card-small>
							<ui-insights-card-small
								:value="courseData.statCards[type.tct.id].partialFilledCount"
								outlined
								@click="openPostings(row.id, type.tct.id, 'partFilled')"
							>
								<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.PartiallyF5521890266584712" />
							</ui-insights-card-small>
							<ui-insights-card-small
								:value="courseData.statCards[type.tct.id].rankingsCount"
								outlined
								@click="openRankings(type.tct.id)"
							>
								<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Rankings4800310751890036" />
							</ui-insights-card-small>
						</div>
					</div>

					<ui-icon-list-cards>
						<ui-step-card
							v-for="step in stepsByType[type.tct.id]"
							:key="step.id"
							:step="step"
							:step-type-label="stepTypeLabels[step.type]"
							@remind-students="remindStudents(step)"
							@open-students-progress="openStudentsProgressStep(step.id, type.tct.id)"
							@open-questionnaires-search="openQuestionnairesSearch(step.id)"
							@started-click="startedClick(step)"
							@pending-click="pendingClick(step)"
							@declined-click="declinedClick(step)"
						></ui-step-card>
					</ui-icon-list-cards>
				</div>

			</ui-tab-section>
			<ui-tab-section tab-id="reports">
				<div class="margin--b--m display--flex wrap gap--8 space--between">
					<ui-dropdown :title="selectedReportType.reportLabel" outline>
						<ui-dropdown-item
								v-for="plugin in reportsData.reportPluginsList"
								:key="plugin.reportKey"
								@click="selectReportType(plugin)">
							{{plugin.reportLabel}}
						</ui-dropdown-item>
					</ui-dropdown>
					<div v-if="selectedReportType.reportKey === 'termCourseCompetencyDevelopmentReport'" class="display--flex">
						<button type="button" class="btn__default btn--info display--inline-flex align--middle margin--r--s"
						        @click="openCompetencyReportView(selectedReportType)">
							<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.ReportView5723264665428115" />
						</button>
						<button type="button" class="btn__default btn--info display--inline-flex align--middle margin--r--s"
						        @click="submitFilterFormAndOpenCompetencyDataView">
							<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.DataView9512025767841231" />
						</button>
						<button type="button" class="btn__default btn--info display--inline-flex align--middle"
						        @click="openCompetencyFilterView(row.id)">
							<i class="material-icons font--14">filter_list</i>
						</button>
					</div>
				</div>
				<template v-if="isReportDataNotEmpty && selectedReportType.reportKey === 'summaryReport'">
					<div class="display--flex reports-wrapper">
						<div class="grow--1">
							<div id="reportsSummary" class="muuri-container">
								<div class="item">
									<div class="item-content margin--a--s status-chart insight-card card--wide--banner shadow--box--none">
										<ui-donut-chart v-if="studentsByStatusChartData.length"
										                :chart-data="studentsByStatusChartData"
										                title="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Studentsby9838125761478957" />"></ui-donut-chart>
									</div>
								</div>
								<div class="item">
									<ui-insights-card class="item-content margin--a--s" type="wide"
									                  :value="reportsData.studentCount">
										<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.TotalStude7403622642158269" />
									</ui-insights-card>
								</div>
								<div class="item">
									<ui-insights-card class="item-content margin--a--s" type="wide"
									                  :value="reportsData.totalTrackedHours">
										<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.TotalHours9738153041826527" />
									</ui-insights-card>
								</div>
								<div class="item">
									<div class="item-content margin--a--s experiences insight-card card--wide--banner padding--l--s padding--r--s shadow--box--none">
										<ui-bar-chart v-if="experiencesByProgressChartData.length"
										              :chart-data="experiencesByProgressChartData"
										              title="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Experience0546162409155763" />"></ui-bar-chart>
									</div>
								</div>
								<div class="item">
									<ui-insights-card class="item-content margin--a--s"
									                 :value="reportsData.recordCount">
										<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Placements8961640576426642" />
									</ui-insights-card>
								</div>
								<div class="item">
									<ui-insights-card class="item-content margin--a--s"
									                 :value="reportCompletionPercentage + '%'">
										<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Completion1291625376094216" />
									</ui-insights-card>
								</div>
								<div class="item">
									<ui-insights-card class="item-content margin--a--s"
									                 :value="reportsData.partnersCount">
										<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Industrypa3103660418742687" />
									</ui-insights-card>
								</div>
								<div class="item">
									<ui-insights-card class="item-content margin--a--s"
									                 :value="reportPlacementPercentage + '%'">
										<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Placements6562983453501265" />
									</ui-insights-card>
								</div>
							</div>
						</div>
						<div v-if="topIndustryPartners.length" class="top-partners">
							<h3 class="margin--b--m"><orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.TopIndustr1977116175836172" /></h3>
							<ui-icon-list-cards>
								<ui-icon-list-card
										v-for="partner in topIndustryPartners"
										:key="partner.id"
										icon="person"
										:title="partner.name"
										:subtitle="partner.isOrg ? 'Organization' : 'User'">
							<span class="text--small">
								{{partner.count}} <orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Students0897000159475235" />
							</span>
								</ui-icon-list-card>
							</ui-icon-list-cards>
						</div>
					</div>
				</template>
				<template v-if="selectedReportType.reportKey === 'termCoursePlacementMapReport'">
					<placement-map :tc-id="row.id"></placement-map>
				</template>
				<template v-if="selectedReportType.reportKey === 'termCourseSingleTypeRecordsReport' ||
								selectedReportType.reportKey === 'termCourseMultiTypeRecordsReport'">
					<h3 class="margin--b--m">
						<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.ReportView9176136344057437" />
					</h3>
					<div class="term-course-placements">
						<div class="insight-card padding--l--s padding--r--s shadow--box--none height--100"
						     v-if="recordsByIndustryTag.length">
							<ui-bar-chart :chart-data="recordsByIndustryTag"
							              title="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.RecordsbyI2338967046833827" />"></ui-bar-chart>
						</div>
						<div class="insight-card padding--l--s padding--r--s shadow--box--none height--100"
						     v-if="recordsByDemographicTag.length">
							<ui-bar-chart :chart-data="recordsByDemographicTag"
							              title="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.RecordsbyD6473435206114213" />"></ui-bar-chart>
						</div>
						<ui-insights-card :value="reportsData.totalTrackedHours" type="wide">
							<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.TotalTrack2118361105931080" />
						</ui-insights-card>
					</div>
				</template>
				<template v-if="selectedReportType.reportKey === 'termCourseCompetencyDevelopmentReport'">
					<div v-show="showCompetenciesContainer" v-for="(expStep, expType) in competenciesReportData">
						<ui-group-list v-for="(competenciesGroup, step) in expStep" :key="step" :title="typeName(expType)" :subtitle="stepName(step)">

								<template v-if="competenciesGroup.root">
									<ui-competency-selection-card
											v-for="competency in competenciesGroup.root"
											:key="competency.name"
											:title="competency.name">
										<template #footer>
											<span class="display--flex align--middle font--bold font--10 margin--l--s margin--r--s">
												{{competency.amount}} <orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Students7490709740728058" />
											</span>
										</template>
									</ui-competency-selection-card>
								</template>

								<template #bottom>
									<ui-group-list-subgroup v-for="(competencies, group) in competencyGroupWithoutRoot(competenciesGroup)"
											:title="formatCompetenciesSubgroups(group)">

										<ui-competency-selection-card
												v-for="competency in competencies"
												:key="competency.name"
												:title="competency.name">
											<template #footer>
												<span class="display--flex align--middle font--bold font--10 margin--l--s margin--r--s">
													{{competency.amount}} <orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Students7490709740728058" />
												</span>
											</template>
										</ui-competency-selection-card>

									</ui-group-list-subgroup>
								</template>

							</ui-group-list>
					</div>
					<div id="filterView" v-show="showCompetenciesFilterView" v-html="filterForm"></div>
				</template>
			</ui-tab-section>
		</div>
	</ui-tabs>

	<ui-sidebar v-model="showActionsSidebar" :title="Actions"
				remove-content-padding>
		<div class="margin--l--s display--flex flex--column">
			<c:set var="isAdmin" value="${moduleUser.expAdminOrExpCoordinatorOrCourseCoordinator}"/>
			<c:if test="${isAdmin || not empty currentUser.assignedTypes['Faculty Course Management']}">
				<ui-dropdown title="Actions" size="big" plain pill type="info" class="padding--a--xs">
						<c:if test="${isAdmin}">
							<ui-dropdown-item @click="displayTermCourseConfig">
								<orbis:message code="i18n.exp_termCourseActions.EditConten4667828142009575" />
							</ui-dropdown-item>
							<ui-dropdown-item @click="showCourseStatusModal = true">
								<orbis:message code="i18n.exp_termCourseActions.ChangeStat7490844696834799" />
							</ui-dropdown-item>
							<ui-dropdown-item @click="displayTermCourseExperienceTypesConfig">
								<orbis:message code="i18n.exp_termCourseActions.ManageExpe5568416195323823" />
							</ui-dropdown-item>
							<c:if test="${pendingStudents > 0}">
								<ui-dropdown-item @click="approveTermCoursePendingStudents">
									<orbis:message code="i18n.exp_termCourseActions.ApprovePen3240919496916174" />
								</ui-dropdown-item>
							</c:if>
							<ui-dropdown-item @click="displayEnrollTermCourseStudents">
								<orbis:message code="i18n.exp_termCourseActions.EnrollNewS0784874685661524" />
							</ui-dropdown-item>
							<ui-dropdown-item @click="displayNewPostingWizard">
								<orbis:message code="i18n.exp_termCourseActions.AddAPosting" />
							</ui-dropdown-item>
							<ui-dropdown-item @click="displayEmailTermCourseStudents">
								<orbis:message code="i18n.exp_termCourseActions.EmailEnrol7416520781513926" />
							</ui-dropdown-item>
							<ui-dropdown-item @click="showAddFacultyModal = true">
								<orbis:message code="i18n.exp_termCourseActions.AssignaFac4111633495218508" />
							</ui-dropdown-item>
							<ui-dropdown-item @click="showAddTermCourseCoordinator = true">
								<orbis:message code="i18n.exp_termCourseActions.AssignaMan5670566891188169" />
							</ui-dropdown-item>
							<ui-dropdown-item @click="displayTermCourseResourceEdit">
								<orbis:message code="i18n.exp_termCourseActions.AddaResour0377977508717083" />
							</ui-dropdown-item>
							<ui-dropdown-item @click="displayTermCourseAccess">
								<orbis:message code="i18n.exp_termCourseActions.ConfigureA2801516845998324" />
							</ui-dropdown-item>
							<ui-dropdown-item @click="displayCloneTermCourse">
								<orbis:message code="i18n.exp_termCourseActions.Clone9767880041540337" />
							</ui-dropdown-item>
							<ui-dropdown-item @click="showDeleteModal = true" v-if="courseData.studentsCount == 0">
								<orbis:message code="i18n.exp_termCourseActions.Delete6880532887688352" />
							</ui-dropdown-item>
							<ui-dropdown-item disabled="true" v-if="courseData.studentsCount != 0">
								<orbis:message code="i18n.exp_termCourseActions.Delete6880532887688352" />
							</ui-dropdown-item>
						</c:if>
					</ui-dropdown>
			</c:if>

			<c:if test="${isAdmin && (not empty ica_noteVisibility['EXPTERMCOURSE'] || not empty ica_taskVisibility['EXPTERMCOURSE'] || not empty ica_formVisibility['EXPTERMCOURSE'])}">
				<c:set var="showCreateTaskListItem" value="${interactionModule.tasksEnabled && not empty ica_taskVisibility['EXPTERMCOURSE']}" />
				<c:set var="showAddNoteListItem" value="${interactionModule.notesEnabled && not empty ica_noteVisibility['EXPTERMCOURSE']}" />
				<c:set var="showSubmitFormListItem" value="${interactionModule.formsEnabled && not empty ica_formVisibility['EXPTERMCOURSE']}" />
				<c:set var="showSendMessageListItem" value="${interactionModule.messagesEnabled && ica_showMessageBtn && not empty ica_messageVisibility}" />
				<c:if test="${showCreateTaskListItem}">
					<ui-button class="border-radius--4 progress__bar__btn" type="info" @click="displayInteractionTaskEdit">
						<orbis:message code="i18n.interaction_commonActions.CreateATask" />
					</ui-button>
				</c:if>
				<c:if test="${showAddNoteListItem}">
					<ui-button  class="border-radius--4 progress__bar__btn" type="info" btn-style="plainPill" @click="displayInteractionNoteEdit">
						<orbis:message code="i18n.interaction_commonActions.AddANote" />
					</ui-button>
				</c:if>
				<c:if test="${showSubmitFormListItem}">
					<ui-button  class="border-radius--4 progress__bar__btn" type="info" btn-style="plainPill" @click="displayInteractionFormEdit">
						<orbis:message code="i18n.interaction_commonActions.SubmitAForm" />
					</ui-button>
				</c:if>

			</c:if>

			<c:if test="${isAdmin}">
				<ui-dropdown v-if="questionnaireCount" class="padding--a--xs" title="Search" type="info" size="big" plain pill>
					<ui-dropdown-item @click="displaySearch('termCourseStudents')">
						<orbis:message code="i18n.exp_termCourseActions.Searchstud0596179801911888" />
					</ui-dropdown-item>
					<ui-dropdown-item v-if="termCourseTypes.length > 1" @click="showSearchOpportunitiesModal = true">
						<orbis:message code="i18n.exp_termCourseActions.SearchOppo7587944303807580" />
					</ui-dropdown-item>
					<ui-dropdown-item v-if="termCourseTypes.length === 1" @click="displaySearchOpportunities">
						<orbis:message code="i18n.exp_termCourseActions.SearchOppo7587944303807580" />
					</ui-dropdown-item>
					<ui-dropdown-item @click="checkExpTypes()">
						<orbis:message code="i18n.exp_termCourseActions.SearchReco5233215669724451" />
					</ui-dropdown-item>
					<c:if test="${(isAdmin || not empty currentUser.assignedTypes['Experiential - Faculty View Access'] || not empty currentUser.assignedTypes['Faculty Course Management'])}">
						<ui-dropdown-item @click="displaySelectQuestionnaireSearch">
							<orbis:message code="i18n.exp_termCourseActions.SearchQues6653835893281833" />
						</ui-dropdown-item>
					</c:if>
				</ui-dropdown>
				<ui-dropdown v-if="questionnaireCount === 0" title="Search" type="info" size="big" plain pill>
					<ui-dropdown-item @click="displaySearch('termCourseStudents')">
						<orbis:message code="i18n.exp_termCourseActions.Searchstud0596179801911888" />
					</ui-dropdown-item>
					<ui-dropdown-item v-if="termCourseTypes.length > 1" @click="showSearchOpportunitiesModal = true">
						<orbis:message code="i18n.exp_termCourseActions.SearchOppo7587944303807580" />
					</ui-dropdown-item>
					<ui-dropdown-item v-if="termCourseTypes.length === 1" @click="displaySearchOpportunities">
						<orbis:message code="i18n.exp_termCourseActions.SearchOppo7587944303807580" />
					</ui-dropdown-item>
					<ui-dropdown-item @click="checkExpTypes()">
						<orbis:message code="i18n.exp_termCourseActions.SearchReco5233215669724451" />
					</ui-dropdown-item>
				</ui-dropdown>
			</c:if>

			<ui-button class="border-radius--4 progress__bar__btn" type="info" btn-style="plainPill" @click="showCourseProgressSidebar = true">
				<orbis:message code="i18n.exp_termCourseActions.Checkup0660145074850475" />
			</ui-button>
		</div>
	</ui-sidebar>

	<ui-sidebar
		v-model="showWorkflowSidebar"
		:title="workflowSidebarTitle"
		subtitle="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Workflow7567622313326295" />"
		remove-content-padding
	>
		<div class="sidebar--action__hero">
			<div class="padding--l--m padding--r--m padding--t--l padding--b--l display--flex align--middle" style="height: 144px">
				<span class="tag-label info text--large is--box display--flex">
					<span class="margin--r--m">
						Steps
					</span>
					<span class="tag-label color--bg--dark">
						{{workflowSteps.length}}
					</span>
				</span>
			</div>
		</div>
		<ui-tabs>
			<template #tabs>
				<ui-tab id="steps"><orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.WorkflowSt9850244125172580" /></ui-tab>
				<ui-tab v-if="workflowTemplate" id="options"><orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.WorkflowOp2832133867388079" /></ui-tab>
			</template>
			<ui-tab-section tab-id="steps" class="steps-tab grow--1 display--flex flex--column color--bg--white">
                <draggable
					:list="workflowSteps"
					tag="ui-icon-list-cards"
					id="workflowSteps"
					class="grow--1 steps-cards-list"
					animation="250"
					handle=".icon-reorder"
					force-fallback
				>
					<template #item="{element: step}">
						<ui-icon-list-card
							:icon="step.locked ? 'lock' : 'work'"
							:id="step.id"
							:title="step.label"
							:subtitle="step.optional ? 'Optional' : 'Required'"
							:display-margin-bottom="false"
						>
							<template v-if="isEdited" #left-section>
									<button v-if="isEdited" class="icon-reorder workflow-steps-reorder-btn">
										<i class="material-icons font--20">drag_handle</i>
									</button>
							</template>
							<span class="text--small">
								{{stepTypeLabels[step.type]}}
							</span>
							<div class="display--flex">
								<ui-button size="small" type="info" class=" margin--r--s" @click="showStepConfig(step)">
									<i class="material-icons font--20">settings</i>
								</ui-button>
								<ui-button size="small" type="info" :disabled="step.type === 4" @click="deleteStepModalDisplay(step)">
									<i class="material-icons font--20">delete</i>
								</ui-button>
							</div>
						</ui-icon-list-card>
					</template>
				</draggable>
				<%--TODO: Open dropdown to top--%>
				<div class="steps-actions display--flex">
					<template v-if="isEdited">
						<button class="btn__hero--text btn--default border-radius--0 grow--1 align--center" @click="toggleEditStepOrder">
							<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.SaveStepOr8610576163073729" />
						</button>
						<button
							class="btn__hero--text btn--info border-radius--0 drop-down__btn display--flex align--middle"
							ref="cancelEditStepOrderBtn"
							@click="cancelEditStepOrder"
						>
							<i class="material-icons">close</i>
						</button>
						<ui-tooltip v-if="$refs.cancelEditStepOrderBtn" :element="$refs.cancelEditStepOrderBtn" teleport-to-body :style-map="{'tooltip': 'z-index: 3;'}">
							<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.CancelEdit5905523913042690" />
						</ui-tooltip>
					</template>
					<template v-else>
						<ui-dropdown
							title="Add"
							class="grow--1"
							size="large"
							:class-map="{button: 'border-radius--0 width--100 align--center'}"
						>
							<ui-dropdown-item v-for="(value, key) in stepsToAdd" @click="addStep(key)">
								{{value}}
							</ui-dropdown-item>
						</ui-dropdown>
						<ui-dropdown
							class="border-radius--0 steps-actions-menu"
							:class-map="{button: 'border-radius--0'}"
							title="settings"
							size="large"
							type="info"
							is-icon-dropdown
						>
							<ui-dropdown-item @click="toggleEditStepOrder">
								<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.EditOrder3236131910450042" />
							</ui-dropdown-item>
							<ui-dropdown-item v-show="workflowTemplates" @click="showWorkflowTemplateModal = true">
								<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.ChangeWork4401293208083388" />
							</ui-dropdown-item>
						</ui-dropdown>
					</template>
				</div>
			</ui-tab-section>
			<ui-tab-section v-show="workflowTemplate" tab-id="options" class="steps-tab grow--1 display--flex flex--column color--bg--white">
				<div class="grow--1 steps-cards-list padding--l--m padding--r--m">
					<div class="input__group">
						<label class="label" for="name">
							<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.TemplateNa4342109065369836" />
						</label>
						<div class="display--flex align--center">
							<input
								class="input--box display--block"
								data-rule-maxlength="255"
								type="text"
								name="name"
								id="name"
								v-model="workflowTemplate.name"
							>
						</div>
					</div>
					<ui-checkbox
						v-model="workflowTemplate.allowRepeatExperiences"
						class="margin--b--l"
						type="toggle"
						label-style="help"
						column
						title="<orbis:message code="i18n.exp_expTypeWorkflowTemplateEdit.AllowRepea0405243231117114" />"
						label="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Allowstude0407627937926432" />"
					></ui-checkbox>
					<ui-checkbox
						v-model="workflowTemplate.allowStudentRepititions"
						:disabled="!workflowTemplate.allowRepeatExperiences"
						class="margin--b--l"
						type="toggle"
						label-style="help"
						column
						title="<orbis:message code="i18n.exp_expTypeWorkflowTemplateEdit.Allowstude5803471227000195" />"
						label="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Allowstude1943696465021220" />"
					></ui-checkbox>
					<ui-checkbox
						v-model="workflowTemplate.defaultTemplate"
						class="margin--b--l"
						type="toggle"
						label-style="help"
						column
						title="<orbis:message code="i18n.exp_expTypeWorkflowTemplateEdit.DefaultTemplate12850585913296" />"
						label="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Makethiste6782737743561713" />"
					></ui-checkbox>
					<ui-checkbox
						v-model="workflowTemplate.integrationCreateRecords"
						class="margin--b--l"
						type="toggle"
						label-style="help"
						column
						title="<orbis:message code="i18n.exp_expTypeWorkflowTemplateEdit.DontCreate12850585913296" />"
						label="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Integratio3532504635842290" />"
					></ui-checkbox>

					<template v-if="expType.enablePostings">
						<ui-checkbox
							v-model="workflowTemplate.studentRankingsEnabled"
							class="margin--b--l"
							type="toggle"
							label-style="help"
							column
							title="<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.EnableStud7530516131275511" />"
						></ui-checkbox>
						<ui-checkbox
							v-model="workflowTemplate.sequentialRankingsRequired"
							:disabled="!workflowTemplate.studentRankingsEnabled"
							class="margin--b--l"
							type="toggle"
							label-style="help"
							column
							title="<orbis:message code="i18n.exp_expTypeWorkflowTemplateEdit.RequireSeq4704207249517081" />"
							label="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Requiretha7197068077564702" />"
						></ui-checkbox>

						<div class="select margin--b--l">
							<label for="postingTargetingFormId">
								<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Selectaddi4792770022526716" />
							</label>
							<select
								id="postingTargetingFormId"
								name="postingTargetingFormId"
								class="js--ui-select sel_TestValidationStatus"
								v-model="workflowTemplate.postingTargetingFormId"
							>
								<option id="selectItem_7638315421771931" value="">
									<orbis:message code="i18n.exp_expTypeWorkflowTemplateEdit.NoRequired5435113335243526"/>
								</option>
								<option
									v-for="form in workflowData.postingTargetingForms"
									:key="form.id"
									:value="form.id"
									:selected="workflowTemplate.postingTargetingFormId === form.id"
								>
									{{form.name}}
								</option>
							</select>
							<div class="select__arrow"></div>
							<p class="text--help">
								<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Ifanopport6778426925082394" />
							</p>
						</div>
					</template>

					<ui-checkbox
						v-model="workflowTemplate.placementRecordsAutoCreatingEnabled"
						class="margin--b--l"
						type="toggle"
						label-style="help"
						column
						title="<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.AutoCreate9100137164690005"/>"
						label="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Automatica9612921351382737" />/program"
					></ui-checkbox>

					<div v-show="workflowTemplate.placementRecordsAutoCreatingEnabled">
						<div class="input__group">
							<label class="label" for="defaultTitle">
								<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.DefaultTit8384931065952021" />
							</label>
							<div class="display--flex align--center">
								<input
									class="input--box display--block"
									data-rule-maxlength="255"
									type="text"
									name="defaultTitle"
									id="defaultTitle"
									v-model="workflowTemplate.recordDefaultTitle"
								>
							</div>
						</div>

						<div class="input__group">
							<label class="label" for="defaultTitleL2">
								<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.DefaultTit3394997864631637" />
							</label>
							<div class="display--flex align--center">
								<input
									class="input--box display--block"
									data-rule-maxlength="255"
									type="text"
									name="defaultTitleL2"
									id="defaultTitleL2"
									v-model="workflowTemplate.recordDefaultTitleL2"
								>
							</div>
						</div>

						<label for="recordDefaultDescription">
							<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.DefaultDes0888955502111839"/>
						</label>
						<ui-ckeditor id="recordDefaultDescription" v-model="workflowTemplate.recordDefaultDescription"></ui-ckeditor>

						<label for="recordDefaultDescriptionL2">
							(fr)
						</label>
						<ui-ckeditor id="recordDefaultDescriptionL2" v-model="workflowTemplate.recordDefaultDescriptionL2"></ui-ckeditor>

						<div class="input__group">
							<label class="label" for="name">
								<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.DefaultHou6867179632233486" />
							</label>
							<div class="display--flex align--center">
								<input
									class="input--box display--block"
									min="0"
									type="number"
									name="recordDefaultHoursCompleted"
									id="recordDefaultHoursCompleted"
									v-model="workflowTemplate.recordDefaultHoursCompleted"
								>
							</div>
						</div>
					</div>

					<ui-tooltip v-if="workflowData.hasTrackHoursStep" :element="$refs.enableTimeTrackerCheckbox">
						<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Thissettin3070952994941101" />
					</ui-tooltip>

					<ui-checkbox
						ref="enableTimeTrackerCheckbox"
						v-model="workflowTemplate.enableTimeTracker"
						:disabled="workflowData.hasTrackHoursStep"
						class="margin--b--l"
						type="toggle"
						label-style="help"
						column
						title="<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.UseNEWtime9487216327062466" />"
						label="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Allowstude8341395913465407" />"
					></ui-checkbox>


					<div v-show="workflowTemplate.enableTimeTracker">
						<fieldset class="display--flex flex--column">
							<legend>
								<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Howwouldyo9061045299988640"/>
							</legend>

							<label>
								<input
									type="radio"
									name="enforceMinimumRequirements"
									value=""
									v-model="workflowTemplate.enforceMinimumRequirements"
								>
								<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Dontenforc2783390098418445"/>
							</label>

							<label>
								<input
									type="radio"
									name="enforceMinimumRequirements"
									value="hours"
									v-model="workflowTemplate.enforceMinimumRequirements"
								>
								<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Enforceami9820053419439035"/>
							</label>

							<label>
								<input
									type="radio"
									name="enforceMinimumRequirements"
									value="entries"
									v-model="workflowTemplate.enforceMinimumRequirements"
								>
								<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Enforceami7469254587863298"/>
							</label>
						</fieldset>

						<div v-show="workflowTemplate.enforceMinimumRequirements" class="input__group">
							<label class="label" for="name">
								<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Whatsthemi1465999529576949"/>
							</label>
							<div class="display--flex align--center">
								<input
									class="input--box display--block"
									type="number"
									name="timeRequirement"
									id="timeRequirement"
									v-model="workflowTemplate.timeRequirement"
								>
							</div>
						</div>

                        <fieldset class="display--flex flex--column margin--t--l">
                            <legend>
                                <orbis:message code="i18n.ccrm_competenciableModuleEditForm.Competency8195639533331422"/>
                            </legend>

                            <label v-if="workflowData.canDisableCompentencies">
                                <input
									type="radio"
									name="competencyUsecase"
									value="4"
									v-model="workflowTemplate.competencyUsecase"
								>
                                <orbis:message code="i18n.ccrm_competenciableEditForm.Turnoffcom5534470076228965" />
                            </label>

                            <label>
                                <input
									type="radio"
									name="competencyUsecase"
									value="0"
									v-model="workflowTemplate.competencyUsecase"
								>
                                <orbis:message code="i18n.ccrm_competenciableModuleEditForm.Participan2844463262773155" />
                            </label>

                            <label>
                                <input
									type="radio"
									name="competencyUsecase"
									value="1"
									v-model="workflowTemplate.competencyUsecase"
								>
                                <orbis:message code="i18n.ccrm_competenciableModuleEditForm.Participan3649703299698679" />
                            </label>

                            <label>
                                <input
									type="radio"
									name="competencyUsecase"
									value="2"
									v-model="workflowTemplate.competencyUsecase"
								>
                                <orbis:message code="i18n.ccrm_competenciableModuleEditForm.Participan8001289956249845" />
                            </label>

                            <label>
                                <input
									type="radio"
									name="competencyUsecase"
									value="3"
									v-model="workflowTemplate.competencyUsecase"
								>
                                <orbis:message code="i18n.ccrm_competenciableModuleEditForm.Participan1201289956249834" />
                            </label>
                        </fieldset>

                        <div v-show="![0, 4].includes(workflowTemplate.competencyUsecase)" class="select margin--t--l">
                            <select
								id="assignedC"
								name="assignedC"
								class="js--ui-select sel_TestValidationStatus"
								multiple
								v-model="workflowTemplate.assignedC"
							>
                                <option
									v-for="competency in workflowData.availableCompetencies"
									:key="competency.id"
									:value="competency.id"
									:selected="workflowData.selectedCompetencies.includes(competency.id)"
								>
                                    {{competency.name}}
                            </select>
                            <div class="select__arrow"></div>
                            <p class="text--help">
                                <orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Whoneedsto4346110858886527" />
                            </p>
                        </div>

                        <div v-show="![0, 4].includes(workflowTemplate.competencyUsecase)" class="input__group">
                            <label class="label" for="minCompetencies">
                                <orbis:message code="i18n.ccrm_competenciableModuleEditForm.Minimumnum0649567979113731" />
                            </label>
                            <div class="display--flex align--center">
                                <input
									class="input--box display--block"
									type="number"
									name="minCompetencies"
									id="minCompetencies"
									v-model="workflowTemplate.minimumCompetencies"
								>
                            </div>

                            <label class="label" for="maxCompetencies">
                                <orbis:message code="i18n.ccrm_competenciableModuleEditForm.Maximumnum9126551986277475" />
                            </label>
                            <div class="display--flex align--center">
                                <input
									class="input--box display--block"
									type="number"
									name="maxCompetencies"
									id="maxCompetencies"
									v-model="workflowTemplate.maximumCompetencies"
								>
                            </div>
                        </div>

						<ui-checkbox
							v-model="workflowTemplate.logsNeedApproval"
							class="margin--b--l"
							type="toggle"
							label-style="help"
							column
							title="<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Doyouwantt8104463710351847" />"
						></ui-checkbox>

						<div v-show="workflowTemplate.logsNeedApproval">
							<fieldset class="display--flex flex--column">
								<legend>
									<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Whoneedsto4346110858886527"/>
								</legend>

								<div class="select margin--t--l">
									<select
										id="workLogApproverType"
										name="workLogApproverType"
										class="js--ui-select sel_TestValidationStatus"
										multiple
										v-model="workflowTemplate.workLogApproverType"
									>
										<option value="1" :selected="workflowTemplate.staffMemberApproves">
											<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.StaffMembe7787468722589875" />
										</option>
										<option value="2" :selected="workflowTemplate.supervisorApproves">
											<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.StudentsSu8377305115983950" />
										</option>
										<option value="3" :selected="workflowTemplate.facultySupervisorApproves">
											<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.StudentsFa7430366765059907" />
										</option>
										<option v-show="expType.enableFieldSupervisors" value="4"
										        :selected="workflowTemplate.siteSupervisorApproves">
											<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.StudentsSi5217940756906983" />
										</option>
									</select>
									<div class="select__arrow"></div>
									<p class="text--help">
										<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Whoneedsto4346110858886527" />
									</p>
								</div>
							</fieldset>

							<fieldset v-show="workflowTemplate.workLogApproverType && workflowTemplate.workLogApproverType !== 1">
								<legend><orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Worklogapp1695585354458765" /></legend>
								<div class="input__group">
									<label class="label" for="name">
										<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Emailaddre9590409589689266"/>
									</label>
									<div class="display--flex align--center">
										<input
											class="input--box display--block"
											type="text"
											name="workLogApprovalEmailFrom"
											id="workLogApprovalEmailFrom"
											v-model="workflowTemplate.workLogApprovalEmailFrom"
										>
									</div>
								</div>

								<div class="input__group">
									<label class="label" for="name">
										<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Emailsubje2159264465411571"/>
									</label>
									<div class="display--flex align--center">
										<input
											class="input--box display--block"
											type="text"
											name="workLogApprovalEmailSubject"
											id="workLogApprovalEmailSubject"
											v-model="workflowTemplate.workLogApprovalEmailSubject"
										>
									</div>
								</div>

								<div class="input__group">
									<label class="label" for="name">
										<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Emailsubje2159264465411571"/> (fr)
									</label>
									<div class="display--flex align--center">
										<input
											class="input--box display--block"
											type="text"
											name="l2WorkLogApprovalEmailSubject"
											id="l2WorkLogApprovalEmailSubject"
											v-model="workflowTemplate.l2WorkLogApprovalEmailSubject"
										>
									</div>
								</div>

								<label for="workLogApprovalEmailBody">
									<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Emailbody4958022698507689"/>
								</label>
								<ui-ckeditor
									name="workLogApprovalEmailBody"
									id="workLogApprovalEmailBody"
									v-model="workflowTemplate.workLogApprovalEmailBody"
								></ui-ckeditor>
								<label for="l2WorkLogApprovalEmailBody">
									<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Emailbody4958022698507689"/> (fr)
								</label>
								<ui-ckeditor
									name="l2WorkLogApprovalEmailBody"
									id="l2WorkLogApprovalEmailBody"
									v-model="workflowTemplate.l2WorkLogApprovalEmailBody"
								></ui-ckeditor>

								<div class="input__group">
									<label class="label" for="name">
										<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Emaillink1788991637204956"/>
									</label>
									<div class="display--flex align--center">
										<input
											class="input--box display--block"
											type="text"
											name="workLogApprovalEmailLink"
											id="workLogApprovalEmailLink"
											v-model="workflowTemplate.workLogApprovalEmailLink"
										>
									</div>
								</div>

								<div class="input__group">
									<label class="label" for="name">
										<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Emaillink1788991637204956"/> (fr)
									</label>
									<div class="display--flex align--center">
										<input
											class="input--box display--block"
											type="text"
											name="l2WorkLogApprovalEmailLink"
											id="l2WorkLogApprovalEmailLink"
											v-model="workflowTemplate.l2WorkLogApprovalEmailLink"
										>
									</div>
								</div>
							</fieldset>
						</div>
					</div>

					<ui-checkbox
						v-model="workflowTemplate.enableStepCompletionInAnyOrder"
						class="margin--b--l"
						type="toggle"
						label-style="help"
						column
						title="<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Allowsteps2815344324297986" />"
						label="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Allowsteps7393000734896513" />"
					></ui-checkbox>
					<ui-checkbox
						v-model="workflowTemplate.showBadgesOnExperiential"
						class="margin--b--l"
						type="toggle"
						label-style="help"
						column
						title="<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Showstuden8717430961292994"/>"
					></ui-checkbox>

					<div v-show="workflowTemplate.showBadgesOnExperiential">
						<label for="badge">
							<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Uploadyour4065995010809245" />
						</label>
<%--						TODO: Max file size?--%>
						<ui-file-upload
							id="badge"
							name="badgeFile"
							:max-files-count="1"
							accept="images"
							mini
							v-model="workflowTemplate.badgeFile"
						></ui-file-upload>

						<div class="select margin--t--l">
							<select
								id="badgeDisplayOption"
								name="badgeDisplayOption"
								class="js--ui-select sel_TestValidationStatus"
								v-model="workflowTemplate.experientialBadgesDisplayOption"
							>
								<option value="0">
									<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.DisplayCar4289666776211614" />
								</option>
								<option value="1">
									<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.DisplayBad5689323640794841" />
								</option>
							</select>
							<div class="select__arrow"></div>
							<p class="text--help">
								<orbis:message code="i18n.exp_expTypeWorkflowTemplateDetails.Choosehowt2305295249573389" />
							</p>
						</div>
					</div>

				</div>
				<div class="steps-actions display--flex">
					<button class="btn__hero--text btn--default border-radius--0 grow--1 align--center" @click="saveWorkflowOptions">
						<orbis:message code="i18n.exp_expTypeWorkflowTemplateEdit.SaveChange9684450313812333" />
					</button>
				</div>
			</ui-tab-section>
		</ui-tabs>
	</ui-sidebar>

	<ui-sidebar v-model="showResourceLibrarySidebar" title="Resource Library" remove-content-padding>
		<ui-empty-state v-if="!resources?.length">
			<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Noresults0893703917482723" />
		</ui-empty-state>
		<ui-survey-cards v-else>
			<ui-resource-library-item
				v-for="item in resources"
				:key="item.id"
				:title="item.name"
				:icon="item.icon"
				@download="downloadDocument(item.id)"
				@edit="editDocument(item.id)"
				@view-logs="viewDocumentLogs(item.id)"
				@delete="deleteDocument(item.id)">
			</ui-resource-library-item>
		</ui-survey-cards>
	</ui-sidebar>

	<ui-sidebar v-model="showCoordinatorsSidebar" title="<orbis:message code="i18n.exp_course.Coordinato9283258894275064"/>" remove-content-padding>
		<ui-empty-state v-if="!coordinators?.length">
			<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Noresults8589858599451485" />
		</ui-empty-state>
		<template v-else>
			<div class="display--flex space--between align--top padding--t--l padding--r--m padding--b--m padding--l--m">
				<h3 class="margin--b--none">
					{{courseData.courseLabel}} <br />
					{{courseData.termName}}
				</h3>
			</div>
			<ui-survey-cards>
				<ui-coordinator-card
					v-for="coordinator in coordinators"
					:key="coordinator.id"
					:title="coordinator.user.firstAndLastName"
					:email="coordinator.user.emailAddress"
					:phone="coordinator.user.phoneNumber"
					icon="account_circle"
					@click="openCoordinator(coordinator.id)"
					@delete="removeCoordinator(coordinator.id)"
				></ui-coordinator-card>
			</ui-survey-cards>
		</template>
	</ui-sidebar>

	<ui-sidebar v-if="showCourseProgressSidebar" v-model="showCourseProgressSidebar" title="<orbis:message code="i18n.exp_termCourseActions.Progress3588000528177084" />">
		<div class="panel-default">
			<div>
				<ui-button v-if="progressBar.tacTotal > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.tacType)">
					{{progressBar.tacPercent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.tacStepLabel : module.l2TacStepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.questTotal > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.questType)">
					{{progressBar.questPercent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.quesQualStepLabel : module.l2QuesQualStepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.eventTotal > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.eventType)">
					{{progressBar.eventPercent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.attendEventStepLabel : module.l2AttendEventStepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.frmGrpTotal > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.frmGrpType)">
					{{progressBar.frmGrpPercent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.formGroupStepLabel : module.l2FormGroupStepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.createRecordTotal > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.createRecordType)">
					{{progressBar.createRecordPercent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.findRecordStepLabel : module.l2FindRecordStepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.trackHoursTotal > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.trackHoursType)">
					{{progressBar.trackHoursPercent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.hoursStepLabel : module.l2HoursStepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.stevalTotal > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.stevalType)">
					{{progressBar.stevalPercent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.studEvalStepLabel : module.l2StudEvalStepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.facevalTotal > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.facevalType)">
					{{progressBar.facevalPercent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.facEvalStepLabel : module.l2FacEvalStepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.ipevalTotal > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.ipevalType)">
					{{progressBar.ipevalPercent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.empEvalStepLabel : module.l2EmpEvalStepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.journalTotal > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.journalType)">
					{{progressBar.journalPercent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.journalStepLabel : module.l2JournalStepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.custom1Total > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.custom1Type)">
					{{custom1Percent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.custom1StepLabel : module.l2Custom1StepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.custom2Total > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.custom2Type)">
					{{progressBar.custom2Percent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.custom2StepLabel : module.l2Custom2StepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.custom3Total > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.custom3Type)">
					{{progressBar.custom3Percent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.custom3StepLabel : module.l2Custom3StepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.custom4Total > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.custom4Type)">
					{{progressBar.custom4Percent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.custom4StepLabel : module.l2Custom4StepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.custom5Total > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.custom5Type)">
					{{progressBar.custom5Percent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.custom5StepLabel : module.l2Custom5StepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.custom6Total > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.custom6Type)">
					{{progressBar.custom6Percent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.custom6StepLabel : module.l2Custom6StepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.ecommTotal > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.ecommType)">
					{{progressBar.ecommPercent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.ecommerceStepLabel : module.l2EcommerceStepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.appointmentTotal > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.appointmentType)">
					{{progressBar.appointmentPercent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.appointmentStepLabel : module.l2AppointmentStepLabel }" />
				</ui-button>
				<ui-button v-if="progressBar.waitTotal > 0" class="margin--b--s width--100" type="info" plain pill @click="displayTermCourseStepProgress(progressBar.waitType)">
					{{progressBar.waitPercent }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.StepCompleted" arguments="${isL1 ? module.adminWaitStepLabel : module.l2AdminWaitStepLabel }" />
				</ui-button>
				<ui-button type="info" class="margin--b--s width--100" plain pill @click="displayTermCourseRecords(row.id, 'inactiveWithApprovedRecord')">
					{{progressBar.inactiveStudentsApprovedRecordCount }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.InactiveSt9697898025571522" />
				</ui-button>
				<ui-button type="info" class="margin--b--s width--100" plain pill @click="displayTermCourseRecords(row.id, 'inactiveWithPendingRecord')">
					{{progressBar.inactiveStudentsPendingRecordCount }} <orbis:message code="i18n.exp_termCourseStudentStepProgress.InactiveSt0484107467418920" />
				</ui-button>
			</div>
		</div>
	</ui-sidebar>

	<ui-faculty-assigned-sidebar
		v-model="showFacultiesSidebar"
		:faculties="faculties"
		:course-label="courseData.courseLabel"
		:term-name="courseData.termName"
		@remove-faculty="removeFaculty"
	></ui-faculty-assigned-sidebar>

<%--Modals--%>
	<ui-modal v-model="showSearchRecordsModal" title="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.SelectType7911799122255465" />">
		<div class="panel-default">
			<label>
				<orbis:message code="i18n.exp_termCourseActions.Select4482573567985848" />
				<strong>
					<orbis:message code="i18n.exp_termCourseActions.Experience2190894438593364" />
				</strong>:
			</label>

			<ui-select v-model="searchRecordsType">
				<ui-select-item v-for="type in termCourseTypes" :value="type.tct.id">
					{{type.tct.type.name}}
				</ui-select-item>
			</ui-select>
		</div>
		<template #override-buttons>
			<ui-button class="border-radius--4 margin--r--m" @click="searchRecordsBySelectedType()">
				<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Search7795470217778951" />
			</ui-button>
			<ui-button btn-style="plain" type="info" class="margin--l--m border-radius--4" @click="showSearchRecordsModal = false">
				<orbis:message code="i18n.exp_termCourseTypeStepsAjax.CANCEL4159683140998700" />
			</ui-button>
		</template>
	</ui-modal>

	<ui-modal v-model="showDeleteModal" title="<orbis:message code="i18n.exp_termCourseActions.Delete6880532887688352" />" >
		<div class="margin--b--s">
			<orbis:message code="i18n.exp_termCourseActions.Areyousure6802545144360173" />
		</div>
		<template #override-buttons>
			<ui-button type="error" class="border-radius--4 color--font--white margin--r--m" @click="deleteTermCourse()">
				<orbis:message code="i18n.exp_termCourseActions.Delete6880532887688352" />
			</ui-button>
			<ui-button btn-style="plain" type="info" class="margin--l--m border-radius--4" @click="showDeleteModal = false">
				<orbis:message code="i18n.exp_termCourseTypeStepsAjax.CANCEL4159683140998700" />
			</ui-button>
		</template>
	</ui-modal>

	<ui-modal v-model="showSearchOpportunitiesModal" title="<orbis:message code="i18n.exp_termCourseActions.SearchOppo7587944303807580" />">
		<div class="panel-default">
			<label>
				<orbis:message code="i18n.exp_termCourseActions.Select4482573567985848" />
				<strong>
					<orbis:message code="i18n.exp_termCourseActions.Experience2190894438593364" />
				</strong>:
			</label>

			<ui-select v-model="searchOpportunityType">
				<ui-select-item :value="" selected>
					<orbis:message code="i18n.exp_termCourseActions.AllExperie8250783858302685" />
				</ui-select-item>
				<ui-select-item v-for="type in termCourseTypes" :value="type.tct.type.id">
					{{type.tct.type.name}}
				</ui-select-item>
			</ui-select>
		</div>
		<template #override-buttons>
			<ui-button class="border-radius--4 margin--r--m" @click="displaySearchOpportunities">
				<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Search7795470217778951" />
			</ui-button>
			<ui-button btn-style="plain" type="info" class="margin--l--m border-radius--4" @click="showSearchOpportunitiesModal = false">
				<orbis:message code="i18n.exp_termCourseTypeStepsAjax.CANCEL4159683140998700" />
			</ui-button>
		</template>
	</ui-modal>

	<ui-modal v-model="showCourseStatusModal" title="Change Status">
		<div>
			<strong class="display--flex"><orbis:message code="i18n.exp_termCourseActions.Open5931787866765020" /></strong>
			<p class="margin--t--s"><orbis:message code="i18n.exp_termCourseActions.Termcourse5855767184239069" /></p>
		</div>
		<div>
			<strong class="display--flex"><orbis:message code="i18n.exp_termCourseActions.Closed3165082949976204" /></strong>
			<p class="margin--t--s"><orbis:message code="i18n.exp_termCourseActions.Termcourse9349439368688960" /></p>
		</div>
		<div>
			<strong class="display--flex"><orbis:message code="i18n.exp_termCourseActions.Archived2839684481083125" /></strong>
			<p class="margin--t--s"><orbis:message code="i18n.exp_termCourseActions.Termcourse7564089424190581" /></p>
		</div>

		<ui-select label="Status" v-model="newStatus">
			<ui-select-item :value="0">
				<orbis:message code="i18n.exp_termCourseActions.Open5931787866765020" />
			</ui-select-item>
			<ui-select-item :value="1">
				<orbis:message code="i18n.exp_termCourseActions.Closed3165082949976204" />
			</ui-select-item>
			<ui-select-item :value="2">
				<orbis:message code="i18n.exp_termCourseActions.Archived2839684481083125" />
			</ui-select-item>
		</ui-select>

		<template #override-buttons>
			<ui-button class="border-radius--4 margin--r--m" @click="changeStatus">
				<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Confirm3473464767356207" />
			</ui-button>
			<ui-button btn-style="plain" type="info" class="margin--l--m border-radius--4" @click="showCourseStatusModal = false">
				<orbis:message code="i18n.exp_termCourseTypeStepsAjax.CANCEL4159683140998700" />
			</ui-button>
		</template>
	</ui-modal>

	<ui-modal v-model="showRemindStudentsModal" title="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.RemindStud1128069921567348" />">
		<div class="modal-body display--flex flex--column margin--b--l">
			<div class="margin--b--l">
				<orbis:message code="i18n.exp_homeActiveCourses_remindStudentsModal.Areyousure2742326733721631" />
			</div>
			<label class="display--flex flex--column font--bold font--12 margin--b--l">
                <span>
                    <orbis:message code="i18n.exp_homeActiveCourses_remindStudentsModal.Subject7446917194638083" />
                </span>
				<ui-textbox v-model="emailReminderSubject"/>
			</label>
			<label class="display--flex flex--column font--bold font--12">
                <span class="margin--b--s">
                    <orbis:message code="i18n.exp_homeActiveCourses_remindStudentsModal.Email8030496135031241" />
                </span>
				<ui-ckeditor v-model="emailReminderEmail"></ui-ckeditor>
			</label>
			<ui-checkbox
				v-model="sendReminderToStarted"
				class="margin--b--l"
				type="toggle"
				label-style="help"
				label="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Sendremind2034858032067768" />"
			></ui-checkbox>
			<ui-checkbox
				v-model="sendReminderToDeclined"
				class="margin--b--l"
				type="toggle"
				label-style="help"
				label="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Sendremind9792516423798462" />"
			></ui-checkbox>
		</div>
		<template #override-buttons>
			<ui-button class="border-radius--4" @click="emailReminders">
				<orbis:message code="i18n.exp_homeActiveCourses_remindStudentsModal.EmailRemin8144526142886510" />
			</ui-button>
			<ui-button btn-style="plain" type="info" class="margin--l--m border-radius--4" @click="closeRemindStudentsModal">
				<orbis:message code="i18n.exp_termCourseTypeStepsAjax.CANCEL4159683140998700" />
			</ui-button>
		</template>
	</ui-modal>

	<ui-modal v-model="showAddFacultyModal" title="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.AddFaculty5936184018601070" />">
		<label>
			<span class="font--12 color--font--alt margin--b--s">
				<orbis:message code="i18n.exp_termCourseActions.UserLookup5539595319809643" />
			</span>
			<ui-select-box
				:options="termCourseFacultySearchResults"
				@search="handleTermCourseFacultySearch"
				@option:selected="handleTermCourseFacultySelected"
			></ui-select-box>
		</label>
		<span class="font--12 color--font--alt font--italic margin--t--s">
			<orbis:message code="i18n.exp_termCourseActions.Searchfora0025204022571374" />
		</span>
		<p class="margin--t--l margin--b--l font--bold">
			<orbis:message code="i18n.common.orUPPERCASE" />
		</p>
		<label>
			<span class="font--12 color--font--alt margin--b--s">
				<orbis:message code="i18n.exp_termCourseActions.EnteraUser7129661275174332" />
			</span>
			<textarea
				class="width--100"
				style="height: 200px"
				type="text"
				v-model="termCourseFacultyList"
			></textarea>
		</label>
		<template #override-buttons>
			<ui-button class="border-radius--4" @click="bulkAddFacultyToTermCourse">
				<orbis:message code="i18n.exp_termCourseActions.AddFaculty3936453903524795" />
			</ui-button>
			<ui-button btn-style="plain" type="info" class="margin--l--m border-radius--4" @click="closeAddTermCourseFacultyModal">
				<orbis:message code="i18n.exp_termCourseTypeStepsAjax.CANCEL4159683140998700" />
			</ui-button>
		</template>
	</ui-modal>

	<ui-modal v-model="showUnlockStepOrderModal" title="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Confirmati7368347611537240" />">
		<div class="modal-body margin--b--l">
			<orbis:message code="i18n.exp_termCourseTypeSteps.pAreyousur9156613765263154" />
		</div>
		<template #override-buttons>
			<ui-button class="border-radius--4 margin--r--m" @click="saveEditStepOrder">
				<orbis:message code="i18n.exp_termCourseTypeSteps.EDITSTEPOR9033997629047273" />
			</ui-button>
			<ui-button btn-style="plain" type="info" class="margin--l--m border-radius--4" @click="cancelEditStepOrder">
				<orbis:message code="i18n.exp_termCourseTypeStepsAjax.CANCEL4159683140998700" />
			</ui-button>
		</template>
	</ui-modal>

	<ui-modal v-model="showAddTermCourseCoordinator" title="<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.AddCoordin3074945714099487" />">
		<label>
			<span class="font--12 color--font--alt margin--b--s">
				<orbis:message code="i18n.exp_termCourseActions.UserLookup5539595319809643" />
			</span>
			<ui-select-box
				:options="termCourseCoordinatorSearchResults"
				@search="handleTermCourseCoordinatorSearch"
				@option:selected="handleTermCourseCoordinatorSelected"
			></ui-select-box>
		</label>
		<span class="font--12 color--font--alt font--italic margin--t--s">
			<orbis:message code="i18n.exp_termCourseActions.Searchfora0025204022571374" />
		</span>
		<p class="margin--t--l margin--b--l font--bold">
			<orbis:message code="i18n.common.orUPPERCASE" />
		</p>
		<label>
			<span class="font--12 color--font--alt margin--b--s">
				<orbis:message code="i18n.exp_termCourseActions.EnteraUser9460062654320446" />
			</span>
			<textarea
				class="width--100"
				style="height: 200px"
				type="text"
				v-model="termCourseCoordinatorsList"
			></textarea>
		</label>
		<template #override-buttons>
			<ui-button class="border-radius--4" @click="bulkAddCoordinatorsToTermCourse">
				<orbis:message code="i18n.exp_termCourseActions.AddCourseP6184982338458796" />
			</ui-button>
			<ui-button btn-style="plain" type="info" class="margin--l--m border-radius--4" @click="closeAddTermCourseCoordinatorModal">
				<orbis:message code="i18n.exp_termCourseTypeStepsAjax.CANCEL4159683140998700" />
			</ui-button>
		</template>
	</ui-modal>

	<ui-modal v-model="showWorkflowTemplateModal" title="<orbis:message code="i18n.exp_termCourseTypeSteps.ChangeWork1987197397294895" />">
		<fieldset class="input__group js--error-placement-container">
			<legend class="label js--error-placement margin--b--xs"></legend>
			<label v-for="template in workflowTemplates" class="label--is--hijacked no--callout">
				<input type="radio" :name="template.id" :value="template.id" v-model="selectedWorkflowId">
				<span class="label--span">{{template.name}}</span>
			</label>
		</fieldset>

		<template #override-buttons>
			<ui-button class="border-radius--4 margin--r--m" @click="changeWorkflow">
				<orbis:message code="i18n.exp_termCourseTypeSteps.APPLYNEWWO5385082328373191" />
			</ui-button>
			<ui-button btn-style="plain" type="info" class="margin--l--m border-radius--4" @click="showWorkflowTemplateModal = false">
				<orbis:message code="i18n.exp_termCourseTypeStepsAjax.CANCEL4159683140998700" />
			</ui-button>
		</template>
	</ui-modal>

	<ui-modal v-model="showDeleteStepModal" title="<orbis:message code="i18n.exp_termCourseTypeSteps.ChangeWork1987197397294895" />">
        <div class="margin--b--s">
            <orbis:message code="i18n.exp_termCourseTypeStepsAjax.Areyousure6952743167591042" />
        </div>
		<template #override-buttons>
			<ui-button type="error" class="border-radius--4 color--font--white margin--r--m" @click="deleteStep(tctStepIdToDelete)">
				<orbis:message code="i18n.exp_termCourseTypeStepsAjax.DELETESTEP6561968523667516" />
			</ui-button>
			<ui-button btn-style="plain" type="info" class="margin--l--m border-radius--4" @click="showDeleteStepModal = false">
				<orbis:message code="i18n.exp_termCourseTypeStepsAjax.CANCEL4159683140998700" />
			</ui-button>
		</template>
	</ui-modal>

	<Teleport to="body">
		<div id="stepConfigContainer"></div>
	</Teleport>

	<button type="button" class="btn btn--menu--circular always--circular ui--actions-group color--bg--default js--interaction-btn display--block padding--a--none" @click="loadActionsSidebar">
		<i class="material-icons">more_vert</i>
	</button>
</template>

<script type="text/javascript">
    const docViewerDocument = {
        template: "#docViewerDocument",
		components: {
			draggable: vuedraggable,
			'ui-resource-library-item': resourceLibraryItem,
			'ui-coordinator-card': coordinatorCard,
			'ui-faculty-assigned-sidebar': facultyAssignedSidebar,
			'placement-map': placementMap,
			'ui-ckeditor': uiCkEditorVue,
			'ui-step-card': stepCard,
		},
        props: {
            row: Object,
        },
		data: () => ({
			courseData: {},
			resources: null,
			coordinators: null,
			faculties: null,
			stepsByType: null,
			stepTypeLabels: null,
            termCourseTypes: null,
			experienceStatusCounts: null,
			questionnaireCount: null,
			tagsAssigned: null,
			newStatus: null,
			progressBar: null,
			searchOpportunityType: null,
			workflowData: {},
			workflowTemplate: {},
			workflowTemplates: null,
			expType: {},
			workflowSteps: null,
			stepsToAdd: null,
			workflowTctId: null,
            workflowSidebarTitle: null,
			searchRecordsType: null,
			tctStepIdToDelete: null,
			selectedWorkflowId: null,
			isEdited: false,
			showWorkflowSidebar: false,
			showActionsSidebar: false,
			showCourseStatusModal: false,
			showSearchOpportunitiesModal: false,
			showSearchRecordsModal: false,
			showCourseProgressSidebar: false,
			showResourceLibrarySidebar: false,
			showCoordinatorsSidebar: false,
			showDeleteModal: false,
			showFacultiesSidebar: false,
			showAddFacultyModal: false,
			termCourseFacultySearchResults: [],
			termCourseFacultyList: '',
			showAddTermCourseCoordinator: false,
			termCourseCoordinatorSearchResults: [],
			termCourseCoordinatorsList: '',
			showRemindStudentsModal: false,
			showUnlockStepOrderModal: false,
			showWorkflowTemplateModal: false,
			showDeleteStepModal: false,
			remindStep: {},
			emailReminderSubject: '',
			emailReminderEmail: '',
			sendReminderToStarted: false,
			sendReminderToDeclined: false,
			sendEmailOnCreateRecords: false,
			reportsData: {},
			reportGeoData: [],
			selectedReportType: {},
			topIndustryPartners: [],
			studentsByStatusChartData: [],
			experiencesByProgressChartData: [],
			recordsByIndustryTag: [],
			recordsByDemographicTag: [],
			workflowOptions: {
				allowMultipleExperiences: true
			},
			competenciesReportData: [],
			filterForm: null,
			showCompetenciesContainer: false,
			showCompetenciesFilterView: false,
		}),

	    mounted() {
		    this.refreshTermCourseData();
	    },

	    watch: {
		    row(val) {
			    this.refreshTermCourseData();
		    }
	    },

		methods: {
			emailReminders() {
				orbisApp.showLoadingOverlay();
				$.post("", {
					action: '${o:encrypt("ajaxSendStudentStepRemindEmail", currentUser)}',
					tctsId: this.remindStep.id,
					subject: this.emailReminderSubject,
					email: this.emailReminderEmail,
					sendReminderToStarted: this.sendReminderToStarted,
					sendReminderToDeclined: this.sendReminderToDeclined
				}, (data, status, xhr) => {
					if (data.success) {
						this.closeRemindStudentsModal();
						orbisApp.hideLoadingOverlay();
						orbisAppSr.showNotification("Email sent", "success");
					}
				}, "json");
			},
			refreshTermCourseData() {
				this.resetReportData();
				this.resetReportPlugin();
				this.getTermCourseData(this.row.id, data => {
					this.courseData = data;
					this.resources = data.resources;
					this.coordinators = data.coordinators;
					this.faculties = data.faculties;
					this.stepsByType = data.stepsByType;
					this.stepTypeLabels = data.stepTypeLabels;
					this.termCourseTypes = data.termCourseTypes;
					this.experienceStatusCounts = data.experienceStatusCounts;
					this.questionnaireCount = data.questionnaireCount;
					this.tagsAssigned = data.tagsAssigned;
					this.progressBar = data.progressBar;
				});
			},
			populateReportEntity(reportData) {
				if (reportData.reportKey === 'summaryReport') {
					this.studentsByStatusChartData = [
						{
							y: reportData.approvedStudentsCount,
							name: 'Approved',
							color: '#27E0D2'
						}, {
							y: reportData.pendingStudentsCount,
							name: 'Pending',
							color: '#FFFFFF'
						}, {
							y: reportData.completedStudentsCount,
							name: 'Completed',
							color: '#FFA800'
						}, {
							y: reportData.inactiveStudentsCount,
							name: 'Inactive',
							color: '#ED4747'
						}
					];

					this.experiencesByProgressChartData = [
						{
							y: reportData.activeStudentsWithCompletedExperiences,
							name: 'Active-Completed'
						},
						{
							y: reportData.activeStudentsWithInProgressExperiences,
							name: 'Active-InProgress'
						},
						{
							y: reportData.inactiveStudentsWithInProgressExperiences,
							name: 'Inactive-InProgress'
						}
					];

					this.topIndustryPartners = reportData.topPartners;
					this.$nextTick(() => {
						new Muuri("#reportsSummary", {
							fillGaps: true,
							rounding: true,
						});
					});
				} else if (reportData.reportKey === 'termCourseSingleTypeRecordsReport' ||
						reportData.reportKey === 'termCourseMultiTypeRecordsReport') {

					for (let [key, value] of Object.entries(reportData.recordsByIndustryTagChartData)) {
						this.recordsByIndustryTag.push({
							y: value,
							name: key
						})
					}

					for (let [key, value] of Object.entries(reportData.recordsByDemographicTagChartData)) {
						this.recordsByDemographicTag.push({
							y: value,
							name: key
						})
					}
				} else if (reportData.reportKey === "termCourseCompetencyDevelopmentReport") {
					this.competenciesReportData = reportData.competencyDevelopmentCounts;
					this.showCompetenciesContainer = true;
				}
			},
			initReports() {
				this.resetReportData();
				this.resetReportPlugin();
				this.getReportData(this.selectedReportType, this.row.id, this.populateReportEntity);
			},
			selectReportType(plugin) {
				if (this.selectedReportType.reportKey !== plugin.reportKey) {
					this.resetReportData();
					this.selectedReportType = plugin;
					this.getReportData(this.selectedReportType, this.row.id, this.populateReportEntity);
				}
			},
			getReportData(reportType, termCourseId, callback) {
				orbisApp.showLoadingOverlay();
				$.post("", {
					action: '${o:encrypt("ajaxDisplayTermCourseReports", currentUser)}',
					reportKey: reportType.reportKey,
					dataViewerReport: true,
					termCourseId
				}, (data, status, xhr) => {
					this.reportsData = data;
					callback(data);
					orbisApp.hideLoadingOverlay();
				}, "json");
			},
			getTermCourseData(termCourseId, callback) {
				orbisApp.showLoadingOverlay();
				$.post("", {
					action: '${o:encrypt("ajaxGetTermCourseData", currentUser)}',
					termCourseId
				}, (data, status, xhr) => {
					orbisApp.hideLoadingOverlay()
					if (orbisAppSr.checkAjaxResponse(xhr))
						callback(data);
				}, "json");
			},
			loadWorkflowSidebar(termCourseTypeId, termCourseTypeName) {
				orbisApp.showLoadingOverlay();
				this.showWorkflowSidebar = true;
				this.workflowTctId = termCourseTypeId;
				this.workflowSidebarTitle = termCourseTypeName;
				this.workflowSteps = this.stepsByType[termCourseTypeId];

				this.refreshWorkflowData(termCourseTypeId);
			},
			loadActionsSidebar() {
				this.showActionsSidebar = true;
			},
			refreshWorkflowData(termCourseTypeId) {
				$.post("", {
					action: '${o:encrypt("ajaxLoadEditWorkflowSidebar", currentUser)}',
					tctId: termCourseTypeId
				}, (workflowData, status, xhr) => {
					this.workflowTemplate = workflowData.workflowTemplate;
					this.workflowTemplates = workflowData.workflowTemplates;
					this.expType = workflowData.expType;
					this.stepsToAdd = workflowData.stepsToAdd;
					this.workflowData = workflowData;
					orbisApp.hideLoadingOverlay();
				}, "json");
			},
			showStepConfig(step) {
				orbisApp.showLoadingOverlay();
				var request = {
					action : '<o:encrypt action="loadTermCourseTypeStepActionsSidebar"/>',
					tctStepId: step.id,
					tctStepLabel: step.label,
					tctStepLabelL2: step.l2Label,
					stepType: step.type,
					templateId: this.workflowTemplate.id,
				};

				$.post("", request, html => {
					$('#stepConfigContainer').replaceWith(html);
					$('#stepConfigSidebar').uiShow();
					orbisApp.hideLoadingOverlay();
				});
			},
			checkExpTypes() {
				if (this.termCourseTypes.length > 1) {
					this.showSearchRecordsModal = true;
				}
				else {
					this.searchRecords(this.termCourseTypes[0].tct.id);
				}
			},
			searchRecordsBySelectedType() {
				this.searchRecords(this.searchRecordsType);
			},
			searchRecords(tctId) {
				var request = {
					action: '<o:encrypt action="search" subAction="search"/>',
					searchType: "records",
					tctId: tctId
				};
				orbisApp.buildForm(request).submit();
			},
			refreshStepsForTct(tctId, callback) {
				$.post("", {
					action: '${o:encrypt("ajaxLoadStepsForTct", currentUser)}',
					tctId
				}, (data, status, xhr) => {
					this.stepsByType[tctId] = data.steps;
					callback(data.steps);
				}, "json");
			},
			addStep(stepTypeId) {
				orbisAppSr.showLoadingOverlay()
				$.post("", {
					action: '${o:encrypt("ajaxAddNewTCT", currentUser)}',
					tctId: this.workflowTctId,
					stepType: stepTypeId
				}, (data) => {
					if (data.success) {
						orbisApp.hideLoadingOverlay();
						this.refreshStepsForTct(this.workflowTctId, steps => this.workflowSteps = steps);
						this.refreshWorkflowData(this.workflowTctId);
						orbisAppSr.showNotification("<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.StepAdded8823382806977591" />", "success");
					}
				}, "json");
			},
			deleteStep(tctStepId) {
				orbisApp.showLoadingOverlay();
				$.post("", {
					action: '${o:encrypt("ajaxDeleteTermCourseTypeStep", currentUser)}',
					tctStepId
				}, (data) => {
					if (data.success) {
						orbisApp.hideLoadingOverlay();
						this.showDeleteStepModal = false;
						this.refreshStepsForTct(this.workflowTctId, steps => this.workflowSteps = steps);
						this.refreshWorkflowData(this.workflowTctId);
						orbisAppSr.showNotification("<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.StepDelete9607965842332085" />", "success");
					}
				}, "json");
			},
			changeWorkflow() {
				orbisApp.showLoadingOverlay();

				let request = {
					action: '${o:encrypt("ajaxChangeTermCourseWorkflowTemplate", currentUser)}',
					tctId: this.workflowTctId
				};
				request['workflow' + this.expType.id] =  this.selectedWorkflowId;

				$.post("", request, (data, status, xhr) => {
					if (data.success) {
						orbisApp.hideLoadingOverlay();
						this.showWorkflowTemplateModal = false;
						this.workflowTemplate = data.template;
						this.refreshStepsForTct(this.workflowTctId, steps => this.workflowSteps = steps);
						orbisAppSr.showNotification("<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Workflowch5325497441221922" />", "success");
					}
				}, "json");
			},
			saveWorkflowOptions() {
				orbisApp.showLoadingOverlay();
				$.post("", {
					action: '${o:encrypt("ajaxSaveWorkflowOptions", currentUser)}',
					templateId: this.workflowTemplate.id,
					expTypeId: this.expType.id,
					name: this.workflowTemplate.name,
					allowRepeatExperiences: this.workflowTemplate.allowRepeatExperiences,
					allowStudentRepititions: this.workflowTemplate.allowStudentRepititions,
					sequentialRankingsRequired: this.workflowTemplate.sequentialRankingsRequired,
					postingTargetingFormId: this.workflowTemplate.postingTargetingFormId,
					defaultTemplate: this.workflowTemplate.defaultTemplate,
					integrationCreateRecords: this.workflowTemplate.integrationCreateRecords,
					enableStudentRankings: this.workflowTemplate.studentRankingsEnabled,
					autoCreatePlacementRecords: this.workflowTemplate.placementRecordsAutoCreatingEnabled,
					recordDefaultTitle: this.workflowTemplate.recordDefaultTitle,
					recordDefaultTitleL2: this.workflowTemplate.recordDefaultTitleL2,
					recordDefaultDescription: this.workflowTemplate.recordDefaultDescription,
					recordDefaultDescriptionL2: this.workflowTemplate.recordDefaultDescriptionL2,
					recordDefaultHoursCompleted: this.workflowTemplate.recordDefaultHoursCompleted,
					enableTimeTracker: this.workflowTemplate.enableTimeTracker,
					enforceMinimumRequirements: this.workflowTemplate.enforceMinimumRequirements,
					timeRequirement: this.workflowTemplate.timeRequirement,
					logsNeedApproval: this.workflowTemplate.logsNeedApproval,
					workLogApproverType: this.workflowTemplate.workLogApproverType,
					workLogApprovalEmailFrom: this.workflowTemplate.workLogApprovalEmailFrom,
					workLogApprovalEmailSubject: this.workflowTemplate.workLogApprovalEmailSubject,
					l2WorkLogApprovalEmailSubject: this.workflowTemplate.l2WorkLogApprovalEmailSubject,
					workLogApprovalEmailBody: this.workflowTemplate.workLogApprovalEmailBody,
					l2WorkLogApprovalEmailBody: this.workflowTemplate.l2WorkLogApprovalEmailBody,
					workLogApprovalEmailLink: this.workflowTemplate.workLogApprovalEmailLink,
					l2WorkLogApprovalEmailLink: this.workflowTemplate.l2WorkLogApprovalEmailLink,
					enableStepCompletionInAnyOrder: this.workflowTemplate.enableStepCompletionInAnyOrder,
					showBadges: this.workflowTemplate.showBadgesOnExperiential,
					badge: this.workflowTemplate.badgeFile,
					badgeDisplayOption: this.workflowTemplate.experientialBadgesDisplayOption,
					// Competencies
					competencyUsecase: this.workflowTemplate.competencyUsecase,
					minCompetencies: this.workflowTemplate.minCompetencies,
					maxCompetencies: this.workflowTemplate.maxCompetencies,
					assignedC: this.workflowTemplate.assignedC
				}, (data, status, xhr) => {
					if (data.success) {
						this.refreshTermCourseData();
						this.loadWorkflowSidebar(this.workflowTctId, this.workflowSidebarTitle);
						orbisApp.hideLoadingOverlay();
						orbisAppSr.showNotification("<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Workflowte9153541405662863" />", "success");
					}
				}, "json");
			},
			deleteStepModalDisplay({id}) {
				this.tctStepIdToDelete = id;
				this.showDeleteStepModal = true;
			},
			toggleEditStepOrder() {
				if (this.isEdited) {
					this.showUnlockStepOrderModal = true;
				} else {
					this.isEdited = true;
				}
			},
			saveEditStepOrder() {
				this.isEdited = false;
				orbisAppSr.showLoadingOverlay();

				const stepIds = this.workflowSteps.map(s => s.id);
				$.post("", {
					action: '${o:encrypt("ajaxReorderTctSteps", currentUser)}',
					stepIds: JSON.stringify(stepIds)
				}, (data) => {
					if (data.success) {
						this.showUnlockStepOrderModal = false;
						this.refreshStepsForTct(this.workflowTctId, steps => this.workflowSteps = steps);
						orbisApp.hideLoadingOverlay();
						orbisAppSr.showNotification("<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Steporders2449356816972522" />", "success");
					}
				}, "json");
			},
			cancelEditStepOrder() {
				this.isEdited = false;
				this.showUnlockStepOrderModal = false;
			},
			remindStudents(step) {
				this.remindStep = step;
				this.emailReminderSubject = this.remindStep.label + ' step reminder';
				this.emailReminderEmail = 'The ' + this.remindStep.label + ' in ' + this.courseName + ' is available to be completed.';
				this.showRemindStudentsModal = true;
			},
			closeRemindStudentsModal() {
				this.emailReminderSubject = '';
				this.emailReminderEmail = '';
				this.showRemindStudentsModal = false;
			},
			openPendingStudents(tcId) {
				var request = {
					action: '<o:encrypt action="displayTermCourseStudents"/>',
					status: 'pending',
					fromDataViewer: true,
					tcId
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			openStudents(tcId) {
				var request = {
					action: '<o:encrypt action="displayTermCourseStudents"/>',
					fromDataViewer: true,
					tcId
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			removeCoordinator(termCourseCoordinatorId) {
				orbisApp.showLoadingOverlay();
				$.post("", {
					action: '${o:encrypt("ajaxRemoveTermCourseCoordinator", currentUser)}',
					termCourseCoordinatorId
				}, (data, status, xhr) => {
					if (data.success) {
						this.coordinators = this.coordinators.filter(c => c.id !== termCourseCoordinatorId);
						this.showRemoveCoordinatorModalFlag = true;
						this.courseData.coordinatorsCount -= 1;
						orbisApp.hideLoadingOverlay();
						orbisAppSr.showNotification("<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Coordinato5291478001421877" />", "success");
					}
				}, "json");
			},
			openCoordinator(termCourseCoordinatorId) {
				var request = {
					action: '<o:encrypt action="displayContactOverview"/>',
					userId: termCourseCoordinatorId,
				};
				orbisApp.buildForm(request, "${acrmSE.fullPath}.htm").submit();
			},
			removeFaculty(facultyId) {
				orbisApp.showLoadingOverlay();
				$.post("", {
					action: '${o:encrypt("ajaxRemoveTermCourseFaculty", currentUser)}',
					facultyId
				}, (data, status, xhr) => {
					if (data.success) {
						this.faculties = this.faculties.filter(c => c.id !== facultyId);
						this.courseData.facultyCount -= 1;
						orbisApp.hideLoadingOverlay();
						orbisAppSr.showNotification("<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.FacultyRem8973384795082378" />", "success");
					}
				}, "json");
			},
			resetReportData() {
				this.reportsData = {};
				this.reportGeoData = [];
				this.topIndustryPartners = [];
				this.studentsByStatusChartData = [];
				this.experiencesByProgressChartData = [];
				this.recordsByIndustryTag = [];
				this.recordsByDemographicTag = [];
			},
			resetReportPlugin() {
				this.selectedReportType = {
					reportKey: "summaryReport",
					reportLabel: "<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.TermCourse9321005951304274" />"
				};
			},
			openPlacementRecords(tcId, tctId) {
				var request = {
					action: '<o:encrypt action="displayTermCourseRecords"/>',
					status: "active",
					fromDataViewer: true,
					tcId,
					tctId,
					filterToApply: "activeStudents"
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			displayTermCourseRecords(tcId, filterToApply) {
				var request = {
					action: '<o:encrypt action="displayTermCourseRecords"/>',
					fromDataViewer: true,
					tcId: tcId,
					filterToApply: filterToApply,
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			openUnplacedStudents(tctId) {
				var request = {
					action: '<o:encrypt action="displayUnplacedStudentsTermCourseType"/>',
					fromDataViewer: true,
					tctId
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			openPostings(tcId, tctId, type) {
				var request = {
					action: '<o:encrypt action="displayTermCourseTypeActivePostings"/>',
					fromDataViewer: true,
					tcId,
					tctId,
					type
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			openRankings(tctId) {
				var request = {
					action: '<o:encrypt action="displayTermCourseTypeRankings"/>',
					fromDataViewer: true,
					tctId
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			openStudentsProgressStep(tctStepId, tctId) {
				var request = {
					action: '<o:encrypt action="displayMassUpdateTCTStepProgressDocViewer"/>',
					tctStepId,
					tctId
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			openQuestionnairesSearch(tctStepId) {
				var request = {
					action: '<o:encrypt action="search"/>',
					subAction: "search",
					searchType: "questionnaires",
					tctStepId
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			formatCompetenciesSubgroups(subgroup) {
				return subgroup.split('-').map(s => s.trim());
			},
			submitFilterFormAndOpenCompetencyDataView() {
				orbisAppSr.showLoadingOverlay();
				if (this.showCompetenciesFilterView) {
					this.submitFilterForm(this.openCompetencyDataView);
				} else {
					this.openCompetencyDataView();
				}
				orbisAppSr.hideLoadingOverlay();
			},
			openCompetencyDataView() {
				var request = {
					action: '<o:encrypt action="displayCompetencyReportDataView"/>',
					reportKey: 'termCourseCompetencyDevelopmentReport',
					termCourseId: this.row.id
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			openCompetencyFilterView(termCourseId) {
				orbisAppSr.showLoadingOverlay();
				$.post("", {
					action: '<o:encrypt action="displayTermCourseReports" subAction="displayFilters"/>',
					reportKey: 'termCourseCompetencyDevelopmentReport',
					forceSr: true,
					termCourseId
				}, (responseHtml, status, xhr) => {
					if (responseHtml) {
						this.filterForm = $(responseHtml).find('#filterForm')[0].outerHTML;
						this.showCompetenciesContainer = false;
						this.showCompetenciesFilterView = true;
						orbisAppSr.hideLoadingOverlay();
					}
				});
			},
			openCompetencyReportView(selectedReportType) {
				this.selectReportType(selectedReportType);
				this.showCompetenciesContainer = true;
				this.showCompetenciesFilterView = false;
			},
			submitFilterForm(callback) {
				const form = $('#filterForm');
				const formData = form.serialize();
				$.post("", formData, function () {
					callback();
				});
			},
			competencyGroupWithoutRoot(competenciesGroups) {
				const { root, ...rest } = competenciesGroups;
				return rest;
			},
			typeName(typeName) {
				const prefix = '<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.TypeName3977807038289705" />';
				return prefix + typeName;
			},
			stepName(stepName) {
				const prefix = '<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.StepName7198674551424901" />';
				return prefix + stepName;
			},
			createRecords(placeWithIndustryPartner, tctId) {
				var request = {
					action: '<o:encrypt action="displayTermCourseTypeBulkPlacementWizard"/>',
					tctId: tctId,
					placeWithIndustryPartner: placeWithIndustryPartner
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			downloadDocument(resourceId) {
				var request = {
					action: '<o:encrypt action="downloadTermCourseResource"/>',
					resourceId: resourceId,
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			editDocument(resourceId) {
				var request = {
					action: '<o:encrypt action="displayTermCourseResourceEdit"/>',
					resourceId: resourceId,
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			viewDocumentLogs(resourceId) {
				var request = {
					action: '<o:encrypt action="displayTermCourseResourceLogs"/>',
					resourceId: resourceId,
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			deleteDocument(resourceId) {
				$.post("", {
					action: '<o:encrypt action="deleteTermCourseResource"/>',
					resourceId: resourceId,
				}, (data) => {
					this.refreshTermCourseData();
				}, "json");
			},
			startedClick(step) {
				this.openTermCourseStepStudentsByStatus(step, 1);
			},
			pendingClick(step) {
				this.openTermCourseStepStudentsByStatus(step, 0);
			},
			declinedClick(step) {
				this.openTermCourseStepStudentsByStatus(step, 4);
			},
			openTermCourseStepStudentsByStatus(step, status) {
				var request = {
					action: '<o:encrypt action="displayTermCourseStepStudents"/>',
					status: status,
					tctStepId: step.id
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			placeStudentInOpportunity(expTypeId) {
				var request = {
					action: '<o:encrypt action="displayMassPlacementTool"/>',
					tctId: expTypeId,
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			displayTermCourseTags() {
				var request = {
					action: '<o:encrypt action="displayTermCourseTags"/>',
					tcId: this.row.id,
				};
				orbisApp.buildForm(request, "", true).submit();
			},
			displayTermCourseConfig() {
				var request = {
					action: '<o:encrypt action="displayTermCourseConfig"/>',
					tcId: this.row.id
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			displayTermCourseExperienceTypesConfig() {
				var request = {
					action: '<o:encrypt action="displayTermCourseExperienceTypesConfig"/>',
					tcId: this.row.id
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			approveTermCoursePendingStudents() {
				var request = {
					action: '<o:encrypt action="approveTermCoursePendingStudents"/>',
					tcId: this.row.id
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			displayEnrollTermCourseStudents() {
				var request = {
					action: '<o:encrypt action="displayEnrollStudentsWizard"/>',
					termCourseId: this.row.id
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			displayNewPostingWizard() {
				var request = {
					action: '<o:encrypt action="displayAddOpportunityWizard"/>',
					tcId: this.row.id
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			displayEmailTermCourseStudents() {
				var request = {
					action: '<o:encrypt action="displayEmailTermCourseStudents"/>',
					tcId: this.row.id
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			displayTermCourseResourceEdit() {
				var request = {
					action: '<o:encrypt action="displayTermCourseResourceEdit"/>',
					tcId: this.row.id
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			displayTermCourseAccess() {
				var request = {
					action: '<o:encrypt action="displayTermCourseAccess"/>',
					tcId: this.row.id
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			displayCloneTermCourse() {
				var request = {
					action: '<o:encrypt action="displayCloneTermCourse"/>',
					tcId: this.row.id
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			changeStatus() {
				orbisAppSr.showLoadingOverlay();
				$.post("", {
					action: '${o:encrypt("updateTermCourseStatus", currentUser)}',
					tcId: this.row.id,
					status: this.newStatus,
					fromDataviewer: true
				}, (data, status, xhr) => {
					if (data.success) {
						this.courseData.status = this.newStatus;
						this.row.data.status.value = this.newStatus;
						this.showCourseStatusModal = false;
						orbisAppSr.hideLoadingOverlay();
					}
				}, "json");
			},
			displaySearch(type) {
				var request = {
					action: '<o:encrypt action="search" subAction="search"/>',
					tcId: this.row.id,
					selectedTerm: this.courseData.termId,
					searchType: type,
					expType: this.searchOpportunityType
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			displaySearchOpportunities() {
				var request = {
					action: '<o:encrypt action="search" subAction="search"/>',
					tcId: this.row.id,
					searchType: "postings",
					uiForm: true,
					expTypeId: this.searchOpportunityType
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			displaySelectQuestionnaireSearch() {
				var request = {
					action: '<o:encrypt action="displaySelectQuestionnaireSearch" />',
					tcId: this.row.id,
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			displayTermCourseStepProgress(type) {
				var request = {
					action: '<o:encrypt action="displayTermCourseStepProgress" />',
					tcId: this.row.id,
					stepType: type,
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			bulkAddFacultyToTermCourse() {
				orbisAppSr.showLoadingOverlay();
				$.post("", {
					action: '${o:encrypt("bulkAddFacultyMemberToTermCourse", currentUser)}',
					termCourseId: this.row.id,
					facultyUsernames: this.termCourseFacultyList,
					fromDataViewer: true
				}, (data, status, xhr) => {
					if (data.success) {
						this.updateFaculties();
						this.closeAddTermCourseFacultyModal();
						this.courseData.facultyCount += data.added;
						orbisAppSr.hideLoadingOverlay();
						orbisAppSr.showNotification("<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Facultymem4987137599410561" />", "success");
					}
				}, "json");
			},
			updateFaculties() {
				$.post("", {
					action: '${o:encrypt("ajaxGetTermCourseFaculties", currentUser)}',
					termCourseId: this.row.id,
				}, (data, status, xhr) => {
					orbisApp.hideLoadingOverlay()
					if (orbisAppSr.checkAjaxResponse(xhr))
						this.faculties = data.termCourseFaculties;
				}, "json");
			},
			bulkAddCoordinatorsToTermCourse() {
				orbisAppSr.showLoadingOverlay();
				$.post("", {
					action: '${o:encrypt("bulkAddCoordinatorsToTermCourse", currentUser)}',
					termCourseId: this.row.id,
					coordinatorUsernames: this.termCourseCoordinatorsList,
					fromDataViewer: true
				}, (data, status, xhr) => {
					if (data.success) {
						this.updateCoordinators();
						this.closeAddTermCourseCoordinatorModal();
						this.courseData.coordinatorsCount += data.added;
						orbisAppSr.hideLoadingOverlay();
						orbisAppSr.showNotification("<orbis:message code="i18n.exp_homeActiveCourses_docViewerDocument.Coursemana3082433071307628" />", "success");
					}
				}, "json");
			},
			updateCoordinators() {
				$.post("", {
					action: '${o:encrypt("ajaxGetTermCourseCoordinators", currentUser)}',
					termCourseId: this.row.id,
				}, (data, status, xhr) => {
					orbisApp.hideLoadingOverlay()
					if (orbisAppSr.checkAjaxResponse(xhr))
						this.coordinators = data.coordinators;
				}, "json");
			},
			displayInteractionNoteEdit() {
				var request = {
					action: '<o:encrypt action="displayInteractionNoteEdit" />',
					termCourseId: this.row.id,
					type: 'EXPTERMCOURSE',
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			displayInteractionTaskEdit() {
				var request = {
					action: '<o:encrypt action="displayInteractionTaskEdit" />',
					termCourseId: this.row.id,
					type: 'EXPTERMCOURSE',
					taskType: 'EXPTERMCOURSE'
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			displayInteractionFormEdit() {
				var request = {
					action: '<o:encrypt action="displayInteractionFormEdit" />',
					termCourseId: this.row.id,
					type: 'EXPTERMCOURSE'
				};
				orbisApp.buildForm(request, '', true).submit();
			},
			closeAddTermCourseFacultyModal() {
				this.showAddFacultyModal = false;
				this.termCourseFacultyList = '';
			},
			handleTermCourseFacultySearch(searchText, loading) {
				if(!searchText) {
					this.termCourseFacultySearchResults = [];
					return;
				}
				loading(true);
				$.post("", {
					action: '<o:encrypt action="lookupFacultyMembers"/>',
					term: searchText,
					minLength: 3,
					random: Math.random() * 10000,
				}, (data) => {
					if(!data) return;
					this.termCourseFacultySearchResults = data;
					loading(false);
				}, "json");
			},
			handleTermCourseFacultySelected({id}) {
				this.termCourseFacultyList += id + "\r\n";
			},
			closeAddTermCourseCoordinatorModal() {
				this.showAddTermCourseCoordinator = false;
				this.termCourseCoordinatorsList = '';
			},
			handleTermCourseCoordinatorSearch(searchText, loading) {
				if(!searchText) {
					this.termCourseCoordinatorSearchResults = [];
					return;
				}
				loading(true);
				$.post("", {
					action: '<o:encrypt action="lookupCourseCoordinators"/>',
					term: searchText,
					minLength: 3,
					random: Math.random() * 10000,
				}, (data) => {
					if(!data) return;
					this.termCourseCoordinatorSearchResults = data;
					loading(false);
				}, "json");
			},
			handleTermCourseCoordinatorSelected({id}) {
				this.termCourseCoordinatorsList += id + "\r\n";
			},
			deleteTermCourse() {
				var request = {
					action: '<o:encrypt action="deleteTermCourse" />',
					tcId: this.row.id
				};
				orbisApp.buildForm(request, '').submit();
			},
		},

	    computed: {
		    termCourseStatusLabel() {
			    switch (this.courseData.status) {
				    case 0: return '<orbis:message code="i18n.exp_termCourseDetails.Open4784506349984699" />';
				    case 1: return '<orbis:message code="i18n.exp_termCourseDetails.Closed5455247119578263" />';
				    case 2: return '<orbis:message code="i18n.exp_termCourseDetails.Archived7484151490498773" />';
			    }
		    },

		    reportPlacementPercentage() {
			    const recordCount = this.reportsData.recordCount;
			    const requiredExperiencesCount = this.reportsData.requiredExperiencesCount;
			    const studentCount = this.reportsData.studentCount;

			    const percentage = requiredExperiencesCount && studentCount
					    ? recordCount / (requiredExperiencesCount * studentCount) * 100
					    : 0

			    return percentage.toFixed();
		    },

		    reportCompletionPercentage() {
			    const studentCount = this.reportsData.studentCount;
				const percentage = studentCount
						? this.reportsData.completedStudentsCount / studentCount * 100
						: 0;
			    return percentage.toFixed();
		    },

		    isReportDataNotEmpty() {
			    return Object.keys(this.reportsData).length > 0;
		    }

	    }
    };
</script>

<style>
	.gap--8 {
		gap: 8px
	}
	.modal__btn--close-posting {
		background: #444;
		color: #FFFFFF;
		height: 72px !important;
		width: 64px;
		position: absolute;
		top: 0;
		right: 0;
		z-index: 5;
	}

	.workflow-steps-reorder-btn {
		background: none;
		color: #232323;
		border: none;
		box-shadow: none !important;
	}

	.workflow-options--save-btn {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 64px;
	}

	@media (min-width: 1024px) {
		.doc-viewer__document-content {
			max-width: 100%;
			padding-bottom: 0!important;
		}
		.dashboard-header--mini__content--right {
			max-width: 304px;
		}
	}

	.sidebar--action__content {
		display: flex;
		flex-direction: column;
		height: 100%;
	}

	.steps-tab {
		overflow-y: auto;


		.steps-cards-list {
			overflow-y: auto;
		}
	}



	/*Report summary grid*/

	.muuri-container {
		position: relative;
	}
	.item {
		display: block;
		position: absolute;
		z-index: 1;
	}
	.item-content {
		position: relative;
	}

	.status-chart {
		height: 617px;
	}

	.experiences {
		height: 304px;
	}

	.reports-wrapper {
		flex-direction: column;
		gap: 16px;
	}

	@media (min-width: 1024px) {
		.reports-wrapper {
			flex-direction: row;
		}
	}

	.top-partners {
		width: 324px;
	}

	/*Term course placements grid*/

	.term-course-placements {
		display: grid;
		grid-template-columns: repeat(3, 304px);
		grid-template-rows: 304px;
		gap: 32px;
	}

	.active-courses-insights {
		gap: 16px;
		justify-content: center;
	}

	@media (min-width: 1024px) {
		.active-courses-insights {
			justify-content: flex-start;
		}
	}

	.actions--button {
 		width: 64px;
		height: 64px;
	}

	.progress__bar__btn {
		background-color: unset;
		color: rgba(0, 0, 0, 0.66);
		padding: 8px 10px;
		font-size: 14px;
		width: fit-content;
	}
</style>
