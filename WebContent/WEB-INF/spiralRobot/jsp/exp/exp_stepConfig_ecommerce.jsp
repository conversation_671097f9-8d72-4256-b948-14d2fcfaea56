<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>
<%-- <ui:isConverted toolConversion="true" type="complete" />   --%>

<c:if test="${configSaved}">
	<ui:notification type="success">
		<orbis:message code="i18n.exp_termsAndConditionsStepConfig.configurationSavedSuccessfully" />
	</ui:notification>
</c:if>

<ui:navBack>
	<ui:navBackItem action="displayHome">
		<orbis:message code="i18n.common.backToHome" />
	</ui:navBackItem>
   	<c:if test="${empty step.template}">
	   	<ui:navBackItem action="displayTermCourse" tcId="${step.tct.termCourse.id}">
			<orbis:message code="i18n.exp_termCourseTypeDetails.BacktoTermCourse" />
		</ui:navBackItem>
		<ui:navBackItem action="displayTermCourseTypeSteps" tctId="${step.tct.id}">
			<orbis:message code="i18n.exp_stepConfig_appointment.BackToMana2642912009367091" />
		</ui:navBackItem>
	</c:if>
	<c:if test="${not empty step.template && moduleUser.expAdminOrExpCoordinatorOrCourseCoordinator}">
   		<ui:navBackItem action="displayWorkflowTemplateSteps" templateId="${step.template.id}">
   			<orbis:message code="i18n.exp_stepConfig_appointment.BackToTemp6218826827474444" />
   		</ui:navBackItem>
    </c:if>
</ui:navBack>
<ui:header i18n_subtitle="${ isL1 ? step.tct.termCourse.termCourseName : step.tct.termCourse.l2TermCourseName }">
	<orbis:message arguments="${isL1 ? step.label : step.l2Label}" code="i18n.exp_stepConfig_trackHours.Managesite3471631908362808" />
</ui:header>

<div class="orbis-posting-actions">
	<center>
		<ui:button classes="saveBtn" onclick="$('form#saveStepForm').submit()"><orbis:message code="i18n.common.save" /></ui:button>
	</center>
</div>

<script type="text/javascript">
	$(document).ready(function(){
		$(".moduleRadio").change(function(){
			$("#productChecks").removeClass("display--none");
			$(".module" + $(this).val()).removeClass("display--none");
			$(".module" + $(this).val()).children().prop("disabled", false);
			$(".typeCheckbox").not(".module" + $(this).val()).addClass("display--none");
			$(".typeCheckbox").not(".module" + $(this).val()).children().prop("disabled", true);
		});
	});
</script>

<ui:panel>
	<ui:form id="saveStepForm" tctStepId="${step.id}" action="saveEcommerceStep">
		<fieldset>
			<legend class="label margin--b-m"><orbis:message code="i18n.exp_stepConfig_appointment.Label2158984152441877" /></legend>
			<ui:textbox type="text" name="label" id="label" value="${step.label}">${langLabelL1}</ui:textbox>
			<div class="${isBilingualModeOn ? '' : 'display--none'}">
				<ui:textbox type="text" name="l2Label" id="l2Label" value="${step.l2Label}" >${langLabelL2}</ui:textbox>
			</div>
		</fieldset>
		<fieldset>
			<legend class="label margin--b--m"><orbis:message code="i18n.exp_stepConfig_ecommerce.Instructio7980876000543191" /></legend>
			<ui:textarea type="rich" name="instructions" id="instructions" i18n_title="${langLabelL1}">${step.instructions}</ui:textarea>
			<div class="${isBilingualModeOn ? '' : 'display--none'}">
				<ui:textarea type="rich" name="l2Instructions" id="l2Instructions" i18n_title="${langLabelL2}">${step.l2Instructions}</ui:textarea>
			</div>
		</fieldset>
		<div class="margin--t--l">
			<ui:checkbox id="optional" name="optional" i18n_helpText="i18n.exp_stepConfig_other.Enablingth6704229047751898" checked="${step.optional}">
				<orbis:message code="i18n.exp_stepConfig_other.OptionalSt2818084034338378" />
			</ui:checkbox>
		</div>
		<c:set var="currentStore" value=""/>
		<ui:radioGroup name="moduleRadio" i18n_title="Online Store">
			<c:forEach var="p" items="${experientialProducts}">
				<c:if test="${currentStore != p.category.module.id}">
					<c:set var="currentStore" value="${p.category.module.id}"/>
					<ui:radioButton name="moduleRadio" classes="moduleRadio"  value="${p.category.module.id}">${p.category.module.moduleName}</ui:radioButton>
				</c:if>
			</c:forEach>
		</ui:radioGroup>
		<ui:checkboxGroup id="productChecks" i18n_title="i18n.exp_stepConfig_ecommerce.RequiredPu0179082338852627">
			<c:forEach var="p" items="${experientialProducts}">
				<ui:checkbox name="requiredPurchase" classes="module${p.category.module.id}" value="${p.id}" checked="${o:contains(selectedProducts, p.id)}">
					${p.category.name} - ${p.label}
					<c:if test="${o:contains(selectedProducts, p.id)}">
						<script type="text/javascript">
							$(document).ready(function(){
								$(".moduleRadio[value='${p.category.module.id}']").prop("checked", true);
								$(".moduleRadio[value='${p.category.module.id}']").change();
							});
						</script>
					</c:if>
				</ui:checkbox>
			</c:forEach>
		</ui:checkboxGroup>
	</ui:form>
</ui:panel>
<div class="orbis-posting-actions">
	<center>
		<ui:button classes="saveBtn" onclick="$('form#saveStepForm').submit()"><orbis:message code="i18n.common.save" /></ui:button>
	</center>
</div>
