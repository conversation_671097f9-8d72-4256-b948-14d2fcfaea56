<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<style>
	.message {
		width: 300px;
	}

	.pointer {
		cursor: pointer;
	}
</style>

<script>
	var player;

	function initVideos() {
		if($("div.youtubeVideo").length)
		{
			if (player !== undefined) {
				onYouTubeIframeAPIReady();
			}
			else {
				orbisScriptManager.loadScript('https://www.youtube.com/iframe_api');
			}
		}
	}

	function onYouTubeIframeAPIReady() {
		$("div.youtubeVideo").each(function () {
			player = new YT.Player(this.id, {
				videoId: this.id,
				height: $(this).data("youtube-video-height"),
				width: $(this).data("youtube-video-width"),
				origin: window.location.origin
			});
		});

		orbisScriptManager.loadScript('/core/orbis/scripts/fitVids/fitVids.js', function () {
			fitvids({
				ignore: [
					':not(.youtubeVideo)',
					'.youtubeVideo[data-youtube-video-height]',
					'.youtubeVideo[data-youtube-video-width]'
				]
			});
		});
	}

	function openModal(modalId) {
		$('#messagePreview' + modalId).uiShow();
		initVideos();
	}
</script>

<c:set var="dashboard" value="true"/>
<c:set var="hideActions" value="true"/>
<c:set var="subtitle" value="i18n.dashboard_messages.Messages8110243960017825"/>
<%@ include file="../dashboard_staffTitle.jsp"%>

<ui:grid>
	<ui:gridCol width="12">
		<div id="legacyDashboardMessagesContainer">

			<c:if test="${empty messages}">
				<ui:note type="info">
					<orbis:message code="i18n.dashboard_messages.Nomessagesfound"/>
				</ui:note>
			</c:if>
			<c:if test="${not empty messages}">

				<div class="container--table">
					<table class="table zebra width--100 ">
						<caption class="table__caption"><orbis:message
								code="i18n.dashboard_messages.Messages8110243960017825"/></caption>
						<thead class="table__header">
						<tr class="table__row--header">
							<th class="table__heading"><orbis:message code="i18n.dashboard_messages.Message"/></th>
							<th class="table__heading"><orbis:message
									code="i18n.dashboard_messages.LiveOn4106918867198850"/></th>
							<th class="table__heading"><orbis:message
									code="i18n.dashboard_messages.ExpireOn2177869985682286"/></th>
							<th class="table__heading"><orbis:message code="i18n.dashboard_messages.Dashboard"/></th>
						</tr>
						</thead>
						<tbody>
						<c:forEach var="m" items="${messages}">
							<tr class="table__row--body" data-message-id="${m.id}">
								<td class="table__value">
									<div class="display--flex align--middle">
										<i class="material-icons margin--r--s font--20 pointer" onclick="openModal(${m.id})">
											open_in_new
										</i>

										<div class="message">
											<div class="text--bold">${m.title}</div>
											<orbis:truncatedValue charCount="40" value="${m.message}"/>
										</div>
									</div>
								</td>
								<td class="table__value">
									<c:if test="${m.liveDate != null}">${m.liveDate}</c:if>
									<c:if test="${m.liveDate == null}"><orbis:message
											code="i18n.dashboard_messages.NA1350317814017753"/></c:if>
								</td>
								<td class="table__value">
									<c:if test="${m.expireDate != null}">${m.expireDate}</c:if>
									<c:if test="${m.expireDate == null}"><orbis:message
											code="i18n.dashboard_messages.NA1350317814017753"/></c:if>
								</td>
								<td class="table__value">
									<c:forEach var="messageUserGroup" items="${m.userGroups}">
										<c:set var="visibility"><spring:message code="i18n.dashboard_messages.All"/></c:set>
										<c:if test="${not empty messageUserGroup.permissions}">
											<c:set var="visibility"><spring:message code="i18n.dashboard_messages.Limited"/></c:set>
										</c:if>
										<p>${messageUserGroup.userGroup.name} : ${visibility}</p>
									</c:forEach>
								</td>
							</tr>

						</c:forEach>
						</tbody>
						<tfoot>
						<tr class="table__row--footer">
							<td class="table__value--footer" colspan="4"><orbis:message
									code="i18n.dashboard_messages.Messages8110243960017825"/></td>
						</tr>
						</tfoot>
					</table>
				</div>
			</c:if>

		</div>

		<c:forEach var="m" items="${messages}">
			<ui:modal id="messagePreview${m.id}"
			          i18n_title="i18n.dashboard_legacyMessages.MessagePre1962235296135266">
				<div>${m.message}</div>
			</ui:modal>
		</c:forEach>

	</ui:gridCol>
</ui:grid>
