<%@ include file="/WEB-INF/jsp/include.jsp"%>

<script type="text/javascript">
	const process = {env: {NODE_ENV: "production"}};
</script>

<orbis:addComponent component="animate" />
<orbis:addComponent component="srComponents" />
<orbis:addComponent component="calendarExplorer" />
<orbis:addComponent component="vue3PerfectScrollbar" />

<%@ include file="dashboard_userSidebar_calendar.vue.jsp"%>
<%@ include file="dashboard_userSidebar_forms.vue.jsp"%>

<style type="text/css">
	.floatMe {
		position: absolute;
		left: 0;
		right: 0;
	}

	.floating--action-bar button.is--active, .floating--action-bar a.is--active {
	    background: rgba(0,0,0,0.9);
	}

	#userSidebarContent {
		height: 100%;
	}
</style>


<div id="userSidebarContent">

</div>

<template id="userSidebarTemplate">
	<perfect-scrollbar style="height: 100%;">
		<div style="padding-bottom: 96px;">

				<div v-show="currentView == 'calendar'" style="animation-duration: 300ms;">
					<calendar />
				</div>
				<div v-show="currentView == 'forms'" style="animation-duration: 300ms;">
					<forms />
				</div>
		</div>
	</perfect-scrollbar>

	<ui-actions-bar class="position--absolute" force-label-hide="true">
		<ui-actions-bar-item icon="calendar_month" @click="currentView = 'calendar'" :class="{'is--active': currentView == 'calendar'}">
			My Calendar
		</ui-actions-bar-item>

		<ui-actions-bar-item v-if="userFormsWidgetPermission" id="sidebar-forms-btn" icon="assignment"  @click="currentView = 'forms'" :class="{'is--active': currentView == 'forms'}">
			Forms
		</ui-actions-bar-item>
	</ui-actions-bar>
</template>

<script type="text/javascript">

	const userSidebarVue = {
		template: '#userSidebarTemplate',

		components: {
			calendar: userSidebarCalendar,
			forms: userSidebarForms
		},

		data: () => ({
			currentView: 'calendar',
			userFormsWidgetPermission: ${not empty userFormsWidgetPermission ? userFormsWidgetPermission : false},
		}),

		watch: {
			currentView(){
				window.changeProdSidebarTitle(this.getCurrentViewTitle());
			}
		},

		methods: {
			getCurrentViewTitle(){
				switch (this.currentView) {
					// case 'notifications':
					// 	return 'Notifications'
					case 'calendar':
						return 'Calendar'
					case 'forms':
						return 'Forms'
					default:
						return '';
				}
			}
		},

		mounted(){
			window.changeProdSidebarTitle(this.getCurrentViewTitle());
		}
	}

	const app = Vue.createApp(userSidebarVue);
	app.use(CalendarExplorer);
	app.use(SRComponents);
	app.use(Vue3PerfectScrollbar.default);
	window.calendarExplorerApp = app.mount("#userSidebarContent");

	window.changeView = function(view) {
		window.calendarExplorerApp.currentView = view;
	}
</script>
