<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<style>
  .icon-60 {
    font-size: 60px;
  }
  .read-btn {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: end;
  }

  .card-message img {
    display: none;
  }

  .card-message .youtubeVideo {
    display: none;
  }
</style>

<orbis:addComponent component="perfectScrollbar" callback="$('#dashboard-messages-container').perfectScrollbar()" />

<div class="display--flex align--middle padding--t--s padding--l--s margin--b--s margin--t--m">
  <h3 class="margin--b--none padding--l--xs">
    <orbis:message code="i18n.dashboard_studentHome.Announceme4984979357736865" />
  </h3>
</div>

<ul id="dashboard-messages-container" class="padding--l--s card-group__cards list--plain display--flex">

  <c:forEach var="message" items="${dashboardMessages}">

    <li class="card--round is--big margin--b--m has--description record">

      <div class="card--round__top position--relative">
        <c:set var="filePath" value="${o:getFilePath(message.postImage, true)}" />
        <c:if test="${not empty filePath}">
          <img class="card--round__background--img width--100 no-after" style="object-fit: cover;"
               src="${filePath}" alt="Image">
        </c:if>
        <c:if test="${empty filePath}">
          <div class="display--flex align--center align--middle height--100">
            <i class="material-icons icon-60">image</i>
          </div>
        </c:if>
      </div>


      <div class="card--round__bottom position--relative overflow--hidden">
        <div class="display--flex dist--between align--middle margin--b--xs">
          <h4 class="text--default">${message.title}</h4>
          <h6 class="color--font--warning">
            <fmt:formatDate value="${message.liveDate}" pattern="MMM dd"/>
          </h6>
        </div>

        <div class="card-message font-size--small text-align--left">
            ${message.message}
        </div>

        <div class="read-btn">
          <ui:button type="success" style="pill" action="displayDashboardMessage"
                     messageId="${message.id}">
            <orbis:message code="i18n.dashboard_studentHome.Read6649535457266531" />
          </ui:button>
        </div>
      </div>

    </li>

  </c:forEach>

</ul>

<div class="padding--l--s margin--b--m">
  <ui:button type="success" style="outlinePill" action="displayUserDashboardMessages"><orbis:message code="i18n.dashboard_studentHome.ViewAll1699183741484851" /></ui:button>
</div>
