<%@ include file="/WEB-INF/jsp/include.jsp"%>

<template id="calendarSectionTemplate">
	<calendar-explorer dark-mode :events="events" :types="typesConfig" @event-click="eventClick"></calendar-explorer>
</template>

<script type="text/javascript">

 	const userSidebarCalendar = {
		template: "#calendarSectionTemplate",
	    data() {
		    const params = {
			    action: '${o:encrypt("ajaxLoadMyCalendarEvents", currentUser)}',
			    calendarExplorer: true,
			    skipFilters: true
		    };

		    return {
			    events: {
				    url: '',
				    params: params
			    },
			    typesConfig: ${calendarTypesConfig}
		    };
	    },
	    methods: {
			eventClick(eventObj) {
				window.open(eventObj.url, '_blank');
			}
	    }
	};

</script>