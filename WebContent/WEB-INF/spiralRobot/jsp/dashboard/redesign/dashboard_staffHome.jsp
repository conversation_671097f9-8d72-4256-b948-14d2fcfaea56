<%@ include file="/WEB-INF/jsp/include.jsp"%>

<style>
    .toggle-productivity-sidebar-actions {
        z-index: 9;
        transition: bottom 0.2s ease-in-out;
    }

    .footer--visible .toggle-productivity-sidebar-actions {
        bottom: 80px !important;
    }

    @media screen and (max-width: 700px) {
        .sidebar--action.calendar__sidebar {
            width: 100%;
        }

        .toggle-productivity-sidebar-actions {
            left: auto;
            transform: none;
            right: 90px;
            height: 60px;
        }
        .toggle-productivity-sidebar-actions button {
            height: 60px;
        }
    }
</style>

<script>
    function changeProdSidebarTitle(title) {
        $("#userSidebar .modal__title-posting h4").text(title);
    }
</script>

<ui:productivity>

    <ui:productivitySidebar id="userSidebar" i18n_title="i18n.dashboard_staffHome.Calendar7819175933315668">
        <%@ include file="dashboard_userSidebar.jsp" %>
    </ui:productivitySidebar>

    <c:set var="headerTheme" value="transparent" />
    <%@ include file="../dashboard_staffTitle.jsp"%>

    <c:if test="${o:canViewDashboardItem('pc8', currentUser, siteElement.contentItem) && not empty dashboardMessages}">
        <%@ include file="dashboard_dashboardMessagesPanel.jsp" %>
    </c:if>

    <%@ include file="dashboard_staffHomeWidgets.jsp"%>

</ui:productivity>

<ui:actionsBar classes="toggle-productivity-sidebar-actions js--toggle-productivity-sidebar">
    <ui:actionsBarItem icon="calendar_month" show="userSidebar">
        <orbis:message code="i18n.dashboard_alumniHome.Calendar2790451143687124" />
    </ui:actionsBarItem>
</ui:actionsBar>
