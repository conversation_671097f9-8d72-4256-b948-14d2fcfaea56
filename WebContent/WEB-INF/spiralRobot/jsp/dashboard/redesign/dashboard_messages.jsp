<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<orbis:addComponent component="dataTables"/>

<script type="text/javascript">
	var controllerPath = "${siteElement.fullPath}.htm";

	$(document).ready(function() {
		loadMessages();
	});

	function updateMessagesOrder(orderList) {
		var request = new Object();
		request.action = '<o:encrypt action="updateDashboardMessagesOrder" />';
		request.orderList = JSON.stringify(orderList);
		$("#dashboardMessagesContainer").load(
				controllerPath,
				request,
				initSorting
		)
	}

	function loadMessages() {
		var request = new Object();
		request.action = '<o:encrypt action="ajaxLoadDashboardMessages" />';
		$("#dashboardMessagesContainer").load(
				controllerPath,
				request,
				initSorting
		)
	}

	function fixHelper(e, ui)
	{
		ui.children().each(function() {
			$(this).width($(this).width());
		});
		return ui;
	}

	function initSorting(){
		var $tbody = $("#dashboardMessagesContainer tbody");
		$tbody.sortable({
			helper : fixHelper,
			start: function(e, ui){
				ui.placeholder.height(ui.item.height());
			},
			update : function(event, ui) {
				var orderList = $tbody.sortable("toArray", {attribute: "data-message-id"});
				updateMessagesOrder(orderList);
			}
		});
	}
</script>

<style>
	.doc-viewer--page-header {
		padding: 16px 8px;
		background: #232323;
	}

	.ignore--main-padding {
		transform: translateX(-16px);
		width: calc(100% + 32px);
	}
</style>

<c:if test="${not empty deleteSuccess}">
	<ui:notification type="success"><orbis:message code="i18n.dashboard_messages.DeletedSuccessfully" /></ui:notification>
</c:if>

<c:set var="dashboard" value="true"/>
<c:set var="hideActions" value="true"/>

<%@ include file="../dashboard_staffTitle.jsp"%>

<ui:actionsGroup i18n_title="i18n.common.actions">
	<ui:actionsGroupItem action="displayLegacyDashboardMessages">
		<orbis:message code="i18n.dashboard_messages.LegacyMess8860516646156817" />
	</ui:actionsGroupItem>
	<ui:actionsGroupItem action="displayDashboardMessageEdit">
		<orbis:message code="i18n.dashboard_messages.CreateaMessage"/>
	</ui:actionsGroupItem>
</ui:actionsGroup>

<ui:grid>
	<ui:gridCol width="12">
		<div id="dashboardMessagesContainer"></div>
	</ui:gridCol>
</ui:grid>
