<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<c:set var="isRoot" scope="request" value="${currentUser.root}" />
<c:set var="hasSystemConfig" scope="request" value="${not empty currentUser.assignedTypes['System Configuration Rights']}" />

<orbis:addComponent component="srComponents" />
<orbis:addComponent component="momentjs" />
<orbis:addComponent component="vuedraggable" />

<orbis:message var="fullLangLabelL1" scope="request" code="i18n.common.language.${orbisDefaultLocale}" />
<orbis:message var="fullLangLabelL2" scope="request" code="i18n.common.language.${isBilingualModeOn ? orbisSecondaryLocale : 'fr'}" />

<style>
    .dashboard__config--section:nth-child(odd) {
        background-color: #e5e5e5;
    }

	.dashboard__config--section__subgroup:nth-child(odd) {
        background-color: rgba(0, 0, 0, 0.10);
	}

	.dashboard__config--section.ui__checkbox .ui__checkbox__title{
		margin-bottom: 0 !important;
		margin-left: 4px;
		font-size: 16px;
	}

    .sub--category--toggle.ui__checkbox .ui__checkbox__title{
        margin-bottom: 0 !important;
        margin-left: 4px;
    }
</style>

<%@ include file="/WEB-INF/jsp/ckeditor.vue.jsp"%>

<%-- Hit a jsp size limit while using the directive include, switched to jsp:include --%>
<%-- Config Fields --%>
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_titlePermissionField.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_messagesLocationField.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_widgetConfigField.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_staffWidgetSubGroupConfigField.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_selectAll.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_sortSidebar.vue.jsp" />

<%-- Config Pages --%>
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_studentCommonOptions.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_studentTabsConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_studentWidgetConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_studentActionBarConfig.vue.jsp" />

<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_alumniCommonOptionsConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_alumniTabsConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_alumniWidgetConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_alumniActionBarConfig.vue.jsp" />

<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_employerCommonOptionsConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_employerTabsConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_employerWidgetConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_employerActionBarConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_employerJobPostingModuleConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_employerExpModuleConfig.vue.jsp" />

<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_facultyCommonOptionsConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_facultyTabsConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_facultyActionBarConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_facultyWidgetConfig.vue.jsp" />

<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_staffDashboardViewsConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_staffCommonOptionsConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_staffTabsConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_staffWidgetConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_staffInteractionsWidgetConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_staffActionBarConfig.vue.jsp" />

<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_portalUserCommonOptionsConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_portalUserTabsConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_portalUserWidgetConfig.vue.jsp" />

<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_availablePermissionsConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_manageSiteDocumentsConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_documentTypeConfig.vue.jsp" />
<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_messagesConfig.vue.jsp" />

<c:if test="${hasSystemConfig}">
	<jsp:include page="/WEB-INF/spiralRobot/jsp/dashboard/moduleConfig/dashboard_newUiConfig.vue.jsp" />
</c:if>


<template id="dashboardModuleConfigTemplate">
	<ui-configuration-list v-model="configListToggle" show-group-in-header>

		<ui-configuration-list-item-group title="<orbis:message code="i18n.dashboard_moduleConfig.Student7253623267149884" htmlEscape="true" />">
			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardC4983795294412951" htmlEscape="true" />">
				<student-common-options-config :permission-map="studentPermissionsMap" />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardT8131430170192330" htmlEscape="true" />">

				<template #header-actions>
					<ui-button v-if="studentTabsConf && studentTabsConf.dataLoaded" type="white" btn-style="outlinePill" @click="studentTabsConf.showSortSidebar = !studentTabsConf.showSortSidebar">
						<i class="material-icons margin--r--xs">swap_vert</i>
						<orbis:message code="i18n.dashboard_moduleConfig.Sort1351655660128443" />
					</ui-button>
				</template>

				<student-tabs-config ref="studentTabsConf" :permission-map="studentPermissionsMap" />

			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardW5151564243583063" htmlEscape="true" />">
				<student-widget-config :permission-map="studentPermissionsMap" />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardA5060247728308311" htmlEscape="true" />">
				<student-action-bar-config />
			</ui-configuration-list-item>
		</ui-configuration-list-item-group>

		<ui-configuration-list-item-group title="<orbis:message code="i18n.dashboard_moduleConfig.Alumni3784150174687527" htmlEscape="true" />">
			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardC3950855880693479" htmlEscape="true" />">
				<alumni-common-options-config :permission-map="alumniPermissionsMap" />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardT2872378933014341" htmlEscape="true" />">

				<template #header-actions>
					<ui-button v-if="alumniTabsConf && alumniTabsConf.dataLoaded" type="white" btn-style="outlinePill" @click="alumniTabsConf.showSortSidebar = !alumniTabsConf.showSortSidebar">
						<i class="material-icons margin--r--xs">swap_vert</i>
						<orbis:message code="i18n.dashboard_moduleConfig.Sort1351655660128443" />
					</ui-button>
				</template>

				<alumni-tabs-config ref="alumniTabsConf" :permission-map="alumniPermissionsMap" />

			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardW8576088766935622" htmlEscape="true" />">
				<alumni-widget-config :permission-map="alumniPermissionsMap" />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardA6663389665895498" htmlEscape="true" />">
				<alumni-action-bar-config />
			</ui-configuration-list-item>
		</ui-configuration-list-item-group>

		<ui-configuration-list-item-group title="<orbis:message code="i18n.dashboard_moduleConfig.Employer1975581480919537" htmlEscape="true" />">
			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardC5006841220716769" htmlEscape="true" />">
				<employer-common-options-config :permission-map="employerPermissionsMap" />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardT2930360551839795" htmlEscape="true" />">
				<template #header-actions>
					<ui-button v-if="employerTabsConf && employerTabsConf.dataLoaded" type="white" btn-style="outlinePill" @click="employerTabsConf.showSortSidebar = !employerTabsConf.showSortSidebar">
						<i class="material-icons margin--r--xs">swap_vert</i>
						<orbis:message code="i18n.dashboard_moduleConfig.Sort1351655660128443" />
					</ui-button>
				</template>

				<employer-tabs-config ref="employerTabsConf" :permission-map="employerPermissionsMap" />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardW9086748885630164" htmlEscape="true" />">
				<employer-widget-config :permission-map="employerPermissionsMap" />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.JobPosting9704080199461996" htmlEscape="true" />">
				<employer-job-posting-module-config />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.Experienti1027768307190077" htmlEscape="true" />">
				<employer-exp-module-config />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardA0066014655003222" htmlEscape="true" />">
				<employer-action-bar-config />
			</ui-configuration-list-item>
		</ui-configuration-list-item-group>

		<ui-configuration-list-item-group title="<orbis:message code="i18n.dashboard_moduleConfig.StaffFacul3582803017484574" htmlEscape="true" />">
			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardC8222756229970526" htmlEscape="true" />">
				<faculty-common-options-config :permission-map="facultyPermissionsMap" />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardT5672687157419671" htmlEscape="true" />">

				<template #header-actions>
					<ui-button v-if="facultyTabsConf && facultyTabsConf.dataLoaded" type="white" btn-style="outlinePill" @click="facultyTabsConf.showSortSidebar = !facultyTabsConf.showSortSidebar">
						<i class="material-icons margin--r--xs">swap_vert</i>
						<orbis:message code="i18n.dashboard_moduleConfig.Sort1351655660128443" />
					</ui-button>
				</template>

				<faculty-tabs-config ref="facultyTabsConf" :permission-map="facultyPermissionsMap" />

			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardW2425421433399902" htmlEscape="true" />">
				<faculty-widget-config :permission-map="facultyPermissionsMap" />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardA6702678900435922" htmlEscape="true" />">
				<faculty-action-bar-config />
			</ui-configuration-list-item>
		</ui-configuration-list-item-group>

		<ui-configuration-list-item-group title="<orbis:message code="i18n.dashboard_moduleConfig.PortalStaf0978622237229894" htmlEscape="true" />">
			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardV3419857843711851" htmlEscape="true" />">
				<staff-dashboard-views-config />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardC6202875465842433" htmlEscape="true" />">
				<staff-common-options-config :permission-map="staffPermissionsMap" />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardT9888152991178795" htmlEscape="true" />">
				<template #header-actions>
					<ui-button v-if="staffTabsConf && staffTabsConf.dataLoaded" type="white" btn-style="outlinePill" @click="staffTabsConf.showSortSidebar = !staffTabsConf.showSortSidebar">
						<i class="material-icons margin--r--xs">swap_vert</i>
						<orbis:message code="i18n.dashboard_moduleConfig.Sort1351655660128443" />
					</ui-button>
				</template>

				<staff-tabs-config ref="staffTabsConf" :permission-map="staffPermissionsMap" />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardW1886664520248076" htmlEscape="true" />">
				<template #header-actions>
					<ui-button v-if="staffWidgetsConf && staffWidgetsConf.dataLoaded" type="white" btn-style="outlinePill" @click="staffWidgetsConf.showSortSidebar = !staffWidgetsConf.showSortSidebar">
						<i class="material-icons margin--r--xs">swap_vert</i>
						<orbis:message code="i18n.dashboard_moduleConfig.Sort1351655660128443" />
					</ui-button>
				</template>

				<staff-widget-config ref="staffWidgetsConf" :permission-map="staffPermissionsMap"></staff-widget-config>
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardI7197702112302714" htmlEscape="true" />">
				<template #header-actions>
					<ui-button v-if="staffInteractionWidgetsConf && staffInteractionWidgetsConf.dataLoaded" type="white" btn-style="outlinePill" @click="staffInteractionWidgetsConf.showSortSidebar = !staffInteractionWidgetsConf.showSortSidebar">
						<i class="material-icons margin--r--xs">swap_vert</i>
						<orbis:message code="i18n.dashboard_moduleConfig.Sort1351655660128443" />
					</ui-button>
				</template>

				<staff-interactions-widget-config ref="staffInteractionWidgetsConf" :permission-map="staffPermissionsMap" />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardA1716300499759816" htmlEscape="true" />">
				<staff-action-bar-config />
			</ui-configuration-list-item>
		</ui-configuration-list-item-group>

		<ui-configuration-list-item-group title="<orbis:message code="i18n.dashboard_moduleConfig.PortalUser4830775936960011" htmlEscape="true" />">
			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardC7684827240213132" htmlEscape="true" />">
				<portal-user-common-options-config :permission-map="portalUserPermissionsMap" />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardT2282288843019885" htmlEscape="true" />">

				<template #header-actions>
					<ui-button v-if="portalUserTabsConf && portalUserTabsConf.dataLoaded" type="white" btn-style="outlinePill" @click="portalUserTabsConf.showSortSidebar = !portalUserTabsConf.showSortSidebar">
						<i class="material-icons margin--r--xs">swap_vert</i>
						<orbis:message code="i18n.dashboard_moduleConfig.Sort1351655660128443" />
					</ui-button>
				</template>

				<portal-user-tabs-config ref="portalUserTabsConf" :permission-map="portalUserPermissionsMap" />

			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardW0766899149054667" htmlEscape="true" />">
				<portal-user-widget-config :permission-map="portalUserPermissionsMap" />
			</ui-configuration-list-item>
		</ui-configuration-list-item-group>

		<ui-configuration-list-item-group title="<orbis:message code="i18n.dashboard_moduleConfig.Permission8375243675361165" htmlEscape="true" />">
			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.AvailableP7508510270994619" htmlEscape="true" />">
				<available-permissions-config />
			</ui-configuration-list-item>
		</ui-configuration-list-item-group>

		<ui-configuration-list-item-group title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardM4573829846037315" htmlEscape="true" />">
			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DashboardM1226438898709656" htmlEscape="true" />">

				<template #header-actions>
					<ui-button type="white" btn-style="outlinePill" @click="goToLegacyMessages">
						<orbis:message code="i18n.dashboard_messages.LegacyMess8860516646156817" />
					</ui-button>

					<ui-button type="white" btn-style="outlinePill" class="margin--l--s" @click="createNewDashboardMessage">
						<orbis:message code="i18n.dashboard_messages.CreateaMessage"/>
					</ui-button>
				</template>

				<messages-config />

			</ui-configuration-list-item>
		</ui-configuration-list-item-group>

		<ui-configuration-list-item-group title="<orbis:message code="i18n.dashboard_moduleConfig.ManageSite3673577274409377" htmlEscape="true" />">
			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.Configurat7312002597338186" htmlEscape="true" />">
				<manage-site-documents-config />
			</ui-configuration-list-item>

			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.DocumentTy3587735679360999" htmlEscape="true" />">

				<template #header-actions>
					<ui-button type="white" btn-style="outlinePill" @click="addNewDocType">
						<orbis:message code="i18n.doc_manageDocTypes.AddNewType" />
					</ui-button>
				</template>

				<document-type-config />

			</ui-configuration-list-item>
		</ui-configuration-list-item-group>

<c:if test="${hasSystemConfig}">
		<ui-configuration-list-item-group title="<orbis:message code="i18n.dashboard_moduleConfig.NewUI2246520389789032" htmlEscape="true" />">
			<ui-configuration-list-item title="<orbis:message code="i18n.dashboard_moduleConfig.NewUISwitc8107186860056233" htmlEscape="true" />">
				<new-ui-config />
			</ui-configuration-list-item>
		</ui-configuration-list-item-group>
</c:if>

	</ui-configuration-list>
</template>

<div id="dashboardModuleConfigPlaceholder"></div>

<script type="text/javascript">

	const dashboardModuleConfigVue = {
		template: '#dashboardModuleConfigTemplate',
		components: {
			'student-common-options-config': studentCommonOptionsConfig,
			'student-tabs-config': studentTabsConfig,
            'student-widget-config': studentWidgetConfig,
            'student-action-bar-config': studentActionBarConfig,

			'alumni-common-options-config': alumniCommonOptionsConfig,
			'alumni-tabs-config': alumniTabsConfig,
            'alumni-widget-config': alumniWidgetConfig,
            'alumni-action-bar-config': alumniActionBarConfig,

            'employer-common-options-config': employerCommonOptionsConfig,
			'employer-tabs-config': employerTabsConfig,
            'employer-action-bar-config': employerActionBarConfig,
            'employer-widget-config': employerWidgetConfig,
            'employer-job-posting-module-config': employerJobPostingModuleConfig,
            'employer-exp-module-config': employerExpModuleConfig,

			'staff-dashboard-views-config': staffDashboardViewsConfig,
			'staff-common-options-config': staffCommonOptionsConfig,
            'staff-tabs-config': staffTabsConfig,
            'staff-widget-config': staffWidgetConfig,
            'staff-interactions-widget-config': staffInteractionsWidgetConfig,
            'staff-action-bar-config': staffActionBarConfig,

			'faculty-common-options-config': facultyCommonOptionsConfig,
			'faculty-tabs-config': facultyTabsConfig,
            'faculty-action-bar-config': facultyActionBarConfig,
            'faculty-widget-config': facultyWidgetConfig,

			'portal-user-common-options-config': portalUserCommonOptionsConfig,
			'portal-user-tabs-config': portalUserTabsConfig,
            'portal-user-widget-config': portalUserWidgetConfig,

			'available-permissions-config': availablePermissionsConfig,
			'manage-site-documents-config': manageSiteDocumentsConfig,
			'document-type-config': documentTypeConfig,
			'messages-config': messagesConfig,
<c:if test="${hasSystemConfig}">
			'new-ui-config': newUiConfig,
</c:if>
		},

		setup(props, { expose }){
			const configListToggle = Vue.ref(false);
            const studentPermissionsMap = ${not empty studentPermissionsMap ? studentPermissionsMap : '{}'};
            const alumniPermissionsMap = ${not empty alumniPermissionsMap ? alumniPermissionsMap : '{}'};
            const staffPermissionsMap = ${not empty staffPermissionsMap ? staffPermissionsMap : '{}'};
            const employerPermissionsMap = ${not empty employerPermissionsMap ? employerPermissionsMap : '{}'};
            const facultyPermissionsMap = ${not empty facultyPermissionsMap ? facultyPermissionsMap : '{}'};
            const portalUserPermissionsMap = ${not empty portalUserPermissionsMap ? portalUserPermissionsMap : '{}'};

            const studentTabsConf = Vue.ref(null);
            const alumniTabsConf = Vue.ref(null);
            const employerTabsConf = Vue.ref(null);
            const portalUserTabsConf = Vue.ref(null);
            const facultyTabsConf = Vue.ref(null);
            const staffTabsConf = Vue.ref(null);
            const staffWidgetsConf = Vue.ref(null);
            const staffInteractionWidgetsConf = Vue.ref(null);

            expose({
				toggle(){
                    configListToggle.value = !configListToggle.value;
				}
			});

            return {
                configListToggle,
				studentPermissionsMap,
				alumniPermissionsMap,
				staffPermissionsMap,
				employerPermissionsMap,
				facultyPermissionsMap,
				portalUserPermissionsMap,

                studentTabsConf,
                alumniTabsConf,
                employerTabsConf,
                portalUserTabsConf,
                facultyTabsConf,
                staffTabsConf,
                staffWidgetsConf,
                staffInteractionWidgetsConf,

				addNewDocType(){
					orbisApp.buildForm({
						action: '${o:encrypt("displayDocTypeEdit", currentUser)}',
					}).submit();
				},

				createNewDashboardMessage(){
					orbisApp.buildForm({
						action: '${o:encrypt("displayDashboardMessageEdit", currentUser)}',
					}).submit();
				},

				goToLegacyMessages(){
					orbisApp.buildForm({
						action: '${o:encrypt("displayLegacyDashboardMessages", currentUser)}',
					}).submit();
				}
			};
		}
    };

    const dashboardConfigApp = Vue.createApp(dashboardModuleConfigVue);
    dashboardConfigApp.use(SRComponents);

    dashboardConfigApp
    	.component("title-permission-field", titlePermissionField)
    	.component("messages-location-field", messagesLocationField)
		.component("widget-config-field", widgetConfigField)
		.component("staff-widget-sub-group-config-field", staffWidgetSubGroupConfigField)
		.component("select-all", selectAll)
		.component("sort-sidebar", sortSidebar);

    const dashboardConfigMountedApp = dashboardConfigApp.mount("#dashboardModuleConfigPlaceholder");

</script>