<%@ include file="/WEB-INF/spiralRobot/jsp/include.jsp"%>

<%-- old JSP reference: dashboard_messagesAjax.jsp --%>
<orbis:addComponent component="vuedraggable"/>

<template id="messagesConfigTemplate">

	<div class="overflow--hidden" v-if="!dashboardMessages.length">
		<ui-empty-state title="<orbis:message code="i18n.dashboard_messagesConfig.NoMessages2814609575798031" htmlEscape="true" />">
			<orbis:message code="i18n.dashboard_messagesConfig.Thereareno1844387770981714" />
		</ui-empty-state>
	</div>

	<div v-else class="table--config">
		<draggable tag="table"
		           v-model="dashboardMessages"
		           item-key="id"
		           @change="updateOrder"
		           class="table zebra width--100">

			<template #item="{element: message}">
				<tbody>
					<tr class="table__row--body">
						<td class="table__value">
							<ui-button @click="editMessage(message)"
							           tooltip="<orbis:message code="i18n.dashboard_messagesConfig.Edit5346451553663370" htmlEscape="true" />"
							           size="small" btn-style="outlinePill"
							           aria-label="<orbis:message code="i18n.dashboard_messagesConfig.Edit5346451553663370" htmlEscape="true" />">
								<i class="material-icons">edit</i>
							</ui-button>

							<ui-button @click="deleteMessage(message)"
							           tooltip="<orbis:message code="i18n.dashboard_messagesConfig.Delete3143072234321877" htmlEscape="true" />"
							           class="margin--l--s" size="small" ref="editButtons" type="error"
							           btn-style="outlinePill"
							           aria-label="<orbis:message code="i18n.dashboard_messagesConfig.Delete3143072234321877" htmlEscape="true" />">
								<i class="material-icons">delete</i>
							</ui-button>
						</td>

						<td class="table__value">
							<div v-for="ugvEntry in message.userGroupVisibility" class="font--bold">
								{{ ugvEntry }}
							</div>
						</td>

						<td class="table__value">
							<div class="font--bold" v-html="message.title"></div>
							<p>{{ message.messageShortPreview }}</p>

							<ui-button btn-style="outlinePill" @click="previewMessage(message)">
								<orbis:message code="i18n.dashboard_messagesConfig.ViewMessag8679854051641286"/>
							</ui-button>
						</td>

						<td class="table__value">{{ message.messageType }}</td>
						<td class="table__value">{{ handleDateDisplay(message.liveDate) }}</td>
						<td class="table__value">{{ handleDateDisplay(message.expireDate) }}</td>
					</tr>
				</tbody>
			</template>
			<template #header>
				<caption class="table__caption only--screen-reader"><orbis:message
						code="i18n.dashboard_messages.Messages8110243960017825"/></caption>
				<thead class="table__header">
					<tr class="table__row--header">
						<th class="table__heading"></th>
						<th class="table__heading"><orbis:message code="i18n.dashboard_messages.Dashboard"/></th>
						<th class="table__heading"><orbis:message code="i18n.dashboard_messages.Message"/></th>
						<th class="table__heading">Type</th>
						<th class="table__heading"><orbis:message code="i18n.dashboard_messages.LiveOn4106918867198850"/></th>
						<th class="table__heading"><orbis:message code="i18n.dashboard_messages.ExpireOn2177869985682286"/></th>
					</tr>
				</thead>
			</template>
		</draggable>
	</div>

	<ui-confirm-modal
		v-if="messageSelected"
		v-model="deleteModalState"
		confirm-button-type="error"
		confirm-label="<orbis:message code="i18n.dashboard_messagesConfig.Delete2868465618015630" htmlEscape="true" />"
		:title="`<orbis:message code="i18n.dashboard_messagesConfig.Deletemess0479592180666162" htmlEscape="true" />`"
		ref="confirmDelete"
		@hide="messageSelected = null"
		@confirm="handleMessageDelete">
		<p>
			<orbis:message javaScriptEscape="true"  code="i18n.dashboard_messages.Areyousure5727963206405785" />
		</p>
	</ui-confirm-modal>

	<ui-document-overlay v-if="messageSelected" v-model="messagePreviewModalState">
		<article class="blog-post-preview">
			<div class="blog-article__title-group">
				<h1 class="blog-article__title margin--b--s">{{messageSelected.title}}</h1>
				<div v-if="messageSelected.liveDate" class="display--flex align--middle margin--b--s">
					<div class="display--flex flex--column">
						<span class="display--flex align--middle text--small">
							{{handleDateDisplay(messageSelected.liveDate)}}
						</span>
					</div>
				</div>
			</div>

			<div v-if="messageSelected.postImage" class="post-image-container">
				<img class="card--round__background--img no-after post-image" src="${messageSelected.postImage}" alt="Image">
			</div>

			<div class="blog-article__body-group" v-html="messageSelected.message"></div>
		</article>
	</ui-document-overlay>

</template>

<script type="text/javascript">

	const messagesConfig = {
        template: '#messagesConfigTemplate',
        props: {
            permissionMap: Object
        },
		components: {
			draggable: vuedraggable
		},
		setup() {
			const dashboardMessages = Vue.ref({});
            const messageSelected = Vue.ref(null);
            const deleteModalState = Vue.ref(false);
            const messagePreviewModalState = Vue.ref(false);

            const emptyStateCallToAction = [{
                label: '<orbis:message code="i18n.dashboard_messages.CreateaMessage" javaScriptEscape="true" />',
                action: () => {
                    orbisApp.buildForm({
                        action: '${o:encrypt("displayDashboardMessageEdit", currentUser)}',
                    }).submit();
                }
            }]

            function fetchConfig() {
                var request = {
                    action : '${o:encrypt("ajaxGetDashboardMessagesConfig", currentUser)}',
                    rand : Math.floor(Math.random() * 100000)
                };

                orbisAppSr.showLoadingOverlay();

                $.post('', request, function(data, status, xhr)
                {
                    orbisAppSr.hideLoadingOverlay();

                    if (orbisApp.checkAjaxResponse(xhr))
                    {
                        dashboardMessages.value = data;
                    }
                }, "json");
			}

            function editMessage({ id }) {
				orbisApp.buildForm({
					action: '${o:encrypt("displayDashboardMessageEdit", currentUser)}',
                    messageId: id
				}).submit();
            }

            function deleteMessage(message) {
                messageSelected.value = message;
                deleteModalState.value = true;
            }

            function handleMessageDelete() {
                const message = messageSelected.value;

				var request = {
					action : '${o:encrypt("ajaxDeleteMessage", currentUser)}',
					messageId: message.id,
					rand : Math.floor(Math.random() * 100000)
				};

				orbisAppSr.showLoadingOverlay();

				$.post('', request, function(data, status, xhr)
				{
					orbisAppSr.hideLoadingOverlay();

					if (orbisApp.checkAjaxResponse(xhr))
					{
                        if(data.error) {
                            orbisAppSr.showNotification('<orbis:message code="i18n.dashboard_messagesConfig.Therewasan3685197635339573" javaScriptEscape="true" />', 'error');
						}
                        else {
                            dashboardMessages.value = dashboardMessages.value.filter(m => m.id !== request.messageId);
                            orbisAppSr.showNotification(`<orbis:message code="i18n.dashboard_messagesConfig.Messagedel7522528619843584" javaScriptEscape="true" />`, 'success');
						}
					}
				}, "json");

                deleteModalState.value = false;
            }

            function handleDateDisplay(isoDate) {
                if(isoDate) {
                    return moment(isoDate).format(orbisAppLocalized.dateTimeFormats.moment.dateMediumTimeShort);
                }

				return '<orbis:message code="i18n.dashboard_messages.NA1350317814017753" javaScriptEscape="true" />';
            }

            function previewMessage(message) {
                messageSelected.value = message;
                messagePreviewModalState.value = true;
            }

			function updateOrder() {
				var orderList = dashboardMessages.value.map(it => it.id);

				var request = {
					action: '<o:encrypt action="updateDashboardMessagesOrder" />',
					orderList: JSON.stringify(orderList)
				};

				$.post('', request);
			}

            Vue.onMounted(() => {
                fetchConfig();
			});

            return {
                dashboardMessages,
                messageSelected,
                editMessage,
                deleteMessage,
				handleMessageDelete,
                emptyStateCallToAction,
                handleDateDisplay,
                messagePreviewModalState,
                deleteModalState,
                previewMessage,
	            updateOrder
            };
		}
    };

</script>