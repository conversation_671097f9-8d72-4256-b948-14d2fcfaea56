<%@ taglib prefix="o" uri="/WEB-INF/tlds/orbis.tld"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jstl/core_rt"%>

<c:set var="recaptchaSiteKey" value="${o:getConfig('RECAPTCHA_SITE_KEY')}"/>
<c:set var="enableRecaptchaV3" value="${o:getConfig('ENABLE_RECAPTCHA_V3') }" ></c:set>
<c:if test="${not empty recaptchaSiteKey}">
    <c:set var="random" scope="page" value="${o:generateId()}"/>

    <c:if test="${ enableRecaptchaV3 == 1 }">
	    <input type="hidden" id="g-recaptcha-response" name="g-recaptcha-response" value="" />
	    <script type="text/javascript">
	      var onloadCallbackV3_${random} = function() {
	          grecaptcha.execute('${recaptchaSiteKey}', { 'action' : 'default'}).then(function (token) {
	        	  document.getElementById('g-recaptcha-response').value = token;
	        	  
	        	  if(typeof captchaSolvedCallback !== "undefined")
        		  {
		          	captchaSolvedCallback();
		          }
	            });
	      };
	    </script>
	    <script src="https://www.google.com/recaptcha/api.js?onload=onloadCallbackV3_${random}&render=${recaptchaSiteKey}" async defer></script>
    </c:if>
    <c:if test="${ enableRecaptchaV3 != 1 }">
		<script type="text/javascript">
	      var onloadCallback_${random} = function() {
	          grecaptcha.render('recaptcha_${random}', { 'sitekey' : '${recaptchaSiteKey}', 'callback' : 'captchaSolvedCallback' });
	       };
		</script> 
		<div id="recaptcha_${random}"></div>
   		<script src="https://www.google.com/recaptcha/api.js?onload=onloadCallback_${random}&render=explicit" async defer></script>   
    </c:if>
</c:if>