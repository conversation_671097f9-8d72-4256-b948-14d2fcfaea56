<%@ taglib prefix="c" uri="http://java.sun.com/jstl/core_rt" %>
<%@ taglib prefix="orbis" uri="/WEB-INF/tlds/orbis-taglib.tld" %> 
<%@ taglib prefix="spring" uri="http://www.springframework.org/tags" %>

<%@ attribute 
	name="formId" 
	required="false" 
	type="java.lang.String" %>
<%@ attribute 
	name="validateOpts" 
	required="false" 
	type="java.lang.String" %>
<%@ attribute 
	name="callback" 
	required="false" 
	type="java.lang.String" %>

<c:if test="${not empty validationData.pageErrors}">
	<orbis:errorMessage timeOpen="true">
		<orbis:message code="i18n.validate.Therehaveb01569098362544219" />:
		<ul>
			<c:forEach var="error" items="${validationData.pageErrors}">
				<li>
					${error.translation}
				</li>
			</c:forEach>
		</ul>
	</orbis:errorMessage>
</c:if>

<script type="text/javascript">
	var pageValidators = pageValidators;
	
	if(!pageValidators)
	{
		pageValidators = new Object();
	}
	
	function initFormValidation_${ formId }() {
		<c:if test="${not empty pageScope.formId}">
			var orbisValidateFormId = "${pageScope.formId}";
		</c:if>
		<c:if test="${not empty pageScope.validateOpts}">
			var $validator = $("form#" + orbisValidateFormId).validate($.extend({}, orbisApp.validateOpts, ${pageScope.validateOpts}));
		</c:if>
		<c:if test="${empty pageScope.validateOpts}">
			var $validator = $("form#" + orbisValidateFormId).validate(orbisApp.validateOpts);
		</c:if>
		
		<c:if test="${not empty validationData.fieldErrors}">
			$validator.showErrors(${validationData.formErrorsJson});
		</c:if>
		
		<c:if test="${not empty pageScope.formId}">
			$("form#" + orbisValidateFormId).find(".required").attr("aria-required", "true");
		</c:if>
		
		pageValidators[orbisValidateFormId] = $validator;
		
		$("form#" + orbisValidateFormId).trigger("validatorInitialized");
		
		${callback};
	}
</script>

<orbis:addComponent component="jqueryValidate" version="1.11.1" callback="initFormValidation_${ formId }()" />