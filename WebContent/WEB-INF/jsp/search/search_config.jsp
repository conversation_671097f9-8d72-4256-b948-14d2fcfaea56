<%@ include file="/WEB-INF/jsp/include.jsp"%>

<c:if test="${not empty isShortCircuitView}">
	<!DOCTYPE html>
	<html lang="en">
	<head>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<%@ include file="/WEB-INF/jsp/linkedResources.jsp" %>
	</head>
	<body>
</c:if>

<style>
	input[type=radio], input[type=checkbox] { margin-top: -2px; margin:0px 5px 0px 5px;}
</style>

<c:if test="${empty searchModel || empty searchModel.masterEntity}">
	<orbis:message code="i18n.search_config.SearchMode5167136247998168" />
</c:if>

<c:if test="${not empty searchModel.errorMessages}">
	<c:set var="msgs" value="${searchModel.errorMessages}" />		
	<%@ include file="/WEB-INF/jsp/i18nMsgs.jsp" %>			
</c:if>

<c:if test="${not empty searchModel && not empty searchModel.masterEntity}">

	<orbis:addComponent component="orbisNotePopups" />
	<script type="text/javascript">

	var controllerPath = "${siteElement.fullPath}.htm";
		$(document).ready(function(){
			$("#mainPanel").show();
			
			var relation ="${relation}";	
			var relationArray = relation.split(',');
			
			for (var i = 0; i < relationArray.length; i++)
			{
				var rel = relationArray[i];
				var request = {
					action : '<o:encrypt action="${actionValue}"/>',
					subAction : "loadConfirRelationship",
					reportKey : "${reportKey}",
					rel :  $("#"+ rel+"_tab").parent().parent().parent().data("rel"),
					rand : Math.floor(Math.random() * 100000)
				};
				  $("#"+ rel+"_tab").load(controllerPath, request, function(responseText, textStatus, xhr){
				});  
			}
		});
	</script>
	
	<%-- <c:forEach var="rel" items="${relation}">
	<script type="text/javascript">
		loadCriteria("${rel}");
		</script>
	</c:forEach> --%>

	<div id="mainPanel" style="display: none;">
		<%@ include file="/WEB-INF/jsp/search/search_configHeader.jsp"%>
		<c:if test="${empty searchModel.attributes['SUPPRESS_CONFIG_TITLE']}">
			<div class="orbisModuleHeader">
				<div class="row-fluid">
					<div class="span8">
						<h1>
							<orbis:message code="i18n.search_config.filters" />
							<c:choose>
								<c:when test="${fn:startsWith(searchModel.masterEntity.entityLabel, 'i18n')}">
									<orbis:message code="${searchModel.masterEntity.entityLabel}" />
								</c:when>
								<c:otherwise>
									${searchModel.masterEntity.entityLabel}
								</c:otherwise>
							</c:choose>
						</h1>
					</div>
					<div class="span4 pull-right">
						<form enctype="multipart/form-data" id="displayHomeFromSearchForm" method="post">
                            <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
							<o:encrypt input="true" action="displayHome" />
							<c:if test="${not empty searchModel.attributes['extraHiddenFieldForTermTab']}">
								${searchModel.attributes['extraHiddenFieldForTermTab']}
							</c:if>
						</form>
						<ul class="pager">
                            <c:if test="${not empty searchModel.attributes['termCourseId']}">
                                <orbis:navButton title="i18n.exp_termCourseStudentInteraction.BacktoTermCourse" action="displayTermCourse" tcId="${searchModel.attributes['termCourseId']}" />
                            </c:if>
							<c:if test="${not empty searchModel.attributes['programId']}">
								<orbis:navButton title="i18n.search_results.BacktoProg152539731546804" action="displayProgram" programId="${searchModel.attributes['programId']}"/>
							</c:if>
                            <c:if test="${empty searchModel.attributes['termCourseId']}">
                                <li><a href="javascript:void(0)" onclick="$('#displayHomeFromSearchForm').submit();"> <i class="icon-chevron-left"></i>  <orbis:message code="i18n.search_config.Backtosite5467892580977283" arguments="${isL1 ? siteElement.elementTitle : (not empty siteElement.elementTitle2 ? siteElement.elementTitle2 : siteElement.elementTitle)}" />
                                </a></li>
                            </c:if>

							<c:if test="${not empty searchModel.attributes['programId']}">
								<form enctype="multipart/form-data" id="displayProgramFromSearchForm" method="post" class="hide">
                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
									<o:encrypt input="true" action="displayProgram" />
									<input type="hidden" name="programId" value="${searchModel.attributes['programId']}" />
								</form>
								<li>
									<a href="javascript:void(0)" onclick="$('#displayProgramFromSearchForm').submit();"> <i class="icon-chevron-left"></i>
										<orbis:message code="i18n.search_results.BacktoProg152539731546804" /> 
									</a>
								</li>
							</c:if>
						</ul>
					</div>
				</div>
			</div>
		</c:if>
		
		<div class="orbis-posting-actions">
			<div style="text-align: center">
				<o:nav anchor="true" anchorClass="btn btn-primary btn-small sel_ClearCriteria" action="${actionValue}" subAction="viewCriteria" clearCriteria="true"><orbis:message code="i18n.search_config.ClearCrite1385293436867241" /></o:nav>
			</div>
		</div>

		<form enctype="multipart/form-data" id="questionForm" action="${siteElement.fullPath}.htm" method="POST" class="form-horizontal">
            <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
			<o:encrypt input="true" action="${actionValue}" subAction="search" /> 
			 
			<input type="hidden" name="state" value="${searchModel.state}" />

			<div class="box boxContent">
				<c:if test="${not empty filters}">
					<div class="row-fluid">
						<orbis:message code="i18n.report_main.BCurrentFi40441036814194387" />
						<ul>
							${filters}
						</ul>
					</div>
				</c:if>
				<ul class="pager">
					<li>
						<input class="hidden" type="submit" value="<orbis:message code='i18n.search_config.Next' />" />
						<a href="javascript:void(0);" class="sel_NextButton_AdvancedSearchTest" onclick="$('#questionForm').submit();"> <orbis:message code="i18n.search_config.Next" /> <i class="icon-chevron-right icon-white"></i> </a>
					</li>
				</ul>
			</div>
			<%@ include file="/WEB-INF/jsp/search/search_configMaster.jsp"%>

			<%@ include file="/WEB-INF/jsp/search/search_configDetails.jsp"%>
			
			<%@ include file="/WEB-INF/jsp/search/search_configFooter.jsp"%>

			<div class="box boxContent">
				<ul class="pager">
					<li><a href="javascript:void(0);" onclick="$('#questionForm').submit();"> <orbis:message code="i18n.search_config.Next" /> <i class="icon-chevron-right icon-white"></i> </a></li>
				</ul>
			</div>
			
		</form>

	</div>

</c:if>