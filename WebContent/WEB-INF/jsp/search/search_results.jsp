<%@ include file="/WEB-INF/jsp/include.jsp"%>

<c:if test="${not empty isShortCircuitView}">
	<!DOCTYPE html>
	<html lang="en">
	<head>
		<META http-equiv="Content-Type" content="text/html; charset=utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
		<meta name="apple-mobile-web-app-capable" content="yes">
		<%@ include file="/WEB-INF/jsp/linkedResources.jsp" %>
	</head>
	<body>
</c:if>

<script type="text/javascript">

	var controllerPath = "${siteElement.fullPath}.htm";

	var resultsActions = {
		ondblClickRow : function(rowId){
			<c:if test="${searchModel.canViewDetails}">
				orbisApp.openTempMessageDialog('<spring:message code="i18n.search_results.PleaseWait" javaScriptEscape="true"/>');
				var params = {"action": "${o:encrypt(actionValue, currentUser)}", "subAction":"onDoubleClickRow", "rowId": rowId, "state": "${searchModel.state}", "searchType":"${searchModel.masterEntity.entityLabel}", "entity":"${searchModel.masterEntity.entityHqlName}" };
				<c:if test="${not empty searchModel.attributes['extraHiddenFields']}" >
					$.extend(params, ${searchModel.attributes['extraHiddenFields']});
				</c:if>
				orbisApp.buildForm(params, "${siteElement.fullPath}.htm", false).submit();
			</c:if>
		},
		onNewWindow : function(rowId, newTab){
			<c:if test="${searchModel.canViewDetails}">
				var params = {"action": "${o:encrypt(actionValue, currentUser)}", "subAction":"onDoubleClickRow", "rowId": rowId, "state": "${searchModel.state}", "searchType":"${searchModel.masterEntity.entityLabel}", "entity":"${searchModel.masterEntity.entityHqlName}" };
				<c:if test="${not empty searchModel.attributes['extraHiddenFields']}" >
					$.extend(params, ${searchModel.attributes['extraHiddenFields']});
				</c:if>
				orbisApp.buildForm(params, "${siteElement.fullPath}.htm", newTab).submit();
			</c:if>
		}
	}
	
	$(window).bind('beforeunload', function(){
		//saveLastViewed();
	});
	
	$(document).ready(function(){
		//saveLastViewed();
	});
	
	function saveLastViewed()
	{
		saveSearch(true, {
			name : "lastSearchViewed",
			description : "Last Viewed Search",
			shareLevel : 0
		});
	}
</script>

<c:if test="${not empty searchModel.attributes['customJavascript']}">
	<script type="text/javascript" src="${searchModel.attributes['customJavascript']}"></script>
</c:if>


	<c:if test="${not empty searchModel.plugins}">
		<c:forEach var="p" items="${searchModel.plugins}">
			<script type="text/javascript" src="${p}"></script>
		</c:forEach>
	</c:if>


<div class="row-fluid">
	<div class="span12">
		<c:if test="${not empty searchModel.attributes['returnForm']}">
			${searchModel.attributes['returnForm']}
		</c:if>
		
		<c:if test="${ empty showBackToHomeNav}">				
			<div class="orbisModuleHeader">
				<div class="row-fluid">		
					<div class="span4">
					<h1><orbis:message code="i18n.search_results.SearchResults" /></h1>
					</div>		
					<div class="span8 pull-right">
						<ul class="pager">
							<c:if test="${empty searchModel.attributes['extraHiddenFieldForTab2']}">
								<form enctype="multipart/form-data" id="displayHomeForm" method="post" class="hide">
                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
									<o:encrypt input="true" action="displayHome" />
									<input type="hidden" name="currentTabSelected" value="dashboard">
									<c:if test="${not empty searchModel.attributes['extraHiddenFieldForTermTab']}">
										${searchModel.attributes['extraHiddenFieldForTermTab']}
									</c:if>										
								</form>
								<li>
									<a href="javascript:void(0)" onclick="$('#displayHomeForm').submit();"> <i class="icon-chevron-left"></i>
										<orbis:message code="i18n.search_results.Backtosite3540890063458083" arguments="${isL1 ? siteElement.elementTitle : (not empty siteElement.elementTitle2 ? siteElement.elementTitle2 : siteElement.elementTitle)}" />
									</a>
								</li>
							</c:if>
							<c:if test="${not empty searchModel.attributes['extraHiddenFieldForTab2']}">
								<form enctype="multipart/form-data" id="displayHomeTabForm" method="post" class="hide">
                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
									<c:if test="${not empty searchModel.attributes['extraHiddenFieldForTab2']}">
										${searchModel.attributes['extraHiddenFieldForTab2']}
									</c:if>
								</form>
								<li>
									<a href="javascript:void(0)" onclick="$('#displayHomeTabForm').submit();"> <i class="icon-chevron-left"></i>
										<orbis:message code="i18n.common.backToDashboard" />
									</a>
								</li>
							</c:if>	
							<c:if test="${not empty searchModel.attributes['programId']}">
								<form enctype="multipart/form-data" id="displayProgramForm" method="post" class="hide">
                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
									<o:encrypt input="true" action="displayProgram" />
									<input type="hidden" name="programId" value="${searchModel.attributes['programId']}" />
								</form>
								<li>
									<a href="javascript:void(0)" onclick="$('#displayProgramForm').submit();"> <i class="icon-chevron-left"></i>
<orbis:message code="i18n.search_results.BacktoProg152539731546804" /> 
</a>
								</li>
							</c:if>				
							<c:if test="${not empty searchModel.attributes['timePeriod']}">
								<form enctype="multipart/form-data" id="displayTimePeriodTabForm" method="post" class="hide">
                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
									<c:if test="${not empty searchModel.attributes['timePeriod']}">
										${searchModel.attributes['timePeriod']}
									</c:if>
								</form>
								<li>
									<a href="javascript:void(0)" onclick="$('#displayTimePeriodTabForm').submit();"> <i class="icon-chevron-left"></i>
										<orbis:message code="i18n.search_results.BacktoTimePeriods" />
									</a>
								</li>
							</c:if>
							<c:if test="${not empty searchModel.attributes['termCourseId']}">
								<form enctype="multipart/form-data" id="displayTermCourseForm" method="post" class="hide">
                                    <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
									<o:encrypt input="true" action="displayTermCourse" />
									<input type="hidden" name="tcId" value="${searchModel.attributes['termCourseId']}" />
								</form>
								<li>
									<a href="javascript:void(0)" onclick="$('#displayTermCourseForm').submit();"> <i class="icon-chevron-left"></i>
										Back to Term Course
									</a>
								</li>
							</c:if>
						</ul>
					</div>
				</div>
			</div>	
		</c:if>
		
		<c:if test="${not empty filters}">
			<div class="orbisModuleHeader">
				<div class="row-fluid">
					<orbis:message code="i18n.report_main.BCurrentFi40441036814194387" />
					<ul>
						${filters}
					</ul>
				</div>
			</div>
		</c:if>
		
		
		<%@ include file="/WEB-INF/jsp/search/search_resultsHeader.jsp"%>
		
		<%@ include file="search_gridSetup.jsp"%>
		
		<%@ include file="/WEB-INF/jsp/search/search_resultsFooter.jsp"%>

	</div>
</div>


