<div class="box">
	<div class="boxContent">
			
		<orbis:validate formId="advancedRegisterForm" />
		
		<form enctype="multipart/form-data" id="advancedRegisterForm" method="post" class="form-horizontal">
            <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
			<o:encrypt input="true" action="saveAdvancedRegister" />
			<input type="hidden" name="eventId"	value="${reg.globalEvent.id }"/>
			<input type="hidden" name="regTypeId" value="${advReg.regType.id }" />
			<input type="hidden" name="eventUserId"	value="${reg.user}" />
			<input type="hidden" name="regId" value="${reg.id}" />
			<input type="hidden" name="challenge" value="${challenge}" />
			<input type="hidden" name="wlId" value="${wl.id}" />
			<input type="hidden" name="wl_challenge" value="${wl_challenge}" />
			
			<c:set var="DFModel" value="${regTypeModel}" />
			<c:set var="DFAnswerrEntity" value="${advReg}" />
			<%@ include file="/WEB-INF/jsp/df/df_form1.jsp"%>
	
			<br/>

			<c:if test="${isPublicPage}">
				<div class="control-group">
					<div class="controls">
						<orbis:recaptcha />
					</div>
				</div>
				<script>
					function captchaSolvedCallback()
					{
						$("#submitBtn").prop("disabled", false);
					}
				</script>
			</c:if>
			<input type="submit" id="submitBtn" ${isPublicPage ? "disabled" : "" } class="btn btn-primary btn-small" value="<spring:message code="i18n.grad_questionsEdit.Submit" />" />
		</form>
	</div>
</div>