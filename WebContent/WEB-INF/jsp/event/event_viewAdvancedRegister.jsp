<script type="text/javascript">
	function reloadPage()
	{
		<o:nav action="displayViewAdvancedRegister" regId="${reg.id}" />
	}
</script>

<c:if test="${reg.regType.status == 1 }">
	<div class="row-fluid">
		<div class="span12">
			<div class="orbis-posting-actions">
				<div style="text-align: center">
					<c:if test="${currentUser.portalStaff}">
						<o:nav anchor="true" anchorClass="btn btn-primary btn-small" action="displayAdvancedRegister" regId="${reg.id}">
							<orbis:message code="i18n.event_viewAdvancedRegister.EditRegist2374637729629114" />
						</o:nav>
						<c:if test="${reg.attended && not empty reg.dateCreated && empty reg.dateUpdated}">
							<o:nav anchor="true" anchorClass="btn btn-primary btn-small" action="displaySatisfactionSurvey" eventId="${reg.globalEvent.id}" userId="${reg.user.id}">
								<orbis:message code="i18n.global_registrants.CompleteSurvey" />
							</o:nav>
						</c:if>
						<c:if test="${!competenciesDisabled}">
							<c:choose>
								<c:when test="${reg.attended}">
									<o:nav anchor="true" anchorClass="btn btn-primary btn-small" action="displayAchievedCompetenciesEdit" regId="${reg.id}">
										<orbis:message code="i18n.event_viewAdvancedRegister.Competenci2725972782330998" />
									</o:nav>
								</c:when>
								<c:when test="${not empty reg.user}">
									<orbis:message var="tooltipText" code="i18n.event_viewAdvancedRegister.Availableo5966269457802801" htmlEscape="true" />
								 	<div class="atooltip" style="display: inline-block;" title="${tooltipText}">
										<a href="javascript:void(0);" class="btn btn-primary btn-small disabled isDisabled">
											<orbis:message code="i18n.event_viewAdvancedRegister.Competenci2725972782330998" />
										</a>
								 	</div>
								</c:when>
							</c:choose>	
						</c:if>
					</c:if>
					<c:if test="${evAdmin}">
						<o:nav anchor="true" anchorClass="btn btn-primary btn-small" action="displayAdvRegEmailer" regId="${reg.id }" comingFrom="displayViewAdvancedRegister" emailTo="advRegistrant"><orbis:message code="i18n.event_viewAdvancedRegister.Email" /></o:nav>
					</c:if>
					<c:set var="printMessage">
						<c:choose>
							<c:when test="${ empty reg.regType.ecommerceOrder || isPaid}">
								<orbis:message code="i18n.event_viewAdvancedRegister.PrintRecei5244938359547295" />
							</c:when>
							<c:otherwise>
								<orbis:message code="i18n.event_viewAdvancedRegister.PrintInvoi2233416177316556" />
							</c:otherwise>
						</c:choose>
					</c:set>
					<o:nav anchor="true" anchorClass="btn btn-primary btn-small" action="printDetailedAdvReg" regId="${reg.id}" challenge="${challenge}">${printMessage}</o:nav>
				</div>
			</div>
		</div>
	</div>
</c:if>

<c:set var="detailsView" value="view"/>
<%@ include file="event_advancedRegisterDetails.jsp"%>

<c:if test="${not empty reg.regType.ecommerceOrder && comingFrom != 'publicSide'}">
	<div class="box">
		<div class="boxTitle">
			<spring:message code="i18n.os_orderDetails.EcommerceDetails" />
		</div>
		<div class="boxContent">
			<c:set var="orderEntityClass" value="${reg.regType['class'].name}" />
			<c:set var="orderEntityId" value="${reg.regType.id}" />
			<c:set var="orderId" value="${reg.regType.ecommerceOrder.id}" />
			<c:set var="orderNumberPrefix" value="${reg.globalEvent.orderNumberPrefix}"/>
			<c:set var="onOrderStatusUpdate" value="reloadPage"/>
			<c:if test="${!evAdmin}">
				<c:set var="adminMode" value="false" />
			</c:if>
			<%@ include file="/WEB-INF/jsp/ecommerce/ecommerce_order_history.jsp" %>
		</div>
	</div>
</c:if>