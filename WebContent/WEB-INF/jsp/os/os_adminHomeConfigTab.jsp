<%@ include file="/WEB-INF/jsp/include.jsp" %>

<orbis:navigation title="${module.moduleName}">
</orbis:navigation>
 
<div class="is--spiral--robot">
	<ui:actionsGroup i18n_title="i18n.common.actions" id="os_interactions" >
		<ui:actionsGroupItem action="displayPlaceOrder"><orbis:message code="i18n.os_adminHomeOverviewTab.PlaceOrderOnBehalf" /></ui:actionsGroupItem>
		<ui:actionsGroupItem action="search" subAction="search" searchType="orders"><orbis:message code="i18n.os_adminHomeOverviewTab.SearchOrders" /></ui:actionsGroupItem>
	</ui:actionsGroup>
</div>

<div class="row-fluid">
	<div class="span12">
		<div class="orbisTabContainer">
			<div class="tabbable">
				<c:set var="currentTabSelected" value="configTab" />
				<%@ include file="os_adminHomeTabs.jsp"%>
				<div class="tab-content">
					<div class="tab-pane fade in active" id="dashboard">
						<div class="boxContent">
							<div class="row-fluid">
								<div class="span4">
									<orbis:message code="i18n.os_adminHomeConfigTab.bModuleOptionsb" />
									<ul class="nav nav-tabs nav-stacked">
										<li>
											<o:nav anchor="true" action="displayConfigureModuleGeneral"><orbis:message code="i18n.os_adminHomeConfigTab.GeneralMod7545288338885628" /></o:nav>
										</li>
										<li>
											<o:nav anchor="true" action="displayManageProductCatalogue"><orbis:message code="i18n.os_adminHomeConfigTab.ManageProd8241370692394232" /></o:nav>
										</li>
										<li>
											<o:nav anchor="true" action="displayManageProductCategories"><orbis:message code="i18n.os_adminHomeConfigTab.ManageProd6068702237937227" /></o:nav>
										</li>
										<li>
											<o:nav anchor="true" action="displayManageEmailsAndContent"><orbis:message code="i18n.os_adminHomeConfigTab.ManageStoc6722094613467734" /></o:nav>
										</li>
										<li>
											<o:nav anchor="true" action="displayTaxOverrideTags"><orbis:message code="i18n.os_adminHomeConfigTab.ManageTaxO7059693119248658" /></o:nav>
										</li>
										<li>
											<o:nav anchor="true" action="displayAdmins"><orbis:message code="i18n.os_adminHomeConfigTab.ManageModuleAdmins" /></o:nav>
										</li>
									</ul>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
