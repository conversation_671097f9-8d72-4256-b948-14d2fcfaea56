<%@ include file="/WEB-INF/jsp/include.jsp" %>
<c:if test="${not empty successMessage}"><orbis:successMessage>${successMessage}</orbis:successMessage></c:if>
<c:if test="${not empty errorMessage}"><orbis:errorMessage>${errorMessage}</orbis:errorMessage></c:if>

<c:if test="${not empty emailSent}">
	<orbis:successMessage>
		<orbis:message code="i18n.os_orderDetails.Emailhasbeensent" />
	</orbis:successMessage>
</c:if>

<orbis:navigation title="${module.moduleName}">
	<c:if test="${empty studentStep}">
		<orbis:navButton title="i18n.os_orderDetails.BacktoHome" action="displayHome" primaryIcon="icon-chevron-left" />
		<c:if test="${comingFrom == 'list'}">
			<orbis:navButton title="i18n.os_orderDetails.BacktoOrdersList" action="displayOrdersList" listType="${listType}" primaryIcon="icon-chevron-left" />
		</c:if>
		<c:if test="${comingFrom == 'customer'}">
			<orbis:navButton title="i18n.os_orderDetails.BacktoCust41657158412692974" action="displayCustomer" customerId="${customerId}" primaryIcon="icon-chevron-left" />
		</c:if>
		<c:if test="${comingFrom == 'customerAsCustomer'}">
			<orbis:navButton title="i18n.os_orderDetails.BacktoMyOrders" action="displayCustomersOrdersList" primaryIcon="icon-chevron-left" />
		</c:if>
		<c:if test="${comingFrom == 'org'}">
			<orbis:navButton title="i18n.os_orderDetails.BacktoOrganization" action="displayCustomerOrg" orgId="${orgId}" primaryIcon="icon-chevron-left" />
		</c:if>
	</c:if>
	<c:if test="${not empty studentStep}">
		<orbis:navButton title="i18n.os_orderDetails.Backtoorde0827295368022529" titleArgs="${order.customer.id==currentUser.id ? 'Your' : 'Student'}" siteElementPath="${studentStep.student.termCourse.term.module.siteElementPath}" action="displayTermCourseStudent" tcsId="${studentStep.student.id}" primaryIcon="icon-chevron-left" />
	</c:if>
</orbis:navigation>

<div class="box">
	<div class="boxTitle">
		<orbis:message code="i18n.os_orderDetails.Orderorder2822051410110803" arguments="${order.orderNumber}" />
	</div>
	<div class="boxContent">
		<table class="table table-condensed">
			<col width="30%">
			<tr>
				<td><orbis:message code="i18n.os_orderDetails.OrderStatus" /></td>
				<td>
					<span class="label ${order.status==2 ? 'label-success' : 'label-important'}"><orbis:message code="${order.statusI18n}" /></span>
				</td>
			</tr>
			<tr>
				<td><orbis:message code="i18n.os_orderDetails.DateCreated" /></td>
				<td>
					<B><fmt:formatDate value="${order.orderDate}" pattern="${orbisDateMedium2}" /></B>
					<orbis:message code="i18n.os_orderDetails.at" /> 
					<B><fmt:formatDate value="${order.orderDate}" pattern="${orbisTimeShort2}" /></B> 
				</td>
			</tr>
			<c:if test="${not empty order.createdBy}">
				<tr>
					<td><orbis:message code="i18n.os_orderDetails.CreatedBy" /></td>
					<td><B>${order.createdBy.fullNameWithUsername}</B></td>
				</tr>
			</c:if>
			<c:if test="${order.status == 3 || order.status == 4 && not empty order.cancelledDate}">
				<tr>
					<td><orbis:message code="i18n.os_orderDetails.DateCancelled" /></td>
					<td>
						<B><fmt:formatDate value="${order.cancelledDate}" pattern="${orbisDateMedium2}" /></B>
						<orbis:message code="i18n.os_orderDetails.at8751893523741235" /> 
						<B><fmt:formatDate value="${order.cancelledDate}" pattern="${orbisTimeShort2}" /></B> 
					</td>
				</tr>
			</c:if>
			<c:if test="${order.status == 4 && not empty order.deletedDate}">
				<tr>
					<td><orbis:message code="i18n.os_orderDetails.DateDeleted" /></td>
					<td>
						<B><fmt:formatDate value="${order.deletedDate}" pattern="${orbisDateMedium2}" /></B>
						<orbis:message code="i18n.os_orderDetails.at8751893523741235" /> 
						<B><fmt:formatDate value="${order.deletedDate}" pattern="${orbisTimeShort2}" /></B> 
					</td>
				</tr>
			</c:if>
		</table>			
		<c:if test="${order.status == 0 || order.status == 1}">
			<div class="well" style="text-align: center">
				<div class="alert alert-error">
					<orbis:message code="i18n.os_orderDetails.Thisorderh272325086618844" />
				</div>
				<o:nav anchor="true" anchorClass="btn btn-primary btn-small" action="displayPlaceOrder" osOrderId="${order.id}"><orbis:message code="i18n.os_orderDetails.FinishOrder" /></o:nav>
				<o:nav anchor="true" anchorClass="btn btn-primary btn-small" action="cancelOrder" osOrderId="${order.id}"><orbis:message code="i18n.os_orderDetails.CancelOrder" /></o:nav>
			</div>
		</c:if>
		<c:if test="${order.status == 3}">
			<div class="well" style="text-align: center">
				<div class="alert alert-error">
					<orbis:message code="i18n.os_orderDetails.Thisorderh8596645909355966" />
				</div>
				<o:nav anchor="true" anchorClass="btn btn-danger" action="deleteOrder" osOrderId="${order.id}"><orbis:message code="i18n.os_orderDetails.DeleteOrder" /></o:nav>
			</div>
		</c:if>
	</div>
</div>
<c:if test="${order.status == 2}">
	<div class="row-fluid">
		<div class="span12">
			<div class="orbis-posting-actions">
				<div style="text-align: center">
					<c:if test="${!osAdmin}">
						<o:nav anchor="true" anchorClass="btn btn-primary btn-small" action="cancelOrder" osOrderId="${order.id}"><orbis:message code="i18n.os_orderDetails.CancelOrder" /></o:nav>
					</c:if>
					<c:if test="${osAdmin}">
						<a href="javascript:void(0)" class="btn btn-primary btn-small" onclick="$('#adminCancelModal').modal('show');"><orbis:message code="i18n.os_orderDetails.CancelOrder" /></a>
						<c:set var="itsANew2"><spring:message code="i18n.os_orderDetails.Invoice" /></c:set>
						<c:if test="${empty order.ecommerceOrder || order.ecommerceOrder.status == 2}">
							<c:set var="itsANew2"><spring:message code="i18n.os_orderDetails.Receipt" /></c:set>
						</c:if>
						<o:nav anchor="true" anchorClass="btn btn-primary btn-small" action="displayOSOrderEmailer" osOrderId="${order.id}" comingFrom="displayOrder"><orbis:message code="i18n.os_orderDetails.Emailorder49009150969764836" arguments="${itsANew2}" /></o:nav>
					</c:if>
				</div>
			</div>
		</div>
	</div>
</c:if>

<div class="box">
	<div class="boxTitle">
		<orbis:message code="i18n.os_orderDetails.OrderDetails" />
	</div>
	<div class="boxContent">
		<c:if test="${module.useDefaultReceiptHeaderFooter}">
			<c:if test="${order.ecommerceOrder.status == 2}">
				<p>${ecModule.receiptHeaderAsHtml}</p>
				<%@ include file="os_orderReviewAndReceipt.jsp" %>
				<p>${module.receiptAdditionalMessage}</p>
				<p>${ecModule.receiptFooterAsHtml}</p>
			</c:if>
			<c:if test="${order.ecommerceOrder.status != 2}">
				<p>${ecModule.invoiceHeaderAsHtml}</p>
				<%@ include file="os_orderReviewAndReceipt.jsp" %>
				<p>${module.invoiceAdditionalMessage}</p>
				<p>${ecModule.invoiceFooterAsHtml}</p>
			</c:if>
		</c:if>
		<c:if test="${!module.useDefaultReceiptHeaderFooter}">					
			<c:if test="${order.ecommerceOrder.status == 2}">
				<p>${module.moduleReceiptHeader}</p>
				<%@ include file="os_orderReviewAndReceipt.jsp" %>
				<p>${module.receiptAdditionalMessage}</p>
				<p>${module.moduleReceiptFooter}</p>
			</c:if>
			<c:if test="${order.ecommerceOrder.status != 2}">
				<p>${module.moduleInvoiceHeader}</p>
				<%@ include file="os_orderReviewAndReceipt.jsp" %>
				<p>${module.invoiceAdditionalMessage}</p>
				<p>${module.moduleInvoiceFooter}</p>
			</c:if>
		</c:if>
	</div>
</div>

<script type="text/javascript">
	function reloadPage()
	{
		<o:nav action="displayOrder" osOrderId="${order.id}" />
	}
</script>

<c:if test="${not empty order.ecommerceOrder}">
	<div class="box">
		<div class="boxTitle">
			<orbis:message code="i18n.os_orderDetails.EcommerceDetails" />
		</div>
		<div class="boxContent">
			<c:set var="orderEntityClass" value="${order['class'].name}" />
			<c:set var="orderEntityId" value="${order.id}" />
			<c:set var="orderId" value="${order.ecommerceOrder.id}" />
			<c:set var="orderNumberPrefix" value="${module.orderPrefix}"/>
			<c:set var="onOrderStatusUpdate" value="reloadPage"/>
			<c:if test="${!osAdmin}">
				<c:set var="adminMode" value="false" />
			</c:if>
			<%@ include file="/WEB-INF/jsp/ecommerce/ecommerce_order_history.jsp" %>
		</div>
	</div>
</c:if>

<div id="adminCancelModal" class="modal hide" tabindex="-1" role="dialog"
	aria-labelledby="Cancel Order" aria-hidden="true">
	<div class="modal-header">
		<button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
		<h3><orbis:message code="i18n.os_orderDetails.EmailCustomer" /></h3>
	</div>
	<div class="modal-body">
		<div class="row-fluid">
			<p><orbis:message code="i18n.os_orderDetails.Doyouwisht5493943258526356" /></p>
		</div>
	</div>
	<div class="modal-footer">
		<button class="btn" data-dismiss="modal" onclick="<o:nav action="cancelOrder" osOrderId="${order.id}" sendCancelEmail="true" />"" aria-hidden="true"><orbis:message code="i18n.os_orderDetails.SendEmail" /></button>
		<button class="btn" data-dismiss="modal" onclick="<o:nav action="cancelOrder" osOrderId="${order.id}" sendCancelEmail="false" />"" aria-hidden="true"><orbis:message code="i18n.os_orderDetails.DoNotSendEmail" /></button>
		<button class="btn" data-dismiss="modal" aria-hidden="true"><orbis:message code="i18n.os_orderDetails.DoNotCancel" /></button>
	</div>
</div>


