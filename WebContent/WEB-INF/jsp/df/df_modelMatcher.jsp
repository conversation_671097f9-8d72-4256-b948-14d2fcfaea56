<%@ include file="/WEB-INF/jsp/include.jsp"%>

<orbis:navigation title="i18n.df_modelMatcher.FormMatche6503899952288133">
	<orbis:navButton title="i18n.df_auditLog.BacktoHome" action="displayHome" primaryIcon="icon-chevron-left" />
	<orbis:navButton title="i18n.df_auditLog.BacktoEditor" action="question" subAction="displayDFQuestionEditor"
		dfModelEntityType="${dfModelEntityType}" dfModelEntityId="${dfModelEntityId}" primaryIcon="icon-chevron-left" />
</orbis:navigation>

<%@ include file="/WEB-INF/jsp/df/df_modelMatcher.vue.jsp"%>

<div id="mcanvas">
	<div class="box">
		<div class="boxTitle">
			Form Matcher
			<span class="pull-right" v-if="network">
				<small><orbis:message code="i18n.df_modelMatcher.Pleasewait5754006568442048" /></small>
			</span>
		</div>
		<div class="boxContent">

			<form enctype="multipart/form-data" class="form-horizontal" method="post" onsubmit="return false;">
                <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>

				<div id="mcanvas">

					<div class="control-group">
						<label class="control-label" for=""> <orbis:message code="i18n.df_modelMatcher.Form4767874073333055" /> </label>
						<div class="controls">
							<select name="secondary" class="required" v-model="matcher.secondary" @change="loadSecondaryQuestions">
								<option :value="null"><orbis:message code="i18n.df_modelMatcher.select1372198269999413" /></option>
								<option v-for="m in secondaryModels" :value="m">{{m.name}}</option>
							</select>
						</div>
					</div>
					<br> <br>

					<div class="panel panel-default matchQuestionsList" v-if="matcher.secondary">
						<div class="panel-body">
							<a href="javascript:;" @click="addQuestionMatch">
								<small><orbis:message code="i18n.df_modelMatcher.Addaquesti4076717384126402" /></small>
							</a>
							<hr>

							<question-match v-for="qm in matcher.questions" v-bind:match="qm"> </question-match>

							<br> <br> <br>

							<input type="submit" class="btn btn-small btn-primary" value="Save" @click="saveMatcher" />

						</div>
					</div>
			</form>

		</div>
	</div>
</div>