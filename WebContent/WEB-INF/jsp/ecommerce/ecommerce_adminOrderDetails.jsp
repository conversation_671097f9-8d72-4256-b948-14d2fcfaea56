<%@ include file="/WEB-INF/jsp/include.jsp"%>

<orbis:navigation title="i18n.ecommerce_adminOrderDetails.EcommerceOrder">
	<orbis:navButton title="i18n.ecommerce_adminOrderDetails.BacktoOverview" action="displayHome" primaryIcon="icon-chevron-left" />
</orbis:navigation>

<div class="orbisTabContainer">
	<c:set var="activeTab" value="orderDetail" />
	<%@ include file="ecommerce_order_nav.jsp"%>

	<div class="tab-content">
		<div class="tab-pane active" id="orderDetails">
			<div class="row-fluid">
				<div class="span12">
				<c:if test="${not empty orderEntity}">
					<table class="table table-bordered table-condensed">
						<thead>
							<tr class="subheader">
								<td colspan="2"><spring:message code="i18n.ecommerce_adminOrderDetails.BOrderInformationB" /></td>
							</tr>
						</thead>
						<tbody>
							<tr>
								<td style="width: 25%;"><spring:message code="i18n.ecommerce_adminOrderDetails.OrderType" /></td>
								<td><B>${orderType}</B>
								</td>
							</tr>
							<tr>
								<td><spring:message code="i18n.ecommerce_adminOrderDetails.OrderDescription" /></td>
								<td>
									<o:nav anchor="true" action="${orderEntity.detailsPageAction}" orderId="${order.id}" quickLookup="true" siteElement="${orderEntity.detailsPageSiteElement}">
										${typeDetail}
									</o:nav>
								</td>
							</tr>
						</tbody>
					</table>
				</c:if>
				<c:if test="${empty orderEntity}">
						<div class="alert alert-danger">
							<orbis:message code="i18n.ecommerce_adminOrderDetails.strongThee8117498453013108" />
						</div>
				</c:if>

					<table class="table table-bordered table-condensed">
						<thead>
							<tr class="subheader">
								<td colspan="2"><spring:message code="i18n.ecommerce_adminOrderDetails.BCustomerI1885346670702398" /> <c:if test="${not empty order.customer.id}">
											<div class="pull-right">
											<o:nav anchor="true" anchorClass="btn btn-mini btn-primary" action="displayCustomerOrders" userId="${order.customer.id}" orderId="${order.id}" quickLookup="true"><spring:message code="i18n.ecommerce_adminOrderDetails.ViewAllCus2569089194739287" /> </o:nav>
									</div>
									</c:if></td>
							</tr>
						</thead>
						<c:if test="${empty order.customer}">
							<tr>
								<td colspan="2"><spring:message code="i18n.ecommerce_adminOrderDetails.AnonymousCustomer" /></td>
							</tr>
							<c:if test="${not empty order.overrideEmail}">
								<tr>
									<td><orbis:message code="i18n.ecommerce_adminOrderDetails.Email3118488049596305" /></td>
									<td>${order.overrideEmail}</td>
								</tr>
							</c:if>
						</c:if>
						<c:if test="${not empty order.customer}">
							<tr>
								<td style="width: 25%;"><spring:message code="i18n.ecommerce_adminOrderDetails.NameUserType" /></td>
								<td><c:choose>
										<c:when test="${not empty acrmSE}">
											<o:nav anchor="true" action="displayContactInfoEditForUser" userId="${order.customer.id}" quickLookup="true" siteElement="${acrmSE}">${order.customer.fullName} (${order.customer.primaryGroup.name})</o:nav>
										</c:when>
										<c:otherwise>
									${order.customer.fullName} (${order.customer.primaryGroup.name})
								</c:otherwise>
									</c:choose>
								</td>
							</tr>
							<tr>
								<td style="width: 25%;"><spring:message code="i18n.ecommerce_adminOrderDetails.Username" /></td>
								<td><c:choose>
										<c:when test="${not empty acrmSE}">
											<o:nav anchor="true" action="displayContactInfoEditForUser" userId="${order.customer.id}" quickLookup="true" siteElement="${acrmSE}">${order.customer.username}</o:nav>
										</c:when>
										<c:otherwise>
									${order.customer.username}
								</c:otherwise>
									</c:choose>
								</td>
							</tr>
							<tr>
								<td><spring:message code="i18n.ecommerce_adminOrderDetails.OrganizationDivision" /></td>
								<td>${order.customer.organization} ${(not empty order.customer.organization && not empty order.customer.company) ? "/" : ""} ${order.customer.company}</td>
							</tr>
							<tr>
								<td><spring:message code="i18n.ecommerce_adminOrderDetails.eMail" /></td>
								<td>
									<c:if test="${not empty order.overrideEmail}">
										${order.overrideEmail}
									</c:if>
									<c:if test="${empty order.overrideEmail}">
										${order.customer.email}
									</c:if>
								</td>
							</tr>
							<tr>
								<td><spring:message code="i18n.ecommerce_adminOrderDetails.Telephone" /></td>
								<td>${order.customer.phoneNumber }</td>
							</tr>
						</c:if>
					</table>

					<div style="border: 1px solid #DDD;">
						<div style="background-color: #7e7e7e; color: #FFF; padding: 4px;">
							<B><spring:message code="i18n.ecommerce_adminOrderDetails.OrderHistory" /></B>
						</div>

						<c:set var="buttons">
							<c:if test="${not empty orderEntity}">
								<a href="javascript:void(0)" class="btn btn-small btn-primary" style="margin-left: 2px;" onclick="createAdjustment()">
									<spring:message code="i18n.ecommerce_adminOrderDetails.CreateAdjustment" />
								</a>
							</c:if>
							<a href="javascript:void(0)" class="btn btn-small btn-primary" style="margin-left: 2px;" onclick="emailOrderHistory()">
								<spring:message code="i18n.ecommerce_adminOrderDetails.Email" />
							</a>
							<a href="javascript:void(0)" class="btn btn-small btn-primary" style="margin-left: 2px;" onclick="printOrderHistory()">
								<spring:message code="i18n.ecommerce_adminOrderDetails.Print" />
							</a>
						</c:set>

						<c:set var="orderId" value="${order.id}" />
						<c:set var="customerId" value="${order.customer.id}" />
						<c:set var="orderNumberPrefix" value="ADJUSTMENT" />
						<c:set var="orderEntityClass" value="${order.orderEntityClass}" />
						<c:set var="orderEntityId" value="${order.orderEntityId}" />
						<c:set var="showManageOrderBtn" value="false" />
						<%@ include file="/WEB-INF/jsp/ecommerce/ecommerce_order_history.jsp"%>
					</div>

					<BR>
					<div style="border: 1px solid #DDD;">
						<div style="background-color: #7e7e7e; color: #FFF; padding: 4px;">
							<spring:message code="i18n.ecommerce_adminOrderDetails.BOrderEmailHistoryB" />
						</div>

						<%@ include file="/WEB-INF/jsp/ecommerce/ecommerce_adminOrderDetails_emailLogs.jsp"%>

					</div>
				</div>
			</div>
		</div>
	</div>
</div>
