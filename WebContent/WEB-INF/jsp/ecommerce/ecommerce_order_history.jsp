<%@ include file="/WEB-INF/jsp/include.jsp"%>

<%-- JSP C:SET ARGUMENTS --%>

<%-- orderEntityClass (required): The fully qualified class-name of the order's EcommerceEntity (eg: "com.orbis.web.content.np.NPosting"). --%>
<c:if test="${empty orderEntityClass}"><c:set var="orderEntityClass" value="UNDEFINED"/></c:if>

<%-- orderEntityId (required): The database ID of the order's EcommerceEntity. --%>
<c:if test="${empty orderEntityId}"><c:set var="orderEntityId" value="UNDEFINED"/></c:if>

<%-- customerId (required): The UserDetailsImpl ID of the customer. --%>
<c:if test="${empty customerId}"><c:set var="customerId" value="UNDEFINED"/></c:if>

<%-- orderNumberPrefix (required): The "order number prefix" to be used for new orders. --%>
<c:if test="${empty orderNumberPrefix}"><c:set var="orderNumberPrefix" value="UNDEFINED"/></c:if>

<%-- defaultProductCode (required): The "default product code" to be used for new order-items ("^[a-zA-Z0-9]*$"). --%>
<c:if test="${empty defaultProductCode}"><c:set var="defaultProductCode" value="UNDEFINED"/></c:if>

<%-- adminMode (optional): set to "true" to allow user to create adjustment orders.  Set to "false" to hide this feature. --%>
<c:if test="${empty adminMode}"><c:set var="adminMode" value="true"/></c:if>

<%-- showButtons (optional): set to "true" to display the 'print' and 'email' buttons.  Set to "false" to hide these features. --%>
<c:if test="${empty showButtons}"><c:set var="showButtons" value="true"/></c:if>

<%-- onOrderClick (optional): set with the name of the javascript function to be fired when the user clicks on an order in the history-list --%>
<c:if test="${empty onOrderClick}"><c:set var="onOrderClick" value="showOrderDetailsModal"/></c:if>

<%-- onOrderEmailSent (optional): set with the name of the javascript function to be fired when the user sends an orderHistory/orderDetail email. --%>
<c:if test="${empty onOrderEmailSent}"><c:set var="onOrderEmailSent" value="undefined"/></c:if>

<%-- onOrderStatusUpdate (optional): set with the name of the javascript function to be fired when the user updates the status of an order. --%>
<c:if test="${empty onOrderStatusUpdate}"><c:set var="onOrderStatusUpdate" value="undefined"/></c:if>

<%-- loadCustomerHistory (optional): set to "true" to show ALL orders belonging to the "customerId".  Set to "false" to show only the order history pertaining to the "orderId" --%>
<c:if test="${empty loadCustomerHistory}"><c:set var="loadCustomerHistory" value="false"/></c:if>

<%-- showManageOrderBtn (optional): set to "false" to hide the red "Ecommerce Order" button. --%>
<c:if test="${empty showManageOrderBtn}"><c:set var="showManageOrderBtn" value="true"/></c:if>

<%-- additionalParams (optional): a json-object string of "additional request parameters" that will be appended to the various requests. --%>
<c:if test="${empty additionalParams}"><c:set var="additionalParams" value=""/></c:if>

<c:if test="${empty buttons}">
	<c:set var="buttons">
		<a href="javascript:void(0)" class="btn btn-small" onclick="printOrderHistory()"><spring:message code="i18n.ecommerce_order_history.Print" /></a>
		<a href="javascript:void(0)" class="btn btn-small" onclick="emailOrderHistory()"><spring:message code="i18n.ecommerce_order_history.Email" /></a>
		<c:if test="${adminMode}">
			<a href="javascript:void(0)" class="btn btn-inverse btn-mini pull-right" onclick="createAdjustment()"><spring:message code="i18n.ecommerce_order_history.CreateAdjustment" /></a>
		</c:if>
	</c:set>
</c:if>

<style>
	tfoot.orderItemsFooter td {border-top:2px solid #DDD;}
	table.footerTotals {font-weight:bold;float:right;}
	table.footerTotals td {border:none;text-align:right;padding:0px 0px 0px 5px;}
	table.orderHistoryTable {margin-bottom:0px;}
	table.orderHistoryTable tbody tr:hover {cursor:pointer;}
	table.orderHistoryTable tbody tr.selectedOrder td {font-weight:bold;background-color:#CCE8FF;}
	table.orderHistoryTable tfoot tr td {border-top:2px solid #AAA;border-bottom:2px solid #AAA;background-color:#DDD; font-weight:bold;font-size:14px;}
	table.summaryTable {border:none;}
	table.summaryTable td {text-align:right;border:none;padding:0px;}
	table.summaryTable th {text-align:right;border:none;padding:0px;}
	#orderDetailsModal {width:940px;margin-left:-470px;}
	#emailOrderModal {width:940px;margin-left:-470px;}
</style>

<div class="orbis-posting-actions text-center"> ${buttons} </div>

<div id="orderHistory"><spring:message code="i18n.ecommerce_order_history.loadingorderhistory" /></div>

<script type="text/javascript">
	var orderClickedFunc = ${onOrderClick};
	var orderEmailedFunc = ${onOrderEmailSent};
	var orderStatusUpdateFunc = ${onOrderStatusUpdate};
	
	$(window).load(function(){
		$(document).on("click", "table.orderHistoryTable tbody tr", function(){
			var orderId = $(this).data("orderId");
			$("tr", "table.orderHistoryTable tbody").removeClass("selectedOrder");
			$("tr[data-order-id=" + orderId + "]", "table.orderHistoryTable tbody").addClass("selectedOrder");
			orderClickedFunc(orderId);
		});

		<%-- fixes z-index problems --%>
		$("#orderDetailsModal").appendTo("body");
		$("#emailOrderModal").appendTo("body");
		
		loadHistory();
	});
	
	function loadHistory()
	{
		<c:choose>
			<c:when test="${loadCustomerHistory}">
				loadCustomerHistory("${customerId}", "${orderId}");
			</c:when>
			<c:otherwise>
				loadOrderHistory("${orderId}");
			</c:otherwise>
		</c:choose>
	}

	function loadOrderHistory(orderId)
	{
		var request = {
			action: '<o:encrypt action="ajaxLoadOrderHistory" />',
			orderId: orderId,
			adminMode: ${adminMode},
			rnd: Math.floor(Math.random() * 1000000)
		};
		
		$("#orderHistory").load("${siteElement.fullPath}.htm", request, function(data, status, xhr){
			if (!orbisApp.isHappyAjaxResponse(xhr))
			{
				// try again...
				$("#orderHistory").empty();
				$("#orderHistory").text('<spring:message code="i18n.ecommerce_order_history.loadingorderhistory" javaScriptEscape="true"/>');
				loadHistory(); 
			}
		});
	}

	function loadCustomerHistory(customerId, orderId)
	{
		var request = {
			action: '<o:encrypt action="ajaxLoadCustomerOrders" />',
			customerId: customerId,
			orderId: orderId,
			moduleId: '${ecommerceModuleId}',
			adminMode: ${adminMode},
			rnd: Math.floor(Math.random() * 1000000)
		};
		
		$("#orderHistory").load("${siteElement.fullPath}.htm", request, function(data, status, xhr){
			if (!orbisApp.isHappyAjaxResponse(xhr))
			{
				// try again...
				$("#orderHistory").empty();
				$("#orderHistory").text('<spring:message code="i18n.ecommerce_order_history.loadingorderhistory" javaScriptEscape="true"/>');
				loadHistory(); 
			}
		});
	}

	function showOrderDetailsModal(orderId)
	{
		var request = {
				action: '<o:encrypt action="ajaxLoadOrderDetails" />',
				orderId: orderId,
				adminMode: ${adminMode},
				rnd: Math.floor(Math.random() * 1000000)
			};
			
		$(".modal-body", "#orderDetailsModal").load("${siteElement.fullPath}.htm", request, function(data, status, xhr){
			if (orbisApp.checkAjaxResponse(xhr))
			{
				if (orderDetails.orderStatus == 1 && orderDetails.paymentVendorConfigured)
				{
					$("button#payButton", "#orderDetailsModal").show();
				}
				else 
				{
					$("button#payButton", "#orderDetailsModal").hide();
				}
				
				if (orderDetails.isReconciliationOrder)
				{
					$("button#backToReconcileTargetButton", "#orderDetailsModal").show();
				}
				else 
				{
					$("button#backToReconcileTargetButton", "#orderDetailsModal").hide();
				}
				
				if(orderDetails.adminModuleUrl)
				{
					$("#manageOrderBtn", "#orderDetailsModal").show();
				}
				else
				{
					$("#manageOrderBtn", "#orderDetailsModal").hide();
				}

				$("#orderDetailsModal").data("orderId", orderId);
				$("#orderDetailsModal").data("adminModuleUrl", orderDetails.adminModuleUrl);
				$("#orderDetailsModal").modal("show");
			}
		});
	}

	function printOrderHistory()
	{
		if ($("tr", "table.orderHistoryTable tbody").length == 0)
		{
			orbisApp.alertDialog('<spring:message code="i18n.ecommerce_order_history.Thereisnot6633462601234983" javaScriptEscape="true"/>');
		}
		else
		{
			var form = orbisApp.buildForm({
				action : '<o:encrypt action="printOrderHistory" />',
				orderId : parentOrderId,
				customerId: "${customerId}",
				moduleId: '${ecommerceModuleId}',
				rnd: Math.floor(Math.random() * 1000000)
			}, "${siteElement.fullPath}.htm");
			form.get(0).target = "_blank";
			form.submit();
		}
	}
	
	function printOrder()
	{
		var form = orbisApp.buildForm({
			action : '<o:encrypt action="printOrder" />',
			orderId : $("#orderDetailsModal").data("orderId"),
			rnd: Math.floor(Math.random() * 1000000)
		}, "${siteElement.fullPath}.htm");
		form.get(0).target = "_blank";
		form.submit();
	}
	
	function emailOrderHistory()
	{
		if ($("tr", "table.orderHistoryTable tbody").length == 0)
		{
			orbisApp.alertDialog('<spring:message code="i18n.ecommerce_order_history.Thereisnot0914019325042904" javaScriptEscape="true"/>');
		}
		else
		{
			var request = {
					action: '<o:encrypt action="ajaxLoadEmailOrderHistory" />',
					orderId: parentOrderId,
					customerId: "${customerId}",
					moduleId: '${ecommerceModuleId}',
					adminMode: ${adminMode},
					rnd: Math.floor(Math.random() * 1000000)
				};
				
			$(".modal-body", "#emailOrderModal").load("${siteElement.fullPath}.htm", request, function(data, status, xhr){
				if(orbisApp.checkAjaxResponse(xhr)) {
					$("#emailOrderModalTitle", "#emailOrderModal").text('<spring:message code="i18n.ecommerce_order_history.emailOrderHistory" javaScriptEscape="true"/>');
					$("#emailOrderModal").data("emailType", "orderHistory");
					$("#emailOrderModal").modal("show");
				}
			});
		}
	}

	function emailOrder()
	{
		$("#orderDetailsModal").modal("hide");

		var request = {
				action: '<o:encrypt action="ajaxLoadEmailOrder" />',
				orderId: $("#orderDetailsModal").data("orderId"),
				adminMode: ${adminMode},
				rnd: Math.floor(Math.random() * 1000000)
			};
			
			$(".modal-body", "#emailOrderModal").load("${siteElement.fullPath}.htm", request, function(data, status, xhr){
				if(orbisApp.checkAjaxResponse(xhr)) {
					$("#emailOrderModalTitle", "#emailOrderModal").text('<spring:message code="i18n.ecommerce_order_history.EmailOrderInfo" javaScriptEscape="true"/>');
					$("#emailOrderModal").data("emailType", "orderDetail");
					$("#emailOrderModal").modal("show");
				}
			});
	}
	
	function sendOrderEmail()
	{
		if($("#ecommerceEmailerForm").valid())
		{
			var request = {
				action: '<o:encrypt action="ajaxSendOrderEmail" />',
				emailTo: $("input[name=emailTo]", "#emailOrderModal").val(),
				emailFrom: $("input[name=emailFrom]", "#emailOrderModal").val(),
				emailSubject: $("input[name=emailSubject]", "#emailOrderModal").val(),
				emailMessage: $("textarea[name=emailMessage]", "#emailOrderModal").val(),
				emailOrderInfo: $("#emailOrderInfo", "#emailOrderModal").html(),
				emailType: $("#emailOrderModal").data("emailType"),
				orderEntityClass: "${orderEntityClass}",
				orderEntityId: "${orderEntityId}",
				rnd: Math.floor(Math.random() * 1000000)
			};
	
			$.post("${siteElement.fullPath}.htm", request, function(data, status, xhr){
				if(orbisApp.checkAjaxResponse(xhr)) {
					if (data.successful)
					{
						$("#emailOrderModal").modal("hide");
						orbisApp.timedMessageDialog('<spring:message code="i18n.email_commonEmailerDone.EmailSent" javaScriptEscape="true"/>', 2000);
	
						if (typeof orderEmailedFunc === 'function')
						{
							orderEmailedFunc();	
						}
					}
					else
					{
						var errMsg = $(document.createElement("div")).html('<spring:message code="i18n.email_commonEmailerDone.EmailFailed" javaScriptEscape="true"/>').addClass("alert").addClass("alert-error");
						$(".modal-body", "#emailOrderModal").prepend(errMsg);
						$(errMsg).stepDelay(5000, function() {$(errMsg).fadeOut("slow");});
					}
				}
			}, "json");
		}
		else
		{
			document.getElementById("ecommerceEmailerForm").scrollIntoView();
		}
	}
	
	function manageOrder()
	{
		orbisApp.buildForm({
			action : '<o:encrypt action="displayOrderDetails" />',
			orderId : $("#orderDetailsModal").data("orderId"),
			rnd: Math.floor(Math.random() * 1000000)
		}, $("#orderDetailsModal").data("adminModuleUrl")).submit();
	}
	
	function reconcileOrder()
	{
		var request = {
			action : '<o:encrypt action="reconcileOrderCheckout" />',
			orderId : $("#orderDetailsModal").data("orderId"),
			rnd : Math.floor(Math.random() * 1000000)
		};
		<c:if test="${not empty additionalParams}">
			$.extend(request, ${additionalParams});
		</c:if>
		orbisApp.buildForm(request, "${siteElement.fullPath}.htm").submit();
	}
	
	function backToReconcileTarget()
	{
		showOrderDetailsModal(orderDetails.reconcileTargetId);
	}
</script>

<%-- *** ADMIN MODE JAVASCRIPT *** --%>
<c:if test="${true}">
	<orbis:addComponent component="agilityjs" />
	<orbis:addComponent component="json" />
	<orbis:addComponent component="accounting" />
	<orbis:addComponent component="jqueryValidate" version="1.11.1" />
	<script type="text/javascript">
		var orderItemEditor;
		var parentOrderId;
		var taxOptions = {};
		var order = {};
		var orderItems = {};
		var defaultProductCode = "${defaultProductCode}";

		var validateCfg = {
			rules: {
				productCode: {
					required: true,
					minlength: 4
				},
				productName: {
					required: true
				},
				productDesc: {
					required: true
				},
				subTotal: {
					required: true,
					money: true
				}
			}
		}
		
		/*** AGILITY PROTOTYPE: ORDER-ITEM VIEW  ***/ 
		var orderItemViewProto = $$({
			view: {
			  format: '<tr class="orderItem">\
					       <td style="vertical-align:middle;">\
								<a class="deleteItem" href="javascript:void(0)" title="<spring:message htmlEscape="true" javaScriptEscape="true" code="i18n.ecommerce_order_history.deleteitem" />"><img src="${RESOURCES_URL}/core/images/icons/icon-delete.png"></a>\
						   </td>\
					       <td class="itemClick">\
			  					<span data-bind="productCode" class="boldCaps"></span> - <span data-bind="productName"></span>\
			  					<div data-bind="productDesc" style="font-style:italic;"></div>\
			  				</td>\
						   <td>\
						   		<table class="orderItemTotals">\
						   		</table>\
						   </td>\
				       </tr>',
			   style: '& .boldCaps {font-weight:bold;text-transform:uppercase;}\
				       & .itemClick:hover {cursor:pointer;}\
				       & table.orderItemTotals {float:right;padding:0px;margin:0px;}\
			   		   & table.orderItemTotals td {border:none;text-align:right;padding:0px 0px 0px 5px;}'
			},
			controller: {
				'click .deleteItem': function() {deleteOrderItem(this);},
				'click .itemClick': function() {editOrderItem(this);},
				'create': function() {this.renderTotals();},
				'change': function() {this.renderTotals();}
			}, 
			renderTotals: function() {
				var subTotal = parseFloat(this.model.get("subTotal"));
				var runningTotal = subTotal;
				this.view.$(".orderItemTotals").empty();
				this.view.$(".orderItemTotals").append(this.renderTotal("", subTotal));
				var taxOptions = this.model.get("taxOptions");
				if (!$.isEmptyObject(taxOptions))
				{
					for (var taxId in taxOptions)
					{
						var taxOption = taxOptions[taxId];
						if (taxOption.checked)
						{
							var taxLabel = taxOption.taxCode + ' (' + taxOption.taxValue + '%)';
							taxOption.taxTotal = accounting.toFixed((subTotal * (taxOption.taxValue / 100)), 2);
							runningTotal += parseFloat(taxOption.taxTotal);
							this.view.$(".orderItemTotals").append(this.renderTotal(taxLabel, taxOption.taxTotal));
						}
						else
						{
							delete taxOption['taxTotal'];
						}
					}
				}
				
				var thisModel = this.model.get();
				thisModel.total = runningTotal;
				this.model.set(thisModel, {silent:true});
				
				this.formatTotals();
			},
			renderTotal: function(label, total) {
				return '<tr><td>' + label + '</td><td class="amt">' + total + '</td></tr>';
			},
			formatTotals: function() {
				this.view.$(".amt").each(function() {
					$(this).text(accounting.formatMoney($(this).text()));
				});
			}
		});

		let csrfToken = $("meta[name='_csrf']").attr("content");
		let csrfTokenParamName = $("meta[name='_csrf_param_name']").attr("content");
		let csrfInput = `<input type="hidden" name="\${csrfTokenParamName}" value="\${csrfToken}"/>`;
		
		/*** AGILITY PROTOTYPE: ORDER-ITEM EDITOR  ***/ 
		var orderItemEditorProto = $$({
			view: {
			  format: `<form enctype="multipart/form-data">
			            \${csrfInput}
			  			<table class="table table-condensed">
	  				       <tr>
						       <td><spring:message code="i18n.ecommerce_order_history.ProductCode" /></td>
							   <td><input type="text" data-bind="productCode" name="productCode"></td>
					       </tr>
	  				       <tr>
						       <td><spring:message code="i18n.ecommerce_order_history.ProductName" /></td>
							   <td><input type="text" data-bind="productName" name="productName"></td>
					       </tr>
	  				       <tr>
						       <td><spring:message code="i18n.ecommerce_order_history.ProductDescription" /></td>
							   <td><input type="text" data-bind="productDesc" name="productDesc"></td>
					       </tr>
	  				       <tr>
						       <td><spring:message code="i18n.ecommerce_order_history.Charge" /></td>
							   <td><input type="text" data-bind="subTotal" placeholder="0.00" style="text-align:right;" name="subTotal"></td>
					       </tr>
	  				       <tr class="taxRow">
						       <td><spring:message code="i18n.ecommerce_order_history.ApplicableTaxes" /></td>
							   <td class="taxCell"></td>
					       </tr>
				       	</table>
				      </form>`,
				style: '& .taxRow {display:none;}'
			},
			controller: {
				'create': function(){
					var taxOptions = this.model.get("taxOptions");
					if (!$.isEmptyObject(taxOptions))
					{
						for (var taxId in taxOptions)
						{
							this.view.$(".taxCell").append(this.renderTaxOption(taxOptions[taxId]));
						}
						this.view.$(".taxRow").show();
					}
				},
				'click .taxChk': function(evt){
					var taxId = $(evt.target).data("taxId");
					var taxOptions = this.model.get("taxOptions");
					taxOptions[taxId].checked = evt.target.checked;
				}
			},
			renderTaxOption: function(taxOption) {
				return '<input type="checkbox" class="taxChk" data-tax-id="' + taxOption.taxId + '" ' + (taxOption.checked ? 'checked' : '') + '> ' + taxOption.taxCode + ' (' + taxOption.taxValue + '%)<BR>';
			}
		});	
		
		$(window).load(function(){
		     jQuery.validator.addMethod("money", function(value, element) {
		         return this.optional(element) || /^[+-]?[0-9]{1,3}(?:,?[0-9]{3})*(?:\.[0-9]{2})?$/.test(value);
		     }, '<spring:message code="i18n.ecommerce_order_history.MustBeInCu25142900998939477" javaScriptEscape="true" />');

			 <%-- fixes z-index problems --%>
		     $("#editOrderModal").appendTo("body");
		     $("#editOrderItemModal").appendTo("body");
		     $("#orderStatusModal").appendTo("body");
		     $("#orderNoteModal").appendTo("body");
		});
		
		
		function createAdjustment()
		{
			$("#editOrderModal").modal("show");
		}
		
		function editOrderItem(orderItem)
		{
			var editModel = {};

			if (orderItem)
			{
				// edit existing order-item...
				editModel = orderItem.model.get();
				editModel.orderItem = orderItem;
			}
			else
			{
				// add/edit NEW order-item...
				editModel.taxOptions = jQuery.extend(true, {}, taxOptions);
				editModel.id = "NEW_" + Math.floor(Math.random() * 10000000);
				editModel.productCode = defaultProductCode;
			}

			orderItemEditor = $$(orderItemEditorProto, editModel);
			
			$(".modal-body", "#editOrderItemModal").empty();
			$$.document.append(orderItemEditor, $(".modal-body", "#editOrderItemModal"));
			$("form", "#editOrderItemModal").validate(validateCfg);
			$("#editOrderModal").modal("hide");
			$("#editOrderItemModal").modal("show");
		}
		
		function saveOrderItem()
		{
			if ($("form", "#editOrderItemModal").valid())
			{
				var orderItem = orderItemEditor.model.get("orderItem"); 
				if (orderItem)
				{
					// update existing order-item...
					var viewModel = orderItemEditor.model.get();
					delete viewModel['orderItem'];
					orderItem.model.set(viewModel);
				}
				else
				{
					// add new order-item...
					orderItem = $$(orderItemViewProto, orderItemEditor.model.get());
					$$.document.append(orderItem, $(".orderItemsList", "#editOrderModal"));
				}
		
				orderItems[orderItem.model.get("id")] = orderItem;
				refreshOrder();
				
				$("#editOrderModal").modal("show");
				$("#editOrderItemModal").modal("hide");
			}
		}
		
		function deleteOrderItem(orderItem)
		{
			delete orderItems[orderItem.model.get("id")];
			orderItem.destroy();
			refreshOrder();
		}
		function cancelOrderItem()
		{
			$("#editOrderModal").modal("show");
			$("#editOrderItemModal").modal("hide");
		}
		
		function refreshOrder()
		{
			// *** CALC TOTALS...
			var totalsModel = {};
			totalsModel.runningSubTotal = 0;
			totalsModel.runningTotal = 0;
			totalsModel.taxOptions = jQuery.extend(true, {}, taxOptions);

			for (var k in orderItems)
			{
				var orderItem = orderItems[k];
				if (orderItem.model.get("subTotal"))
				{
					totalsModel.runningSubTotal += parseFloat(orderItem.model.get("subTotal"));
					totalsModel.runningTotal += parseFloat(orderItem.model.get("subTotal"));
				}

				for (var taxId in orderItem.model.get("taxOptions"))
				{
					var taxOption = orderItem.model.get("taxOptions")[taxId];
					
					if (taxOption.checked)
					{
						totalsModel.taxOptions[taxId].checked = true;
						if (!totalsModel.taxOptions[taxId].taxTotal)
						{
							totalsModel.taxOptions[taxId].taxTotal = 0;
						}
						var taxTotal = parseFloat(taxOption.taxTotal);
						totalsModel.taxOptions[taxId].taxTotal += taxTotal;
						totalsModel.runningTotal += taxTotal;
					}
				}
			}
			
			// *** RENDER TOTALS...
			$(".footerTotals").empty();
			$(".footerTotals").append(renderTotal("Sub Total", totalsModel.runningSubTotal));
			for (var taxId in totalsModel.taxOptions)
			{
				var taxOption = totalsModel.taxOptions[taxId];
				if (taxOption.checked)
				{
					$(".footerTotals").append(renderTotal(taxOption.taxCode, taxOption.taxTotal));
				}
			}
			$(".footerTotals").append(renderTotal("TOTAL", totalsModel.runningTotal));
			$(".amt", ".footerTotals").each(function(){$(this).text(accounting.formatMoney($(this).text()))});
			
			// *** REFRESH 'ORDER' OBJECT (gets sent at checkout-time)...
			order = {};
			order.subTotal = totalsModel.runningSubTotal;
			order.total = totalsModel.runningTotal;
			order.orderItems = {};
			for (var k in orderItems)
			{
				order.orderItems[k] = orderItems[k].model.get();
			}
		}
		
		function renderTotal(label, total) {
			return '<tr><td>' + label + '</td><td class="amt">' + total + '</td></tr>';
		}

		function checkout()
		{
			if (!order.orderItems || order.orderItems.length == 0)
			{
				orbisApp.alertDialog('<spring:message code="i18n.ecommerce_order_history.Youneedtoc25142900998939477" javaScriptEscape="true"/>');
			}
			else
			{
				order.customerId = "${customerId}";
				order.orderNumberPrefix = "${orderNumberPrefix}";
				order.parentOrderId = parentOrderId;
				order.paymentType = $("select[name=paymentType]", "#editOrderModal").val();
				order.note = $("input[name=note]", "#editOrderModal").val();
				order.orderEntityClass = "${orderEntityClass}";
				order.orderEntityId = "${orderEntityId}";

				orbisApp.buildForm({
					action : '<o:encrypt action="checkoutAdjustmentOrder" />',
					jsonOrder : JSON.stringify(order)
				}, "${siteElement.fullPath}.htm").submit();
			}
		}
		
		function openStatusModal()
		{
			$("#orderDetailsModal").modal("hide");
			$("#orderStatusModal").modal("show");
		}
		
		function closeStatusModal()
		{
			$("#orderStatusModal").modal("hide");
			$("#orderDetailsModal").modal("show");
		}
		
		function saveStatus()
		{
			var status = $("#statusSelect", "#orderStatusModal").val();
			
			if (!$.isNumeric(status))
			{
				orbisApp.alertDialog('<spring:message code="i18n.ecommerce_order_history.Astatushas9023851526486635" javaScriptEscape="true"/>');
			}
			else
			{
				closeStatusModal();

				var request = {
					action: '<o:encrypt action="ajaxSaveOrderStatus" />',
					orderId: $("#orderDetailsModal").data("orderId"),
					status: $("#statusSelect", "#orderStatusModal").val()
				};

				$.post("${siteElement.fullPath}.htm", request, function(data, status, xhr){
					if(orbisApp.checkAjaxResponse(xhr)) {
						showOrderDetailsModal($("#orderDetailsModal").data("orderId"));
						loadHistory();
						
						if (typeof orderStatusUpdateFunc === 'function')
						{
							orderStatusUpdateFunc();
						}
					}
				}, "json");
			}
		}

		function openNoteModal()
		{
			$("#noteInput", "#orderNoteModal").val("");
			$("#orderNoteModal").modal("show");
		}

		function saveEcommerceOrderNote()
		{
			$("#orderNoteModal").modal("hide");

			var request = {
				action: '<o:encrypt action="ajaxSaveOrderNote" />',
				orderId: $("#orderDetailsModal").data("orderId"),
				note: $("#noteInput", "#orderNoteModal").val()
			};

			$.post("${siteElement.fullPath}.htm", request, function(data, status, xhr){
				if(orbisApp.checkAjaxResponse(xhr)) {
					showOrderDetailsModal($("#orderDetailsModal").data("orderId"));
				}
			}, "json");
		}
	</script>
</c:if>

<%-- ORDER DETAILS DIALOG --%>
<div id="orderDetailsModal" class="modal hide" tabindex="-1" role="dialog" aria-labelledby="orderDetailsTitle" aria-hidden="true">
  <div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
    <h3 id="orderDetailsTitle"><spring:message code="i18n.ecommerce_order_history.OrderDetails" /></h3>
  </div>
  <div class="modal-body">
  </div>
  <div class="modal-footer">
	<c:if test="${adminMode}">
	    <button class="btn btn-primary pull-left" onclick="openStatusModal()"><spring:message code="i18n.ecommerce_order_history.UpdateStatus" /></button>
	    <button class="btn btn-primary pull-left" onclick="openNoteModal()"><spring:message code="i18n.ecommerce_order_history.AddNote" /></button>
	</c:if>
	<button id="payButton" class="btn btn-danger pull-left" onclick="reconcileOrder()"><spring:message code="i18n.ecommerce_order_history.reconcileOrder" /></button>
	<button id="backToReconcileTargetButton" class="btn btn-inverse pull-left" onclick="backToReconcileTarget()" aria-label="Back to Reconcile Target"><B class="icon-hand-left"></B></button>

	<c:if test="${showManageOrderBtn}">
	    <button id="manageOrderBtn" class="btn btn-danger" onclick="manageOrder()" style="display:none;"><spring:message code="i18n.ecommerce_order_history.EcommerceOrder" /></button>
	</c:if>
    <button class="btn btn-info" onclick="printOrder()"><spring:message code="i18n.ecommerce_order_history.Print" /></button>
    <button class="btn btn-info" onclick="emailOrder()"><spring:message code="i18n.ecommerce_order_history.Email" /></button>
    <button class="btn btn-inverse" data-dismiss="modal" aria-hidden="true"><spring:message code="i18n.ecommerce_order_history.Close" /></button>
  </div>
</div>

<%-- EMAIL ORDER DIALOG --%>
<div id="emailOrderModal" class="modal hide" tabindex="-1" role="dialog" aria-labelledby="emailOrderModalTitle" aria-hidden="true">
  <div class="modal-header">
    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
    <h3 id="emailOrderModalTitle"><spring:message code="i18n.ecommerce_order_history.EmailOrderInfo" /></h3>
  </div>
  <div class="modal-body">
  </div>
  <div class="modal-footer">
    <button class="btn btn-primary" onclick="sendOrderEmail()"><spring:message code="i18n.ecommerce_order_history.Send" /></button>
    <button class="btn btn-inverse" data-dismiss="modal" aria-hidden="true"><spring:message code="i18n.ecommerce_order_history.Close" /></button>
  </div>
</div>


<%-- *** ADMIN MODE DIALOGS *** --%>
<c:if test="${adminMode}">

	<%-- EDIT ORDER DIALOG --%>
	<div id="editOrderModal" class="modal hide" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
	  <div class="modal-header">
	    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
	    <h3 id="myModalLabel"><spring:message code="i18n.ecommerce_order_history.CreateAdju32371574560610605" /></h3>
	  </div>
	  <div class="modal-body">
	    
		<form enctype="multipart/form-data" >
			<input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
			<spring:message code="i18n.ecommerce_order_history.BORDERCONFIGB" />
			<table class="table table-condensed">
				<tr>
					<td><spring:message code="i18n.ecommerce_order_history.Type" /></td>
					<td>
						<select name="paymentType">
							<option value="0"><spring:message code="i18n.ecommerce_order_history.CreditCard" /></option>
							<option value="1"><spring:message code="i18n.ecommerce_order_history.Cash" /></option>
							<option value="2"><spring:message code="i18n.ecommerce_order_history.Cheque" /></option>
							<option value="3"><spring:message code="i18n.ecommerce_order_history.FreeOverride" /></option>
							<option value="4"><spring:message code="i18n.ecommerce_order_history.Invoice" /></option>
							<option value="5"><spring:message code="i18n.ecommerce_order_history.REFUND" /></option>
						</select>
					</td>
				</tr>
				<tr>
					<td><spring:message code="i18n.ecommerce_order_history.Noteoptional" /></td>
					<td>
						<input type="text" name="note" maxlength="255">
					</td>
				</tr>
			</table>
			
			<spring:message code="i18n.ecommerce_order_history.BORDERITEMSB" />
			<table class="table table-condensed table-hover">
				<col width="25">
				<tbody class="orderItemsList"></tbody>
				<tfoot class="orderItemsFooter">
					<tr>
						<td colspan="2" style="min-width:100px;">
							<a href="javascript:void(0)" class="btn btn-inverse btn-mini" onclick="editOrderItem()"><spring:message code="i18n.ecommerce_order_history.AddOrderItem" /></a>
						</td>
						<td style="text-align:right;">
					   		<table class="footerTotals">
					   		</table>
						</td>
					</tr>
				</tfoot>
			</table>
		</form>
	    
	  </div>
	  <div class="modal-footer">
	    <a href="#" class="btn" data-dismiss="modal"><spring:message code="i18n.ecommerce_order_history.Close" /></a>
	    <a href="#" class="btn btn-primary" onclick="checkout()"><spring:message code="i18n.ecommerce_order_history.Checkout" /></a>
	  </div>
	</div>
	
	<%-- ORDER-ITEM DIALOG --%>
	<div id="editOrderItemModal" class="modal hide" tabindex="-1" role="dialog" aria-labelledby="orderItemTitle" aria-hidden="true">
	  <div class="modal-header">
	    <button type="button" class="close" onclick="cancelOrderItem()" aria-hidden="true">&times;</button>
	    <h3 id="orderItemTitle"><spring:message code="i18n.ecommerce_order_history.OrderItem" /></h3>
	  </div>
	  <div class="modal-body">
	  </div>
	  <div class="modal-footer">
	    <a href="#" class="btn" onclick="cancelOrderItem()"><spring:message code="i18n.ecommerce_order_history.Cancel" /></a>
	    <a href="#" class="btn btn-primary" onclick="saveOrderItem()"><spring:message code="i18n.ecommerce_order_history.Save" /></a>
	  </div>
	</div>

	<%-- ORDER-STATUS DIALOG --%>
	<div id="orderStatusModal" class="modal hide" tabindex="-1" role="dialog" aria-labelledby="paymentStatusTitle" aria-hidden="true">
	  <div class="modal-header">
	    <button type="button" class="close" aria-hidden="true" onclick="closeStatusModal()">&times;</button>
	    <h3 id="paymentStatusTitle"><spring:message code="i18n.ecommerce_order_history.ChangePaymentStatus" /></h3>
	  </div>
	  <div class="modal-body">
			<spring:message code="i18n.ecommerce_order_history.BSelectStatusB" />
			<select id="statusSelect">
				<option></option>
				<option value="1"><spring:message code="i18n.ecommerce_order_history.PENDING" /></option>
				<option value="2"><spring:message code="i18n.ecommerce_order_history.PAID" /></option>
				<option value="3"><spring:message code="i18n.ecommerce_order_history.FAILED" /></option>
				<option value="4"><spring:message code="i18n.ecommerce_order_history.INCOMPLETE" /></option>
				<option value="5"><spring:message code="i18n.ecommerce_order_history.CANCELLED" /></option>
			</select>
	  </div>
	  <div class="modal-footer">
	    <button class="btn btn-primary" onclick="saveStatus()"><spring:message code="i18n.ecommerce_order_history.SaveStatus" /></button>
	    <button class="btn btn-inverse" onclick="closeStatusModal()" aria-hidden="true"><spring:message code="i18n.ecommerce_order_history.Close" /></button>
	  </div>
	</div>
	
	<%-- ORDER-NOTE DIALOG --%>
	<div id="orderNoteModal" class="modal hide" tabindex="-1" role="dialog" aria-labelledby="newNoteTitle" aria-hidden="true">
	  <div class="modal-header">
	    <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
	    <h3 id="newNoteTitle"><spring:message code="i18n.ecommerce_order_history.CreateNewNote" /></h3>
	  </div>
	  <div class="modal-body">
		<spring:message code="i18n.ecommerce_order_history.BEnterNoteB" /><BR>
		<input id="noteInput" type="text" style="width:95%;">
	  </div>
	  <div class="modal-footer">
	    <button class="btn btn-primary" onclick="saveEcommerceOrderNote()"><spring:message code="i18n.ecommerce_order_history.SaveNote" /></button>
	    <button class="btn btn-inverse" data-dismiss="modal" aria-hidden="true"><spring:message code="i18n.ecommerce_order_history.Close" /></button>
	  </div>
	</div>
</c:if>
