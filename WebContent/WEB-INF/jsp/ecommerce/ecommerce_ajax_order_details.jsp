<%@ include file="/WEB-INF/jsp/include.jsp"%>

<c:if test="${not empty order}">
<c:if test="${empty adminMode}"><c:set var="adminMode" value="true"/></c:if>
<script type="text/javascript">

	var orderDetails = {
		orderStatus : "${order.status}",
		isReconciliationOrder : ${order.reconciliationOrder},
		reconcileTargetId : ${order.reconciliationOrder ? order.reconcileTarget.id : "undefined"},
		adminModuleUrl : "${adminModuleUrl}",
		paymentVendorConfigured: ${paymentVendorConfigured}
	};

</script>
<style>
	.sectionHeader {font-weight:bold;font-size:12pt;border-bottom: 1px solid #000000;}
	.formTableDiv {width:500px;margin-left:auto;margin-right:auto;padding:15px 10px 15px 10px;}
	.formTable td {text-align:left;}
	.dAnswer {font-weight: bold;}	
	.alignRight {text-align: right;} 
	.noteTable {width:100%;}
	.noteTable td {padding:3px;border-bottom:1px dotted #DDD;}
	.noteHeader {font-weight:bold;}
</style>

<table class="table table-condensed">
	<tbody>
		<tr>
			<td style="border:none;"><orbis:message code="i18n.ecommerce_ajax_order_details.OrderNumber" /></td>
			<td style="border:none;"><B>${order.orderNumber}</B></td>
		</tr>
		<tr>
			<td><orbis:message code="i18n.ecommerce_ajax_order_details.OrderDate" /></td>
			<td>
				<fmt:formatDate value="${order.orderDate}" pattern="${orbisDateLong2} @ ${orbisTimeShort}" />
			</td>
		</tr>
		<tr>
			<td><orbis:message code="i18n.ecommerce_ajax_order_details.OrderTypeDetail" /></td>
			<td>
				<o:nav anchor="true" action="${orderEntity.detailsPageAction}" orderId="${order.id}" siteElement="${orderEntity.detailsPageSiteElement}">
					${orderType} - ${typeDetail}
				</o:nav>
			</td>
		</tr>
		<tr>
			<td><orbis:message code="i18n.ecommerce_ajax_order_details.PaymentStatus" /></td>
			<td>
				<orbis:message code="i18n.common.eCommerce.order.status.${order.status}"/> 
			</td>
		</tr>
		<tr>
			<td><orbis:message code="i18n.ecommerce_ajax_order_details.PaymentMethod" /></td>
			<td><orbis:message code="i18n.common.eCommerce.order.type.${order.paymentType}"/></td>
		</tr>
		<c:if test="${order.paymentType == 0}">
			<tr>
				<td><orbis:message code="i18n.ecommerce_ajax_order_details.CreditCard14889910454103739" /></td>
				<td>
					<table class="subTable">
						<tr>
							<td><orbis:message code="i18n.ecommerce_ajax_order_details.PaymentVendor" /></td>
							<td class="dAnswer">${order.paymentVendorLabel}</td>
						</tr>
						<c:if test="${not empty order.cardType}">
							<tr>
								<td><orbis:message code="i18n.ecommerce_ajax_order_details.CardType" /></td>
								<td class="dAnswer">${order.cardType}</td>
							</tr>
						</c:if>
						<c:if test="${not empty order.transactionId}">
							<tr>
								<td><orbis:message code="i18n.ecommerce_ajax_order_details.TransactionID" /></td>
								<td class="dAnswer">
									<c:if test="${not empty order.transactionId}">
										${order.transactionId}
									</c:if>
								</td>
							</tr>
						</c:if>
					</table> 
					<c:if test="${not empty order.transactionDataMap}">
						<a id="showMoreTxn" href="javascript:void(0)" onclick="$(this).hide();$('#txnInfo').show('blind');"><orbis:message code="i18n.ecommerce_ajax_order_details.showmoreinfo" /></a>
						<table id="txnInfo" class="subTable" style="display: none;">
							<c:forEach var="map" items="${order.transactionDataMap}">
								<tr>
									<td>${map.key}</td>
									<td style="font-weight: bold;"><c:choose>
											<c:when test="${map.key == 'exact_ctr'}">
												<pre>${map.value}</pre>
											</c:when>
											<c:otherwise>
												${map.value}
											</c:otherwise>
										</c:choose>
									</td>
								</tr>
							</c:forEach>
							<tr>
								<td colspan="2">
									<a href="javascript:void(0)" onclick="$('#showMoreTxn').show();$('#txnInfo').hide('blind');"><orbis:message code="i18n.ecommerce_ajax_order_details.showlessinfo" /></a>
								</td>
							</tr>
						</table>
					</c:if>
				</td>
			</tr>
		</c:if>
	</tbody>
</table>


<c:if test="${not empty orderItems}">
	<div class="formTableDiv">
		<div class="sectionHeader">
			<orbis:message code="i18n.ecommerce_ajax_order_details.OrderItems" />
		</div>
		<div style="margin-top:10px;">
			<table width="100%">
				<c:forEach var="item" items="${orderItems}" varStatus="itemStatus">
					<tr ${itemStatus.first ? '' : 'style="border-top:1px solid #DDD;"'}>
						<td>
							<c:if test="${isL1}">
								<B>${item.productName} (${item.productCode})</B>
								<BR>${item.productDesc}
							</c:if>
							<c:if test="${isL2}">
								<B>${item.l2ProductName} (${item.productCode})</B>
								<BR>${item.l2ProductDesc}
							</c:if>	
						</td>
						<td>
							<table width="100%">
								<c:if test="${not empty item.subTotal}">
									<tr>
										<td class="alignRight">
											<orbis:message code="i18n.ecommerce_ajax_order_details.sub" />:
										</td>
										<td class="alignRight">
											<fmt:formatNumber value="${item.subTotal}" type="currency"  currencySymbol="$" />
										</td>
									</tr>
								</c:if>
								<c:if test="${siteCode != 'hec'}">
									<c:if test="${not empty item.tax1Name && not empty item.tax1}">
										<tr>
											<td class="alignRight">
												${item.tax1Name}: 
											</td>
											<td class="alignRight">
												<fmt:formatNumber value="${item.tax1}" type="currency"  currencySymbol="$" /> 
											</td>
										</tr>
									</c:if>
									<c:if test="${not empty item.tax2Name && not empty item.tax2}">
										<tr>
											<td class="alignRight">
												${item.tax2Name}: 
											</td>
											<td class="alignRight">
												<fmt:formatNumber value="${item.tax2}" type="currency"  currencySymbol="$" /> 
											</td>
										</tr>
									</c:if>
									<c:if test="${not empty item.tax3Name && not empty item.tax3}">
										<tr>
											<td class="alignRight">
												${item.tax3Name}: 
											</td>
											<td class="alignRight">
												<fmt:formatNumber value="${item.tax3}" type="currency"  currencySymbol="$" /> 
											</td>
										</tr>
									</c:if>
									<c:if test="${not empty item.total}">
										<tr>
											<td class="alignRight">
											</td>
											<td class="alignRight">
												<B><fmt:formatNumber value="${item.total}" type="currency"  currencySymbol="$" /></B>
											</td>
										</tr>
									</c:if>
								</c:if>
							</table>	
						</td>
					</tr>
				</c:forEach>
				<tr>
					<td></td>					
					<td class="alignRight" style="border-top:2px solid #000;">
						<table width="100%" style="line-height:1.1em;">
							<tr>
								<td align="right" style="font-weight:bold;"><orbis:message code="i18n.ecommerce_ajax_order_details.SubTotal" />:</td>
								<td align="right" style="font-weight:bold;"><fmt:formatNumber value="${order.subTotal}" type="currency"  currencySymbol="$" /></td>
							</tr>
							<c:if test="${not empty order.tax1Name}">
								<tr>
									<td align="right" style="font-weight:bold;">${order.tax1Name}:</td>
									<td align="right" style="font-weight:bold;"><fmt:formatNumber value="${order.tax1}" type="currency"  currencySymbol="$" /></td>
								</tr>
							</c:if>
							<c:if test="${not empty order.tax2Name}">
								<tr>
									<td align="right" style="font-weight:bold;">${order.tax2Name}:</td>
									<td align="right" style="font-weight:bold;"><fmt:formatNumber value="${order.tax2}" type="currency"  currencySymbol="$" /></td>
								</tr>
							</c:if>
							<c:if test="${not empty order.tax3Name}">
								<tr>
									<td align="right" style="font-weight:bold;">${order.tax3Name}:</td>
									<td align="right" style="font-weight:bold;"><fmt:formatNumber value="${order.tax3}" type="currency"  currencySymbol="$" /></td>
								</tr>
							</c:if>
							<c:if test="${not empty order.tax4Name}">
								<tr>
									<td align="right" style="font-weight:bold;">${order.tax4Name}:</td>
									<td align="right" style="font-weight:bold;"><fmt:formatNumber value="${order.tax4}" type="currency"  currencySymbol="$" /></td>
								</tr>
							</c:if>
							<tr>
								<td align="right" style="font-weight:bold;font-size:12pt;"><orbis:message code="i18n.ecommerce_ajax_order_details.TOTAL" />:</td>
								<td align="right" style="font-weight:bold;font-size:12pt;"><fmt:formatNumber value="${order.totalAmount}" type="currency"  currencySymbol="$" /></td>
							</tr>
						</table>
					</td>					
				</tr>
			</table>
		</div> 
		
	</div>
</c:if>

<c:if test="${not empty orderNotes}">
	<div class="formTableDiv">
		<div class="sectionHeader">
			<orbis:message code="i18n.ecommerce_ajax_order_details.OrderNotes" />
		</div>
		<div style="margin-top:10px;">
			<table class="noteTable">
				<c:forEach var="note" items="${orderNotes}">
					<tr class="altRow">
						<td>
							<div class="noteHeader">
								<fmt:formatDate value="${note.dateCreated}" pattern="${orbisDateAndTime}" /> - ${note.createdBy}
							</div>
							${note.note}
						</td>
					</tr>
				</c:forEach>
			</table>
		</div> 
	</div>
</c:if>

<c:if test="${not empty reconciliationOrders}">
	<BR>
	<BR>
	<div class="well">
		<div class="sectionHeader">
			<orbis:message code="i18n.ecommerce_ajax_order_details.ReconciliationOrders" />
		</div>
		<div style="margin-top:10px;">
			<table class="orderHistoryTable table table-condensed table-hover">
				<thead>
					<tr>
						<th><orbis:message code="i18n.ecommerce_ajax_order_history.Order" /></th>
						<th><orbis:message code="i18n.ecommerce_ajax_order_history.Date" /></th>
						<th><orbis:message code="i18n.ecommerce_ajax_order_history.Type" /></th>
						<th><orbis:message code="i18n.ecommerce_ajax_order_history.Status" /></th>
						<th style="text-align:right;"><orbis:message code="i18n.ecommerce_ajax_order_history.Debit" /></th>
						<th style="text-align:right;"><orbis:message code="i18n.ecommerce_ajax_order_history.Credit" /></th>
						<th style="text-align:right;"><orbis:message code="i18n.ecommerce_ajax_order_history.Other" /></th>
					</tr>
				</thead>
				<tbody>
					<c:forEach var="histOrder" items="${reconciliationOrders}">
						<%@ include file="/WEB-INF/jsp/ecommerce/ecommerce_ajax_order_history_order.jsp"%>
					</c:forEach>
				</tbody>
			</table>
		</div> 
	</div>
</c:if>
</c:if>
<c:if test="${empty order}">
	<script type="text/javascript">
	
		var orderDetails = {};
	
	</script>
	<div class="alert alert-danger"><strong><orbis:message code="i18n.ecommerce_ajax_order_details.Ordernotfound" /></strong></div>
</c:if>
