<%@ include file="/WEB-INF/jsp/include.jsp"%>
<%@ include file="cc_include.jsp"%>

<c:if test="${o:isSpiralRobotCompatible(siteElement, pageContext)}">
	<jsp:include page="/WEB-INF/spiralRobot/jsp/cc/cc_addNewPositionWizard.jsp" />
</c:if>

<c:if test="${!o:isSpiralRobotCompatible(siteElement, pageContext)}">

	<%@ taglib prefix="w" uri="/WEB-INF/tlds/wizard.tld" %>
	
	<c:set var="level1" value="${ccModule.showLevelCategory ? 'category' 
								: ccModule.showLevelOrganization ? 'organization' 
								: ccModule.showLevelDepartment ? 'department' : 'activityId'}" />
	
	<c:set var="level2" value="${level1 == 'activityId' ? ''
								: level1 == 'category' ? (ccModule.showLevelOrganization ? 'organization' 
														: (ccModule.showLevelDepartment ? 'department' : 'activityId'))
								: (level1 == 'organization' && ccModule.showLevelDepartment ? 'department' : 'activityId')}" />
	
	<c:set var="level3" value="${level2 == 'activityId' || level2 == '' ? ''
								: level2 == 'organization' && ccModule.showLevelDepartment ? 'department' : 'activityId'}" />
	
	<script type="text/javascript">
		var activityDetailsParams = {};
		
		function captchaSolvedCallback()
		{
			document.getElementById("btnSubmitContainer").style.display = "inline";
		}
	
		$(document).ready(function() {
			<c:if test="${not empty preSelectPeriodId && empty activity}">
				loadOptionsFor("${level1}");
			</c:if>
			
			$("input[name='otherCategory']").val("");
			$("input[name='otherOrganization']").val("");
			$("input[name='otherDepartment']").val("");
			
			$("select[name='periodId']").change(function() {
				$("select[name='category']").find("option[value!='']").remove();
				$("select[name='organization']").find("option[value!='']").remove();
				$("select[name='department']").find("option[value!='']").remove();
				$("select[name='activityId']").find("option[value!='']").remove();
				$("input[name='otherCategory']").val("").hide();
				$("input[name='otherOrganization']").val("").hide();
				$("input[name='otherDepartment']").val("").hide();
				$("#activityDetailsDiv").hide();
				orbisApp.ajax.activityDetailsEditStep.applyDefaultContent();
				
				loadOptionsFor("${level1}");
			});
			
			$("select[name='category']").change(function() {
				$("select[name='organization']").find("option[value!='']").remove();
				$("select[name='department']").find("option[value!='']").remove();
				$("select[name='activityId']").find("option[value!='']").remove();
				$("input[name='otherOrganization']").val("").hide();
				$("input[name='otherDepartment']").val("").hide();
				$("#activityDetailsDiv").hide();
				orbisApp.ajax.activityDetailsEditStep.applyDefaultContent();
				if($(this).val() == -1)
				{
					$("input[name='otherCategory']").show();
				}
				else
				{
					$("input[name='otherCategory']").hide();
					$("input[name='otherCategory']").val("");
				}
				
				loadOptionsFor("${level2}");
			});
			
			$("select[name='organization']").change(function() {
				$("select[name='department']").find("option[value!='']").remove();
				$("select[name='activityId']").find("option[value!='']").remove();
				$("input[name='otherDepartment']").val("").hide();
				$("#activityDetailsDiv").hide();
				orbisApp.ajax.activityDetailsEditStep.applyDefaultContent();
				if($(this).val() == -1)
				{
					$("input[name='otherOrganization']").show();
				}
				else
				{
					$("input[name='otherOrganization']").hide();
					$("input[name='otherOrganization']").val("");
				}
				
				loadOptionsFor("${ccModule.showLevelCategory ? level3 : level2}");
			});
			
			$("select[name='department']").change(function() {
				$("select[name='activityId']").find("option[value!='']").remove();
				$("#activityDetailsDiv").hide();
				orbisApp.ajax.activityDetailsEditStep.applyDefaultContent();
				if($(this).val() == -1)
				{
					$("input[name='otherDepartment']").show();
				}
				else
				{
					$("input[name='otherDepartment']").hide();
					$("input[name='otherDepartment']").val("");
				}
				
				loadOptionsFor("activityId");
			});

			var activitySelect = $("select[name='activityId']");
			activitySelect.change(function() {
				activityDetailsParams.activityId = $(this).val();
				if($(this).val() == -1)
				{
					orbisApp.ajax.activityDetailsEditStep.load();
					$("#activityDetailsDiv").show();
					$("#checkPositionsBtn").hide();
					orbisApp.ajax.availablePositionCompetencies.load();
				}
				else if($(this).val() == "")
				{
					$("#activityDetailsDiv").hide();
					$("#checkPositionsBtn").hide();
				}
				else
				{
					$("#activityDetailsDiv").hide();
					orbisApp.ajax.activityDetailsEditStep.applyDefaultContent();
					$("#checkPositionsBtn").show();
					orbisApp.ajax.availablePositionCompetencies.load();
				}
			});

			var activityId = parseInt(activitySelect.val());
			if(Number.isInteger(activityId))
			{
				activityDetailsParams.activityId = activityId;
				orbisApp.ajax.availablePositionCompetencies.load();
			}
		});
	
		function loadOptionsFor(level) {
			var $select = $("select[name='" + level + "']");
			
			var request = {
				action: '<o:encrypt action="ajaxLoadSelectOptions" />',
				level: level,
				periodId: $("select[name='periodId']").val(),
				category: $("select[name='category']").val(),
				organization: $("[name='organization']").val(),
				department: $("select[name='department']").val(),
				rand: Math.floor(Math.random() * 100000)
			};
			
			$.post("${siteElement.fullPath}.htm", request, function(data, status, xhr) {
				if (orbisApp.checkAjaxResponse(xhr))
				{
					if (data)
					{
						<c:set var="canAddNewActivities" value="${ccModule.studentsCanAdd || ('student' != currentRole && 'alumni' != currentRole && 'anonymous' != currentRole)}" />
						if (level == "activityId")
						{
							<c:if test="${canAddNewActivities}">
								$select.append($('<option>').text('<orbis:message code="i18n.cc_addNewPositionWizard.AddNew5657234479835532" javaScriptEscape="true" />').val('-1'));
							</c:if>
							$.each(data, function(index, val) {
								$select.append($('<option>').text(val[1]).val(val[0]));
							});
							$("#checkPositionsBtn").hide();
						}
						else
						{
							<c:if test="${canAddNewActivities}">
								$select.append($('<option>').text('<orbis:message code="i18n.cc_addNewPositionWizard.AddNew5657234479835532" javaScriptEscape="true" />').val('-1'));
							</c:if>
							$.each(data, function(index, val) {
								$select.append($('<option>').text(val).val(val));
							});
						}
					}
				}
			}, 'json');
		}
		
		function validatePositionForm()
		{
			var validator = $("#requestForm").validate();
			var allValid = true;
			var elementName;
			
			<c:forEach var="positionQuestion" items="${dfModel.questionsMappedById}">
				elementName = "question_" + "${positionQuestion.key}";
				if($("[name='" + elementName + "']").length) 
				{
					var elementValid = validator.element("[name = '" + elementName + "']");
					if(!elementValid)
					{
						allValid = false;
					}
				}
				else if($("[name='" + elementName + "_startDate']").length)
				{
					var elementValid = validator.element("[name = '" + elementName + "_startDate']");
					if(!elementValid)
					{
						allValid = false;
					}
					
					elementValid = validator.element("[name = '" + elementName + "_endDate']");
					if(!elementValid)
					{
						allValid = false;
					}
				}
				else if($("[id='" + elementName + "__hours']").length)
				{
					var elementValid = validator.element("[id= '" + elementName + "__hours']");
					if(!elementValid)
					{
						allValid = false;
					}
					
					elementValid = validator.element("[id='" + elementName + "__period']");
					if(!elementValid)
					{
						allValid = false;
					}
				}
				else if($("[id='" + elementName + "__x']").length)
				{
					var elementValid = validator.element("[id= '" + elementName + "__x']");
					if(!elementValid)
					{
						allValid = false;
					}
					
					elementValid = validator.element("[id='" + elementName + "__y']");
					if(!elementValid)
					{
						allValid = false;
					}
				}
				else if($("[id='" + elementName + "__url']").length)
				{
					var elementValid = validator.element("[id= '" + elementName + "__url']");
					if(!elementValid)
					{
						allValid = false;
					}
					
					elementValid = validator.element("[id='" + elementName + "__linkText']");
					if(!elementValid)
					{
						allValid = false;
					}
				}
			</c:forEach>
			
			return allValid;
		}
		
		function checkPositions() {
			var request = {
				action: '<o:encrypt action="loadPositions" />',
				activityId: $("#requestForm select[name='activityId']").val(),
				rand: Math.floor(Math.random()*100000)
			};
	
			$(".modal-body", "#chkPositionsModal").empty();
	
			$(".modal-body", "#chkPositionsModal").load("${siteElement.fullPath}.htm", request, function(data, status, xhr) {
				if (orbisApp.checkAjaxResponse(xhr))
				{
					$("#chkPositionsModal").modal("show");
				}
			});
		}
		
		function sendRequest() {
			<c:if test="${'admin' == currentRole || 'activityDirector' == currentRole}">
				$("#requestForm input[name=validators]").val(getValidators());
			</c:if>
	
			return $("#requestForm").validate().form();
		}

		function validateCompetenciesStep() {
			var valid = true;
			var errorMessage = '';

			if (parentCompetencyUsecase && parentMinimumCompetencies)
			{
				var selectedCompetenciesSize = $('input[name=assignedC]:checked').length;
				if (selectedCompetenciesSize && selectedCompetenciesSize < parentMinimumCompetencies)
				{
					valid = false;
					errorMessage += minimumCompetenciesErrorMessage;
				}
			}

			if(!valid)
			{
				orbisApp.displayErrorMessage(errorMessage);
			}

			return valid;
		}
	</script>
	
	<w:wizard step="${not empty activity ? 2 : 0}" nextButton = "#w1-nextButton" previousButton = "#w1-prevButton" submitButton = "#btnSubmit">
		<w:step title = "i18n.cc_addNewPositionWizard.Grouping4825619835957879" target = "#w1-step-1" materialIcon = "category">
			<w:validate>
				var valid = true;
				var errorMessage = "";
				
				if($("select[name='periodId']").val() == "")
				{
					valid = false;
					errorMessage += "<orbis:message code="i18n.cc_addNewPositionWizard.Pleasesele4605167673319196" javaScriptEscape="true" /><br>";
				}
	
				<c:if test="${ccModule.showLevelCategory}">
					if($("select[name='category']").val() == "" || ($("select[name='category']").val() == -1 && $("input[name='otherCategory']").val() == ""))
					{
						valid = false;
						errorMessage += "<orbis:message code="i18n.cc_addNewPositionWizard.Pleasesele7126722971779747" javaScriptEscape="true" /><br>";
					}
				</c:if>
				
				<c:if test="${ccModule.showLevelOrganization}">
					if($("select[name='organization']").val() == "" || ($("select[name='organization']").val() == -1 && $("input[name='otherOrganization']").val() == ""))
					{
						valid = false;
						errorMessage += "<orbis:message code="i18n.cc_addNewPositionWizard.Pleasesele9935563414406511" javaScriptEscape="true" /><br>";
					}
				</c:if>
				
				<c:if test="${ccModule.showLevelDepartment}">
					if($("select[name='department']").val() == "" || ($("select[name='department']").val() == -1 && $("input[name='otherDepartment']").val() == ""))
					{
						valid = false;
						errorMessage += "<orbis:message code="i18n.cc_addNewPositionWizard.Pleasesele5447198925059708" javaScriptEscape="true" /><br>";
					}
				</c:if>
				
				if(errorMessage != "")
				{
					orbisApp.displayErrorMessage(errorMessage);
				}
				
				return valid;
			</w:validate>
		</w:step>
		<w:step title = "i18n.cc_addNewPositionWizard.Activity2976627826536707" target = "#w1-step-2" materialIcon = "folder">
			<w:validate>
				var valid = true;
				var errorMessage = "";
				
				if($("select[name='activityId']").val() == '')
				{
					valid = false;
					errorMessage += "<orbis:message code="i18n.cc_addNewPositionWizard.Pleasesele9120399733926383" javaScriptEscape="true" /><br>";
				}
				else if($("select[name='activityId']").val() == '-1')
				{
					if(!validateActivityForm())
					{
						valid = false;
						errorMessage += "<orbis:message code="i18n.cc_addNewPositionWizard.Pleasechec5357769166573352" javaScriptEscape="true" /><br>";
					}
					
					if(checkForExistingActivityName())
					{
						valid = false;
						errorMessage += "<orbis:message code="i18n.cc_requestActivityDetailsEditAjax.Thereisalr9130169561381084" javaScriptEscape="true" />"
					}
				}
				
				if(errorMessage != "")
				{
					orbisApp.displayErrorMessage(errorMessage);
				}
				
				return valid;
			</w:validate>
		</w:step>
		<w:step title = "i18n.cc_addNewPositionWizard.Position5677510786021032" target = "#w1-step-3" materialIcon = "insert_drive_file">
			<w:validate>
				var valid = true;
				var errorMessage = "";
				
				if(!validatePositionForm())
				{
					valid = false;
					errorMessage += "<orbis:message code="i18n.cc_addNewPositionWizard.Pleasechec7114842178979648" javaScriptEscape="true" /><br>";
				}
				
				<c:if test="${not empty titleQuestionField}">
					if($("select[name='activityId']").val() > -1)
					{
						var request = $.extend({
							action: "<o:encrypt action="checkForDuplicatePosition" />",
							posTitle: $("[name='${titleQuestionField}']").val(),
							rand: Math.floor(Math.random() * 100000)
						}, activityDetailsParams);
						
						$.ajax({
							type : "POST",
							url : "${siteElement.fullPath}.htm",
							data : request,
							async : false,
							dataType : "json",
							success : function(data, status, xhr) 
							{
								if (orbisApp.checkAjaxResponse(xhr))
								{
									if(data.duplicate)
									{
										valid = false;
										errorMessage += "<orbis:message code="i18n.cc_addNewPositionWizard.Thispositi3422262632876503" javaScriptEscape="true" />";
									}
								}
								else
								{
									valid = false;
									errorMessage += "<orbis:message code="i18n.cc_addNewPositionWizard.Errorcheck3985967722772284" javaScriptEscape="true" />";
								}
							}
						});
					}
				</c:if>
				
				if(!valid)
				{
					orbisApp.displayErrorMessage(errorMessage);
				}
				
				return valid;
			</w:validate>
		</w:step>
		<c:if test="${!competenciesDisabled}">
			<w:step title="i18n.CompetenciesStep.Competenci5252843337878675" target="#competenciesStep" materialIcon="autorenew" validate="validateCompetenciesStep();"/>
		</c:if>
		<w:step title = "i18n.cc_addNewPositionWizard.Validator6710771191188643" target = "#w1-step-4" materialIcon = "gavel" />
		<c:if test="${'admin' != currentRole}">
			<w:step title = "i18n.cc_addNewPositionWizard.Requestor3075377444259113" target = "#w1-step-5" materialIcon = "person" />
		</c:if>
	</w:wizard>
	
	<div class = "box boxContent">
		<orbis:validate formId="requestForm" />
		<form id="requestForm" enctype="multipart/form-data" method="post" class="form-horizontal" onsubmit="return sendRequest();">
            <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
			<o:encrypt input="true" action="savePositionRequest" />
			
			<div id = "w1-step-1" style = "display:none;">
				<p class="sel_selectTimePeriod"><orbis:message code="i18n.cc_addNewPositionWizard.Selectaper9472631744864160" /></p>
				<select name="periodId" class="sel_TestPeriod">
					<option value=""><orbis:message code="i18n.cc_addNewPositionWizard.Select2322677370964378" /></option>
					<c:forEach var="p" items="${periods}">
						<c:choose>
							<c:when test="${not empty activity && activity.period.id== p.id}">
								<option value="${p.id}" selected>${p.name}</option>
							</c:when>
							<c:when test="${p.id==preSelectPeriodId}">
								<option value="${p.id}" selected> ${p.name}</option>
							</c:when>
							<c:otherwise>
								<option value="${p.id}">${p.name}</option>
							</c:otherwise>
						</c:choose>
					</c:forEach>
				</select>
				
				<c:if test="${ccModule.showLevelCategory}">
					<br><br>
					<p><orbis:message code="i18n.cc_addNewPositionWizard.Selectacat9153185930064121" /></p>
					<select name="category" class="sel_CategoryTest">
						<option value=""><orbis:message code="i18n.cc_addNewPositionWizard.Select0619508500489884" /></option>
						<c:if test="${not empty activity && not empty categoryOptions}">
							<c:forEach var="cat" items="${categoryOptions}">
								<option value="${cat}" ${cat == activity.category || cat == activity.unescapedCategory ? 'selected' : '' }>
									${cat}
								</option>
							</c:forEach>
						</c:if>
					</select>
					<orbis:message var="placeholderText" code="i18n.cc_addNewPositionWizard.Nameofnewc2972044497391754" htmlEscape="true" />
					<input type="text" name="otherCategory" placeholder="${placeholderText}" style="display:none;">
				</c:if>
				
				<c:if test="${ccModule.showLevelOrganization}">
					<br><br>
					<p><orbis:message code="i18n.cc_addNewPositionWizard.Selectanor2511856840125076" /></p>
					<select name="organization" class="sel_testOrganization">
						<option value=""><orbis:message code="i18n.cc_addNewPositionWizard.Select7393623725761676" /></option>
						<c:if test="${not empty activity && not empty organizationOptions}">
							<c:forEach var="org" items="${organizationOptions}">
								<option value="${org}" ${org == activity.organization || org == activity.unescapedOrganization ? 'selected' : '' }>
									${org}
								</option>
							</c:forEach>
						</c:if>  
					</select>
					<orbis:message var="placeholderText" code="i18n.cc_addNewPositionWizard.Nameofnewo0201912988520849" htmlEscape="true" />
					<input type="text" name="otherOrganization" placeholder="${placeholderText}" style="display:none;">
				</c:if>
				
				<c:if test="${ccModule.showLevelDepartment}">
					<br><br>
					<p><orbis:message code="i18n.cc_addNewPositionWizard.Selectadep1953544803382090" /></p>
					<select name="department" class="sel_testDepartment">
						<option value=""><orbis:message code="i18n.cc_addNewPositionWizard.Select3471230024877659" /></option>
						<c:if test="${not empty activity && not empty departmentOptions}">
							<c:forEach var="dept" items="${departmentOptions}">
								<option value="${dept}" ${dept == activity.department || dept == activity.unescapedDepartment ? 'selected' : '' }>
									${dept}
								</option>
							</c:forEach>
						</c:if>  
					</select>
					<orbis:message var="placeholderText" code="i18n.cc_addNewPositionWizard.Nameofnewd4114703595691460" htmlEscape="true" />
					<input type="text" name="otherDepartment" placeholder="${placeholderText}" style="display:none;">
				</c:if>
			</div>
			
			<div id = "w1-step-2" style = "display:none;">
				<p><orbis:message code="i18n.cc_addNewPositionWizard.Selectanac2519052287402169" /></p>
				<select name="activityId" class="sel_testActivity">
					<option value=""><orbis:message code="i18n.cc_addNewPositionWizard.Select8493526115358750" /></option>
					<c:if test="${not empty activity && not empty activityOptions}">
						<c:forEach var="acti" items="${activityOptions}">
							<option value="${acti[0]}" ${acti[0] == activity.id ? 'selected' : '' }>${acti[1]}</option>
						</c:forEach>
						<c:if test="${activity.status !=1}">
							<option value="${activity.id}" selected>${activity.activity}</option>
						</c:if>
					</c:if>
				</select>
				<a href="javascript:void(0);" id="checkPositionsBtn" class="btn hide" onclick="checkPositions();">
					<orbis:message code='i18n.cc_addActivity.checkForExistingPositions' />
				</a>
				<div id="activityDetailsDiv" style="display:none; margin-top:15px;">
					<ui:ajax id="activityDetailsEditStep" action="ajaxLoadActivityDetailsEdit" ccWizardView="true" overrideVisibility="true" autoLoad="false"/>
					<c:if test="${ccModule.enableClubs}">
						<div class="box box--fancy">
							<div class="boxContent">
								<label>
									<input type="checkbox" name="club">
									<orbis:message code="i18n.cc_addNewPositionWizard.Isaclub7560254420737406" />
								</label>
							</div>
						</div>
					</c:if>
					<c:if test="${'admin' == currentRole || 'staff' == currentRole || 'activityDirector' == currentRole}">
						<c:set var="module" value="${ccModule}" />
						<%@ include file="cc_newPositionAddDirectors.jsp" %>
						<c:set var="module" value="${siteElement.contentItem}" />
					</c:if>
					<c:if test="${tagsExists}">
						<div class="box">
							<div class="boxTitle">
								<orbis:message code="i18n.cc_addNewPositionWizard.Tags2733230650386111" />
							</div>
							<div class="boxContent">
								<input type="hidden" name="tagAction" value="saveTags" />
								<%@ include file="/WEB-INF/jsp/ccrm/ccrm_tagsAssign_tagPanel.jsp"%>
							</div>
						</div>
					</c:if>
				</div>
			</div>
			
			<div id = "w1-step-3" style = "display:none;">
				<div id="positionForm">
					<%@ include file="cc_newPositionDetailsAndContactInfo.jsp" %>
				</div>
				<c:if test="${ccModule.enableExternalValidations && currentRole == 'admin'}">
					<label>
						<input type="checkbox" name="externalPosition">
						<orbis:message code="i18n.cc_addNewPositionWizard.ExternalPo6712200666118777" />
					</label>
				</c:if>
				<c:if test="${ccModule.enableTimeTracking}">
					<label>
						<input type="checkbox" name="enableTimeTracking" checked>
						<orbis:message code="i18n.cc_addNewPositionWizard.EnableTime0788980199937782" />
					</label>
				</c:if>
			</div>

			<div id="competenciesStep" style="display:none;">
				<ui:ajax id="availablePositionCompetencies" action="ajaxLoadAvailablePositionCompetencies" jsParams="activityDetailsParams" overrideVisibility="true" autoLoad="false"/>
			</div>

			<div id = "w1-step-4" style = "display:none;">
				<c:if test="${'admin' == currentRole || 'activityDirector' == currentRole}">
					<input type="hidden" name="validators" value="">
				</c:if>
				<%@ include file="cc_newPositionAddValidators.jsp" %>
			</div>
			
			<c:if test="${'admin' != currentRole}">
				<div id = "w1-step-5" style = "display:none;">
					<div class="box">
						<div class="boxTitle">
							<orbis:message code="i18n.cc_requestPositionHome.personalDetails" />
						</div>
						<div class="boxContent">
							<div class="well">
								<I class="icon-info-sign icon-large"></I> <orbis:message code="i18n.cc_requestPositionHome.followingDetailsNecessary" />
							</div>
							<div class="control-group">
								<label class="control-label" for="firstName">
									<orbis:message code="i18n.cc_requestPositionHome.firstName" /> *
								</label>
								<div class="controls">
									<input type="text" id="firstName" name="firstName" style="width: 300px" class="required" value="${currentUser.firstName}">
								</div>
							</div>
							<div class="control-group">
								<label class="control-label" for="lastName">
									<orbis:message code="i18n.cc_requestPositionHome.lastName" /> *
								</label>
								<div class="controls">
									<input type="text" id="lastName" name="lastName" style="width: 300px" class="required" value="${currentUser.lastName}">
								</div>
							</div>
							<div class="control-group">
								<label class="control-label" for="requestPosition">
									<orbis:message code="i18n.cc_requestPositionHome.requestPosition" /> *
								</label>
								<div class="controls">
									<input type="text" id="requestPosition" name="requestPosition" class="sel_RequestPosition" style="width: 300px" class="required">
								</div>
							</div>
							<div class="control-group">
								<label class="control-label" for="email">
									<orbis:message code="i18n.common.email.email" /> *
								</label>
								<div class="controls">
									<input type="text" id="email" name="email" style="width: 300px" class="required sel_requesterEmailTest" value="${currentUser.email}">
								</div>
							</div>
							<div class="control-group">
								<label class="control-label" for="phone">
									<orbis:message code="i18n.cc_requestPositionHome.phone" /> *
								</label>
								<div class="controls">
									<input type="text" id="phone" name="phone" class="required sel_requesterPhoneTest" value="${currentUser.phoneNumber}">
								</div>
							</div>
							<c:if test="${empty currentUser && not empty o:getConfig('RECAPTCHA_SITE_KEY')}">
								<orbis:recaptcha />
							</c:if>
						</div>
					</div>
				</div>
			</c:if>
			
			<div style = "margin-top:10px;">
				<button type="button" id = "w1-prevButton" class = "btn">
					<orbis:message code="i18n.cc_addNewPositionWizard.Previous0360011055586125" />
				</button>
				<button type="button" id = "w1-nextButton" class = "btn btn-primary sel_testNextButton">
					<orbis:message code="i18n.cc_addNewPositionWizard.Next0473694277960685" />
				</button>
				<div id="btnSubmitContainer" style="display:${empty currentUser && not empty o:getConfig('RECAPTCHA_SITE_KEY') ? 'none' : 'inline'}">
					<input type="submit" id="btnSubmit" class="btn btn-primary sel_buttonFinishTest" style="display: none;" value="<orbis:message code="i18n.cc_addNewPositionWizard.Finish9155210565108158" />">
				</div>
			</div>
		</form>
	</div>
	
	<style>
		#chkPositionsModal { width:900px; margin:-250px 0 0 -450px; }
	</style>
	<div id="chkPositionsModal" class="modal hide">
		<div class="modal-header">
			<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
			<h3><orbis:message code="i18n.cc_requestPositionCheckPositionsDialog.existingPositionsFor" />:</h3>
		</div>
		<div class="modal-body">
		</div>
		<div class="modal-footer">
			<a href="javascript:void(0);" class="btn btn-inverse" data-dismiss="modal"><orbis:message code="i18n.common.close" /></a>
		</div>
	</div>
</c:if>