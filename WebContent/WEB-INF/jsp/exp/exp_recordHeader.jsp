<%@ include file="/WEB-INF/jsp/include.jsp"%>


<div class="is--spiral--robot">
	<ui:userProfileHeader user="${record.owner}" classes="margin--b--l">
	
		<c:if test="${not empty headerNavigation}">
			<ui:section key="topControls">
				${headerNavigation}
			</ui:section>
		</c:if>
		
		<ui:section key="subtitle">
			<div class="width--100 text--truncate">
				${isL1 ? record.studentStep.student.termCourse.termCourseName : record.studentStep.student.termCourse.l2TermCourseName}
			</div>
			<div class="width--100 text--truncate">
				${isL1 ? record.studentStep.step.tct.type.name : record.studentStep.step.tct.type.l2Name}
			</div>
		</ui:section>

		<ui:headerTags>
			<%@ include file="/WEB-INF/spiralRobot/jsp/ccrm/ccrm_headerQuestionsItems.jsp"%>
			<%@ include file="/WEB-INF/spiralRobot/jsp/exp/exp_recordIndustryPartnerList.jsp" %>
		</ui:headerTags>

	</ui:userProfileHeader>
</div>

<div class="box boxContent">
	<table class="table table-condensed stat-table">
		<c:if test="${!currentUser.student}">
			<tr>
				<td width="20%"><orbis:message code="i18n.exp_recordHeader.StudentNam7407568910593551" /></td>
				<td>
					<c:if test="${isAdmin}">
						<o:nav anchor="true" action="displayTermCourseStudent" tcsId="${record.studentStep.student.id}">
							<strong>${record.owner.fullName}</strong>
						</o:nav>
					</c:if>
					<c:if test = "${!isAdmin}">
						<strong>${record.owner.fullName}</strong>
					</c:if>
				</td>
			</tr>
		</c:if>
		<tr>
			<td><orbis:message code="i18n.exp_recordHeader.Course7128503692888966" /></td>
			<td>
				<c:if test="${isAdmin}">
					<o:nav anchor="true" action="displayTermCourse" tcId="${record.studentStep.student.termCourse.id}">
						<strong>${isL1 ? record.studentStep.student.termCourse.termCourseName : record.studentStep.student.termCourse.l2TermCourseName}</strong>
					</o:nav>
				</c:if>
				<c:if test="${!isAdmin}">
					<strong>${isL1 ? record.studentStep.student.termCourse.termCourseName : record.studentStep.student.termCourse.l2TermCourseName}</strong>
				</c:if>
			</td>
		</tr>
		
		<tr>
			<td><orbis:message code="i18n.exp_recordHeader.Experience4973105116817224" /></td>
			<td>
				<c:if test="${isAdmin}">
					<o:nav anchor="true" action="displayExpType" expTypeId="${record.studentStep.step.tct.type.id}">
						<strong>${isL1 ? record.studentStep.step.tct.type.name : record.studentStep.step.tct.type.l2Name}</strong>
					</o:nav>
					<strong>${(isL1 && not empty record.studentStep.step.tct.name) || (isL2 && not empty record.studentStep.step.tct.l2Name) ? ' - ' :''}${isL1 ? record.studentStep.step.tct.name : record.studentStep.step.tct.l2Name}</strong>
				</c:if>
				<c:if test="${!isAdmin}">
					<strong>${isL1 ? record.studentStep.step.tct.type.name : record.studentStep.step.tct.type.l2Name}${(isL1 && not empty record.studentStep.step.tct.name) || (isL2 && not empty record.studentStep.step.tct.l2Name) ? ' - ' :''}${isL1 ? record.studentStep.step.tct.name : record.studentStep.step.tct.l2Name}</strong>	
				</c:if>
			</td>
		</tr>
		<tr>
			<td><orbis:message code="i18n.exp_recordHeader.DateCreate1069822059406299" /></td>
			<td>
				<strong>
					<orbis:formatDate value="${record.dateCreated}" pattern="${orbisDateLongTimeShort}" />
					<c:if test="${ not empty record.createdBy.fullName }">
						(<orbis:message code="i18n.exp_recordHeader.Byrecordcr0957431834854569" arguments="\${record.createdBy.fullName}" />)
					</c:if>
				</strong>
			</td>
		</tr>
		<tr>
			<td><orbis:message code="i18n.exp_recordHeader.LastUpdate4121638205271635" /></td>
			<td>
				<strong>
					<c:if test="${empty record.dateUpdated}">N/A</c:if>
					<orbis:formatDate value="${record.dateUpdated}" pattern="${orbisDateLongTimeShort}" />
					<c:if test="${ not empty record.updatedBy.fullName }">
						(<orbis:message code="i18n.exp_recordHeader.Byrecordcr0957431834854569" arguments="\${record.updatedBy.fullName}" />)
					</c:if>
				</strong>
			</td>
		</tr>
		
		<c:if test="${not empty record.posting}">
			<tr>
				<td><orbis:message code="i18n.exp_recordHeader.Posting1994942652121448" /></td>
				<td>
					<o:nav anchor="true" action="displayPosting" postingId="${record.posting.id}">
						<strong>
							${isL1 ? record.posting.postingName : record.posting.l2PostingName}
							<c:if test="${ isAdmin }">
								(${record.posting.id}) 
							</c:if>	
						</strong>
					</o:nav>
				</td>
			</tr>
			<c:if test="${record.posting.type.enableAttendanceSlots && not empty attendanceSlots}">
				<tr>
					<td><orbis:message code="i18n.exp_recordHeader.Attendance2410397617680179" /></td>
					<td>
						<c:if test="${record.posting.type.studentsSubmitAttendanceSlotPreferences && currentUser.id==record.owner.id}">
							<c:if test="${empty recordAttSlot}">
								<%@ include file="exp_recordAttendanceSlotPreferencesForm.jsp" %>
							</c:if>
							<c:if test="${not empty recordAttSlot}">
								<%@ include file="exp_postingAttendanceSlotVars.jsp" %>
								<orbis:message code="i18n.exp_recordHeader.YourSlot6843140420199360" /> <orbis:message code="i18n.exp_posting.attSlotatt1564831564894566" arguments="${dayOfWeek},${startTime},${endTime}" />
							</c:if>
						</c:if>
						<c:if test="${!(record.posting.type.studentsSubmitAttendanceSlotPreferences && currentUser.id==record.owner.id)}">
							<c:set var="hasSlot" value="false" />
							<div id="attShowDiv" style="display:block">
								<c:forEach var="attSlot" items="${attendanceSlots}">
									<%@ include file="exp_postingAttendanceSlotVars.jsp" %>
									<c:if test="${recordAttSlotId == attSlot.attSlot.id}">
										<orbis:message code="i18n.exp_recordHeader.dayOfWeekf1733544707258239" arguments="${dayOfWeek},${startTime},${endTime}" />
										<c:set var="hasSlot" value="true" />
									</c:if> 
								</c:forEach>
							</div>

							<c:if test="${record.posting.status==1}">
								<button type="button" id="changeSlotBtn" class="btn btn-small" ><orbis:message code="i18n.exp_recordHeader.ChangeAtte7736297986188141" /></button>
							</c:if>
							
							<div id="attSelectDiv" style="display:none">
								<input type="hidden" name="cancelSlot" id="cancelSlot" value="">
								<select id="attendanceSlotSelect" name="attendanceSlotSelect">
									<c:if test="${empty recordAttSlotId}">
										<c:set var="recordAttSlotId" value="${recordAttSlot.attendanceSlot.id}"/>
									</c:if>
									
									<c:forEach var="attSlot" items="${attendanceSlots}">
										<%@ include file="exp_postingAttendanceSlotVars.jsp" %>
										<c:if test="${attSlot.attSlot.numStudents > attSlot.attSlot.takenSlots || recordAttSlotId == attSlot.attSlot.id}">
											<option value="${attSlot.attSlot.id}" ${recordAttSlotId == attSlot.attSlot.id ? 'selected' : ''}>
												<orbis:message code="i18n.exp_recordHeader.dayOfWeekf1733544707258239" arguments="${dayOfWeek},${startTime},${endTime}" />
												<c:if test="${currentUser.id!=record.owner.id}">
													<orbis:message code="i18n.exp_recordSelectAttendanceSlot.attSlotatt0732311704535796" arguments="${attSlot.attSlot.numStudents-attSlot.attSlot.takenSlots }" />
												</c:if>
												<c:if test="${not empty attSlot.studentChoice.choiceNumber}">
													<orbis:message code="i18n.exp_recordHeader.Student9008056217667185" />
													<c:if test="${attSlot.studentChoice.choiceNumber != -1}">
														<orbis:message code="i18n.exp_postingAttendanceSlots.choice.${attSlot.studentChoice.choiceNumber}" />
													</c:if>
													<c:if test="${attSlot.studentChoice.choiceNumber == -1}">
														<orbis:message code="i18n.exp_postingAttendanceSlots.NotAvailab8659676586508142" />
													</c:if>
												</c:if>
											</option>
										</c:if> 
									</c:forEach>
								</select>
							</div>
							<button type="button" id="changeSlotButton" class="btn btn-small" style="display:none"><orbis:message code="i18n.exp_recordHeader.ChangeAtte7736297986188141" /></button>
							<c:if test="${hasSlot && (currentUser.id == record.owner.id || currentUser.portalStaff)}">
								<button type="button" id="cancelSlotBtn" class="btn btn-small" ><orbis:message code="i18n.exp_recordHeader.CancelAtte2029439083245817"/></button>
							</c:if>

							<ui:modal i18n_title="i18n.exp_recordHeader.CancelAtte2029439083245817" id="cancelAttendSlot" includeClose="false">
								<div>
									<orbis:message code="i18n.exp_recordHeader.ClickbCanc1222428614983849" />
									</br>
									<orbis:message code="i18n.exp_recordHeader.ClickbCanc9272307166880403" />
								</div>
								</br>
								<div>
									<ui:button hide="cancelButton" type="info" onclick="cancelModalButton();">
										<orbis:message code="i18n.exp_recordHeader.Cancel0776873042848485" />
									</ui:button>
									<c:if test="${'msu' != siteCode}">
										<ui:button hide="cancelAttendSlot" type="info" onclick="cancelSlotOnly();">
											<orbis:message code="i18n.exp_recordHeader.CancelSlot1952711339978355" />
										</ui:button>
									</c:if>
									<ui:button hide="cancelDeleteButton" type="info" onclick="cancelSlotDeletePost();">
										<orbis:message code="i18n.exp_recordHeader.CancelSlot0127113857307781" />
									</ui:button>
								</div> 
							</ui:modal>

							<script type="text/javascript">
								function cancelSlotDeletePost() {
									var request = {
										action: '<o:encrypt action="cancelAttendSlotDelFromPosting"/>',
										recordId: '${record.id}',
										cancelSlot: 'on',
										attendanceSlotId: $("#attendanceSlotSelect").val()
									};
									orbisApp.buildForm(request).submit();
									$("#cancelAttendSlot").uiHide();
								};
								function cancelSlotOnly(){
									var request = {
										action: '<o:encrypt action="updateRecordAttendanceSlot"/>',
										recordId: '${record.id}',
										cancelSlot: 'on',
										attendanceSlotId: $("#attendanceSlotSelect").val()
									};
										orbisApp.buildForm(request).submit();
								};
								function cancelModalButton() {
									$("#cancelAttendSlot").uiHide();
								};

								$(document).ready(function(){
									$("#changeSlotBtn").click(function(){
										$("#changeSlotBtn").hide();
										$("#attShowDiv").hide();
										$("#attSelectDiv").show();
										$("#changeSlotButton").show();
									});
									$("#cancelSlotBtn").click(function(){
										$("#cancelAttendSlot").uiShow();
									});

									$("#attendanceSlotSelect").change(function(){
										$("#changeSlotButton").show();
									});
									
									$("#changeSlotButton").click(function(){
										var request = {
											action: '<o:encrypt action="updateRecordAttendanceSlot"/>',
											recordId: '${record.id}',
											cancelSlot: $("#cancelSlot").val(),
											attendanceSlotId: $("#attendanceSlotSelect").val()
										};
										if (${recordAttSlotId != 0})
										{
											var confirmMessage = ""
											if ($("#cancelSlot").val()!=''){
												confirmMessage = "<orbis:message code="i18n.exp_recordHeader.Areyousure0648183231640615Cancel" />";
											}
											else
											{
												if (${isAdmin})
												{
													confirmMessage = "<orbis:message code="i18n.exp_recordHeader.Areyousure5174825374866761" />";
												}
												else
												{
													confirmMessage = "<orbis:message code="i18n.exp_recordHeader.Areyousure0648183231640615" />";
												}
											}
											orbisApp.confirmDialog(confirmMessage, function(){orbisApp.buildForm(request).submit()});
										}
										else
										{
											orbisApp.buildForm(request).submit();
										}
									});
								});
							</script>
						</c:if>
					</td>
				</tr>
			</c:if>
		</c:if>
		
		<%@ include file="exp_hourTracking.jsp"%>
		
		<c:if test="${journalEnabled}">
			<tr>
				<td><orbis:message code="i18n.exp_recordHeader.RequiredJo0623701105897409" /></td>
				<td><strong>${requiredJournalEntries}</strong></td>
			</tr>
			<tr>
				<td><orbis:message code="i18n.exp_recordHeader.TotalJourn2283399523451160" /></td>
				<td><strong>${totalJournalEntries}</strong></td>
			</tr>
		</c:if>
		<c:if test="${record.studentStep.step.tct.type.enableFieldSupervisors && not empty fieldSupervisors}">
			<tr>
				<td><orbis:message code="i18n.exp_recordHeader.FieldSuper2275385682111130" /></td>
				<td>
					<c:forEach var="rfs" items="${fieldSupervisors}" varStatus="status">
						${rfs.fieldSupervisor.preferredFirstName} ${rfs.fieldSupervisor.lastName}
						<c:set var="fullName">${rfs.fieldSupervisor.preferredFirstName} ${rfs.fieldSupervisor.lastName} </c:set>
						<c:if test="${moduleUser.expAdminOrExpCoordinatorOrCourseCoordinator || not empty currentUser.assignedTypes['Faculty Course Management']}">
							<o:nav confirmOnClick="i18n.exp_recordHeader.Areyousure1819350389540367" confirmOnClickArgs="${fullName }" anchor="true" anchorClass="btn btn-mini btn-danger" action="deleteFieldSupervisor" recordFieldSupervisorId="${rfs.id}">x</o:nav>
						</c:if>
						<br>
					</c:forEach>
				</td>
			</tr>
		</c:if>
	</table>
</div>
