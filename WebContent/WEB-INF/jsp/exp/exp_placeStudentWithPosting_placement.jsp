<%@ include file="/WEB-INF/jsp/include.jsp"%>

<div class="placement-card">
	<div>
		<h5 class="placement-card__subtitle" title="<orbis:message text="${ placement.posting.type.name }" htmlEscape="true" />">
			${ placement.posting.type.name }
		</h5>
		<h4 class="placement-card__title" title="<orbis:message text="${ placement.posting.name }" htmlEscape="true" />">
			${ placement.posting.name }
		</h4>
	</div>
	<div class="placement-card__body">
		<c:if test="${ not empty placement.posting.industryPartners }">
			<div style="margin-bottom:12px;">
				<c:forEach var="ip" items="${ placement.posting.industryPartners }">
					<div style="display:flex;align-items:center;">
						<span style="margin-right:4px;">
							<c:choose>
								<c:when test="${ ip.divIp }">
									<i class="material-icons-outlined atooltip" title="<orbis:message code="i18n.exp_placeStudentWithPosting_comparisonModal.Division6129719664251738" htmlEscape="true" />" aria-hidden="true">domain</i>
								</c:when>
								<c:when test="${ ip.orgIp }">
									<i class="material-icons-outlined atooltip" title="<orbis:message code="i18n.exp_placeStudentWithPosting_comparisonModal.Organizati0761283414416047" htmlEscape="true" />" aria-hidden="true">domain</i>
								</c:when>
								<c:otherwise>
									<i class="material-icons-outlined atooltip" title="<orbis:message code="i18n.exp_placeStudentWithPosting_comparisonModal.Employer5226091948584856" htmlEscape="true" />" aria-hidden="true">person</i>
								</c:otherwise>
							</c:choose>
						</span>
						<span>${ ip.displayName }</span>
					</div>
				</c:forEach>
			</div>
		</c:if>
		<p>
			${ placement.posting.synopsis }
		</p>
	</div>
	<div class="placement-card__tags" style="margin-bottom:12px;">
		<c:if test="${ placement.ranking > 0 && placement.ranking != 99 }">
			<span class="chip">
				<span class="chip__icon" style="font-weight:bold;">${ placement.ranking }</span>
				<orbis:message code="i18n.exp_placeStudent_placement.Ranking9929325899136201" />
			</span>
		</c:if>	
		<c:if test="${ placement.applied }">
			<span class="chip">
				<i class="material-icons chip__icon" aria-hidden="true">star</i>
				<orbis:message code="i18n.exp_placeStudent_placement.Applied1312198452261757" />
			</span>
		</c:if>
		<c:if test="${ placement.interested }">
			<span class="chip">
				<i class="material-icons chip__icon" aria-hidden="true">favorite_border</i>
				<orbis:message code="i18n.exp_placeStudent_placement.Interested3542632363608950" />
			</span>
		</c:if>		
		<c:if test="${ not empty placement.posting.geolocation && not empty step.studentExperience.student.student.owner.geolocation }">
			<span class="chip">
				<i class="material-icons chip__icon" aria-hidden="true">location_on</i> 
				<fmt:formatNumber var="distance" value="${ placement.posting.geolocation.distanceInKm( step.studentExperience.student.student.owner.geolocation ) }" maxFractionDigits="0" />
				<orbis:message code="i18n.exp_placeStudent_posting.distancekm3422536775778987" arguments="\${ distance }" />
			</span>
		</c:if>
	</div>
	<div>
		<button
		   class="btn btn-primary placeBtn"
	       data-posting-id="${ placement.posting.id }"
	       data-ses-id="${ step.id }"
		   data-posting-is-occ="${ placement.posting.isOCC }">
			<orbis:message code="i18n.exp_placeStudent_posting.Place8954913047045924" />
		</button>
		<button class="btn btn--text btn--text--primary seeMoreBtn" data-posting-id="${ placement.posting.id }" data-posting-is-occ="${ placement.posting.isOCC }" data-posting-exp-type-id="${expType.id}">
			<orbis:message code="i18n.exp_placeStudentWithPosting_placement.SeeMore0529517431294058" />
		</button>
	</div>
</div>