<%@ include file="/WEB-INF/jsp/include.jsp"%>

<orbis:navigation title="i18n.exp_homeActiveCourses.ActiveCour2334125183776262" subTitle="${isL1 ? siteElement.contentItem.name : siteElement.contentItem.l2Name}">
	<orbis:navButton title="i18n.common.backToHome" action="displayHome" />
</orbis:navigation>

<div class="box boxContent">
	<c:set var="gridFilters">
		<g:filterCollection queryFactory="true">
			<g:filter colID="termName" dynamicSelect="true" />
			<g:filter colID="section" dynamicSelect="true" />
			<g:filter colID="code" dynamicSelect="true" />
			<g:filter colID="termId" dynamicSelect="true" />
			<g:filter colID="facultyOwners"  options="${ facultyOwnerOptions }" customQueryFilter="true"/>
			<g:filter colID="termCourseTags"  options="${ termCourseTagsOptions }" customQueryFilter="true"/>
		</g:filterCollection>
	</c:set>

	<%@ include file="/WEB-INF/jsp/grid/grid_placeHolder.jsp"%>
</div>
