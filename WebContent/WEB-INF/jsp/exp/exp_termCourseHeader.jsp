<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<div class="margin--b--l">
	<div style="display:flex;">
		<div style=" display: flex; align-items: center; " class="margin--r--xl">
			<i class="material-icons exp-place-card__icon" style="font-size:36px;margin-right: 12px" aria-hidden>school</i>		
			<div>
				<div><orbis:message code="i18n.exp_termCourseHeader.Students3426236124586078" /></div>				
				<o:nav anchor="true" action="displayTermCourseStudents" target="_blank" tcId="${termCourse.id}" fromPrograms="${fromPrograms}" fromChart="${fromChart}">
					<strong>${studentsCount }</strong>
				</o:nav>
			</div>
			
		</div>

		<c:if test="${courseCoordinatorCount > 1}">
			<div style=" display: flex; align-items: center; " class="margin--r--xl">
				<i class="material-icons exp-place-card__icon" style="font-size:36px;margin-right: 12px" aria-hidden>person</i>
				<div>
					<div>
						<orbis:message code="i18n.exp_termCourseHeader.Coordinato8608531134236629" />
					</div>
					<strong>
						<o:nav action="displayTermCourseCoordinators" anchorId="expTermCourseCoordinator" anchor="true" termCourseId="${termCourse.id}" target="_blank">
							<strong>${courseCoordinatorCount}</strong>
						</o:nav>
					</strong>
				</div>
			</div>
		</c:if>
		<c:if test="${courseCoordinatorCount == 1 && not empty courseCoordinator}">
			<div style=" display: flex; align-items: center;" class="margin--r--xl">
				<c:if test="${not empty courseCoordinatorImageUrl}">
					<div class='userImageDiv' style="height:50px">
						<img src="${courseCoordinatorImageUrl}"  style="height:100%;box-sizing: border-box"
							 alt="<orbis:message text='${courseCoordinator.fullNameWithUsername}' htmlEscape='true' />"
							 title="<orbis:message text='${courseCoordinator.fullNameWithUsername}' htmlEscape='true' />" />
					</div>
					<div>
						<div>
							<orbis:message code="i18n.exp_termCourseHeader.Coordinato8596663269952724" />
						</div>
						<o:nav action="displayTermCourseCoordinators" anchorId="expTermCourseCoordinator" anchor="true" termCourseId="${termCourse.id}" target="_blank">
							<strong>${courseCoordinator.fullNameWithUsername}</strong>
						</o:nav>
					</div>
				</c:if>
				<c:if test="${empty courseCoordinatorImageUrl}">
					<i class="material-icons exp-place-card__icon" style="font-size:36px;margin-right: 12px" aria-hidden>person</i>
					<div>
						<div>
							<orbis:message code="i18n.exp_termCourseHeader.Coordinato8596663269952724" />
						</div>
						<o:nav action="displayTermCourseCoordinators" anchorId="expTermCourseCoordinatyor" anchor="true" termCourseId="${termCourse.id}" target="_blank">
							<strong>${courseCoordinator.fullNameWithUsername}</strong>
						</o:nav>
					</div>
				</c:if>
			</div>
		</c:if>

		<div style=" display: flex; align-items: center; " class="margin--r--xl">
			<c:if test="${not empty facultyMemberImageUrl && not empty facultyMember && facultyCount==1 }">
				<div class='userImageDiv' style="height:50px; box-sizing: border-box">
					<img src="${facultyMemberImageUrl}" style="height:100%"
						 alt="<orbis:message text='${facultyMember.facultyAdvisor.fullNameWithUsername}' htmlEscape='true' />"
						 title="<orbis:message text='${facultyMember.facultyAdvisor.fullNameWithUsername}' htmlEscape='true' />" />
				</div>
				<div>
					<div><orbis:message code="i18n.exp_termCourseHeader.FacultyAss1564890718773550" /></div>
					<o:nav anchor="true" action="displayTermCourseFaculty" tcId="${termCourse.id}" target="_blank">
						<strong>${facultyMember.facultyAdvisor.fullNameWithUsername }</strong>
					</o:nav>
				</div>
			</c:if>
			<c:if test="${empty facultyMemberImageUrl && not empty facultyMember && facultyCount==1}">
				<i class="material-icons exp-place-card__icon" style="font-size:36px;margin-right: 12px" aria-hidden>person</i>
				<div>
					<div><orbis:message code="i18n.exp_termCourseHeader.FacultyAss1564890718773550" /></div>
					<o:nav anchor="true" action="displayTermCourseFaculty" tcId="${termCourse.id}" target="_blank">
						<strong>${facultyMember.facultyAdvisor.fullNameWithUsername }</strong>
					</o:nav>
				</div>
			</c:if>
			<c:if test="${empty facultyMember && facultyCount > 1 }">
				<i class="material-icons exp-place-card__icon" style="font-size:36px; margin-right: 12px" aria-hidden>person</i>
				<div>
					<div><orbis:message code="i18n.exp_termCourseHeader.FacultyAss1564890718773550" /></div>
					<o:nav anchor="true"  action="displayTermCourseFaculty" tcId="${termCourse.id }" target="_blank"> 
						<strong>${facultyCount}</strong>
					</o:nav>
				</div>
			</c:if>
		</div>
	</div>
</div>