<%@ include file="/WEB-INF/jsp/include.jsp"%>

<c:if test="${configSaved}">
	<orbis:successMessage>
		<orbis:message code="i18n.exp_termsAndConditionsStepConfig.configurationSavedSuccessfully" />
	</orbis:successMessage>
</c:if>

<orbis:navigation title="i18n.exp_stepConfig_trackHours.Managesite3471631908362808" titleArgs="${isL1 ? step.label : step.l2Label}" subTitle="${ isL1 ? step.tct.termCourse.termCourseName : step.tct.termCourse.l2TermCourseName }">
	<orbis:navButton title="i18n.common.backToHome" action="displayHome" />
   	<c:if test="${empty step.template}">
	   	<orbis:navButton title="i18n.exp_termCourseTypeDetails.BacktoTermCourse" action="displayTermCourse" tcId="${step.tct.termCourse.id}" />
	    <orbis:navButton title="i18n.exp_stepConfig_appointment.BackToMana2642912009367091" action="displayTermCourseTypeSteps" tctId="${step.tct.id}" />
    </c:if>
    <c:if test="${not empty step.template && moduleUser.expAdminOrExpCoordinatorOrCourseCoordinator}">
   		<orbis:navButton title="i18n.exp_stepConfig_appointment.BackToTemp6218826827474444" action="displayWorkflowTemplateSteps" templateId="${step.template.id}" />
    </c:if>
</orbis:navigation>

<div class="orbis-posting-actions">
	<center>
		<input class="saveBtn btn btn-primary" onclick="$('form#saveStepForm').submit()" type="button" value="<orbis:message code="i18n.common.save" />">
	</center>
</div>

<script type="text/javascript">
	$(document).ready(function(){
		$(".moduleRadio").change(function(){
			$("#productChecks").show();
			$(".module" + $(this).val()).show();
			$(".module" + $(this).val()).children().prop("disabled", false);
			$(".typeCheckbox").not(".module" + $(this).val()).hide();
			$(".typeCheckbox").not(".module" + $(this).val()).children().prop("disabled", true);
		});
	});
</script>

<div class="panel panel-default">
	<div class="panel-body">
		<form enctype="multipart/form-data" id="saveStepForm" method="post" class="form-horizontal">
            <input type="hidden" name="${_csrf.parameterName}" value="${_csrf.token}"/>
			<o:encrypt input="true" action="saveEcommerceStep" />
			<div class="control-group">
				<label class="control-label" for="label"><orbis:message code="i18n.exp_stepConfig_appointment.Label2158984152441877" /></label>
				<div class="controls">
					${langLabelL1}
					<input type="text" id="label" name="label" value="<orbis:message text='${step.label}' htmlEscape='true' />" />
					<span class="${isBilingualModeOn ? '' : 'hide'}">
						${langLabelL2}
						<input type="text" id="l2Label" name="l2Label" value="<orbis:message text='${step.l2Label}' htmlEscape='true' />" />
					</span>
				</div>
			</div>
			<input type="hidden" name="tctStepId" value="${step.id}" />
			<div class="control-group">
				<label class="control-label" for="instructions"><orbis:message code="i18n.exp_stepConfig_ecommerce.Instructio7980876000543191" /></label>
				<div class="controls">
					${langLabelL1}
					<c:set var="editorId" value="instructions"/>
					<c:set var="editorValue"><c:out value="${step.instructions}" escapeXml="false" /></c:set>
					<c:set var="editorHeight" value="200"/>
					<c:set var="editorWidth" value="600"/>
					<c:set var="editorToolbar" value="richNoUpload"/>
					<%@ include file="/WEB-INF/jsp/ckeditor.jsp"%>
					<div class="${isBilingualModeOn ? '' : 'hide'}">
						${langLabelL2}
						<c:set var="editorId" value="l2Instructions"/>
						<c:set var="editorValue"><c:out value="${step.l2Instructions}" escapeXml="false" /></c:set>
						<c:set var="editorHeight" value="200"/>
						<c:set var="editorWidth" value="600"/>
						<c:set var="editorToolbar" value="richNoUpload"/>
						<%@ include file="/WEB-INF/jsp/ckeditor.jsp"%>
					</div>
				</div>
			</div>
			<div class="control-group">
				<label class="control-label" for="optional"><orbis:message code="i18n.exp_stepConfig_other.OptionalSt2818084034338378"/></label>
				<div class="controls">
					<label class="checkbox">
						<input type="checkbox" name="optional" id="optional" ${step.optional ? 'checked' : ''}>
						<orbis:message code="i18n.exp_stepConfig_other.Enablingth6704229047751898"/>
					</label>
				</div>
			</div>
			<div class="control-group">
				<label class="control-label">Online Store</label>
				<div class="controls">
					<c:set var="currentStore" value=""/>
					<c:forEach var="p" items="${experientialProducts}">
						<c:if test="${currentStore != p.category.module.id}">
							<c:set var="currentStore" value="${p.category.module.id}"/>
							<label class="radio">
								<input type="radio" name="moduleRadio" class="moduleRadio" value="${p.category.module.id}">
								${p.category.module.moduleName}
							</label>
						</c:if>
					</c:forEach>
				</div>
			</div>
			<div class="control-group" id="productChecks" style="display:none">
				<label class="control-label"><orbis:message code="i18n.exp_stepConfig_ecommerce.RequiredPu0179082338852627" /></label>
				<div class="controls">
					<c:forEach var="p" items="${experientialProducts}">
						<label class="checkbox module${p.category.module.id} typeCheckbox">
							<input type="checkbox" name="requiredPurchase" value="${p.id}" ${o:contains(selectedProducts, p.id) ? 'checked' : '' }>
							${p.category.name} - ${p.label}
							<c:if test="${o:contains(selectedProducts, p.id)}">
								<script type="text/javascript">
									$(document).ready(function(){
										$(".moduleRadio[value='${p.category.module.id}']").prop("checked", true);
										$(".moduleRadio[value='${p.category.module.id}']").change();
									});
								</script>
							</c:if>
						</label>
					</c:forEach>
				</div>
			</div>
		</form>
	</div>
</div>

<div class="orbis-posting-actions">
	<center>
		<input class="saveBtn btn btn-primary" onclick="$('form#saveStepForm').submit()" type="button" value="<orbis:message code="i18n.common.save" />">
	</center>
</div>
