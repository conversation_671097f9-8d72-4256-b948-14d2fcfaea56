<%@ include file="/WEB-INF/jsp/include.jsp"%>

<%@ include file="/WEB-INF/jsp/grid/grid_paging.jsp"%>

<g:table>
	<g:column ordinal="-1">
		<jsp:attribute name="header">
			<orbis:message code="i18n.exp_fieldSupervisorStudentsAjax.Actions6381259039980671" />
		</jsp:attribute>
		<jsp:body>
			<c:set var="orbisBuildFormParams">{action:'<o:encrypt action="displayRecord" />', recordId:'${rId}'}</c:set>
			<%@ include file="/WEB-INF/jsp/grid/grid_viewButton.jsp"%>
		</jsp:body>
	</g:column>
	
	<g:column colID="experienceName">
		${expTypeName}${not empty tctName ? o:concat(' - ', tctName) : ''}
	</g:column>
</g:table>
