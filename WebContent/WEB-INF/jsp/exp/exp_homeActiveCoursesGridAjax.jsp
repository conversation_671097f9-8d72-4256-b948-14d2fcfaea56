<%@ include file="/WEB-INF/jsp/include.jsp"%>

<%@ include file="/WEB-INF/jsp/grid/grid_paging.jsp"%>

<script type="text/javascript">
	$(document).ready(function(){
		$(".js--tooltip").tooltip();
	});
</script>

<g:table>
	<g:column ordinal="-1">
		<jsp:attribute name="header">
			<orbis:message code="i18n.exp_homeActiveCoursesGridAjax.Actions8940991962793792" />
		</jsp:attribute>
		<jsp:body>
			<c:set var="orbisBuildFormParams">
				{action: '<o:encrypt action="displayTermCourse" />', termCourseId:'${ tcId }', fromPrograms:'${isProgram ? 'ActivePrograms' : 'ActiveCourses'}', fromChart:'${fromChart}'}
			</c:set>
			<%@ include file="/WEB-INF/jsp/grid/grid_viewButton.jsp"%>
		</jsp:body>
	</g:column>	
	<g:column colID="description">
		<orbis:truncatedValue charCount="20" value="${value}"/>
	</g:column>
	<g:column colID="expTypes">
		<c:forEach var="expt" items="${ expTypes }" varStatus="loop">
			${ expt } <br />
		</c:forEach>
	</g:column>
	<g:column colID="facultyOwners">
		<c:forEach var="ip" items="${facultyOwners}" end="0">
			${ip}
			<c:if test="${fn:length(facultyOwners) > 1}">
				<a href="#ip${tcId}" data-toggle="modal"><orbis:message code="i18n.exp_homeActiveCoursesGridAjax.0more0886437934468634" arguments="${fn:length(facultyOwners)-1}" /></a>
				<div id="ip${tcId}" class="modal hide" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
						<h3 id="myModalLabel">
						<orbis:message code="i18n.exp_homeActiveCoursesGridAjax.nameFacult0344244222119123"  arguments="${name }"/></h3>
					</div>
					<div class="modal-body">
						<ul>
							<c:forEach var="ip" items="${facultyOwners}">
								<li>
									${ip}
								</li>
							</c:forEach>
						</ul>
					</div>
					<div class="modal-footer">
						<button class="btn" data-dismiss="modal" aria-hidden="true"><orbis:message code="i18n.global_home_stats.Close" /></button>
					</div>
				</div>
			</c:if>
		</c:forEach>
	</g:column>
	<g:column colID="termCourseTags">
		<c:forEach var="tag" items="${termCourseTags}" end="0">
			${tag}
			<c:if test="${fn:length(termCourseTags) > 1}">
				<a href="#tag${tcId}" data-toggle="modal"><orbis:message code="i18n.exp_homeActiveCoursesGridAjax.0more0886437934468634" arguments="${fn:length(termCourseTags)-1}" /></a>
				<div id="tag${tcId}" class="modal hide" tabindex="-1" role="dialog" aria-labelledby="tagsModalLabel" aria-hidden="true">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
						<h3 id="tagsModalLabel">
							<orbis:message code="i18n.exp_homeActiveCoursesGridAjax.0Tags6334937629445297"  arguments="${name }"/></h3>
					</div>
					<div class="modal-body">
						<ul>
							<c:forEach var="tag" items="${termCourseTags}">
								<li>
									${tag}
								</li>
							</c:forEach>
						</ul>
					</div>
					<div class="modal-footer">
						<button class="btn" data-dismiss="modal" aria-hidden="true"><orbis:message code="i18n.global_home_stats.Close" /></button>
					</div>
				</div>
			</c:if>
		</c:forEach>
	</g:column>
</g:table>