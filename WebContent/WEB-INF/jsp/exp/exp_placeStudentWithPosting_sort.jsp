<%@ include file="/WEB-INF/jsp/include.jsp"%>
<orbis:addComponent component="orbisNotePopups" />

<div class="exp-filter">
	<div class="exp-filter__header">
		<label for="sort" class="exp-filter__title">
			<orbis:message code="i18n.exp_placeStudent_options.Sort3536994505490249" />:
		</label>
	</div>
	<div style="display:flex;max-width:100%;align-items:center;">
		<select id="sort" name="sort">
			<option value="COMPATIBILITY">
				<orbis:message code="i18n.exp_placeStudent_sort.Compatibil0653871658965982" />
			</option>
			<option value="NAME">
				<orbis:message code="i18n.exp_placeStudent_sort.Name0431296151001223" />
			</option>
			<c:if test="${ not empty step.studentExperience.student.student.owner.geolocation }">
				<option value="DISTANCE">
					<orbis:message code="i18n.exp_placeStudentWithPosting_sort.Distance6973846130301519" />
				</option>
			</c:if>
		</select>
		<span id="compatibilityNote" class="notePopup" 
			  noteTitle="<orbis:message code="i18n.exp_placeStudentWithPosting_sort.Compatibil8088211774486617" htmlEscape="true" />">
			<p><orbis:message code="i18n.exp_placeStudentWithPosting_sort.Compatibil3053069803618212" />:</p>
			<ol>
				<li><orbis:message code="i18n.exp_placeStudentWithPosting_sort.Astudentsr4796111761562512" /></li>
				<li><orbis:message code="i18n.exp_placeStudentWithPosting_sort.Whetherast2394356596423084" /></li>
				<li><orbis:message code="i18n.exp_placeStudentWithPosting_sort.Whetherast6085735031338049" /></li>
				<li><orbis:message code="i18n.exp_placeStudentWithPosting_sort.Astudentsd0302254047707430" /></li>
			</ol>
		</span>
	</div>
</div>

<script>
	$(function(){
		$("#sort").change(function(){
			$("#compatibilityNote").toggle( this.value === "COMPATIBILITY" );
		});
	});
</script>