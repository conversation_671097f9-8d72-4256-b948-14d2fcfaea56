<c:if test="${!o:isSpiralRobotCompatible(siteElement, pageContext)}">
	<c:set var="i18nAnswerOptions" value="${isL1 ? a.answerMap : a.answerMap2}" />
	
	<c:set var="otherText" value="${isL1 ? a.otherAnswer : a.otherAnswer2}" />
	<div class="inputContainer" style="display: inline-block;">
		<%-- RENDER VERTICAL FORMAT --%>
		
		<script type="text/javascript">
	
			$(document).ready(function(){
				$("[name='answer_${a.displayedQuestionOrder}_other']").change(function(){
					var $allChecks = $(".chkGrp${a.displayedQuestionOrder}");
					if($(this).is(":checked"))
					{
						$allChecks.removeClass("required");
						<c:if test="${a.question.requiredFlag}">
							$("input[name=other_${a.displayedQuestionOrder}]").addClass("required");
						</c:if>
					}
					else
					{
						<c:if test="${a.question.requiredFlag}">
							$allChecks.addClass("required");
						</c:if>
						$("input[name=other_${a.displayedQuestionOrder}]").removeClass("required");
					}
				});
				
				$("input#other_${a.displayedQuestionOrder}").change(function(){
					if(orbisApp.trim($(this).val(), " "))
					{
						$("[name='answer_${a.displayedQuestionOrder}_other']").prop("checked", true);
						$("[name='answer_${a.displayedQuestionOrder}_other']").change();
					}
					else
					{
						$("[name='answer_${a.displayedQuestionOrder}_other']").prop("checked", false);
						$("[name='answer_${a.displayedQuestionOrder}_other']").change();
					}
			});
		
				$("[name='answer_${a.displayedQuestionOrder}_other']").change();
			});
			
		</script>
		
		<c:if test="${a.question.displayFormatIndicator == 1}">
		
			<%-- RENDER VERTICALLY WITH SCROLL-WIDGET --%>
			<c:if test="${a.question.optionsInScrollWidget == true}">
				<script type="text/javascript">
					var checkAll${a.displayedQuestionOrder} = false;
					$(document).ready(function () {
						$("#checkAll${a.displayedQuestionOrder}").click(function() {
							checkAll${a.displayedQuestionOrder} = $(this).is(":checked");				
							$("input.chkGrp${a.displayedQuestionOrder}").attr("checked", checkAll${a.displayedQuestionOrder});
						});
					});
				</script>
				<div style="border:1px solid #B0B0B0;overflow:hidden;width:300px;-moz-border-radius: 4px; -webkit-border-radius: 4px;">
					<div style="overflow:hidden;background-color: #B0B0B0;">
						<table width="100%" cellspacing="0">
							<col width="8%"><col width="92%">
							<tr>
								<td><input type="checkbox" id="checkAll${a.displayedQuestionOrder}" /></td>
								<td style="padding-top:4px;"><orbis:message code="i18n.ccrm_questionChoiceMultiEdit.selectAllUPPERCASE" /></td>
							</tr>
						</table>
					</div>
					<div style="overflow:auto;height:100px;">
						<table width="100%" cellspacing="0">
							<col width="8%"><col width="92%">
							<c:forEach var="section" items="${i18nAnswerOptions}">
								<c:if test = "${section.key == 1 || section.key == 3}">
									<c:forEach var="option" items="${section.value}" varStatus="status">
										<tr>
											<c:set var="val" value="" />
											<c:if test="${fn:contains(option.value.text,'|')}">
												<c:set var="val" value="${fn:split(option.value.text, '|')}" />
											</c:if>
											<td style="border-bottom:1px solid #A0A0A0;">
												<input type="checkbox" id="answer_${a.displayedQuestionOrder}_${status.count}" class="arq_${ a.question.id } chkGrp${a.displayedQuestionOrder} <c:if test="${a.question.requiredFlag}">required</c:if>" name="answer_${a.displayedQuestionOrder}" value="${val == '' ? option.value.text :(isL1 ? val[0] : val[1])}" <c:if test="${option.value.checked == true}">checked</c:if>/>
											</td>
											<td style="border-bottom:1px solid #A0A0A0;padding-top:4px;">
												<label for="answer_${a.displayedQuestionOrder}_${status.count}">
													${val == '' ? option.value.text :(isL1 ? val[0] : val[1])}
												</label>
											</td>
										</tr>
									</c:forEach>
								</c:if>
								<c:if test="${section.key == 2 || section.key == 4}">
									<c:if test="${a.question.optionIncludeOtherFlag == true}">
										<tr>
											<td style="border-bottom:1px solid #A0A0A0;">
												<label for="other_${a.displayedQuestionOrder}_check" style="font-size: 0; position: absolute;"><orbis:message code="i18n.ccrm_questionChoiceMultiEdit.Other" /></label>
												<input type="checkbox" id="other_${a.displayedQuestionOrder}_check" class="arq_${ a.question.id }" onclick="unsetCheckBoxOther('${a.displayedQuestionOrder}')" ondblclick="unsetCheckBoxOther('${a.displayedQuestionOrder}')" name="answer_${a.displayedQuestionOrder}_other" value="Other" <c:if test="${not empty otherText}">checked</c:if>/>
											</td>
											<td style="border-bottom:1px solid #A0A0A0;">
												<label>
													<orbis:message code="i18n.ccrm_questionChoiceMultiEdit.other" />: <input type="text" onkeydown="setCheckBoxOther('${a.displayedQuestionOrder}')" onclick="setCheckBoxOther('${a.displayedQuestionOrder}')" ondblclick="setCheckBoxOther('${a.displayedQuestionOrder}')" id="other_${a.displayedQuestionOrder}" name="other_${a.displayedQuestionOrder}" value="${otherText}"/>
												</label>
											</td>
										</tr>
									</c:if>
								</c:if>
							</c:forEach>
						</table>
					</div>
				</div>
			</c:if>
			
			<%-- RENDER VERTICALLY WITHOUT SCROLL-WIDGET --%>
			<c:if test="${a.question.optionsInScrollWidget == false}">
				<table>
					<c:forEach var="section" items="${i18nAnswerOptions}">
						<c:if test = "${section.key == 1 || section.key == 3}">
							<c:forEach var="option" items="${section.value}" varStatus="status">
								<tr>
									<c:set var="val" value="" />
									<c:if test="${fn:contains(option.value.text,'|')}">
										<c:set var="val" value="${fn:split(option.value.text, '|')}" />
									</c:if>
									<td>
									<c:set var="selTest" value = "" />
									<c:if test = "${val == '' ? option.value.text == 'b' : val[0] == 'b' }" >
										<c:set var="selTest" value = "sel_Questions_test" />
									</c:if>
										<input type="checkbox" id="answer_${a.displayedQuestionOrder}_${status.count}" class="${selTest } arq_${ a.question.id } chkGrp${a.displayedQuestionOrder} <c:if test="${a.question.requiredFlag}">required</c:if>" name="answer_${a.displayedQuestionOrder}" value="${val == '' ? option.value.text :(isL1 ? val[0] : val[1])}" <c:if test="${option.value.checked == true}">checked</c:if>/>
									</td>
									<td>
										<label for="answer_${a.displayedQuestionOrder}_${status.count}">
											${val == '' ? option.value.text :(isL1 ? val[0] : val[1])}
										</label>
									</td>
								</tr>
							</c:forEach>
						</c:if>
						<c:if test = "${section.key == 2 || section.key == 4}">
							<c:if test = "${a.question.optionIncludeOtherFlag == true}">
								<tr>
									<td>
										<label for="answer_${a.displayedQuestionOrder}_otherCheck" style="font-size: 0; position: absolute;"><orbis:message code="i18n.ccrm_questionChoiceMultiEdit.Other" /></label>
										<input id="answer_${a.displayedQuestionOrder}_otherCheck" class="arq_${ a.question.id }" type="checkbox" data-order="${a.displayedQuestionOrder}" onclick="unsetCheckBoxOther('${a.displayedQuestionOrder}')" ondblclick="unsetCheckBoxOther('${a.displayedQuestionOrder}')" name="answer_${a.displayedQuestionOrder}_other" value="Other" <c:if test="${not empty otherText}">checked</c:if>/>
									</td>
									<td>
										<label for="answer_${a.displayedQuestionOrder}_other" class="inline-label">
											<orbis:message code="i18n.ccrm_questionChoiceMultiEdit.other" />
										</label>
										<input id="answer_${a.displayedQuestionOrder}_other" type="text" onkeydown="setCheckBoxOther('${a.displayedQuestionOrder}')" onclick="setCheckBoxOther('${a.displayedQuestionOrder}')" ondblclick="setCheckBoxOther('${a.displayedQuestionOrder}')" name="other_${a.displayedQuestionOrder}" value="${otherText}"/>
									</td>
								</tr>
							</c:if>
						</c:if>
					</c:forEach>
				</table>
			</c:if>
			
		</c:if>
		
		<%-- RENDER HORIZONTAL FORMAT --%>
		<c:if test="${a.question.displayFormatIndicator == 2}">
			<table>
				<tr>
					<c:if test = "${a.question.optionIncludeOtherFlag == true}">
						<td><orbis:message code="i18n.ccrm_questionChoiceMultiEdit.other" /></td>
					</c:if>
					<c:forEach var="option" items="${isL1 ? a.question.optionChoiceMap : a.question.optionChoiceMap2}" varStatus="status">
						<td>
							<label for="answer_${a.displayedQuestionOrder}_${status.count}">
								${option.value}
							</label>
						</td>
					</c:forEach>		
				</tr>
				<tr>
					<c:forEach var="section" items="${i18nAnswerOptions}" varStatus="status">
						<c:if test = "${section.key == 1 || section.key == 3}">
							<c:forEach var="option" items="${section.value}" varStatus="status">
								<td>
									<input id="answer_${a.displayedQuestionOrder}_${status.count}" type="checkbox" class="arq_${ a.question.id } chkGrp${a.displayedQuestionOrder} <c:if test="${a.question.requiredFlag}">required</c:if>" name="answer_${a.displayedQuestionOrder}" value="${val == '' ? option.value.text :(isL1 ? val[0] : val[1])}" <c:if test="${option.value.checked == true}">checked</c:if>/>
								</td>
							</c:forEach>
						</c:if>
						<c:if test = "${section.key == 2 || section.key == 4}">
							<c:if test = "${a.question.optionIncludeOtherFlag == true}">
								<td><input type="checkbox" data-order="${a.displayedQuestionOrder}" onclick="unsetCheckBoxOther('${a.displayedQuestionOrder}')" ondblclick="unsetCheckBoxOther('${a.displayedQuestionOrder}')" name="answer_${a.displayedQuestionOrder}_other" value="Other" <c:if test="${not empty a.otherAnswer}">checked</c:if>/></td>
								<td><input  class="sf_input" type="text" onkeydown="setCheckBoxOther('${a.displayedQuestionOrder}')" onclick="setCheckBoxOther('${a.displayedQuestionOrder}')" ondblclick="setCheckBoxOther('${a.displayedQuestionOrder}')" name="other_${a.displayedQuestionOrder}" value="${a.otherAnswer}"></td>
							</c:if>
						</c:if>
					</c:forEach>
				</tr>
			</table>
		</c:if>
	</div>
</c:if>
<c:if test="${o:isSpiralRobotCompatible(siteElement, pageContext)}">
	
	<c:set var="i18nAnswerOptions" value="${isL1 ? a.answerMap : a.answerMap2}" />

	<c:set var="otherText" value="${isL1 ? a.otherAnswer : a.otherAnswer2}" />
	
	<ui:checkboxGroup i18n_title="${questionText}" name="answer_${a.displayedQuestionOrder}" required="${a.question.requiredFlag}" noSidebar="true">
		<c:forEach var="section" items="${i18nAnswerOptions}">
			<c:if test = "${section.key == 1 || section.key == 3}">
				<c:forEach var="option" items="${section.value}" varStatus="status">
					<c:if test="${option.value.checked == true}">
						<c:set var="otherThanOtherSet" value="true" />
					</c:if>
					<ui:checkboxGroupItem value="${option.value.text}" checked="${option.value.checked == true}">
						${option.value.text}
					</ui:checkboxGroupItem>
				</c:forEach>
				<c:if test="${a.question.optionIncludeOtherFlag}">
					<ui:checkboxGroupOther name="answer_${a.displayedQuestionOrder}_other" value="Other" otherBoxName="other_${a.displayedQuestionOrder}" />
				</c:if>
			</c:if>
		</c:forEach>
	</ui:checkboxGroup>
	
</c:if>