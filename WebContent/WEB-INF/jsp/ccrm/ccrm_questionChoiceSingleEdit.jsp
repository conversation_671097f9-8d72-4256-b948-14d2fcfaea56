<c:if test="${!o:isSpiralRobotCompatible(siteElement, pageContext)}">
	<script type="text/javascript">
	
		$(document).ready(function(){
			var firstTime${a.displayedQuestionOrder} = true;
			$("[name='answer_${a.displayedQuestionOrder}']").change(function(){
				var $allChecks = $("[name='answer_${a.displayedQuestionOrder}']");
				<c:if test="${a.question.displayFormatIndicator == 1 || a.question.displayFormatIndicator == 2}">
					if($("#otherRadio_${a.displayedQuestionOrder}").is(":checked"))
					{
						$allChecks.removeClass("required");
						$("input[name=other_${a.displayedQuestionOrder}]").addClass("required");
						$("input[name=other_${a.displayedQuestionOrder}]").parents(".singleSelectContainer:first").removeClass("inputContainer");
						if(!firstTime${a.displayedQuestionOrder})
						{
							$("input[name=other_${a.displayedQuestionOrder}]").parents("form:eq(0)").data("validator").element("input[name=other_${a.displayedQuestionOrder}]");
						}
					}
					else
					{
						<c:if test="${a.question.requiredFlag}">
							$allChecks.addClass("required");
						</c:if>
						$("input[name=other_${a.displayedQuestionOrder}]").removeClass("required");
						if(!firstTime${a.displayedQuestionOrder})
						{
							$("input[name=other_${a.displayedQuestionOrder}]").parents("form:eq(0)").data("validator").element("input[name=other_${a.displayedQuestionOrder}]");
						}
						$("input[name=other_${a.displayedQuestionOrder}]").parents(".singleSelectContainer:first").addClass("inputContainer");
					}
				</c:if>
				<c:if test="${a.question.displayFormatIndicator == 3}">
					if($("#arq_${a.question.id}").val() == 'Other')
					{
						$allChecks.removeClass("required");
						$("input[name=other_${a.displayedQuestionOrder}]").addClass("required");
						if(!firstTime${a.displayedQuestionOrder})
						{
							$("input[name=other_${a.displayedQuestionOrder}]").parents("form:eq(0)").data("validator").element("input[name=other_${a.displayedQuestionOrder}]");
						}
					}
					else
					{
						<c:if test="${a.question.requiredFlag}">
							$allChecks.addClass("required");
						</c:if>
						$("input[name=other_${a.displayedQuestionOrder}]").removeClass("required");
						if(!firstTime${a.displayedQuestionOrder})
						{
							$("input[name=other_${a.displayedQuestionOrder}]").parents("form:eq(0)").data("validator").element("input[name=other_${a.displayedQuestionOrder}]");
						}
					}
				</c:if>
				
				firstTime${a.displayedQuestionOrder} = false;
			});
			
			$("input[name=other_${a.displayedQuestionOrder}]").change(function(){
				if(orbisApp.trim($(this).val(), " "))
				{
					$("#otherRadio_${a.displayedQuestionOrder}").prop("checked", true);
					$("#otherRadio_${a.displayedQuestionOrder}").change();
				}
				else
				{
					$("#otherRadio_${a.displayedQuestionOrder}").prop("checked", false);
					$("#otherRadio_${a.displayedQuestionOrder}").change();
				}
			});
			
			$("#otherRadio_${a.displayedQuestionOrder}").change();
		});
	
	</script>
	
	
	<c:set var="i18nAnswerOptions" value="${isL1 ? a.answerMap : a.answerMap2}" />
	
	<div class="inputContainer singleSelectContainer" style="display: inline-block;">
		<table>
			<c:if test="${a.question.displayFormatIndicator == 1}">
				<c:set var="otherThanOtherSet" value="false" />
				<c:forEach var="section" items="${i18nAnswerOptions}">
					<c:if test = "${section.key == 1 || section.key == 3}">
						<c:forEach var="option" items="${section.value}" varStatus="status">
							<c:if test="${option.value.checked == true}">
								<c:set var="otherThanOtherSet" value="true" />
							</c:if>
							<tr>
								<td>
									<input id="answer_${a.displayedQuestionOrder}_${status.count}" type="radio" onclick="unsetRadioOther('${a.displayedQuestionOrder}')" ondblclick="unsetRadioOther('${a.displayedQuestionOrder}')" name="answer_${a.displayedQuestionOrder}" class="arq_${ a.question.id } <c:if test="${a.question.requiredFlag}">required</c:if>" value="${option.value.text}" <c:if test="${option.value.checked == true}">checked</c:if>/>
								</td>
								<td>
									<label for="answer_${a.displayedQuestionOrder}_${status.count}">
										${option.value.text}
									</label>
								</td>
							</tr>
						</c:forEach>
					</c:if>
					<c:if test = "${section.key == 2 || section.key == 4}">
						<c:if test = "${a.question.optionIncludeOtherFlag == true}">
							<tr>
								<td >
								
									<label for="otherRadio_${a.displayedQuestionOrder}" style="font-size: 0; position: absolute;"><orbis:message code="i18n.ccrm_questionChoiceSingleEdit.Other" /></label>
									<input type="radio" id="otherRadio_${a.displayedQuestionOrder}" name="answer_${a.displayedQuestionOrder}" value="Other" <c:if test="${a.otherFlag || (!otherThanOtherSet && not empty a.answerText)}">checked</c:if>/></td>
								<td>
									<label for="other_${a.displayedQuestionOrder}" class="inline-label">
										<orbis:message code="i18n.ccrm_questionChoiceSingleEdit.other" />
									</label>
								<input type="text" id="other_${a.displayedQuestionOrder}" onkeydown="setRadioOther('${a.displayedQuestionOrder}')" onclick="setRadioOther('${a.displayedQuestionOrder}')" ondblclick="setRadioOther('${a.displayedQuestionOrder}')" name="other_${a.displayedQuestionOrder}" <c:if test="${!otherThanOtherSet && not empty a.answerText}">value="${a.answerText}"</c:if>/></td>
							</tr>
						</c:if>
					</c:if>
				</c:forEach>
			</c:if>
			<c:if test="${a.question.displayFormatIndicator == 2}">
				<c:set var="otherThanOtherSet" value="false" />
				<tr>
					<c:forEach var="option" items="${isL1 ? a.question.optionChoiceMap : a.question.optionChoiceMap2}">
						<td>${option.value}</td>
					</c:forEach>
					<c:if test = "${a.question.optionIncludeOtherFlag == true}">
						<td><orbis:message code="i18n.ccrm_questionChoiceSingleEdit.other" /></td>
					</c:if>
				</tr>
				<tr>
					<c:forEach var="section" items="${i18nAnswerOptions}" varStatus="status">
						<c:if test = "${section.key == 1 || section.key == 3}">
							<c:forEach var="option" items="${section.value}" varStatus="status">
								<c:if test="${option.value.checked == true}">
									<c:set var="otherThanOtherSet" value="true" />
								</c:if>
								<td  ><input type="radio" id="answer_${a.displayedQuestionOrder}_${status.count}" onclick="unsetRadioOther('${a.displayedQuestionOrder}')" ondblclick="unsetRadioOther('${a.displayedQuestionOrder}')" name="answer_${a.displayedQuestionOrder}" value="${option.value.text}" class="arq_${ a.question.id } <c:if test="${a.question.requiredFlag}">required</c:if>" <c:if test="${option.value.checked == true}">checked</c:if>/></td>
							</c:forEach>
						</c:if>
						<c:if test = "${section.key == 2 || section.key == 4}">
							<c:if test = "${a.question.optionIncludeOtherFlag == true}">
								<td>
									<label for="otherRadio_${a.displayedQuestionOrder}" style="font-size: 0; position: absolute;"><orbis:message code="i18n.ccrm_questionChoiceSingleEdit.Other" /></label>
									<input type="radio" id="otherRadio_${a.displayedQuestionOrder}" name="answer_${a.displayedQuestionOrder}" value="Other" <c:if test="${a.otherFlag || (!otherThanOtherSet && not empty a.answerText)}">checked</c:if>/>
									<label for="other_${a.displayedQuestionOrder}" style="font-size: 0; position: absolute;"><orbis:message code="i18n.ccrm_questionChoiceSingleEdit.Other" /></label>
									<input id="other_${a.displayedQuestionOrder}" type="text" onkeydown="setRadioOther('${a.displayedQuestionOrder}')" onclick="setRadioOther('${a.displayedQuestionOrder}')" ondblclick="setRadioOther('${a.displayedQuestionOrder}')" name="other_${a.displayedQuestionOrder}" <c:if test="${!otherThanOtherSet && not empty a.answerText}">value="${a.answerText}"</c:if>/></td>
							</c:if>
						</c:if>
					</c:forEach>
				</tr>
			</c:if>
			<c:if test="${a.question.displayFormatIndicator == 3}">
				<c:set var="otherThanOtherSet" value="false" />
				<tr>
					<td>
						<label for="arq_${a.question.id}" style="font-size: 0; position: absolute;">${questionText}</label>
						<select class="<c:if test="${a.question.requiredFlag}">required</c:if>" id="arq_${a.question.id}" name="answer_${a.displayedQuestionOrder}" onchange="if (this.options[this.options.selectedIndex].value!='Other' && ${a.question.optionIncludeOtherFlag}) { unsetSelectOther('${a.displayedQuestionOrder}'); } else if (this.options[this.options.selectedIndex].value=='Other' && ${a.question.optionIncludeOtherFlag}) { setSelectOther('${a.displayedQuestionOrder}'); }">			   
							<option value="">--<orbis:message code="i18n.common.select" />--</option>
							<c:forEach var="section" items="${i18nAnswerOptions}">
								<c:if test = "${section.key == 1 || section.key == 3}">
									<c:forEach var="option" items="${section.value}">
										<c:if test="${option.value.checked == true}">
											<c:set var="otherThanOtherSet" value="true" />
										</c:if>
										<option value="${option.value.text}" <c:if test="${option.value.checked == true}">SELECTED</c:if>>${option.value.text}</option>
									</c:forEach>
								</c:if>
								<c:if test = "${section.key == 2 || section.key == 4}">
									<c:if test = "${a.question.optionIncludeOtherFlag == true}">
											<option value="Other" <c:if test="${!otherThanOtherSet && not empty a.answerText}">SELECTED</c:if>><orbis:message code="i18n.ccrm_questionChoiceSingleEdit.Other" /></option>
									</c:if>
								</c:if>
							</c:forEach>
						</select>
					<c:if test = "${a.question.optionIncludeOtherFlag == true}">
						<td>
							<div id="otherDiv_${a.displayedQuestionOrder}" style="${otherThanOtherSet || empty a.answerText ? "display:none" : "display:block"}">
							<label for="other_${a.displayedQuestionOrder}" style="font-size: 0; position: absolute;"><orbis:message code="i18n.ccrm_questionChoiceSingleEdit.Other" /></label>
							<input id="other_${a.displayedQuestionOrder}" class="sf_input <c:if test="${!otherThanOtherSet && not empty a.answerText}">required</c:if>"  type="text" onkeydown="setSelectOther('${a.displayedQuestionOrder}')" onclick="setSelectOther('${a.displayedQuestionOrder}')" ondblclick="setSelectOther('${a.displayedQuestionOrder}')" name="other_${a.displayedQuestionOrder}" <c:if test="${!otherThanOtherSet && not empty a.answerText}">value="${a.answerText}"</c:if>/></div></td>
					</c:if>
				</tr>
			</c:if>
		</table>
	</div>
	
	<script type="text/javascript">
		<c:if test="${a.question.displayFormatIndicator == 3}">
		//PRC comment: Logic to prevent the "Other" text field from showing as "Other|province name", as it is stored in the DB.
		//when selected province is "Other"
			if($("#arq_${a.question.id}").val() == 'Other')
			{
				var otherStoredValueArray = new Array();
				var otherStoredValue = $("input[name=other_${a.displayedQuestionOrder}]").val();
				otherStoredValueArray = otherStoredValue.split("|");
				if(otherStoredValueArray.length>1)
				{
					$("input[name=other_${a.displayedQuestionOrder}]").val(otherStoredValueArray[1]);
				}
			}
		//end PRC comment.
		</c:if>
	</script>
</c:if>
<c:if test="${o:isSpiralRobotCompatible(siteElement, pageContext)}">
	<%@ include file="/WEB-INF/spiralRobot/jsp/ccrm/ccrm_questionChoiceSingleEdit.jsp" %>
</c:if>